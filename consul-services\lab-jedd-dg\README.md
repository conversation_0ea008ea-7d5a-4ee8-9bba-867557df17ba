<PERSON><PERSON>'s lab configuration files (consul, nomad, docker) are under this repo at nomad/lab-jedd-dg/FYI/

My lab consists of a server (dg-pan-01) and a workstation (jarre) - both running Debian GNU/Linux.


Normal usage is:

terraform[.exe] init                         # Done once per place you run this from
terraform[.exe] plan -var-file=filename      # Can optionally output this to a .out file - but it describes what *will* be done
terraform[.exe] apply -var-file=filename     # Actually goes and creates / deletes etc the hosts.

Note that if you get some weird errors 'Node does not exist ...' - run the 'terraform apply' again.

## See Also

HashiCorp Manual [Consul Service](https://registry.terraform.io/providers/hashicorp/consul/latest/docs/resources/service)
