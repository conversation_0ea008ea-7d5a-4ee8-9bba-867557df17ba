
// terraform consul services registration for <PERSON><PERSON>'s lab (dg)

terraform {
  required_providers {
    consul = {
      source = "hashicorp/consul"
      version = "2.11.0"
    }
  }
}

provider "consul" {
  address = "dg-pan-01.int.jeddi.org:8500"
  datacenter = "dg"
}


variable "groups" {
  type = map(object({
    port = number
    service_name = string
    // metrics_path = string
    // tags = list(string)
    nodes = list(string)
    meta = map(string)
  }))
}


variable "nodes" {
  type = set(string)
}



#variable "pings" {
#  type = map(object({
#    nodes = list(string)
#  }))
#}

module "promtarget" {
  for_each = var.groups
  source = "./promtarget"
  service = each.value
}

module "pingtarget" {
  for_each = var.nodes
  source = "./pingtarget"
  node = each.value
}

