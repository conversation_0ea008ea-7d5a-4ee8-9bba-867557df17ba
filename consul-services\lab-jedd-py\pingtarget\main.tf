
// pingtarget - jedd lab DG


variable "node"  {
  type = string
}

#variable "service" {
#  type = object({
#    service_name = string
#    // metrics_path = string
#    // tags = list(string)
#    nodes = list(string)
#    meta = map(string)
#  })
#}


resource "consul_node" "pingtarget" {
  #  for_each = toset(var.nodes)

  name = var.node
  address = var.node

  meta = {
    "external-node"  = "true"
    "external-probe" = "true"
  }
}

resource "consul_service" "pingtarget" {
  # for_each = consul_node.pingtarget
  name = "pingtarget"
  node = consul_node.pingtarget.name
}




