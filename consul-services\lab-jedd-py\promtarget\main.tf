
// promtarget - jedd lab DG - taken from Eamon's PROD env

variable "service" {
  type = object({
    port = number
    service_name = string
    // metrics_path = string
    // tags = list(string)
    nodes = list(string)
    meta = map(string)
  })
}


resource "consul_node" "openmetrics" {
  for_each = toset(var.service.nodes)
  name = split(".", split(":", each.value)[0])[0]
  address = split(":", each.value)[0]
  meta = {
    "external-node"  = "true"
    "external-probe" = "true"
  }
}


resource "consul_service" "openmetrics" {
  for_each = consul_node.openmetrics
  name = var.service.service_name
  node = each.value.name
  port = var.service.port

  check {
    check_id = "service:openmetrics"
    name = "OpenMetrics health check"
    status = "passing"
    http = format("http://%s:%d", each.value.address, var.service.port)
    tls_skip_verify = true
    method = "GET"
    interval = "15m"
    timeout = "5s"
  }

  meta = var.service.meta
}

//resource "consul_service" "https" {
//  for_each = toset(var.https)
//  name = "https"
//  node = split(":", each.value)[0]
//  port = split(":", each.value)[1]
//  tags = ["https"]
//
//  // Don't health check as we are using the ssl-exporter to get health information, we dont need to double hit these
//  // servers.
//}
