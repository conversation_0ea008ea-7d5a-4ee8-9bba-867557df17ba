
PROD ObsCol cluster

Create a separate set of ping targets - these are ICMP (no prometheus / telegraf 
agent, no nomad, no nothing) targets that don't fit the formulae of promtarget
that we're using in ../prod/ to create the various agent-based nodes in Consul.

Created 2021-10 for the 'Member Servers' - about 1100 Windows hosts - prior to them
being agented.  We'll doubtless have similar ping-only capable targets forever.


terraform[.exe] init                         # Done once per place you run this from, after a git checkout
terraform[.exe] plan -var-file=filename      # Can optionally output this to a .out file - but it describes what *will* be done
terraform[.exe] apply -var-file=filename     # Actually goes and creates / deletes etc the hosts.

Note that if you get some weird errors 'Node does not exist ...' - run the 'terraform apply' again.

## See Also

HashiCorp Manual [Consul Service](https://registry.terraform.io/providers/hashicorp/consul/latest/docs/resources/service)
