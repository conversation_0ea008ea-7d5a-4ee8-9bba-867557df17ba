
// ObsCol prod - terraform consul services - registration for PING icmp targets

terraform {
  required_providers {
    consul = {
      source = "hashicorp/consul"
      version = "2.11.0"
    }
  }
}

provider "consul" {
  address = "pl0992obscol01.nsw.education:8500"
  datacenter = "dc-cir-un-prod"
  token = "b68e1c4b-dac2-990b-1743-0d13056b56a5"
}

variable "nodes" {
  type = set(string)
  
}

module "pingtarget" {
  for_each = var.nodes
  source = "./pingtarget"
  node = each.value
}

