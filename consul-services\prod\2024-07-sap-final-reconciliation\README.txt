This is the last ditch effort to slice up all those host groups.

<PERSON> had provided sliced cir-app-id && host-group (SID id) mapping for most systems, but
residual stuff doesn't have that option - so we can use the data he's provided and try to
extrapolate from those:


For instance - main groups:

        UATA = QA
        UAT0 = Q0
        UAT2 = Q2
        PROD = P1


Also these particular groups were noted:
  regex 'sapdh' can use id = sapdh
  regex 'sdbo' can use id = sdbo



Regex to determine app-id and sid_id:

        "\w{2}\d{4}(?<cirrus_app_id>\w{3})(?<sid_id>\w{1})\w.*$"

        sid_id mapping works like this:
           
          sid_id   sid_name
          c        CENTRALSERVICES
          f        PORTAL
          g        FIORI
          i        PI
          j        CPS
          k        BPA
          n        ERP
          o        PO
          p        SRM
          t        BWPORTAL
          u        GRC
          v        BW
          w        WEBDISPATCHER


Validating we've got everything (bit brute forcey)
   for i in `cat 2024-07-sap-final-reconciliation/SAP_APP_hosts_list_NW.csv | awk -F ","  '{print $2}' | awk -F '.' '{print $1}' `; do echo -n ${i} "^T"  ; grep -c ${i} services.auto.tfvars.json ; done | grep -v 1$
cl0992sc0cs01 0
tl0991st1nb02 2
tl0991st1nb03 2



