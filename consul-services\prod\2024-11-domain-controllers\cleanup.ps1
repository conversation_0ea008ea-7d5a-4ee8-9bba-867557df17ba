# Read the input file
$hosts = Get-Content ".\prod.txt"
$results = @()

foreach ($hostname in $hosts) {
    # Skip empty lines and lines starting with a period
    if ([string]::IsNullOrWhiteSpace($hostname) -or $hostname.StartsWith(".")) { 
        Write-Host "Skipping: $hostname" -ForegroundColor Gray
        continue 
    }
    
    try {
        # Test if host resolves
        $resolved = Resolve-DnsName $hostname -ErrorAction Stop
        
        # Try to ping the host
        $ping = Test-Connection -ComputerName $hostname -Count 1 -Quiet
        
        if ($ping) {
            # If ping successful, add to results
            $results += $hostname
            Write-Host "Success: $hostname is reachable" -ForegroundColor Green
        } else {
            Write-Host "Failed: $hostname did not respond to ping" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "Failed: $hostname could not be resolved" -ForegroundColor Red
    }
}

# Write results to new file
$results | Out-File ".\responsive_hosts.txt"

Write-Host "`nProcess complete. Responsive hosts have been saved to 'responsive_hosts.txt'"
Write-Host "Original hosts: $($hosts.Count)"
Write-Host "Responsive hosts: $($results.Count)"