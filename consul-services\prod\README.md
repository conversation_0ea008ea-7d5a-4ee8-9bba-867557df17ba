
If running from your VDI - remember to set the proxy.  
Otherwise you'll get lots of timeouts ' still creating [1m30s] '... and similar.

export HTTPS_PROXY=http://proxy.det.nsw.edu.au


Normal usage is:

terraform[.exe] init                         # Done once per place you run this from
terraform[.exe] plan -var-file=filename      # Can optionally output this to a .out file - but it describes what *will* be done
terraform[.exe] apply -var-file=filename     # Actually goes and creates / deletes etc the hosts.

If you get some weird errors 'Node does not exist ...' - run the 'terraform apply' again.

If you get a lot of DESTROY intents, delete your tfstate file (eg terraform.tfstate) and run the plan again.

## See Also

HashiCorp Manual [Consul Service](https://registry.terraform.io/providers/hashicorp/consul/latest/docs/resources/service)
