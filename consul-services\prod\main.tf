terraform {
  required_providers {
    consul = {
      source = "hashicorp/consul"
      version = "2.11.0"
    }
  }
}

provider "consul" {
  address = "pl0992obscol01.nsw.education:8500"
  datacenter = "dc-cir-un-prod"
  token = "b68e1c4b-dac2-990b-1743-0d13056b56a5"
}

variable "groups" {
  type = map(object({
    port = number
    service_name = string
    // metrics_path = string
    // tags = list(string)
    nodes = list(string)
    meta = map(string)
  }))
}

module "promtarget" {
  for_each = var.groups
  source = "./promtarget"
  service = each.value
}
