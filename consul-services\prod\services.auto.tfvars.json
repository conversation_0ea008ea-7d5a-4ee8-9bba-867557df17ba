{"groups": {"aadc_dev_devdetnsw.win": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "aadc", "domain": "devdetnsw.win"}, "nodes": []}, "aadc_dev_workgroup": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac - directories", "op_window": "24x7", "os_manager": "ddac", "app_id": "aadc"}, "nodes": []}, "aadc_preprod_predetnsw.win": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "luke meijer", "op_window": "on_demand", "os_manager": "windows", "app_id": "aadc", "domain": "predetnsw.win"}, "nodes": []}, "aadc_preprod_workgroup": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac - directories", "op_window": "24x7", "os_manager": "ddac", "app_id": "aadc"}, "nodes": []}, "aadc_test_uatdetnsw.win": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "on_demand", "os_manager": "windows", "app_id": "aadc", "domain": "uatdetnsw.win"}, "nodes": []}, "aadc_test_workgroup": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "ddac", "app_id": "aadc"}, "nodes": []}, "aadc_prod_detnsw.win": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "aadc", "domain": "detnsw.win"}, "nodes": ["pw0991aadc0101.nsw.education", "pw0992aadc0101.nsw.education", "pw0472aadc0101.nsw.education"]}, "aadc_prod_workgroup": {"service_name": "openmetrics_aadc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "ddac", "app_id": "aadc"}, "nodes": []}, "aahw_dev_devdetnsw.win": {"service_name": "openmetrics_aahw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "noel reyes", "op_window": "12x5", "os_manager": "windows", "app_id": "aahw", "domain": "devdetnsw.win"}, "nodes": ["dw0992aahw0001.nsw.education"]}, "aap_test_uatdetnsw.win": {"service_name": "openmetrics_aap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "darryl bowden", "op_window": "12x5", "os_manager": "windows", "app_id": "aap", "domain": "uatdetnsw.win"}, "nodes": []}, "aap_prod_detnsw.win": {"service_name": "openmetrics_aap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "solution design & engineering", "op_window": "24x7", "os_manager": "windows", "app_id": "aap", "domain": "detnsw.win"}, "nodes": ["pw0992aap00001.nsw.education", "pw0991aap00001.nsw.education"]}, "acaa_preprod_workgroup": {"service_name": "openmetrics_acaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "acaa"}, "nodes": ["qu0992acaa001.apps.pre.det.nsw.edu.au"]}, "acaa_prod_workgroup": {"service_name": "openmetrics_acaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "acaa"}, "nodes": ["pu0992acaa001.apps.det.nsw.edu.au", "pu0991acaa001.apps.det.nsw.edu.au"]}, "adcnb_dev_workgroup": {"service_name": "openmetrics_adcnb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "ddac", "app_id": "adcnb"}, "nodes": []}, "adcs_dev_workgroup": {"service_name": "openmetrics_adcs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "unknown", "app_id": "adcs"}, "nodes": []}, "adcs_test_uatdetnsw.win": {"service_name": "openmetrics_adcs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adcs", "domain": "uatdetnsw.win"}, "nodes": ["tw0991cacr0102.test.nsw.education"]}, "adcs_prod_detnsw.win": {"service_name": "openmetrics_adcs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adcs", "domain": "detnsw.win"}, "nodes": ["pw0991cais0104.nsw.education"]}, "rwdc_dev_devdetnsw.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "is - active directory team", "op_window": "on_demand", "os_manager": "ddac", "app_id": "rwdc", "domain": "devdetnsw.win"}, "nodes": ["dw0991rwdc0101.nsw.education", "dw0991rwdc0102.dev.nsw.education", "dw0992rwdc0101.dev.nsw.education", "dw0992rwdc0102.devdetnsw.win"]}, "rwdc_dev_devidm.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "is - active directory team", "op_window": "on_demand", "os_manager": "ddac", "app_id": "rwdc", "domain": "devidm.win"}, "nodes": ["dw0991rwdc0201.dev.nsw.education", "dw0992rwdc0201.dev.nsw.education"]}, "rwdc_dev_devext.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "is - active directory team", "op_window": "on_demand", "os_manager": "ddac", "app_id": "rwdc", "domain": "devext.win"}, "nodes": []}, "rwdc_preprod_preext.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "preext.win"}, "nodes": ["qw0992rwdc0302.svcs.pre.det.nsw.edu.au", "qw0992rwdc0301.svcs.pre.det.nsw.edu.au", "qw0991rwdc0302.svcs.pre.det.nsw.edu.au", "qw0991rwdc0301.svcs.pre.det.nsw.edu.au"]}, "rwdc_preprod_preidm.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "preidm.win"}, "nodes": ["qw0992rwdc0201.svcs.pre.det.nsw.edu.au", "qw0992rwdc0202.svcs.pre.det.nsw.edu.au", "qw0991rwdc0201.svcs.pre.det.nsw.edu.au", "qw0991rwdc0202.svcs.pre.det.nsw.edu.au"]}, "rwdc_preprod_uc.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "preuc.win"}, "nodes": ["qw0992rwdc0501.messaging.pre.det.nsw.edu.au", "qw0991rwdc0501.messaging.pre.det.nsw.edu.au"]}, "rwdc_devuc.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "devuc.win"}, "nodes": ["dw0992rwdc0501.dev.nsw.education"]}, "rwdc_test_uatdetnsw.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "uatdetnsw.win"}, "nodes": ["tw0992rwdc0101.ad.test.det.nsw.edu.au", "tw0992rwdc0103.ad.test.det.nsw.edu.au"]}, "rwdc_test_uatidm.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "uatidm.win"}, "nodes": ["tw0992rwdc0201.svcs.test.det.nsw.edu.au", "tw0992rwdc0202.svcs.test.det.nsw.edu.au", "tw0991rwdc0202.svcs.test.det.nsw.edu.au", "tw0991rwdc0201.svcs.test.det.nsw.edu.au"]}, "rwdc_test_uc": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "uc.win"}, "nodes": ["tw0992rwdc0501.messaging.test.det.nsw.edu.au", "tw0991rwdc0501.messaging.test.det.nsw.edu.au", "tw0991rwdc0502.test.nsw.education"]}, "rwdc_test_ext": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "is - active directory team", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "uatext.win"}, "nodes": ["tw0991rwdc0301.test.nsw.education", "tw0992rwdc0301.svcs.test.det.nsw.edu.au", "tw0992rwdc0302.svcs.test.det.nsw.edu.au"]}, "addsc_prod_unknown": {"service_name": "openmetrics_addsc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "na", "op_window": "24x7", "os_manager": "ddac", "app_id": "addsc", "domain": "unknown"}, "nodes": []}, "adfs_dev_devdetnsw.win": {"service_name": "openmetrics_adfs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adfs", "domain": "devdetnsw.win"}, "nodes": ["dw0992adfs0101.dev.nsw.education"]}, "wap_dev_devdetnsw.win": {"service_name": "openmetrics_wap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adfs", "domain": "devdetnsw.win"}, "nodes": []}, "adfs_preprod_predetnsw.win": {"service_name": "openmetrics_adfs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adfs", "domain": "predetnsw.win"}, "nodes": ["qw0991adfs0101.pre.nsw.education", "qw0992adfs0101.pre.nsw.education", "qw0991adfs0103.pre.nsw.education", "qw0992adfs0103.pre.nsw.education"]}, "wap_preprod_predetnsw.win": {"service_name": "openmetrics_wap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "wap", "domain": "predetnsw.win"}, "nodes": ["qw0992wap00101.pre.nsw.education", "qw0991wap00101.pre.nsw.education", "qw0991wap00102.nsw.education", "qw0992wap00102.nsw.education"]}, "adfs_test_uatdetnsw.win": {"service_name": "openmetrics_adfs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adfs", "domain": "uatdetnsw.win"}, "nodes": []}, "wap_test_uatdetnsw.win": {"service_name": "openmetrics_wap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "wap", "domain": "uatdetnsw.win"}, "nodes": ["tw0991wap00101.test.nsw.education", "tw0992wap00101.test.nsw.education"]}, "adfs_prod_detnsw.win": {"service_name": "openmetrics_adfs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adfs", "domain": "detnsw.win"}, "nodes": ["pw0991adfs0108.nsw.education", "pw0991adfs0110.nsw.education", "pw0991adfs0107.nsw.education", "pw0991adfs0112.nsw.education", "pw0991adfs0111.nsw.education", "pw0991adfs0109.nsw.education", "pw0992adfs0107.nsw.education", "pw0992adfs0109.nsw.education", "pw0992adfs0112.nsw.education", "pw0992adfs0108.nsw.education", "pw0992adfs0110.nsw.education", "pw0992adfs0111.nsw.education"]}, "wap_prod_workgroup": {"service_name": "openmetrics_wap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "unknown", "app_id": "wap"}, "nodes": ["pw0991wap00107.nsw.education", "pw0992wap00107.nsw.education", "pw0991wap00108.nsw.education", "pw0991wap00109.nsw.education", "pw0992wap00108.nsw.education", "pw0992wap00109.nsw.education"]}, "admim_dev_devdetnsw.win": {"service_name": "openmetrics_admim", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "admim", "domain": "devdetnsw.win"}, "nodes": []}, "admim_test_uatdetnsw.win": {"service_name": "openmetrics_admim", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "admim", "domain": "uatdetnsw.win"}, "nodes": []}, "admim_prod_detnsw.win": {"service_name": "openmetrics_admim", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "admim", "domain": "detnsw.win"}, "nodes": []}, "admu_dev_devdetnsw.win": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "admu", "domain": "devdetnsw.win"}, "nodes": []}, "admu_dev_workgroup": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "idm - directories", "op_window": "24x7", "os_manager": "windows", "app_id": "admu"}, "nodes": ["dw0992admu0201.nsw.education", "dl0992admu0001.dev.nsw.education", "dw0991admu0301.dev.nsw.education", "dw0991admu0201.dev.nsw.education"]}, "admu_preprod_predetnsw.win": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "idm - directories", "op_window": "24x7", "os_manager": "ddac", "app_id": "admu", "domain": "predetnsw.win"}, "nodes": ["qw0992admu0001.pre.nsw.education", "qw0991admu0101.pre.nsw.education", "qw0992admu0101.pre.nsw.education"]}, "admu_test_uatdetnsw.win": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "idm - directories", "op_window": "24x7", "os_manager": "ddac", "app_id": "admu", "domain": "uatdetnsw.win"}, "nodes": ["tw0991admu0102.test.nsw.education", "tw0992admu0101.test.nsw.education"]}, "admu_test_workgroup": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "ddac", "app_id": "admu"}, "nodes": ["tw0992admu0301.test.nsw.education"]}, "admu_prod_detnsw.win": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "admu", "domain": "detnsw.win"}, "nodes": ["pw0991adkp0101.nsw.education", "pw0991admu0105.nsw.education", "pw0991admo0101.nsw.education", "pw0472admu0101.nsw.education"]}, "admu_prod_workgroup": {"service_name": "openmetrics_admu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "ddac", "app_id": "admu"}, "nodes": ["pw0991admu0201.nsw.education", "pw0992admu0201.nsw.education", "pw0992admu0301.pre.nsw.education", "pw0991admu0701.nsw.education"]}, "adpoc_test_uatdetnsw.win": {"service_name": "openmetrics_adpoc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd directories team", "op_window": "12x5", "os_manager": "ddac", "app_id": "adpoc", "domain": "uatdetnsw.win"}, "nodes": ["tw0992adpoc001.nsw.education"]}, "adrad_prod_detnsw.win": {"service_name": "openmetrics_adrad", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "<PERSON><PERSON>", "domain": "detnsw.win"}, "nodes": []}, "adrdl_dev_devdetnsw.win": {"service_name": "openmetrics_adrdl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adrdl", "domain": "devdetnsw.win"}, "nodes": []}, "adrdl_preprod_predetnsw.win": {"service_name": "openmetrics_adrdl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adrdl", "domain": "predetnsw.win"}, "nodes": []}, "adrdl_test_uatdetnsw.win": {"service_name": "openmetrics_adrdl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adrdl", "domain": "uatdetnsw.win"}, "nodes": []}, "adrdl_prod_detnsw.win": {"service_name": "openmetrics_adrdl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "adrdl", "domain": "detnsw.win"}, "nodes": ["pw0991rdsl0101.nsw.education"]}, "adrdl_prod_workgroup": {"service_name": "openmetrics_adrdl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "idm - directory", "op_window": "24x7", "os_manager": "ddac", "app_id": "adrdl"}, "nodes": []}, "amfa_test_uatdetnsw.win": {"service_name": "openmetrics_amfa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "amfa", "domain": "uatdetnsw.win"}, "nodes": []}, "amso_dev_devdetnsw.win": {"service_name": "openmetrics_amso", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "amso", "domain": "devdetnsw.win"}, "nodes": [{"name": "dw0000amswaa01.apps.dev.det.nsw.edu.au", "meta": {"role": "Web application auxiliary"}}, {"name": "dw0000amsags01.apps.dev.det.nsw.edu.au", "meta": {"role": "ArcGIS"}}, {"name": "dw0000amsowl01.apps.dev.det.nsw.edu.au", "meta": {"role": "Oracle Weblogic"}}]}, "amso_preprod_predetnsw.win": {"service_name": "openmetrics_amso", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "amso", "domain": "predetnsw.win"}, "nodes": [{"name": "qw0000amsags01.apps.pre.det.nsw.edu.au", "meta": {"role": "ArcGIS"}}, {"name": "qw0000amsowl01.apps.pre.det.nsw.edu.au", "meta": {"role": "Oracle Weblogic"}}, {"name": "qw0000amswaa01.apps.pre.det.nsw.edu.au", "meta": {"role": "Web application auxiliary"}}]}, "amso_test_uatdetnsw.win": {"service_name": "openmetrics_amso", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "amso", "domain": "uatdetnsw.win"}, "nodes": [{"name": "tw0000amsags01.apps.test.det.nsw.edu.au", "meta": {"role": "ArcGIS"}}, {"name": "tw0000amsowl01.apps.test.det.nsw.edu.au", "meta": {"role": "Oracle Weblogic"}}, {"name": "tw0000amswaa01.apps.test.det.nsw.edu.au", "meta": {"role": "Web application auxiliary"}}]}, "amso_prod_detnsw.win": {"service_name": "openmetrics_amso", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "amso", "domain": "detnsw.win"}, "nodes": [{"name": "pw0000amsags01.vdr.det.nsw.edu.au", "meta": {"role": "ArcGIS"}}, {"name": "pw0000amsowl01.vdr.det.nsw.edu.au", "meta": {"role": "Oracle Weblogic"}}, {"name": "pw0000amsowl03.vdr.det.nsw.edu.au", "meta": {"role": "Oracle Weblogic"}}, {"name": "pw0000amswaa01.detnsw.win", "meta": {"role": "Web application auxiliary"}}]}, "siams_dev_devdetnsw.win": {"service_name": "openmetrics_siams", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "siams", "domain": "devdetnsw.win"}, "nodes": [{"name": "dw0992siamsro1.devdetnsw.win", "meta": {"role": "Web Server – RPM & OART PHP Applications"}}, {"name": "dw0992siamswl1.devdetnsw.win", "meta": {"role": "WebLogic"}}, {"name": "dw0992siamsag1.devdetnsw.win", "meta": {"role": "ArcGIS"}}, {"name": "dw0992siamsph1.devdetnsw.win", "meta": {"role": "AMS PHP Applications"}}]}, "siams_test_uatdetnsw.win": {"service_name": "openmetrics_siams", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "siams", "domain": "uatdetnsw.win"}, "nodes": [{"name": "tw0991siamsro1.uatdetnsw.win", "meta": {"role": "Web Server – RPM & OART PHP Applications"}}, {"name": "tw0991siamswl1.uatdetnsw.win", "meta": {"role": "WebLogic"}}]}, "siams_preprod_predetnsw.win": {"service_name": "openmetrics_siams", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "siams", "domain": "predetnsw.win"}, "nodes": [{"name": "qw0992siamsro1.predetnsw.win", "meta": {"role": "Web Server – RPM & OART PHP Applications"}}, {"name": "qw0992siamswl1.predetnsw.win", "meta": {"role": "WebLogic"}}, {"name": "qw0992siamsag1.predetnsw.win", "meta": {"role": "ArcGIS"}}, {"name": "qw0992siamsph1.predetnsw.win", "meta": {"role": "AMS PHP Applications"}}]}, "siams_prod_detnsw.win": {"service_name": "openmetrics_siams", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "siams", "domain": "detnsw.win"}, "nodes": [{"name": "pw0991siamsro1.detnsw.win", "meta": {"role": "Web Server – RPM & OART PHP Applications"}}, {"name": "pw0991siamswl1.detnsw.win", "meta": {"role": "WebLogic"}}]}, "ansb_prod_workgroup": {"service_name": "openmetrics_ansb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ansb"}, "nodes": ["pu0992ansb0001.hbm.det.nsw.edu.au", "pu0991ansb0001.hbm.det.nsw.edu.au"]}, "astp_prod_detnsw.win": {"service_name": "openmetrics_astp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "tony guarna<PERSON>a", "op_window": "24x7", "os_manager": "windows", "app_id": "astp", "domain": "detnsw.win"}, "nodes": ["pw0000astpwts2.nsw.education"]}, "aufm_prod_detnsw.win": {"service_name": "openmetrics_aufm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "richard spie<PERSON>k", "op_window": "24x7", "os_manager": "windows", "app_id": "aufm", "domain": "detnsw.win"}, "nodes": ["pw0992aufm0001.nsw.education"]}, "awsi_shared_detnsw.win": {"service_name": "openmetrics_awsi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "service intelligence", "op_window": "24x7", "os_manager": "windows", "app_id": "awsi", "domain": "detnsw.win"}, "nodes": ["pw0991awsi0001.nsw.education"]}, "azmig_test_uatdetnsw.win": {"service_name": "openmetrics_azmig", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "cirrus cloud migration team", "op_window": "24x7", "os_manager": "windows", "app_id": "azmig", "domain": "uatdetnsw.win"}, "nodes": ["tw0992azmig004.test.nsw.education", "tw0991azmig004.test.nsw.education"]}, "azmig_prod_detnsw.win": {"service_name": "openmetrics_azmig", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "cirrus migration team", "op_window": "24x7", "os_manager": "windows", "app_id": "azmig", "domain": "detnsw.win"}, "nodes": ["pw0992azmig004.nsw.education", "pw0991azmig004.nsw.education", "pw0992azmig001.nsw.education"]}, "b000_prod_detnsw.win": {"service_name": "openmetrics_b000", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "b000", "domain": "detnsw.win"}, "nodes": ["pw0000cmz00004.hbm.det.nsw.edu.au"]}, "bamboo_shared_workgroup": {"service_name": "openmetrics_bamboo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "middleware", "op_window": "12x5", "os_manager": "penguins", "app_id": "bamboo"}, "nodes": ["pl0991bamboo00.nsw.education", "pl0991bamboo01.nsw.education", "pl0992bamboo04.nsw.education", "pl0992bamboo06.nsw.education", "pl0992bamboo05.nsw.education", "pl0991bamboo03.nsw.education", "pl0991bamboo02.nsw.education", "pl0991bamboo11.nsw.education", "pl0991bamboo12.nsw.education", "pl0992bamboo00.nsw.education", "pl0475bamboo09.nsw.education", "pl0475bamboo10.nsw.education", "pl0475bamboo07.nsw.education", "pl0475bamboo08.nsw.education", "pl0992bamboo08.nsw.education"]}, "bamboo_prod_workgroup": {"service_name": "openmetrics_bamboo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "middleware", "op_window": "12x5", "os_manager": "penguins", "app_id": "bamboo"}, "nodes": ["pl0475bamboo11.nsw.education"]}, "bamtib_shared_workgroup": {"service_name": "openmetrics_bamtib", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "dave smith", "op_window": "12x5", "os_manager": "penguins", "app_id": "bam<PERSON>b"}, "nodes": ["pl0992bamtib01.nsw.education"]}, "bkdp_dev_unknown": {"service_name": "openmetrics_bkdp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "bkdp", "domain": "unknown"}, "nodes": ["dw0992bkdpm001.backup.det.nsw.edu.au", "dw0991bkdpm001.backup.det.nsw.edu.au"]}, "bkmas_test_uatdetnsw.win": {"service_name": "openmetrics_bkmas", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "salman khan", "op_window": "24x7", "os_manager": "windows", "app_id": "bkmas", "domain": "uatdetnsw.win"}, "nodes": ["tw0991bkmas001.nsw.education"]}, "bl00_test_uatdetnsw.win": {"service_name": "openmetrics_bl00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "bl00", "domain": "uatdetnsw.win"}, "nodes": ["utvdccbl001.apps.test.det.nsw.edu.au"]}, "bl00_prod_detnsw.win": {"service_name": "openmetrics_bl00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "bl00", "domain": "detnsw.win"}, "nodes": ["pw0000csapp001.apps.det.nsw.edu.au"]}, "brik_shared_unset": {"service_name": "openmetrics_brik", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "<EMAIL>", "op_window": "24x7", "os_manager": "penguins", "app_id": "brik", "domain": "unset"}, "nodes": ["ps0475brik1003.nsw.education", "ps0475brik1001.nsw.education", "ps0475brik1002.nsw.education", "ps0475brik1004.nsw.education", "ps0475brik1006.nsw.education", "ps0475brik1005.nsw.education"]}, "brmd_prod_workgroup": {"service_name": "openmetrics_brmd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "brmd"}, "nodes": ["pu0000brmd0001.dbs.det.nsw.edu.au"]}, "bucket_shared_workgroup": {"service_name": "openmetrics_bucket", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "david r. smith", "op_window": "12x5", "os_manager": "penguins", "app_id": "bucket"}, "nodes": ["pl0992bucket02.nsw.education", "pl0992bucket01.nsw.education", "pl0992bucket03.nsw.education", "pl0992bucket05.nsw.education", "pl0992bucket04.nsw.education", "pl0992bucket06.nsw.education"]}, "budapp_test_workgroup": {"service_name": "openmetrics_budapp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "buda<PERSON>"}, "nodes": ["tu0000netprt01.apps.test.det.nsw.edu.au", "tl0475budapp01.nsw.education"]}, "budapp_prod_workgroup": {"service_name": "openmetrics_budapp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "buda<PERSON>"}, "nodes": ["pu0000netprt01.apps.det.nsw.edu.au"]}, "bworks_dev_workgroup": {"service_name": "openmetrics_bworks", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david r. smith", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "bworks"}, "nodes": ["dl0991bworks01.nsw.education"]}, "bworks_preprod_workgroup": {"service_name": "openmetrics_bworks", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "david r. smith", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "bworks"}, "nodes": ["ql0991bworks01.nsw.education", "ql0992bworks01.nsw.education", "ql0992bworks10.nsw.education"]}, "bworks_test_workgroup": {"service_name": "openmetrics_bworks", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "david r. smith", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "bworks"}, "nodes": ["tl0992bworks01.nsw.education", "tl0992bworks02.nsw.education"]}, "bworks_prod_workgroup": {"service_name": "openmetrics_bworks", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "david r. smith", "op_window": "12x5", "os_manager": "penguins", "app_id": "bworks"}, "nodes": ["pl0992bworks01.nsw.education", "pl0991bworks01.nsw.education"]}, "cain_prod_unknown": {"service_name": "openmetrics_cain", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "cain", "domain": "unknown"}, "nodes": ["pw0991cain9902.apps.det.nsw.edu.au"]}, "cash_prod_detnsw.win": {"service_name": "openmetrics_cash", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "cash", "domain": "detnsw.win"}, "nodes": ["pw0992cash0103.ad.det.nsw.edu.au", "pw0991cash0101.ad.det.nsw.edu.au"]}, "cash8d_prod_detnsw.win": {"service_name": "openmetrics_cash8d", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "cash8d", "domain": "detnsw.win"}, "nodes": ["pw0991cash0102.ad.det.nsw.edu.au"]}, "cdm_dev_detnsw.win": {"service_name": "openmetrics_cdm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac - desktop platform", "op_window": "12x5", "os_manager": "windows", "app_id": "cdm", "domain": "detnsw.win"}, "nodes": ["dw0472cdm00002.nsw.education"]}, "cdm_prod_detnsw.win": {"service_name": "openmetrics_cdm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac - desktop platform", "op_window": "24x7", "os_manager": "windows", "app_id": "cdm", "domain": "detnsw.win"}, "nodes": ["pw0472cdm00002.nsw.education", "pw0472cdm00001.nsw.education"]}, "cedrms_dev_devdetnsw.win": {"service_name": "openmetrics_cedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "edrms apps", "op_window": "12x5", "os_manager": "unknown", "app_id": "cedrms", "domain": "devdetnsw.win"}, "nodes": ["dw0992crmswg01.dev.nsw.education"]}, "cedrms_preprod_detnsw.win": {"service_name": "openmetrics_cedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "edrms apps", "op_window": "24x7", "os_manager": "unknown", "app_id": "cedrms", "domain": "detnsw.win"}, "nodes": ["qw0992crmswg01.pre.nsw.education", "qw0992crmswg02.pre.nsw.education", "qw0992crmsev01.pre.nsw.education", "qw0992crmsid01.pre.nsw.education", "qw0991crmswg01.pre.nsw.education", "qw0991crmsev01.pre.nsw.education", "qw0992crmsid02.pre.nsw.education", "qw0992crmswg03.pre.nsw.education"]}, "cedrms_test_uatdetnsw.win": {"service_name": "openmetrics_cedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "edrms apps", "op_window": "12x5", "os_manager": "unknown", "app_id": "cedrms", "domain": "uatdetnsw.win"}, "nodes": ["tw0992crmswg01.test.nsw.education", "tw0992crmswg02.test.nsw.education"]}, "cedrms_prod_detnsw.win": {"service_name": "openmetrics_cedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "edrms apps", "op_window": "24x7", "os_manager": "windows", "app_id": "cedrms", "domain": "detnsw.win"}, "nodes": ["ew0992crmswg01.nsw.education", "pw0992crmsev01.nsw.education", "pw0992crmswg03.nsw.education", "pw0992crmswg01.nsw.education", "pw0992crmsid01.nsw.education", "pw0992crmswg02.nsw.education", "pw0992crmswg04.nsw.education", "pw0992crmsid02.nsw.education", "pw0991crmswg04.nsw.education", "pw0991crmsev01.nsw.education", "pw0991crmswg02.nsw.education", "pw0991crmswg03.nsw.education", "pw0991crmswg01.nsw.education", "pw0992crmswg05.nsw.education"]}, "cesd_prod_detnsw.win": {"service_name": "openmetrics_cesd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "cesd", "domain": "detnsw.win"}, "nodes": ["pw0000cesdbi02.apps.det.nsw.edu.au", "pw0000cesdbi01.apps.det.nsw.edu.au", "pw0000cesdbi04.apps.det.nsw.edu.au", "pw0000cesdbi03.apps.det.nsw.edu.au"]}, "cese_prod_detnsw.win": {"service_name": "openmetrics_cese", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "cese", "domain": "detnsw.win"}, "nodes": ["pw0992cesehs01.apps.det.nsw.edu.au", "pw0992ceseis01.apps.det.nsw.edu.au"]}, "cese37_prod_detnsw.win": {"service_name": "openmetrics_cese37", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "cese37", "domain": "detnsw.win"}, "nodes": ["pw0992ceseap01.apps.det.nsw.edu.au"]}, "ch00_dev_devdetnsw.win": {"service_name": "openmetrics_ch00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ch00", "domain": "devdetnsw.win"}, "nodes": ["udvacapp002.multimedia.test.det.nsw.edu.au", "udvacapp001.multimedia.test.det.nsw.edu.au"]}, "ch00_preprod_uatdetnsw.win": {"service_name": "openmetrics_ch00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ch00", "domain": "uatdetnsw.win"}, "nodes": ["qw0000acsch001.multimedia.pre.det.nsw.edu.au", "qw0000acsch002.multimedia.pre.det.nsw.edu.au"]}, "ch00_prod_detnsw.win": {"service_name": "openmetrics_ch00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ch00", "domain": "detnsw.win"}, "nodes": ["upvacsch001.multimedia.det.nsw.edu.au", "upvacsch002.multimedia.det.nsw.edu.au", "upvacsch004.multimedia.det.nsw.edu.au", "upvacsch003.multimedia.det.nsw.edu.au"]}, "cmy0_dev_devdetnsw.win": {"service_name": "openmetrics_cmy0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "cmy0", "domain": "devdetnsw.win"}, "nodes": ["dw0000cmy00003.apps.test.det.nsw.edu.au", "dw0000cmy00006.apps.test.det.nsw.edu.au", "dw0000cmy00007.apps.test.det.nsw.edu.au", "dw0000cmy00002.apps.test.det.nsw.edu.au", "dw0000cmy00001.apps.test.det.nsw.edu.au"]}, "cmy0_prod_detnsw.win": {"service_name": "openmetrics_cmy0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "cmy0", "domain": "detnsw.win"}, "nodes": ["pw0000cmy00006.apps.det.nsw.edu.au", "pw0000cmy00007.apps.det.nsw.edu.au", "pw0000cmy00001.apps.det.nsw.edu.au", "pw0000cmy00002.apps.det.nsw.edu.au", "pw0000cmy00003.apps.det.nsw.edu.au"]}, "cnsl_dev_unset": {"service_name": "openmetrics_cnsl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "unset", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "cnsl", "domain": "unset"}, "nodes": ["dl0992cnsl0001.nsw.education", "dl0992cnsl0003.nsw.education", "dl0992cnsl0002.nsw.education"]}, "cnsl_prod_unset": {"service_name": "openmetrics_cnsl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "peter g<PERSON>e", "op_window": "24x7", "os_manager": "penguins", "app_id": "cnsl", "domain": "unset"}, "nodes": ["pl0475cnsl0001.nsw.education", "pl0475cnsl0002.nsw.education", "pl0475cnsl0003.nsw.education", "pl0475cnsl0004.nsw.education", "pl0475cnsl0005.nsw.education"]}, "conflu_dmz_workgroup": {"service_name": "openmetrics_conflu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dmz", "support_team": "itd middleware", "op_window": "24x7", "os_manager": "penguins", "app_id": "conflu"}, "nodes": ["pl0475conflu07.nsw.education", "pl0475conflu06.nsw.education"]}, "conflu_prod_workgroup": {"service_name": "openmetrics_conflu", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd middleware", "op_window": "on-demand", "os_manager": "penguins", "app_id": "conflu"}, "nodes": ["pl0475conflu03.nsw.education", "pl0475conflu01.nsw.education", "pl0475conflu02.nsw.education"]}, "coutl_dev_workgroup": {"service_name": "openmetrics_coutl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "cloud ops", "op_window": "12x5", "os_manager": "penguins", "app_id": "coutl"}, "nodes": ["dl0475coutl001.nsw.education", "dl0475coutl002.nsw.education"]}, "coutl_prod_detnsw.win": {"service_name": "openmetrics_coutl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "cloud ops", "op_window": "24x7", "os_manager": "windows", "app_id": "coutl", "domain": "detnsw.win"}, "nodes": ["pw0991coutl001.nsw.education"]}, "coutl_prod_workgroup": {"service_name": "openmetrics_coutl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "cloud ops", "op_window": "12x5", "os_manager": "penguins", "app_id": "coutl"}, "nodes": ["pl0475coutl001.nsw.education", "pl0475coutl002.nsw.education", "pl0475coutl003.nsw.education"]}, "cport_dev_unset": {"service_name": "openmetrics_cport", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "<EMAIL>", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "cport", "domain": "unset"}, "nodes": ["dl0991cport001.nsw.education"]}, "cport_dev_workgroup": {"service_name": "openmetrics_cport", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "cirrus team", "op_window": "24x7", "os_manager": "penguins", "app_id": "cport"}, "nodes": ["dl0991cport002.nsw.education"]}, "cprov_dev_workgroup": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "cprov"}, "nodes": ["dl0991cprovlb1.nsw.education", "dl0991cprovlb2.nsw.education", "dl0991cprov007.nsw.education", "dl0991cprov008.nsw.education", "dl0991cprov009.nsw.education", "dl0991cprovd02.nsw.education"]}, "cprov_shared_detnsw.win": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "cirrus team", "op_window": "24x7", "os_manager": "windows", "app_id": "cprov", "domain": "detnsw.win"}, "nodes": ["pw0991cprov001.nsw.education", "pw0992cprov001.nsw.education", "pw0991cprov002.nsw.education", "pw0992cprov002.nsw.education"]}, "cprov_shared_devdetnsw.win": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "cirrus team", "op_window": "24x7", "os_manager": "windows", "app_id": "cprov", "domain": "devdetnsw.win"}, "nodes": ["dw0992cprov001.nsw.education", "dw0991cprov001.nsw.education"]}, "cprov_shared_predetnsw.win": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "cirrus team", "op_window": "24x7", "os_manager": "windows", "app_id": "cprov", "domain": "predetnsw.win"}, "nodes": ["qw0992cprov001.nsw.education", "qw0991cprov001.nsw.education"]}, "cprov_shared_uatdetnsw.win": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "cirrus team", "op_window": "24x7", "os_manager": "windows", "app_id": "cprov", "domain": "uatdetnsw.win"}, "nodes": ["tw0991cprov001.nsw.education", "tw0992cprov001.nsw.education"]}, "cprov_test_workgroup": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "cirrus team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "cprov"}, "nodes": ["tl0991cprov001.nsw.education", "tl0991cprov002.nsw.education", "tl0991cprov003.nsw.education"]}, "cprov_prod_workgroup": {"service_name": "openmetrics_cprov", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "cprov"}, "nodes": ["pl0991cprovlb2.nsw.education", "pl0991cprovlb1.nsw.education", "pl0991cprov007.nsw.education", "pl0991cprov008.nsw.education", "pl0991cprov009.nsw.education", "pl0991cprov010.nsw.education", "pl0991cprov011.nsw.education", "pl0991cprov012.nsw.education", "pl0991cprovd02.nsw.education"]}, "csndra_test_workgroup": {"service_name": "openmetrics_csndra", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "dave smith", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "csndra"}, "nodes": ["tl0991csndra01.nsw.education"]}, "daikon_dev_devdetnsw.win": {"service_name": "openmetrics_daikon", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac - desktops", "op_window": "24x7", "os_manager": "windows", "app_id": "daikon", "domain": "devdetnsw.win"}, "nodes": ["dw0992daikon01.nsw.education", "dw0991daikon01.nsw.education"]}, "daikon_preprod_predetnsw.win": {"service_name": "openmetrics_daikon", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac - desktops", "op_window": "24x7", "os_manager": "windows", "app_id": "daikon", "domain": "predetnsw.win"}, "nodes": ["qw0991daikon01.nsw.education", "qw0991daikon02.nsw.education", "qw0992daikon01.nsw.education", "qw0992daikon02.nsw.education", "qw0992daikon03.nsw.education", "qw0991daikon03.nsw.education"]}, "daikon_test_uatdetnsw.win": {"service_name": "openmetrics_daikon", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac - desktops", "op_window": "24x7", "os_manager": "windows", "app_id": "daikon", "domain": "uatdetnsw.win"}, "nodes": ["tw0991daikon01.nsw.education", "tw0992daikon01.nsw.education"]}, "daikon_prod_detnsw.win": {"service_name": "openmetrics_daikon", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac - desktops", "op_window": "24x7", "os_manager": "windows", "app_id": "daikon", "domain": "detnsw.win"}, "nodes": ["pw0991daikon01.nsw.education", "pw0991daikon02.nsw.education", "pw0992daikon02.nsw.education", "pw0992daikon01.nsw.education", "pw0991daikon03.nsw.education", "pw0992daikon03.nsw.education"]}, "dds_dev_devdetnsw.win": {"service_name": "openmetrics_dds", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "@it service management applications", "op_window": "12x5", "os_manager": "windows", "app_id": "dds", "domain": "devdetnsw.win"}, "nodes": ["dw0992dds00001.dev.nsw.education"]}, "dds0_test_uatdetnsw.win": {"service_name": "openmetrics_dds0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "dds0", "domain": "uatdetnsw.win"}, "nodes": ["uw0000dds0002.svcs.pre.det.nsw.edu.au", "uw0000dds0001.svcs.pre.det.nsw.edu.au"]}, "dds0_prod_detnsw.win": {"service_name": "openmetrics_dds0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "dds0", "domain": "detnsw.win"}, "nodes": ["pw0000dds0002.svcs.det.nsw.edu.au", "pw0000dds0001.svcs.det.nsw.edu.au"]}, "deifmp_prod_detnsw.win": {"service_name": "openmetrics_deifmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "eric banh", "op_window": "24x7", "os_manager": "windows", "app_id": "deifmp", "domain": "detnsw.win"}, "nodes": ["pw0472deifmp01.nsw.education"]}, "dera_prod_detnsw.win": {"service_name": "openmetrics_dera", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "<PERSON><PERSON><PERSON> hossain", "op_window": "24x7", "os_manager": "windows", "app_id": "dera", "domain": "detnsw.win"}, "nodes": ["pw0472dera0001.nsw.education"]}, "dfsn_dev_devdetnsw.win": {"service_name": "openmetrics_dfsn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "itd-is / ddac directories", "op_window": "24x7", "os_manager": "windows", "app_id": "dfsn", "domain": "devdetnsw.win"}, "nodes": ["dw0472dfsn0001.dev.nsw.education"]}, "dfsn_test_uatdetnsw.win": {"service_name": "openmetrics_dfsn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / ddac directories", "op_window": "24x7", "os_manager": "windows", "app_id": "dfsn", "domain": "uatdetnsw.win"}, "nodes": ["tw0472dfsn0001.test.nsw.education"]}, "dfsn_prod_detnsw.win": {"service_name": "openmetrics_dfsn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / ddac directories", "op_window": "24x7", "os_manager": "windows", "app_id": "dfsn", "domain": "detnsw.win"}, "nodes": ["pw0472dfsn0001.nsw.education", "pw0472dfsn0002.nsw.education", "pw0472dfsn0003.nsw.education", "pw0472dfsn0004.nsw.education"]}, "dfsn1f_dev_devdetnsw.win": {"service_name": "openmetrics_dfsn1f", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "dfsn1f", "domain": "devdetnsw.win"}, "nodes": ["dw0992dfsn0101.ad.test.det.nsw.edu.au"]}, "ddt_dev_devdetnsw.win": {"service_name": "openmetrics_ddt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "sinsw", "op_window": "24x7", "os_manager": "sinsw", "app_id": "ddt", "domain": "devdetnsw.win"}, "nodes": ["dw0472ddtbas01.nsw.education", "dw0472ddtgis01.nsw.education"]}, "dpki_dev_devdetnsw.win": {"service_name": "openmetrics_dpki", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "milan bochin", "op_window": "24x7", "os_manager": "windows", "app_id": "dpki", "domain": "devdetnsw.win"}, "nodes": ["dw0991dpki0101.nsw.education"]}, "dpki_test_uatdetnsw.win": {"service_name": "openmetrics_dpki", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "milan bochin", "op_window": "24x7", "os_manager": "windows", "app_id": "dpki", "domain": "uatdetnsw.win"}, "nodes": ["tw0991dpki0101.nsw.education"]}, "dutil_dev_unknown": {"service_name": "openmetrics_dutil", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "na", "op_window": "24x7", "os_manager": "ddac", "app_id": "<PERSON><PERSON>", "domain": "unknown"}, "nodes": ["dw0992util0601.ad.test.det.nsw.edu.au"]}, "e4se_test_uatdetnsw.win": {"service_name": "openmetrics_e4se", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "e4se", "domain": "uatdetnsw.win"}, "nodes": []}, "e4se48_test_uatdetnsw.win": {"service_name": "openmetrics_e4se48", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "e4se48", "domain": "uatdetnsw.win"}, "nodes": []}, "e4se79_test_uatdetnsw.win": {"service_name": "openmetrics_e4se79", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "e4se79", "domain": "uatdetnsw.win"}, "nodes": []}, "echous_prod_detnsw.win": {"service_name": "openmetrics_echous", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "na", "op_window": "24x7", "os_manager": "windows", "app_id": "echous", "domain": "detnsw.win"}, "nodes": ["pw0992viutl001.vi.det.nsw.edu.au", "pw0991viutl001.vi.det.nsw.edu.au"]}, "elake_dev_detnsw.win": {"service_name": "openmetrics_elake", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "mo chandra<PERSON><PERSON>", "op_window": "on_demand", "os_manager": "windows", "app_id": "elake", "domain": "detnsw.win"}, "nodes": ["dw0472elakeir1.nsw.education"]}, "et00_prod_detnsw.win": {"service_name": "openmetrics_et00", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "et00", "domain": "detnsw.win"}, "nodes": ["upvewnet001.hbm.det.nsw.edu.au"]}, "et4lus_dev_devdetnsw.win": {"service_name": "openmetrics_et4lus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "et4lus", "domain": "devdetnsw.win"}, "nodes": ["dw0000cmxxutl1.apps.test.det.nsw.edu.au"]}, "et4lus_prod_detnsw.win": {"service_name": "openmetrics_et4lus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "et4lus", "domain": "detnsw.win"}, "nodes": ["pw0992cmxxutl01.apps.det.nsw.edu.au", "pw0000cmxxutl3.apps.det.nsw.edu.au", "pw0991cmxxutl01.apps.det.nsw.edu.au"]}, "etla_dev_workgroup": {"service_name": "openmetrics_etla", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "etla"}, "nodes": ["dl0992etla7001.nsw.education"]}, "etla_test_workgroup": {"service_name": "openmetrics_etla", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "etla"}, "nodes": ["tl0992etla7001.nsw.education"]}, "etla_prod_workgroup": {"service_name": "openmetrics_etla", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "etla"}, "nodes": ["pl0992etla7001.nsw.education"]}, "evnt_test_uatdetnsw.win": {"service_name": "openmetrics_evnt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "evnt", "domain": "uatdetnsw.win"}, "nodes": ["tw0992evnt0102.ad.test.det.nsw.edu.au", "tw0992evnt0101.ad.test.det.nsw.edu.au", "tw0991evnt0102.ad.test.det.nsw.edu.au", "tw0991evnt0101.ad.test.det.nsw.edu.au"]}, "evnt2b_prod_detnsw.win": {"service_name": "openmetrics_evnt2b", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "evnt2b", "domain": "detnsw.win"}, "nodes": ["pw0992evnt0101.detnsw.win", "pw0991evnt0101.ad.det.nsw.edu.au"]}, "ewdb_dev_devdetnsw.win": {"service_name": "openmetrics_ewdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "12x5", "os_manager": "windows", "app_id": "ewdb", "domain": "devdetnsw.win"}, "nodes": ["dw0000ewlapoc1.apps.test.det.nsw.edu.au"]}, "ewdb_dev_uatdetnsw.win": {"service_name": "openmetrics_ewdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewdb", "domain": "uatdetnsw.win"}, "nodes": ["qw0000ewlapoc1.apps.pre.det.nsw.edu.au", "qw0000ewdbpoc1.dbs.pre.det.nsw.edu.au"]}, "ewdb_prod_detnsw.win": {"service_name": "openmetrics_ewdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewdb", "domain": "detnsw.win"}, "nodes": ["pw0000ewdbpoc1.dbs.det.nsw.edu.au", "pw0000ewlapoc1.apps.det.nsw.edu.au"]}, "ewjh_test_uatdetnsw.win": {"service_name": "openmetrics_ewjh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewjh", "domain": "uatdetnsw.win"}, "nodes": ["tw0000sapfts01.vdr.det.nsw.edu.au", "tw0000sappoc01.apps.test.det.nsw.edu.au"]}, "ewjh_prod_detnsw.win": {"service_name": "openmetrics_ewjh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewjh", "domain": "detnsw.win"}, "nodes": ["pw0992hanats01.vdr.det.nsw.edu.au", "pw0991hanats01.vdr.det.nsw.edu.au"]}, "ewmg_dev_devdetnsw.win": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "devdetnsw.win"}, "nodes": ["dw0992ewmgt001.hbm.det.nsw.edu.au", "dw0991ewmgt001.hbm.det.nsw.edu.au"]}, "ewmg_dev_unknown": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "unknown"}, "nodes": ["dw0992ewmgt501.hbm.det.nsw.edu.au", "dw0991ewmgt501.hbm.det.nsw.edu.au"]}, "ewmg_preprod_predetnsw.win": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "predetnsw.win"}, "nodes": ["qw0992ewmgt001.hbm.det.nsw.edu.au", "qw0991ewmgt001.hbm.det.nsw.edu.au"]}, "ewmg_preprod_unknown": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "unknown"}, "nodes": ["qw0992ewmgt501.hbm.det.nsw.edu.au", "qw0991ewmgt501.hbm.det.nsw.edu.au"]}, "ewmg_prod_detnsw.win": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "detnsw.win"}, "nodes": ["pw0992ewmgt001.hbm.det.nsw.edu.au", "pw0991ewmgt001.hbm.det.nsw.edu.au"]}, "ewmg_test_uatdetnsw.win": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "uatdetnsw.win"}, "nodes": ["tw0992ewmgt001.hbm.det.nsw.edu.au", "tw0991ewmgt001.hbm.det.nsw.edu.au"]}, "ewmg_test_unknown": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "unknown"}, "nodes": ["tw0992ewmgt501.hbm.det.nsw.edu.au", "tw0991ewmgt501.hbm.det.nsw.edu.au"]}, "ewmg_prod_unknown": {"service_name": "openmetrics_ewmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ewmg", "domain": "unknown"}, "nodes": ["pw0992ewmgt501.hbm.det.nsw.edu.au", "pw0991ewmgt501.hbm.det.nsw.edu.au"]}, "ex13_test_tstcnt.tstdet.win": {"service_name": "openmetrics_ex13", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "ex13", "domain": "tstcnt.tstdet.win"}, "nodes": []}, "ex13_prod_unknown": {"service_name": "openmetrics_ex13", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "ex13", "domain": "unknown"}, "nodes": []}, "ex132d_prod_unknown": {"service_name": "openmetrics_ex132d", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "ex132d", "domain": "unknown"}, "nodes": []}, "ex19_dev_devdetnsw.win": {"service_name": "openmetrics_ex19", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "itd-is / ddac collaboration - messaging", "op_window": "24x7", "os_manager": "windows", "app_id": "ex19", "domain": "devdetnsw.win"}, "nodes": ["dw0991ex19fs01.dev.nsw.education", "dw0992ex19fs02.dev.nsw.education"]}, "ex19_test_uatdetnsw.win": {"service_name": "openmetrics_ex19", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / ddac collaboration - messaging", "op_window": "24x7", "os_manager": "windows", "app_id": "ex19", "domain": "uatdetnsw.win"}, "nodes": ["tw0991ex19ex01.test.nsw.education", "tw0991ex19fs01.test.nsw.education", "tw0992ex19ex02.test.nsw.education", "tw0992ex19fs02.test.nsw.education"]}, "ex19_test_workgroup": {"service_name": "openmetrics_ex19", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / ddac collaboration - messaging", "op_window": "24x7", "os_manager": "windows", "app_id": "ex19"}, "nodes": ["tw0992ex19fs04.test.nsw.education", "tw0991ex19fs03.test.nsw.education"]}, "ex19_prod_detnsw.win": {"service_name": "openmetrics_ex19", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / ddac collaboration - messaging", "op_window": "24x7", "os_manager": "windows", "app_id": "ex19", "domain": "detnsw.win"}, "nodes": ["pw0992ex19ex04.nsw.education", "pw0991ex19fs01.nsw.education", "pw0992ex19fs02.nsw.education", "pw0991ex19fs03.nsw.education", "pw0992ex19fs04.nsw.education", "pw0991ex19ex01.nsw.education", "pw0991ex19ex03.nsw.education", "pw0992ex19ex02.nsw.education", "pw0991ex19fs05.nsw.education", "pw0992ex19fs06.nsw.education", "pw0991ex19fs07.nsw.education", "pw0992ex19fs08.nsw.education", "pw0991ex19stel.nsw.education"]}, "ex19_prod_workgroup": {"service_name": "openmetrics_ex19", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ex19"}, "nodes": []}, "exar_prod_detnsw.win": {"service_name": "openmetrics_exar", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "exar", "domain": "detnsw.win"}, "nodes": []}, "exmm71_prod_central.det.win": {"service_name": "openmetrics_exmm71", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "exmm71", "domain": "central.det.win"}, "nodes": []}, "exps_test_uatdetnsw.win": {"service_name": "openmetrics_exps", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / ddac collaboration - messaging", "op_window": "12x5", "os_manager": "windows", "app_id": "exps", "domain": "uatdetnsw.win"}, "nodes": []}, "exut_dev_uc.det.nsw.edu.au": {"service_name": "openmetrics_exut", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "exut", "domain": "uc.det.nsw.edu.au"}, "nodes": []}, "exut_test_uc.det.nsw.edu.au": {"service_name": "openmetrics_exut", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddacm", "op_window": "24x7", "os_manager": "ddacm", "app_id": "exut", "domain": "uc.det.nsw.edu.au"}, "nodes": []}, "exutil_prod_detnsw.win": {"service_name": "openmetrics_exutil", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac - messaging team", "op_window": "24x7", "os_manager": "windows", "app_id": "exutil", "domain": "detnsw.win"}, "nodes": ["pw0991exadm03.nsw.education", "pw0992exadm04.nsw.education"]}, "file_prod_detnsw.win": {"service_name": "openmetrics_file", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "owen chambers", "op_window": "24x7", "os_manager": "windows", "app_id": "file", "domain": "detnsw.win"}, "nodes": []}, "finops_prod_workgroup": {"service_name": "openmetrics_finops", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "cirrus hybrid cloud", "op_window": "24x7", "os_manager": "penguins", "app_id": "finops"}, "nodes": []}, "fmar_prod_detnsw.win": {"service_name": "openmetrics_fmar", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "fmar", "domain": "detnsw.win"}, "nodes": []}, "fopsts_prod_detnsw.win": {"service_name": "openmetrics_fopsts", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "fopsts", "domain": "detnsw.win"}, "nodes": ["pw00000202fs01.file.det.nsw.edu.au", "pw00000238fs01.file.det.nsw.edu.au", "pw00000210fs02.file.det.nsw.edu.au", "pw00000824fs01.file.det.nsw.edu.au", "pw00000236fs01.file.det.nsw.edu.au", "pw00000218fs01.file.det.nsw.edu.au", "pw00000239fs01.file.det.nsw.edu.au", "pw00000830fs01.file.det.nsw.edu.au", "pw00009565fs01.file.det.nsw.edu.au", "pw00000237fs01.file.det.nsw.edu.au", "pw00000205fs01.file.det.nsw.edu.au", "pw00000828fs01.file.det.nsw.edu.au", "pw00000226fs01.file.det.nsw.edu.au", "pw00000230fs01.file.det.nsw.edu.au", "pw00000381fs01.file.det.nsw.edu.au", "pw00000445fs01.file.det.nsw.edu.au", "pw00000999fs01.file.det.nsw.edu.au"]}, "fort_prod_detnsw.win": {"service_name": "openmetrics_fort", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "network services", "op_window": "24x7", "os_manager": "windows", "app_id": "fort", "domain": "detnsw.win"}, "nodes": ["pw0991fort0001.nsw.education"]}, "fstftp_prod_detnsw.win": {"service_name": "openmetrics_fstftp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "maaran a<PERSON>", "op_window": "12x5", "os_manager": "windows", "app_id": "fstftp", "domain": "detnsw.win"}, "nodes": ["pw0472fstftp01.nsw.education"]}, "fxdm_prod_detnsw.win": {"service_name": "openmetrics_fxdm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ip win", "op_window": "24x7", "os_manager": "windows", "app_id": "fxdm", "domain": "detnsw.win"}, "nodes": ["pw0992fxdm0004.nsw.education", "pw0992fxdm0001.nsw.education", "pw0992fxdm0005.nsw.education", "pw0992fxdm0002.nsw.education", "pw0992fxdm0003.nsw.education", "pw0992fxdm0006.nsw.education"]}, "fxfmp_preprod_detnsw.win": {"service_name": "openmetrics_fxfmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "garry richards", "op_window": "on_demand", "os_manager": "windows", "app_id": "fxfmp", "domain": "detnsw.win"}, "nodes": ["qw0472fxfmpa02.nsw.education", "qw0472fxfmpo01.nsw.education", "qw0472fxfmpo02.nsw.education", "qw0472fxfmpp01.nsw.education", "qw0472fxfmpp02.nsw.education", "qw0472fxfmps01.nsw.education"]}, "fxfmp_preprod_predetnsw.win": {"service_name": "openmetrics_fxfmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "garry richards", "op_window": "24x7", "os_manager": "windows", "app_id": "fxfmp", "domain": "predetnsw.win"}, "nodes": []}, "fxfmp_test_uatdetnsw.win": {"service_name": "openmetrics_fxfmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "garry richards", "op_window": "24x7", "os_manager": "windows", "app_id": "fxfmp", "domain": "uatdetnsw.win"}, "nodes": []}, "fxfmp_prod_detnsw.win": {"service_name": "openmetrics_fxfmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "garry richards", "op_window": "24x7", "os_manager": "windows", "app_id": "fxfmp", "domain": "detnsw.win"}, "nodes": ["pw0991fxfmpo02.nsw.education", "pw0991fxfmpa01.nsw.education", "pw0991fxfmpp01.nsw.education", "pw0991fxfmpo01.nsw.education", "pw0991fxfmpp02.nsw.education", "pw0992fxfmpo02.nsw.education", "pw0991fxfmpp03.nsw.education", "pw0992fxfmpo01.nsw.education", "pw0992fxfmpa01.nsw.education", "pw0992fxfmpp01.nsw.education", "pw0992fxfmpp03.nsw.education", "pw0992fxfmpp02.nsw.education", "pw0992fxfmps01.nsw.education", "pw0991fxfmps01.nsw.education", "pw0472fxfmpa01.nsw.education", "pw0472fxfmpa02.nsw.education", "pw0472fxfmpp01.nsw.education", "pw0472fxfmpp02.nsw.education", "pw0472fxfmpo01.nsw.education", "pw0472fxfmpo02.nsw.education", "pw0472fxfmps01.nsw.education"]}, "guains_prod_detnsw.win": {"service_name": "openmetrics_guains", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "cloudops", "op_window": "12x5", "os_manager": "windows", "app_id": "guains", "domain": "detnsw.win"}, "nodes": ["pw0472guains01.nsw.education"]}, "hcmdm_test_workgroup": {"service_name": "openmetrics_hcmdm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "hcmdm"}, "nodes": ["tl0992hcmdm001.nsw.education"]}, "hcmdm_prod_workgroup": {"service_name": "openmetrics_hcmdm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "hcmdm"}, "nodes": ["pl0992hcmdm001.nsw.education"]}, "hcrpx_dev_workgroup": {"service_name": "openmetrics_hcrpx", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "hcrpx"}, "nodes": ["dl0991hcrpx001.nsw.education", "dl0992hcrpx001.nsw.education"]}, "hcrpx_preprod_workgroup": {"service_name": "openmetrics_hcrpx", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "hcrpx"}, "nodes": ["ql0991hcrpx001.nsw.education", "ql0992hcrpx001.nsw.education"]}, "hcrpx_prod_workgroup": {"service_name": "openmetrics_hcrpx", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "networks", "op_window": "24x7", "os_manager": "penguins", "app_id": "hcrpx"}, "nodes": ["pl0991hcrpx001.nsw.education", "pl0991hcrpx002.nsw.education", "pl0992hcrpx001.nsw.education", "pl0992hcrpx002.nsw.education"]}, "hdpd13_prod_workgroup": {"service_name": "openmetrics_hdpd13", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "hdpd13"}, "nodes": ["pu0992nifigw01.dmz.det.nsw.edu.au", "pu0992nifigw02.dmz.det.nsw.edu.au", "pu0991nifigw01.dmz.det.nsw.edu.au", "pu0000nifi0001.apps.det.nsw.edu.au", "pu0991nifigw02.dmz.det.nsw.edu.au"]}, "hpdc_prod_detnsw.win": {"service_name": "openmetrics_hpdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "wact", "op_window": "24x7", "os_manager": "windows", "app_id": "hpdc", "domain": "detnsw.win"}, "nodes": ["pw0992hpdc0003.nsw.education", "pw0992hpdc0004.nsw.education", "pw0992hpdc0002.nsw.education", "pw0992hpdc0001.nsw.education"]}, "hrwa_test_uatdetnsw.win": {"service_name": "openmetrics_hrwa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "hrwa", "domain": "uatdetnsw.win"}, "nodes": ["tw0992hrwaap01.apps.test.det.nsw.edu.au", "tw0992hrwasr01.apps.test.det.nsw.edu.au"]}, "hrwa_prod_detnsw.win": {"service_name": "openmetrics_hrwa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "hrwa", "domain": "detnsw.win"}, "nodes": ["pw0992hrwaap01.apps.det.nsw.edu.au", "pw0992hrwasr01.apps.det.nsw.edu.au"]}, "hwme_prod_detnsw.win": {"service_name": "openmetrics_hwme", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd - is - ddac - desktop platform - edge services & solutions", "op_window": "24x7", "os_manager": "windows", "app_id": "hwme", "domain": "detnsw.win"}, "nodes": ["pw0992hwmedrm1.nsw.education"]}, "hwmg_prod_unknown": {"service_name": "openmetrics_hwmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "hwmg", "domain": "unknown"}, "nodes": ["pw0991hwmg0101.hbm.det.nsw.edu.au"]}, "ictp_preprod_workgroup": {"service_name": "openmetrics_ictp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "steve mckenna", "op_window": "on_demand", "os_manager": "penguins", "app_id": "ictp"}, "nodes": ["ql0475ictp0001.nsw.education"]}, "ictp_test_workgroup": {"service_name": "openmetrics_ictp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "steve mckenna", "op_window": "on_demand", "os_manager": "penguins", "app_id": "ictp"}, "nodes": ["tl0475ictp0001.nsw.education"]}, "ictp_prod_workgroup": {"service_name": "openmetrics_ictp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "steve mckenna", "op_window": "24x7", "os_manager": "penguins", "app_id": "ictp"}, "nodes": ["pl0475ictp0001.nsw.education"]}, "ieauth_shared_detnsw.win": {"service_name": "openmetrics_ieauth", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is - networks", "op_window": "24x7", "os_manager": "windows", "app_id": "ieauth", "domain": "detnsw.win"}, "nodes": ["tw0991ieauth01.nsw.education", "tw0992ieauth01.nsw.education"]}, "ieauth_shared_uatdetnsw.win": {"service_name": "openmetrics_ieauth", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is - networks", "op_window": "24x7", "os_manager": "windows", "app_id": "ieauth", "domain": "uatdetnsw.win"}, "nodes": ["tw0991ieauth02.test.nsw.education", "tw0992ieauth02.test.nsw.education"]}, "ieidd_dev_workgroup": {"service_name": "openmetrics_ieidd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "network services", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "<PERSON><PERSON><PERSON>"}, "nodes": ["dl0991ieidd002.nsw.education", "dl0991ieidd001.nsw.education", "dl0992ieidd001.nsw.education"]}, "ieidd_prod_workgroup": {"service_name": "openmetrics_ieidd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "network services", "op_window": "24x7", "os_manager": "penguins", "app_id": "<PERSON><PERSON><PERSON>"}, "nodes": ["pl0991ieidd001.nsw.education", "pl0992ieidd002.nsw.education", "pl0992ieidd001.nsw.education", "pl0991ieidd002.nsw.education", "pl0991ieidd003.nsw.education", "pl0992ieidd003.nsw.education", "pl0991ieidd006.nsw.education", "pl0992ieidd006.nsw.education", "pl0991ieidd004.nsw.education", "pl0992ieidd004.nsw.education", "pl0991ieidd005.nsw.education", "pl0992ieidd005.nsw.education"]}, "ielog_preprod_unset": {"service_name": "openmetrics_ielog", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "ielog", "domain": "unset"}, "nodes": ["ql0991ielog001.nsw.education", "ql0992ielog001.nsw.education"]}, "ielog_preprod_workgroup": {"service_name": "openmetrics_ielog", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "ielog"}, "nodes": ["ql0991ielog002.nsw.education"]}, "ielog_prod_workgroup": {"service_name": "openmetrics_ielog", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "networks", "op_window": "24x7", "os_manager": "penguins", "app_id": "ielog"}, "nodes": ["pl0991ielog004.nsw.education", "pl0991ielog003.nsw.education", "pl0992ielog004.nsw.education", "pl0992ielog003.nsw.education", "pl0991ielog005.nsw.education", "pl0992ielog006.nsw.education", "pl0991ielog006.nsw.education", "pl0992ielog005.nsw.education"]}, "ifs_prod_detnsw.win": {"service_name": "openmetrics_ifs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "liz slakey", "op_window": "24x7", "os_manager": "windows", "app_id": "ifs", "domain": "detnsw.win"}, "nodes": ["pw0992ifs00001.nsw.education"]}, "iirs_dev_workgroup": {"service_name": "openmetrics_iirs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jack jiang", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "iirs"}, "nodes": ["dl0991iirs0001.nsw.education", "dl0991iirs0002.nsw.education"]}, "iirs_preprod_workgroup": {"service_name": "openmetrics_iirs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "pedr brown", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "iirs"}, "nodes": ["ql0991iirs0001.nsw.education", "el0992iirs0001.nsw.education", "ql0992iirs0002.nsw.education", "ql0991iirs0002.nsw.education"]}, "iirs_test_workgroup": {"service_name": "openmetrics_iirs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "pedr brown", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "iirs"}, "nodes": ["tl0991iirs0001.nsw.education", "tl0991iirs0002.nsw.education"]}, "iirs_prod_workgroup": {"service_name": "openmetrics_iirs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "pedr brown", "op_window": "24x7", "os_manager": "penguins", "app_id": "iirs"}, "nodes": ["pl0991iirs0001.nsw.education"]}, "isuscn_prod_detnsw.win": {"service_name": "openmetrics_isuscn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "@it c&s sos information security", "op_window": "24x7", "os_manager": "windows", "app_id": "isuscn", "domain": "detnsw.win"}, "nodes": ["pw0992isuscnt1.nsw.education", "pw0992isuscn01.nsw.education", "pw0992isuscn02.nsw.education", "pw0992isuscn03.nsw.education", "pw0991isuscn03.nsw.education", "pw0991isuscn04.nsw.education", "pw0991isuscn05.nsw.education", "pw0992isuscn04.nsw.education", "pw0991isuscn10.nsw.education", "pw0991isuscn09.nsw.education", "pw0992isuscn08.nsw.education", "pw0992isuscn07.nsw.education", "pw0992isuscn06.nsw.education", "pw0991isuscn11.nsw.education"]}, "isuv_prod_detnsw.win": {"service_name": "openmetrics_isuv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "isuv", "domain": "detnsw.win"}, "nodes": ["pw0992isuina01.hbm.det.nsw.edu.au"]}, "itdim_test_uatdetnsw.win": {"service_name": "openmetrics_itdim", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd data centre operations", "op_window": "12x5", "os_manager": "windows", "app_id": "itdim", "domain": "uatdetnsw.win"}, "nodes": ["tw0472itdim001.test.nsw.education"]}, "itdim_prod_detnsw.win": {"service_name": "openmetrics_itdim", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd data centre operations", "op_window": "24x7", "os_manager": "windows", "app_id": "itdim", "domain": "detnsw.win"}, "nodes": ["pw0472itdim001.nsw.education"]}, "ivets_dev_workgroup": {"service_name": "openmetrics_ivets", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ivets"}, "nodes": ["dl0475ivets001.nonprod.training.nsw.gov.au"]}, "ivets_preprod_workgroup": {"service_name": "openmetrics_ivets", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ivets"}, "nodes": ["ql0475ivets001.nonprod.training.nsw.gov.au"]}, "ivets_prod_workgroup": {"service_name": "openmetrics_ivets", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ivets"}, "nodes": ["pl0475ivets001.prod.training.nsw.gov.au"]}, "jhsap_shared_detnsw.win": {"service_name": "openmetrics_jhsap", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "david weeda", "op_window": "24x7", "os_manager": "windows", "app_id": "jhsap", "domain": "detnsw.win"}, "nodes": ["pw0992jhsap001.nsw.education", "pw0991jhsap001.nsw.education"]}, "jhsm_preprod_predetnsw.win": {"service_name": "openmetrics_jhsm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "dan grapilon", "op_window": "24x7", "os_manager": "windows", "app_id": "jhsm", "domain": "predetnsw.win"}, "nodes": ["qw0992jhsm0001.nsw.education"]}, "jhsm_prod_detnsw.win": {"service_name": "openmetrics_jhsm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "dan grapilon", "op_window": "12x5", "os_manager": "windows", "app_id": "jhsm", "domain": "detnsw.win"}, "nodes": ["pw0991jhsm0001.nsw.education"]}, "jira_prod_workgroup": {"service_name": "openmetrics_jira", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "pedr brown", "op_window": "on-demand", "os_manager": "penguins", "app_id": "jira"}, "nodes": ["pl0475jira0002.nsw.education"]}, "kfka_dev_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ashish kamble", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "kfka"}, "nodes": ["dl0991kfkar0001.nsw.education", "dl0991kfkab0001.nsw.education", "dl0991kfkac0002.nsw.education", "dl0991kfkab0002.nsw.education", "dl0991kfkad0001.nsw.education", "dl0991kfkac0001.nsw.education", "dl0991kfkaz0001.nsw.education", "dl0991kfkar0002.nsw.education", "dl0992kfkac002.nsw.education", "dl0992kfkab002.nsw.education", "dl0992kfkaz001.nsw.education", "dl0992kfkac001.nsw.education", "dl0992kfkab001.nsw.education", "dl0992kfkaz202.nsw.education", "dl0991kfkab201.nsw.education", "dl0992kfkab202.nsw.education", "dl0991kfkaz201.nsw.education", "dl0991kfkab207.nsw.education", "dl0992kfkab206.nsw.education", "dl0991kfkab205.nsw.education", "dl0992kfkac202.nsw.education", "dl0992kfkac201.nsw.education"]}, "kfka_preprod_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ashish kamble", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "kfka"}, "nodes": ["ql0992kfkaz001.nsw.education", "ql0992kfkaz002.nsw.education", "ql0992kfkab001.nsw.education", "ql0992kfkab003.nsw.education", "ql0992kfkaz003.nsw.education", "ql0992kfkab002.nsw.education", "ql0992kfkac002.nsw.education", "ql0992kfkac001.nsw.education", "ql0992kfkac003.nsw.education"]}, "kfka_shared_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "ashish kamble", "op_window": "24x5", "os_manager": "penguins", "app_id": "kfka"}, "nodes": ["pl0992kadmin02.nsw.education"]}, "kfka_test_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ashish kamble", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "kfka"}, "nodes": ["tl0992kfkaz001.nsw.education", "tl0992kfkaz003.nsw.education", "tl0992kfkab002.nsw.education", "tl0992kfkab001.nsw.education", "tl0992kfkaz002.nsw.education", "tl0992kfkac002.nsw.education", "tl0992kfkab003.nsw.education", "tl0992kfkac003.nsw.education", "tl0992kfkac001.nsw.education", "tl0991ksql0002.nsw.education", "tl0991ksql0001.nsw.education"]}, "kfka_prod_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ashish kamble", "op_window": "24x7", "os_manager": "penguins", "app_id": "kfka"}, "nodes": ["pl0992kadm0001.nsw.education", "pl0991ksql0001.nsw.education", "pl0991ksql0002.nsw.education"]}, "kfka_prod_broker_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ashish kamble", "op_window": "24x7", "os_manager": "penguins", "app_id": "kfka", "host_group_id": "broker"}, "nodes": ["pl0991kfkab0002.nsw.education", "pl0991kfkab0001.nsw.education", "pl0991kfkab0003.nsw.education"]}, "kfka_prod_connect_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ashish kamble", "op_window": "24x7", "os_manager": "penguins", "app_id": "kfka", "host_group_id": "connect"}, "nodes": ["pl0991kfkac0001.nsw.education", "pl0991kfkac0002.nsw.education", "pl0991kfkac0003.nsw.education"]}, "kfka_prod_zookeeper_workgroup": {"service_name": "openmetrics_kfka", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ashish kamble", "op_window": "24x7", "os_manager": "penguins", "app_id": "kfka", "host_group_id": "zookeeper"}, "nodes": ["pl0991kfkaz0002.nsw.education", "pl0991kfkaz0001.nsw.education", "pl0991kfkaz0003.nsw.education"]}, "limg_dev_workgroup": {"service_name": "openmetrics_limg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "bob", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "limg"}, "nodes": ["dl0991lsoe0008.nsw.education", "dl0991lsoe0015.nsw.education"]}, "limg_shared_workgroup": {"service_name": "openmetrics_limg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "semmer sultan", "op_window": "12x5", "os_manager": "penguins", "app_id": "limg"}, "nodes": ["pl0992limg0003.nsw.education", "pl0991limg0003.nsw.education", "pl0991limg0001.nsw.education", "pl0992limg0001.nsw.education"]}, "limg_prod_workgroup": {"service_name": "openmetrics_limg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "bob", "op_window": "12x5", "os_manager": "penguins", "app_id": "limg"}, "nodes": ["pl0992limg0004.nsw.education", "pl0991limg0004.nsw.education"]}, "lsoe_dev_devdetnsw.win": {"service_name": "openmetrics_lsoe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "12x5", "os_manager": "windows", "app_id": "lsoe", "domain": "devdetnsw.win"}, "nodes": ["dw0472lsoe0001.nsw.education"]}, "lsoe_dev_workgroup": {"service_name": "openmetrics_lsoe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "lsoe"}, "nodes": ["dl0991lsoe0001.nsw.education", "dl0991lsoe0005.nsw.education", "dl0991lsoe0006.nsw.education", "dl0991lsoe0002.nsw.education", "dl0991lsoe0004.nsw.education", "dl0991lsoe0007.nsw.education", "dl0991lsoe0014.nsw.education", "dl0991lsoe0018.nsw.education", "dl0991lsoe0021.nsw.education", "dl0991lsoekec3.nsw.education", "dl0991lsoe0024.nsw.education", "dl0991lsoe0020.nsw.education"]}, "lsoe_test_workgroup": {"service_name": "openmetrics_lsoe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "linux team", "op_window": "12x5", "os_manager": "penguins", "app_id": "lsoe"}, "nodes": ["tl0991cyba0001.nsw.education", "tl0991lsoe0001.nsw.education", "tl0991lsoe0002.nsw.education", "tl0991lsoe0003.nsw.education", "tl0991lsoe0004.nsw.education", "tl0991lsoe0005.nsw.education", "tl0991lsoe0006.nsw.education"]}, "lsoe_prod_workgroup": {"service_name": "openmetrics_lsoe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "12x5", "os_manager": "penguins", "app_id": "lsoe"}, "nodes": ["pl0991lsoe0001.nsw.education", "pl0475lsoe0001.nsw.education", "pl0991lsoe0002.nsw.education"]}, "lxlc_prod_detnsw.win": {"service_name": "openmetrics_lxlc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "lxlc", "domain": "detnsw.win"}, "nodes": ["pw0000lxlcdm01.vdr.det.nsw.edu.au"]}, "lxmv_prod_detnsw.win": {"service_name": "openmetrics_lxmv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "lxmv", "domain": "detnsw.win"}, "nodes": []}, "mantis_dev_devdetnsw.win": {"service_name": "openmetrics_mantis", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "corp-soap / mantis support", "op_window": "24x7", "os_manager": "windows", "app_id": "mantis", "domain": "devdetnsw.win"}, "nodes": ["dw0472mantis01.nsw.education", "dw0472mantis02.nsw.education", "dw0472mantis06.nsw.education", "dw0472mantis03.nsw.education", "dw0472mantis05.nsw.education", "dw0472mantis04.nsw.education"]}, "mantis_prod_detnsw.win": {"service_name": "openmetrics_mantis", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "corp-soap / mantis support", "op_window": "24x7", "os_manager": "windows", "app_id": "mantis", "domain": "detnsw.win"}, "nodes": ["pw0000deiapp05.apps.det.nsw.edu.au", "pw0472mantis01.nsw.education", "pw0472mantis02.nsw.education", "pw0472mantis03.nsw.education", "pw0472mantis04.nsw.education", "pw0472mantis05.nsw.education", "pw0472mantis06.nsw.education"]}, "mfaa_dev_devdetnsw.win": {"service_name": "openmetrics_mfaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "mfaa", "domain": "devdetnsw.win"}, "nodes": ["dw0992rds00102.ad.test.det.nsw.edu.au"]}, "mfaa_preprod_predetnsw.win": {"service_name": "openmetrics_mfaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "mfaa", "domain": "predetnsw.win"}, "nodes": ["qw0991rds00102.ad.pre.det.nsw.edu.au"]}, "mfaa_prod_detnsw.win": {"service_name": "openmetrics_mfaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "mfaa", "domain": "detnsw.win"}, "nodes": ["pw0992mfaw0101.dmz.det.nsw.edu.au", "pw0992mfaa0101.ad.det.nsw.edu.au", "pw0992mfar0101.hbm.det.nsw.edu.au", "pw0992mfag0101.hbm.det.nsw.edu.au", "pw0991mfar0101.hbm.det.nsw.edu.au", "pw0991mfaw0101.dmz.det.nsw.edu.au", "pw0991mfaa0101.ad.det.nsw.edu.au", "pw0991mfag0101.hbm.det.nsw.edu.au", "pw0991web00101.ad.det.nsw.edu.au"]}, "mfaa_prod_unknown": {"service_name": "openmetrics_mfaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "mfaa", "domain": "unknown"}, "nodes": ["pw0991mim00701.ad.det.nsw.edu.au"]}, "mfas_prod_detnsw.win": {"service_name": "openmetrics_mfas", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "will twemlow", "op_window": "24x7", "os_manager": "windows", "app_id": "mfas", "domain": "detnsw.win"}, "nodes": ["pw0992mfas0002.nsw.education", "pw0992mfas0003.nsw.education", "pw0992mfas0004.nsw.education"]}, "mfile_prod_detnsw.win": {"service_name": "openmetrics_mfile", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "mfile", "domain": "detnsw.win"}, "nodes": ["pw00000829fs02.file.det.nsw.edu.au", "pw00000916fs01.file.det.nsw.edu.au", "pw00000910fs01.file.det.nsw.edu.au", "pw0000archfs01.file.det.nsw.edu.au", "pw00000825fs04.file.det.nsw.edu.au", "pw00000825fs03.file.det.nsw.edu.au", "pw00000838fs01.file.det.nsw.edu.au", "pw00000916fs02.file.det.nsw.edu.au", "pw00000490fs01.file.det.nsw.edu.au", "pw00000825fs02.file.det.nsw.edu.au", "pw00000823fs01.file.det.nsw.edu.au", "pw00000829fs04.file.det.nsw.edu.au", "pw00000826fs01.file.det.nsw.edu.au", "pw00000829fs03.file.det.nsw.edu.au", "pw00000910fs02.file.det.nsw.edu.au"]}, "mgti_prod_detnsw.win": {"service_name": "openmetrics_mgti", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "mgti", "domain": "detnsw.win"}, "nodes": ["pw0992mgti0001.prod.det.nsw.edu.au"]}, "mgti96_prod_detnsw.win": {"service_name": "openmetrics_mgti96", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "mgti96", "domain": "detnsw.win"}, "nodes": ["pw0000mgti0000.prod.mgmt.det"]}, "mige_dev_detnsw.win": {"service_name": "openmetrics_mige", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "bob", "op_window": "24x7", "os_manager": "unknown", "app_id": "mige", "domain": "detnsw.win"}, "nodes": ["cw0992mfaw0102.dmz.det.nsw.edu.au"]}, "mige_dev_devdetnsw.win": {"service_name": "openmetrics_mige", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "bob", "op_window": "24x7", "os_manager": "unknown", "app_id": "mige", "domain": "devdetnsw.win"}, "nodes": ["dw0000cesdbi01.apps.dev.det.nsw.edu.au", "dw0000sapps001.dmz.det.nsw.edu.au"]}, "mige_preprod_predetnsw.win": {"service_name": "openmetrics_mige", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "bob", "op_window": "24x7", "os_manager": "penguins", "app_id": "mige", "domain": "predetnsw.win"}, "nodes": ["qw0000sapps001.dmz.det.nsw.edu.au"]}, "mige_preprod_workgroup": {"service_name": "openmetrics_mige", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "bob", "op_window": "24x7", "os_manager": "penguins", "app_id": "mige"}, "nodes": ["qu0991rdew0001.apps.pre.det.nsw.edu.au", "qu0991zkps0001.apps.pre.det.nsw.edu.au", "qu0991rdew0002.apps.pre.det.nsw.edu.au"]}, "mige_prod_workgroup": {"service_name": "openmetrics_mige", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "bob", "op_window": "24x7", "os_manager": "penguins", "app_id": "mige"}, "nodes": ["up-rde-as1.apps.det.nsw.edu.au", "up-rde-as2.apps.det.nsw.edu.au"]}, "mim0_prod_unknown": {"service_name": "openmetrics_mim0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "mim0", "domain": "unknown"}, "nodes": ["pw0992mim00701.ad.det.nsw.edu.au"]}, "mlms_prod_workgroup": {"service_name": "openmetrics_mlms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "kenny ferreira", "op_window": "24x7", "os_manager": "penguins", "app_id": "mlms"}, "nodes": ["pl0991mlms1001.nsw.education"]}, "mrdicc_dev_devdetnsw.win": {"service_name": "openmetrics_mrdicc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "windows", "app_id": "mrdicc", "domain": "devdetnsw.win"}, "nodes": ["dw0992mrdicc02.nsw.education", "dw0992mrdicc01.nsw.education"]}, "mrdicc_preprod_predetnsw.win": {"service_name": "openmetrics_mrdicc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "windows", "app_id": "mrdicc", "domain": "predetnsw.win"}, "nodes": ["qw0992mrdicc02.nsw.education", "qw0992mrdicc01.nsw.education"]}, "mrdicc_test_uatdetnsw.win": {"service_name": "openmetrics_mrdicc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "windows", "app_id": "mrdicc", "domain": "uatdetnsw.win"}, "nodes": ["tw0992mrdicc01.nsw.education", "tw0992mrdicc02.nsw.education"]}, "mrdicc_prod_detnsw.win": {"service_name": "openmetrics_mrdicc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "windows", "app_id": "mrdicc", "domain": "detnsw.win"}, "nodes": ["pw0992mrdicc01.nsw.education", "pw0992mrdicc02.nsw.education"]}, "mskms_prod_detnsw.win": {"service_name": "openmetrics_mskms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "active directory team", "op_window": "24x7", "os_manager": "windows", "app_id": "mskms", "domain": "detnsw.win"}, "nodes": ["pw0991mskms001.nsw.education", "pw0992mskms001.nsw.education"]}, "mtmrep_prod_workgroup": {"service_name": "openmetrics_mtmrep", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "infrastructure applications - monitoring", "op_window": "24x7", "os_manager": "penguins", "app_id": "mtmrep"}, "nodes": ["pl0991mtmrep01.nsw.education"]}, "myplbi_prod_detnsw.win": {"service_name": "openmetrics_myplbi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "vance landall", "op_window": "24x7", "os_manager": "windows", "app_id": "myplbi", "domain": "detnsw.win"}, "nodes": ["pw0472myplbi01.nsw.education"]}, "nbck_dev_uatdetnsw.win": {"service_name": "openmetrics_nbck", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ip windows", "op_window": "24x7", "os_manager": "windows", "app_id": "nbck", "domain": "uatdetnsw.win"}, "nodes": ["tw0992nbckms01.backup.det.nsw.edu.au", "tw0992nbckop01.backup.det.nsw.edu.au", "tw0992nbckmv01.backup.det.nsw.edu.au", "tw0991nbckmv01.backup.det.nsw.edu.au", "tw0991nbckcb01.backup.det.nsw.edu.au", "tw0992nbckcb01.backup.det.nsw.edu.au"]}, "nbck_production_detnsw.win": {"service_name": "openmetrics_nbck", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ip windows", "op_window": "24x7", "os_manager": "windows", "app_id": "nbck", "domain": "detnsw.win"}, "nodes": ["pw0000bkop0001.backup.det.nsw.edu.au", "pw0000bkms0001.backup.det.nsw.edu.au", "pw0991nbckms10.backup.det.nsw.edu.au", "pw0991nbckop10.backup.det.nsw.edu.au", "pw0991nbckmv11.backup.det.nsw.edu.au", "pw0991nbckmv12.backup.det.nsw.edu.au", "pw0992nbckmv11.backup.det.nsw.edu.au", "pw0992nbckmv12.backup.det.nsw.edu.au", "pw0991nbckmv21.backup.det.nsw.edu.au", "pw0991nbckmv22.backup.det.nsw.edu.au", "pw0992nbckmv21.backup.det.nsw.edu.au", "pw0992nbckmv22.backup.det.nsw.edu.au", "pl0991nbckmp16.backup.det.nsw.edu.au", "pl0992nbckmp16.backup.det.nsw.edu.au", "pl0991nbckmp13.backup.det.nsw.edu.au", "pl0992nbckmp13.backup.det.nsw.edu.au", "pl0991nbckmp11.backup.det.nsw.edu.au", "pl0991nbckmp15.backup.det.nsw.edu.au", "pl0992nbckmp11.backup.det.nsw.edu.au", "pl0992nbckmp15.backup.det.nsw.edu.au"]}, "nbck_test_uatdetnsw.win": {"service_name": "openmetrics_nbck", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "salman khan", "op_window": "24x7", "os_manager": "windows", "app_id": "nbck", "domain": "uatdetnsw.win"}, "nodes": ["tw0991bkop0001.nsw.education", "tw0992bkmd0002.nsw.education"]}, "nbcklx_dev_unset": {"service_name": "openmetrics_nbcklx", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "storage team", "op_window": "24x7", "os_manager": "penguins", "app_id": "nbcklx", "domain": "unset"}, "nodes": ["tl0992nbckmv07.backup.det.nsw.edu.au"]}, "ncfb_dev_workgroup": {"service_name": "openmetrics_ncfb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "ncfb"}, "nodes": ["du0991ncfb0001.netmon.det.nsw.edu.au"]}, "ncfb_prod_workgroup": {"service_name": "openmetrics_ncfb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ncfb"}, "nodes": ["pu0992ncfb0001.netmon.det.nsw.edu.au", "pu0991ncfb0001.netmon.det.nsw.edu.au"]}, "ndes_test_uatdetnsw.win": {"service_name": "openmetrics_ndes", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "darren mason", "op_window": "12x5", "os_manager": "windows", "app_id": "ndes", "domain": "uatdetnsw.win"}, "nodes": ["tw0992ndes0001.nsw.education", "tw0992ndes0102.test.nsw.education"]}, "ndes_prod_detnsw.win": {"service_name": "openmetrics_ndes", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "darren mason", "op_window": "24x7", "os_manager": "windows", "app_id": "ndes", "domain": "detnsw.win"}, "nodes": ["pw0992ndes0001.nsw.education", "pw0991ndes0001.nsw.education", "pw0992ndes0102.nsw.education", "pw0991ndes0102.nsw.education"]}, "nexus_shared_workgroup": {"service_name": "openmetrics_nexus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "middle ware", "op_window": "24x7", "os_manager": "penguins", "app_id": "nexus"}, "nodes": ["pl0991nexus001.nsw.education", "tl0475nexus001.nsw.education"]}, "nexus_test_workgroup": {"service_name": "openmetrics_nexus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "middle ware", "op_window": "12x5", "os_manager": "penguins", "app_id": "nexus"}, "nodes": ["tl0992nexus001.nsw.education"]}, "nexus_prod_workgroup": {"service_name": "openmetrics_nexus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "lb", "op_window": "24x7", "os_manager": "penguins", "app_id": "nexus"}, "nodes": ["pl0475nexus001.nsw.education", "pl0475nexus002.nsw.education", "pl0991nexuslb1.nsw.education", "pl0991nexuslb2.nsw.education"]}, "ngcm_shared_workgroup": {"service_name": "openmetrics_ngcm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "binh thai", "op_window": "24x7", "os_manager": "penguins", "app_id": "ngcm"}, "nodes": ["dl0992ngcm0002.nsw.education", "pl0991ngcm0001.nsw.education", "pl0992ngcm0001.nsw.education", "dl0991ngcm0001.nsw.education"]}, "ngsfpa_preprod_detnsw.win": {"service_name": "openmetrics_ngsfpa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "non-government schools funding unit", "op_window": "12x5", "os_manager": "windows", "app_id": "ngsfpa", "domain": "detnsw.win"}, "nodes": ["qw0472ngsfpa01.nsw.education"]}, "ngsfpa_prod_detnsw.win": {"service_name": "openmetrics_ngsfpa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "non-government schools funding unit", "op_window": "12x5", "os_manager": "windows", "app_id": "ngsfpa", "domain": "detnsw.win"}, "nodes": ["pw0472ngsfpa01.nsw.education"]}, "npac_test_workgroup": {"service_name": "openmetrics_npac", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "girish g<PERSON>ire", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "npac"}, "nodes": ["tl0992npac0001.nsw.education"]}, "npac_prod_workgroup": {"service_name": "openmetrics_npac", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "girish g<PERSON>ire", "op_window": "24x7", "os_manager": "penguins", "app_id": "npac"}, "nodes": ["pl0991npac0001.nsw.education", "pl0992npac0001.nsw.education", "pl0991npac0002.nsw.education", "pl0992npac0002.nsw.education"]}, "nps0_prod_central.det.win": {"service_name": "openmetrics_nps0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "nps0", "domain": "central.det.win"}, "nodes": ["pw0992nps01101.ad.det.nsw.edu.au", "pw0991nps01101.ad.det.nsw.edu.au"]}, "nps0_prod_detnsw.win": {"service_name": "openmetrics_nps0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "nps0", "domain": "detnsw.win"}, "nodes": ["pw0992nps00101.ad.det.nsw.edu.au", "pw0992nps00102.ad.det.nsw.edu.au", "pw0991nps00101.ad.det.nsw.edu.au"]}, "nwgf_prod_unset": {"service_name": "openmetrics_nwgf", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "nwgf", "domain": "unset"}, "nodes": ["dl0991nwgf0001.netmon.det.nsw.edu.au", "pl0991nwgf0001.netmon.det.nsw.edu.au"]}, "nwix_prod_detnsw.win": {"service_name": "openmetrics_nwix", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "nwix", "domain": "detnsw.win"}, "nodes": ["pw0991nwixia01.netmon.det.nsw.edu.au"]}, "nwki_preprod_workgroup": {"service_name": "openmetrics_nwki", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "nwki"}, "nodes": ["qu0992nwkit001.netmon.det.nsw.edu.au"]}, "nwki5a_prod_workgroup": {"service_name": "openmetrics_nwki5a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "nwki5a"}, "nodes": ["pu0991nwkit001.netmon.det.nsw.edu.au"]}, "nwtst_preprod_predetnsw.win": {"service_name": "openmetrics_nwtst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "networks", "op_window": "24x7", "os_manager": "windows", "app_id": "nwtst", "domain": "predetnsw.win"}, "nodes": ["qw0992nwtst001.nsw.education"]}, "nwtst_preprod_workgroup": {"service_name": "openmetrics_nwtst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "nwtst"}, "nodes": ["ql0992nwtst001.nsw.education"]}, "nwtst_test_uatdetnsw.win": {"service_name": "openmetrics_nwtst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "networks", "op_window": "24x7", "os_manager": "windows", "app_id": "nwtst", "domain": "uatdetnsw.win"}, "nodes": ["tw0992nwtst001.nsw.education"]}, "nwtst_test_workgroup": {"service_name": "openmetrics_nwtst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "networks", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "nwtst"}, "nodes": ["tl0992nwtst001.nsw.education"]}, "nwut_test_uatdetnsw.win": {"service_name": "openmetrics_nwut", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "nwut", "domain": "uatdetnsw.win"}, "nodes": ["tw0991nwut0001.netmon.det.nsw.edu.au"]}, "nwut_prod_detnsw.win": {"service_name": "openmetrics_nwut", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "nwut", "domain": "detnsw.win"}, "nodes": ["pw0992nwut0001.netmon.det.nsw.edu.au", "pw0991nwut0001.netmon.det.nsw.edu.au"]}, "nwzb_dev_unset": {"service_name": "openmetrics_nwzb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "nwzb", "domain": "unset"}, "nodes": ["dl0991nwzb0051.netmon.det.nsw.edu.au", "dl0991nwpg0051.netmon.det.nsw.edu.au", "dl0991nwzb0052.netmon.det.nsw.edu.au", "dl0991nwzb0053.netmon.det.nsw.edu.au"]}, "nwzb_prod_unset": {"service_name": "openmetrics_nwzb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "netmon", "op_window": "24x7", "os_manager": "penguins", "app_id": "nwzb", "domain": "unset"}, "nodes": ["pl0991nwpg0002.netmon.det.nsw.edu.au", "pl0992nwpg0002.netmon.det.nsw.edu.au", "pl0991nwzb0601.netmon.det.nsw.edu.au", "pl0991nwzb0602.netmon.det.nsw.edu.au", "pl0991nwzb0603.netmon.det.nsw.edu.au", "pl0991nwzb0604.netmon.det.nsw.edu.au", "pl0992nwzb0601.netmon.det.nsw.edu.au", "pl0992nwzb0602.netmon.det.nsw.edu.au", "pl0992nwzb0603.netmon.det.nsw.edu.au"]}, "nwzb2e_prod_workgroup": {"service_name": "openmetrics_nwzb2e", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "nwzb2e"}, "nodes": ["pu0992nwzb0003.netmon.det.nsw.edu.au", "pu0992nwzb0002.netmon.det.nsw.edu.au", "pu0992nwpg0001.netmon.det.nsw.edu.au"]}, "nwzb30_dev_workgroup": {"service_name": "openmetrics_nwzb30", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "nwzb30"}, "nodes": ["du0991ngrf0001.netmon.det.nsw.edu.au", "du0991nwzb0001.netmon.det.nsw.edu.au", "du0991nwzb0002.netmon.det.nsw.edu.au"]}, "nwzb30_prod_workgroup": {"service_name": "openmetrics_nwzb30", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "nwzb30"}, "nodes": ["pu0991nwzb0004.netmon.det.nsw.edu.au", "pu0991nwzb0002.netmon.det.nsw.edu.au", "pu0991nicm0002.netmon.det.nsw.edu.au", "pu0991ngfr0001.netmon.det.nsw.edu.au", "pu0991nwzb0003.netmon.det.nsw.edu.au", "pu0991nwpg0001.netmon.det.nsw.edu.au", "pu0991nsql001.netmon.det.nsw.edu.au", "pu0991nwzb0001.netmon.det.nsw.edu.au"]}, "obs_dmz_workgroup": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dmz", "support_team": "observability", "op_window": "24x7", "os_manager": "penguins", "app_id": "obs"}, "nodes": ["pl0475obsrpx01.nsw.education"]}, "obs_dev_devdetnsw.win": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "observability", "op_window": "12x5", "os_manager": "windows", "app_id": "obs", "domain": "devdetnsw.win"}, "nodes": ["dw0991obs00001.nsw.education"]}, "obs_dev_workgroup": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "observability", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "obs"}, "nodes": ["dl0992obsmrx01.nsw.education", "dl0992obsken01.nsw.education"]}, "obs_shared_workgroup": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "observability", "op_window": "24x7", "os_manager": "penguins", "app_id": "obs"}, "nodes": []}, "obs_test_workgroup": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "observability", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "obs"}, "nodes": ["tl0992obsobs01.test.nsw.education", "tl0992obs00001.test.nsw.education", "tl0992obscol01.nsw.education", "tl0992obscol02.nsw.education", "tl0992obscol03.nsw.education", "tl0992obscol04.nsw.education", "tl0992obscol05.nsw.education", "tl0991obsbld01.nsw.education"]}, "obs_prod_detnsw.win": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "observability", "op_window": "12x5", "os_manager": "windows", "app_id": "obs", "domain": "detnsw.win"}, "nodes": []}, "obs_prod_workgroup": {"service_name": "openmetrics_obs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "observability", "op_window": "24x7", "os_manager": "penguins", "app_id": "obs"}, "nodes": ["pl0992obsobs01.nsw.education", "pl0475obscol06.nsw.education", "pl0475obscol08.nsw.education", "pl0475obscol07.nsw.education", "pl0992obscol01.nsw.education", "pl0992obscol02.nsw.education", "pl0992obscol03.nsw.education", "pl0992obscol04.nsw.education", "pl0992obscol05.nsw.education"]}, "olvr_test_uatdetnsw.win": {"service_name": "openmetrics_olvr", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "geoff jones", "op_window": "on_demand", "os_manager": "windows", "app_id": "olvr", "domain": "uatdetnsw.win"}, "nodes": ["tw0472slrp0001.test.nsw.education", "tw0472slrt0001.test.nsw.education", "tw0472slrt0002.test.nsw.education"]}, "omda_preprod_predetnsw.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "predetnsw.win"}, "nodes": ["qw0000omdacg01.hbm.det.nsw.edu.au"]}, "omda_preprod_unknown": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "unknown"}, "nodes": ["qw0000omdacg51.hbm.det.nsw.edu.au"]}, "omda_test_uatdetnsw.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "uatdetnsw.win"}, "nodes": ["tw0000omdacg01.hbm.det.nsw.edu.au"]}, "omda_test_unknown": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "unknown"}, "nodes": ["tw0000omdacg51.hbm.det.nsw.edu.au"]}, "omda_prod_detnsw.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "detnsw.win"}, "nodes": ["pw0992omdacm01.hbm.det.nsw.edu.au", "pw0991omdacm01.hbm.det.nsw.edu.au", "pw0991omdacm03.hbm.det.nsw.edu.au", "pw0991omdacm02.hbm.det.nsw.edu.au"]}, "omda_prod_devdetnsw.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "devdetnsw.win"}, "nodes": ["dw0000omdacg01.hbm.det.nsw.edu.au"]}, "omda_prod_ext.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "ext.win"}, "nodes": ["pw0000omdacg02.hbm.det.nsw.edu.au"]}, "omda_prod_idm.win": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "idm.win"}, "nodes": ["pw0000omdacg01.hbm.det.nsw.edu.au"]}, "omda_prod_unknown": {"service_name": "openmetrics_omda", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omda", "domain": "unknown"}, "nodes": ["dw0000omdacg52.hbm.det.nsw.edu.au"]}, "omdc_preprod_predetnsw.win": {"service_name": "openmetrics_omdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omdc", "domain": "predetnsw.win"}, "nodes": ["qw0000scommg02.hbm.det.nsw.edu.au", "qw0000scommg01.hbm.det.nsw.edu.au"]}, "omdc_test_detnsw.win": {"service_name": "openmetrics_omdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omdc", "domain": "detnsw.win"}, "nodes": ["tw0000momgw002.hbm.det.nsw.edu.au"]}, "omdc_prod_detnsw.win": {"service_name": "openmetrics_omdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omdc", "domain": "detnsw.win"}, "nodes": ["pw0992momgw502.hbm.det.nsw.edu.au"]}, "omdc_prod_devdetnsw.win": {"service_name": "openmetrics_omdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omdc", "domain": "devdetnsw.win"}, "nodes": ["dw0000momgw002.hbm.det.nsw.edu.au"]}, "omdc_prod_unknown": {"service_name": "openmetrics_omdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "omdc", "domain": "unknown"}, "nodes": ["pw0991momgw701.hbm.det.nsw.edu.au", "pw0991momgw501.hbm.det.nsw.edu.au", "pw0991momgw502.hbm.det.nsw.edu.au"]}, "orae2m_dev_workgroup": {"service_name": "openmetrics_orae2m", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "database team", "op_window": "24x7", "os_manager": "penguins", "app_id": "orae2m"}, "nodes": ["dl0475orae2m01.nsw.education", "dl0475orae2m02.nsw.education"]}, "oram_preprod_workgroup": {"service_name": "openmetrics_oram", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "database team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "oram"}, "nodes": ["ql0991oram0001.nsw.education", "ql0991oram0002.nsw.education"]}, "oram_shared_workgroup": {"service_name": "openmetrics_oram", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "database team", "op_window": "24x7", "os_manager": "penguins", "app_id": "oram"}, "nodes": ["pl0991oramd101.nsw.education", "pl0992oramd201.nsw.education", "pl0991orama101.nsw.education", "pl0991orama102.nsw.education", "pl0992orama201.nsw.education", "pl0991orama103.nsw.education", "pl0992orama203.nsw.education", "pl0992orama202.nsw.education", "dl0991orama101.nsw.education"]}, "oram_prod_workgroup": {"service_name": "openmetrics_oram", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "database team", "op_window": "12x5", "os_manager": "penguins", "app_id": "oram"}, "nodes": ["dl0991orama102.nsw.education"]}, "otd_prod_detnsw.win": {"service_name": "openmetrics_otd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "desktop platform team", "op_window": "24x7", "os_manager": "windows", "app_id": "otd", "domain": "detnsw.win"}, "nodes": ["pw0472otd00001.nsw.education"]}, "panuid_shared_detnsw.win": {"service_name": "openmetrics_panuid", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is - networks", "op_window": "24x7", "os_manager": "windows", "app_id": "panuid", "domain": "detnsw.win"}, "nodes": ["pw0992panuid01.nsw.education", "pw0991panuid01.nsw.education", "pw0991panuid03.nsw.education", "pw0991panuid04.nsw.education", "pw0991panuid05.nsw.education", "pw0991panuid02.nsw.education", "pw0992panuid02.nsw.education", "pw0992panuid04.nsw.education", "pw0992panuid03.nsw.education", "pw0992panuid05.nsw.education"]}, "panuid_prod_detnsw.win": {"service_name": "openmetrics_panuid", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "is - networks", "op_window": "24x7", "os_manager": "windows", "app_id": "panuid", "domain": "detnsw.win"}, "nodes": ["pw0991panuidc1.nsw.education", "pw0992panuidc1.nsw.education"]}, "pgdb_dev_workgroup": {"service_name": "openmetrics_pgdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "tango squad", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "pgdb"}, "nodes": ["dl0991pgdb0001.nsw.education", "dl0991pgdbbrmd.nsw.education"]}, "pgdb_preprod_workgroup": {"service_name": "openmetrics_pgdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "tango squad", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "pgdb"}, "nodes": ["ql0992pgdb0001.nsw.education"]}, "pgdb_test_workgroup": {"service_name": "openmetrics_pgdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "tango squad", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "pgdb"}, "nodes": ["tl0992pgdb0001.nsw.education", "tl0991pgdb0002.nsw.education", "tl0992pgdb0002.nsw.education", "tl0991pgdb0003.nsw.education", "tl0991pgdb0004.nsw.education", "tl0991pgdb0005.nsw.education"]}, "pgdb_prod_workgroup": {"service_name": "openmetrics_pgdb", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "tango squad", "op_window": "24x7", "os_manager": "penguins", "app_id": "pgdb"}, "nodes": ["pl0992pgdb0001.nsw.education"]}, "pnxz_dev_workgroup": {"service_name": "openmetrics_pnxz", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "pnxz"}, "nodes": ["du0992pnxzvr01.dmz.det.nsw.edu.au", "du0991pnxzvr01.dmz.det.nsw.edu.au"]}, "pnxz34_preprod_workgroup": {"service_name": "openmetrics_pnxz34", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "pnxz34"}, "nodes": ["qu0991pnxzvr01.dmz.det.nsw.edu.au", "qu0992pnxzvr01.dmz.det.nsw.edu.au"]}, "pnxz34_prod_workgroup": {"service_name": "openmetrics_pnxz34", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "pnxz34"}, "nodes": ["pu0992pnxzvr01.dmz.det.nsw.edu.au", "pu0992pnxzvr02.dmz.det.nsw.edu.au", "pu0991pnxzvr02.dmz.det.nsw.edu.au", "pu0991pnxzvr01.dmz.det.nsw.edu.au"]}, "ppjh_prod_detnsw.win": {"service_name": "openmetrics_ppjh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "george stefan", "op_window": "24x7", "os_manager": "windows", "app_id": "ppjh", "domain": "detnsw.win"}, "nodes": ["pw0991ppjh0101.nsw.education"]}, "psql_dev_workgroup": {"service_name": "openmetrics_psql", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "psql"}, "nodes": ["du0992psql0002.apps.dev.det.nsw.edu.au", "du0992psql0001.apps.dev.det.nsw.edu.au"]}, "psql33_test_workgroup": {"service_name": "openmetrics_psql33", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "psql33"}, "nodes": ["tu0000psqlp003.dbs.test.det.nsw.edu.au", "tu0000psqlp002.dbs.test.det.nsw.edu.au", "tu0000psqlp001.dbs.test.det.nsw.edu.au"]}, "qaauto_test_uatdetnsw.win": {"service_name": "openmetrics_qaauto", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "qa team", "op_window": "24x7", "os_manager": "windows", "app_id": "qaauto", "domain": "uatdetnsw.win"}, "nodes": ["tw0992qaauto01.nsw.education", "tw0991qaauto01.nsw.education", "tw0991qaauto02.nsw.education"]}, "qaauto_prod_detnsw.win": {"service_name": "openmetrics_qaauto", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "qa team", "op_window": "24x7", "os_manager": "windows", "app_id": "qaauto", "domain": "detnsw.win"}, "nodes": ["pw0992qaauto01.nsw.education", "pw0992qaauto02.nsw.education", "pw0991qapoc01.nsw.education", "pw0991qaauto01.nsw.education", "pw0991qaauto02.nsw.education", "pw0991qaauto03.nsw.education", "pw0991qaauto04.nsw.education", "pw0472qaauto01.nsw.education"]}, "qahq_test_uatdetnsw.win": {"service_name": "openmetrics_qahq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "qahq", "domain": "uatdetnsw.win"}, "nodes": ["tw0000qahqc001.apps.test.det.nsw.edu.au"]}, "qahq_prod_detnsw.win": {"service_name": "openmetrics_qahq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "qahq", "domain": "detnsw.win"}, "nodes": ["pw0000qahqc001.apps.det.nsw.edu.au"]}, "qalr_preprod_predetnsw.win": {"service_name": "openmetrics_qalr", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "adam mccallum", "op_window": "24x7", "os_manager": "windows", "app_id": "qalr", "domain": "predetnsw.win"}, "nodes": ["qw0992qalrg101.nsw.education", "qw0992qalrg102.nsw.education", "qw0991qalrg105.nsw.education", "qw0991qalrc101.nsw.education", "qw0991qalr0001.nsw.education", "qw0991qalrg101.nsw.education", "qw0991qalrg102.nsw.education", "qw0991qalrg103.nsw.education", "qw0991qalrg104.nsw.education", "qw0992qalrg103.nsw.education", "qw0992qalrg104.nsw.education", "qw0992qalrg105.nsw.education", "qw0991qalrg106.nsw.education", "qw0991qalrg107.nsw.education", "qw0992qalrg107.nsw.education", "qw0992qalrg108.nsw.education", "qw0992qalrg109.nsw.education", "qw0992qalrg110.nsw.education", "qw0991qalrg109.nsw.education", "qw0991qalrg110.nsw.education", "qw0991qalrg111.nsw.education", "qw0991qalrg112.nsw.education", "qw0991qalrg113.nsw.education", "qw0991qalrg114.nsw.education", "qw0991qalrg115.nsw.education", "qw0991qalrg116.nsw.education", "qw0991qalrg117.nsw.education", "qw0991qalrg120.nsw.education", "qw0991qalrg121.nsw.education", "qw0991qalrg123.nsw.education", "qw0991qalrg124.nsw.education", "qw0991qalrg125.nsw.education", "qw0991qalrg126.nsw.education", "qw0991qalrg127.nsw.education", "qw0991qalrg128.nsw.education", "qw0991qalrg129.nsw.education", "qw0991qalrg132.nsw.education", "qw0991qalrg133.nsw.education", "qw0991qalrg134.nsw.education", "qw0991qalrg135.nsw.education", "qw0991qalrg137.nsw.education", "qw0991qalrg138.nsw.education", "qw0991qalrg140.nsw.education", "qw0991qalrg118.nsw.education", "qw0991qalrg119.nsw.education", "qw0991qalrg122.nsw.education", "qw0991qalrg130.nsw.education", "qw0991qalrg136.nsw.education", "qw0991qalrg139.nsw.education", "qw0991qalrg131.nsw.education", "qw0992qalrg111.nsw.education", "qw0992qalrg113.nsw.education", "qw0992qalrg114.nsw.education", "qw0992qalrg115.nsw.education", "qw0992qalrg116.nsw.education", "qw0992qalrg117.nsw.education", "qw0992qalrg118.nsw.education", "qw0992qalrg119.nsw.education", "qw0992qalrg121.nsw.education", "qw0992qalrg122.nsw.education", "qw0992qalrg123.nsw.education", "qw0992qalrg124.nsw.education", "qw0992qalrg125.nsw.education", "qw0992qalrg126.nsw.education", "qw0992qalrg127.nsw.education", "qw0992qalrg129.nsw.education", "qw0992qalrg133.nsw.education", "qw0992qalrg134.nsw.education", "qw0992qalrg135.nsw.education", "qw0992qalrg139.nsw.education", "qw0992qalrg140.nsw.education", "qw0992qalrg112.nsw.education", "qw0992qalrg120.nsw.education", "qw0992qalrg130.nsw.education", "qw0992qalrg131.nsw.education", "qw0992qalrg132.nsw.education", "qw0992qalrg136.nsw.education", "qw0992qalrg137.nsw.education", "qw0992qalrg128.nsw.education", "qw0992qalrg138.nsw.education", "qw0991qalrg108.nsw.education", "qw0992qalrg106.nsw.education", "qw0991qalrg501.nsw.education", "qw0992qalrg501.nsw.education", "qw0992qalrg502.nsw.education", "qw0991qalrg502.nsw.education"]}, "qaneo_preprod_predetnsw.win": {"service_name": "openmetrics_qaneo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "quality assurance and test team", "op_window": "24x7", "os_manager": "windows", "app_id": "qaneo", "domain": "predetnsw.win"}, "nodes": ["qw0992qaneoi03.nsw.education", "qw0992qaneoc01.nsw.education", "qw0992qaneoi02.nsw.education", "qw0992qaneoi04.nsw.education", "qw0992qaneoi01.nsw.education", "qw0992qaneoi05.nsw.education", "qw0992qaneoi09.nsw.education", "qw0992qaneoi08.nsw.education", "qw0992qaneoi11.nsw.education", "qw0992qaneoi06.nsw.education", "qw0992qaneoi10.nsw.education", "qw0992qaneoi12.nsw.education", "qw0992qaneoi13.nsw.education", "qw0992qaneoi14.nsw.education", "qw0992qaneoi15.nsw.education", "qw0992qaneoi17.nsw.education", "qw0992qaneoi19.nsw.education", "qw0992qaneoi16.nsw.education", "qw0992qaneoi18.nsw.education", "qw0992qaneoi20.nsw.education", "qw0992qaneoi07.nsw.education"]}, "rdeapp_preprod_detnsw.win": {"service_name": "openmetrics_rdeapp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "rural and distance education team", "op_window": "12x5", "os_manager": "windows", "app_id": "rdeapp", "domain": "detnsw.win"}, "nodes": []}, "rdeapp_prod_detnsw.win": {"service_name": "openmetrics_rdeapp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "rural and distance education team", "op_window": "24x7", "os_manager": "windows", "app_id": "rdeapp", "domain": "detnsw.win"}, "nodes": ["pw0472rdeapp01.nsw.education"]}, "rdgs_dev_detnsw.win": {"service_name": "openmetrics_rdgs", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rdgs", "domain": "detnsw.win"}, "nodes": []}, "rdsaa_prod_rds": {"service_name": "openmetrics_rdsaa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "rdsaa", "domain": "rds.nsw.education"}, "nodes": ["pw0991rdsaag004.rds.nsw.education", "pw0991rdsaag003.rds.nsw.education", "pw0992rdsaab001.rds.nsw.education", "pw0991rdsaab002.rds.nsw.education", "pw0992rdsaaw001.rds.nsw.education", "pw0991rdsaaw002.rds.nsw.education", "pw0992rdsaag001.rds.nsw.education", "pw0992rdsaag002.rds.nsw.education", "pw0991rdsaag002.rds.nsw.education", "pw0991rdsaag001.rds.nsw.education", "pw0992rdsaab002.rds.nsw.education", "pw0991rdsaab001.rds.nsw.education", "pw0992rdsaaw002.rds.nsw.education", "pw0991rdsaaw001.rds.nsw.education"]}, "rds0_dev_devdetnsw.win": {"service_name": "openmetrics_rds0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds0", "domain": "devdetnsw.win"}, "nodes": []}, "rds047_dev_dev.uc.det.nsw.edu.au": {"service_name": "openmetrics_rds047", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds047", "domain": "dev.uc.det.nsw.edu.au"}, "nodes": []}, "rds047_test_unknown": {"service_name": "openmetrics_rds047", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds047", "domain": "unknown"}, "nodes": []}, "rds047_prod_unknown": {"service_name": "openmetrics_rds047", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds047", "domain": "unknown"}, "nodes": []}, "rds057_preprod_unknown": {"service_name": "openmetrics_rds057", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds057", "domain": "unknown"}, "nodes": []}, "rds077_preprod_predetnsw.win": {"service_name": "openmetrics_rds077", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "rds077", "domain": "predetnsw.win"}, "nodes": []}, "react_prod_detnsw.win": {"service_name": "openmetrics_react", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "distance and rural technology office", "op_window": "12x5", "os_manager": "unknown", "app_id": "react", "domain": "detnsw.win"}, "nodes": []}, "remdus_prod_detnsw.win": {"service_name": "openmetrics_remdus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "rem<PERSON>", "domain": "detnsw.win"}, "nodes": ["pw0000mypldm01.svcs.det.nsw.edu.au"]}, "resolv_preprod_predetnsw.win": {"service_name": "openmetrics_resolv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jason kennedy-<PERSON><PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "resolv", "domain": "predetnsw.win"}, "nodes": ["qw0992resolv02.pre.nsw.education"]}, "resolv_test_uatdetnsw.win": {"service_name": "openmetrics_resolv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "jason kennedy-<PERSON><PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "resolv", "domain": "uatdetnsw.win"}, "nodes": []}, "resolv_prod_detnsw.win": {"service_name": "openmetrics_resolv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jason kennedy-<PERSON><PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "resolv", "domain": "detnsw.win"}, "nodes": ["pw0991resolv02.nsw.education"]}, "rest_shared_workgroup": {"service_name": "openmetrics_rest", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "yoshi hem<PERSON>", "op_window": "24x7", "os_manager": "penguins", "app_id": "rest"}, "nodes": ["pl0991rest0001.nsw.education", "pl0992rest0001.nsw.education"]}, "rredp_prod_detnsw.win": {"service_name": "openmetrics_rredp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "will twemlow", "op_window": "24x7", "os_manager": "windows", "app_id": "rredp", "domain": "detnsw.win"}, "nodes": ["pw0992rredp001.nsw.education", "pw0992rredp002.nsw.education"]}, "rsfm_prod_detnsw.win": {"service_name": "openmetrics_rsfm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "kim flack", "op_window": "24x7", "os_manager": "unknown", "app_id": "rsfm", "domain": "detnsw.win"}, "nodes": ["pw0992rsfm0001.nsw.education"]}, "rwdc_prod_idm": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "idm.win"}, "nodes": ["pw0991rwdc0290.nsw.education", "pw0992rwdc0290.nsw.education"]}, "rwdc_prod_detnsw.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "detnsw.win"}, "nodes": ["pw0991rwdc0101.ad.det.nsw.edu.au", "pw0991rwdc0109.ad.det.nsw.edu.au", "pw0991rwdc0114.ad.det.nsw.edu.au", "pw0991rwdc0120.ad.det.nsw.edu.au", "pw0991rwdc0121.ad.det.nsw.edu.au", "pw0991rwdc0122.ad.det.nsw.edu.au", "pw0991rwdc0123.ad.det.nsw.edu.au", "pw0991rwdc0132.ad.det.nsw.edu.au", "pw0991rwdc0133.ad.det.nsw.edu.au", "pw0991rwdc0134.ad.det.nsw.edu.au", "pw0991rwdc0135.ad.det.nsw.edu.au", "pw0991rwdc0136.ad.det.nsw.edu.au", "pw0991rwdc0137.ad.det.nsw.edu.au", "pw0991rwdc0138.ad.det.nsw.edu.au", "pw0991rwdc0139.ad.det.nsw.edu.au", "pw0991rwdc0140.ad.det.nsw.edu.au", "pw0991rwdc0141.ad.det.nsw.edu.au", "pw0991rwdc0142.ad.det.nsw.edu.au", "pw0991rwdc0143.ad.det.nsw.edu.au", "pw0991rwdc0191.nsw.education", "pw0991rwdc0192.nsw.education", "pw0991rwdc0193.nsw.education", "pw0991rwdc0194.nsw.education", "pw0991rwdc0195.nsw.education", "pw0992rwdc0101.ad.det.nsw.edu.au", "pw0992rwdc0109.ad.det.nsw.edu.au", "pw0992rwdc0113.ad.det.nsw.edu.au", "pw0992rwdc0114.ad.det.nsw.edu.au", "pw0992rwdc0120.ad.det.nsw.edu.au", "pw0992rwdc0121.ad.det.nsw.edu.au", "pw0992rwdc0122.ad.det.nsw.edu.au", "pw0992rwdc0123.ad.det.nsw.edu.au", "pw0992rwdc0125.ad.det.nsw.edu.au", "pw0992rwdc0132.ad.det.nsw.edu.au", "pw0992rwdc0133.ad.det.nsw.edu.au", "pw0992rwdc0134.ad.det.nsw.edu.au", "pw0992rwdc0135.ad.det.nsw.edu.au", "pw0992rwdc0136.ad.det.nsw.edu.au", "pw0992rwdc0137.ad.det.nsw.edu.au", "pw0992rwdc0138.ad.det.nsw.edu.au", "pw0992rwdc0139.ad.det.nsw.edu.au", "pw0992rwdc0140.ad.det.nsw.edu.au", "pw0992rwdc0141.ad.det.nsw.edu.au", "pw0992rwdc0142.ad.det.nsw.edu.au", "pw0992rwdc0143.ad.det.nsw.edu.au", "pw0992rwdc0191.nsw.education", "pw0992rwdc0192.nsw.education", "pw0992rwdc0193.nsw.education", "pw0992rwdc0194.nsw.education", "pw0992rwdc0195.nsw.education"]}, "rwdc_prod_ext.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "ext.win"}, "nodes": ["pw0991rwdc0301.svcs.det.nsw.edu.au", "pw0991rwdc0302.svcs.det.nsw.edu.au", "pw0992rwdc0301.svcs.det.nsw.edu.au", "pw0992rwdc0302.svcs.det.nsw.edu.au"]}, "rwdc_prod_idm.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc12", "domain": "idm.win"}, "nodes": ["pw0991rwdc0202.svcs.det.nsw.edu.au", "pw0991rwdc0203.svcs.det.nsw.edu.au", "pw0991rwdc0204.svcs.det.nsw.edu.au", "pw0991rwdc0290.nsw.education", "pw0992rwdc0202.svcs.det.nsw.edu.au", "pw0992rwdc0203.svcs.det.nsw.edu.au", "pw0992rwdc0204.svcs.det.nsw.edu.au", "pw0992rwdc0290.nsw.education"]}, "rwdc_prod_uc": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc12", "domain": "uc"}, "nodes": ["pw0991rwdc0503.messaging.det.nsw.edu.au", "pw0991rwdc0505.nsw.education", "pw0992rwdc0503.messaging.det.nsw.edu.au", "pw0992rwdc0505.nsw.education"]}, "rwdc_prod_priv": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "priv.win"}, "nodes": ["pw0991rwdc0702.ad.det.nsw.edu.au", "pw0992rwdc0702.ad.det.nsw.edu.au"]}, "rwdc_preprod_predetnsw.win": {"service_name": "openmetrics_rwdc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "rwdc", "domain": "predetnsw.win"}, "nodes": ["qw0991rwdc0101.ad.pre.det.nsw.edu.au", "qw0991rwdc0102.ad.pre.det.nsw.edu.au", "qw0991rwdc0103.ad.pre.det.nsw.edu.au", "qw0991rwdc0104.ad.pre.det.nsw.edu.au", "qw0992rwdc0101.ad.pre.det.nsw.edu.au", "qw0992rwdc0102.ad.pre.det.nsw.edu.au", "qw0992rwdc0103.ad.pre.det.nsw.edu.au", "qw0992rwdc0104.ad.pre.det.nsw.edu.au"]}, "saclus_dev_workgroup": {"service_name": "openmetrics_saclus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "scott hamilton", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "saclus"}, "nodes": ["dl0991saclus03.nsw.education", "dl0991saclus01.nsw.education", "dl0991saclus02.nsw.education"]}, "salm_preprod_detnsw.win": {"service_name": "openmetrics_salm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "salm", "op_window": "24x5", "os_manager": "windows", "app_id": "salm", "domain": "detnsw.win"}, "nodes": ["qw0472salmbs01.nsw.education", "qw0472salmbsz1.nsw.education", "ew0472salmbs01.nsw.education"]}, "salm_shared_detnsw.win": {"service_name": "openmetrics_salm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "salm", "op_window": "24x7", "os_manager": "windows", "app_id": "salm", "domain": "detnsw.win"}, "nodes": ["pw0472salm0001.nsw.education"]}, "salm_test_uatdetnsw.win": {"service_name": "openmetrics_salm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "salm", "op_window": "12x5", "os_manager": "windows", "app_id": "salm", "domain": "uatdetnsw.win"}, "nodes": ["tw0472salm0001.nsw.education", "tw0472salm0002.nsw.education", "tw0472salmbs01.test.nsw.education", "tw0472salmbsa1.test.nsw.education"]}, "salm_prod_detnsw.win": {"service_name": "openmetrics_salm", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "salm", "op_window": "24x5", "os_manager": "windows", "app_id": "salm", "domain": "detnsw.win"}, "nodes": ["pw0472salm0002.nsw.education", "qw0472salm0001.nsw.education", "ew0472salm0001.nsw.education", "qw0472salm0002.nsw.education", "pw0472salmbs01.nsw.education"]}, "sap114_test_uatdetnsw.win": {"service_name": "openmetrics_sap114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sap114", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sap114h1.nsw.education"]}, "sapa_prod_detnsw.win": {"service_name": "openmetrics_sapa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sapa", "domain": "detnsw.win"}, "nodes": ["pw0991sapaps1.apps.det.nsw.edu.au", "pw0991sapdba1.dbs.det.nsw.edu.au"]}, "sapcc_dev_workgroup": {"service_name": "openmetrics_sapcc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapcc"}, "nodes": ["dl0992sapcc202.nsw.education", "dl0992sapcc201.nsw.education", "dl0991sapcc102.nsw.education", "dl0991sapcc101.nsw.education"]}, "sapcc_preprod_workgroup": {"service_name": "openmetrics_sapcc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapcc"}, "nodes": ["ql0991sapcc101.nsw.education", "ql0991sapcc102.nsw.education", "ql0992sapcc201.nsw.education", "ql0992sapcc202.nsw.education"]}, "sapcc_prod_workgroup": {"service_name": "openmetrics_sapcc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapcc"}, "nodes": ["pl0992sapcc202.nsw.education", "pl0992sapcc201.nsw.education", "pl0991sapcc101.nsw.education", "pl0991sapcc102.nsw.education"]}, "sapd_dev_devdetnsw.win": {"service_name": "openmetrics_sapd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sapd", "domain": "devdetnsw.win"}, "nodes": ["cw0992smssa01.apps.dev.det.nsw.edu.au", "cw0992sapdb01.dbs.dev.det.nsw.edu.au", "cw0992sapdb03.dbs.dev.det.nsw.edu.au", "cw0992sasua01.apps.dev.det.nsw.edu.au", "cw0992sapdb02.dbs.dev.det.nsw.edu.au", "cw0992smsba01.apps.dev.det.nsw.edu.au", "dw0991smdsa01.apps.dev.det.nsw.edu.au"]}, "sapd_prod_detnsw.win": {"service_name": "openmetrics_sapd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sapd", "domain": "detnsw.win"}, "nodes": ["pw0992sapdba1.dbs.det.nsw.edu.au", "pw0992sapapu1.apps.det.nsw.edu.au", "pw0992sapdbt1.dbs.det.nsw.edu.au"]}, "sapd52_prod_detnsw.win": {"service_name": "openmetrics_sapd52", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sapd52", "domain": "detnsw.win"}, "nodes": ["pw0991sapdbt1.dbs.det.nsw.edu.au"]}, "sapd54_dev_devdetnsw.win": {"service_name": "openmetrics_sapd54", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sapd54", "domain": "devdetnsw.win"}, "nodes": ["cw0991sapdb01.dbs.dev.det.nsw.edu.au", "cw0991sapdb02.dbs.dev.det.nsw.edu.au", "dw0991sapdb01.dbs.dev.det.nsw.edu.au", "dw0991sapdb02.dbs.dev.det.nsw.edu.au"]}, "sapia_preprod_detnsw.win": {"service_name": "openmetrics_sapia", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-es / sap lvl2 finance", "op_window": "on_demand", "os_manager": "windows", "app_id": "sapia", "domain": "detnsw.win"}, "nodes": ["qw0472sapia001.pre.nsw.education", "qw0472sapia002.pre.nsw.education", "qw0472sapia003.pre.nsw.education"]}, "sapia_test_uatdetnsw.win": {"service_name": "openmetrics_sapia", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-es / sap lvl2 finance", "op_window": "on_demand", "os_manager": "windows", "app_id": "sapia", "domain": "uatdetnsw.win"}, "nodes": ["tw0472sapia001.test.nsw.education", "tw0472sapia002.test.nsw.education", "tw0472sqs006n1.test.nsw.education"]}, "sapia_prod_detnsw.win": {"service_name": "openmetrics_sapia", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-es / sap lvl2 finance", "op_window": "24x7", "os_manager": "windows", "app_id": "sapia", "domain": "detnsw.win"}, "nodes": ["pw0472sapia006.nsw.education", "pw0472sapia002.nsw.education", "pw0472sapia005.nsw.education", "pw0472sapia004.nsw.education", "pw0472sapia001.nsw.education", "pw0472sapia003.nsw.education", "pw0472sapia007.nsw.education"]}, "sapiq_dev_workgroup": {"service_name": "openmetrics_sapiq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapiq"}, "nodes": ["dl0992sapiqd01.nsw.education", "dl0991sapiqd01.nsw.education", "dl0992sapiqd02.nsw.education", "du-siq-vs0.apps.dev.det.nsw.edu.au"]}, "sapwd_dev_workgroup": {"service_name": "openmetrics_sapwd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapwd"}, "nodes": ["dl0991sapwdc01.nsw.education", "dl0991sapwdc02.nsw.education", "dl0992sapwdc01.nsw.education", "dl0992sapwdc02.nsw.education"]}, "solman_dev_workgroup": {"service_name": "openmetrics_solman", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "solman"}, "nodes": ["du-mdj-as0.apps.dev.det.nsw.edu.au", "du-mds-as0.apps.dev.det.nsw.edu.au", "du-msj-as0.apps.dev.det.nsw.edu.au", "du-mss-as0.apps.dev.det.nsw.edu.au"]}, "solman_prod_workgroup": {"service_name": "openmetrics_solman", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "david weeda", "op_window": "24x7", "os_manager": "penguins", "app_id": "solman"}, "nodes": ["pu-mpj-as1.apps.det.nsw.edu.au", "pu-mpj-as0.apps.det.nsw.edu.au", "pu-mps-as0.apps.det.nsw.edu.au", "pu-mps-as1.apps.det.nsw.edu.au"]}, "sas114_preprod_detnsw.win": {"service_name": "openmetrics_sas114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sas114", "domain": "detnsw.win"}, "nodes": ["qw0992sas114h1.nsw.education"]}, "sas114_prod_detnsw.win": {"service_name": "openmetrics_sas114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sas114", "domain": "detnsw.win"}, "nodes": ["pw0991sas114h1.nsw.education"]}, "sas115_preprod_detnsw.win": {"service_name": "openmetrics_sas115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sas115", "domain": "detnsw.win"}, "nodes": ["qw0992sas115h1.nsw.education"]}, "sas115_prod_detnsw.win": {"service_name": "openmetrics_sas115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sas115", "domain": "detnsw.win"}, "nodes": ["pw0991sas115h1.nsw.education"]}, "satc_prod_unset": {"service_name": "openmetrics_satc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "unset", "op_window": "unset", "os_manager": "penguins", "app_id": "satc", "domain": "unset"}, "nodes": ["pl0992satc0001.nsw.education", "pl0991satc0001.nsw.education"]}, "satc_prod_workgroup": {"service_name": "openmetrics_satc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "satc"}, "nodes": ["pl0992satc0005.nsw.education"]}, "sats_prod_workgroup": {"service_name": "openmetrics_sats", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sats"}, "nodes": ["pu0991sats0001.hbm.det.nsw.edu.au"]}, "scne_prod_detnsw.win": {"service_name": "openmetrics_scne", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scne", "domain": "detnsw.win"}, "nodes": ["pw0991scnet001.netmon.det.nsw.edu.au"]}, "scoma_dev_devdetnsw.win": {"service_name": "openmetrics_scoma", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "itd-is / ip - windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scoma", "domain": "devdetnsw.win"}, "nodes": ["dw0992scomag01.dev.nsw.education"]}, "scomd_shared_detnsw.win": {"service_name": "openmetrics_scomd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomd", "domain": "detnsw.win"}, "nodes": ["pw0991scomdm01.nsw.education", "pw0992scomdm01.nsw.education", "pw0992scomdm02.nsw.education", "pw0991scomdm02.nsw.education"]}, "scomd_shared_devdetnsw.win": {"service_name": "openmetrics_scomd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomd", "domain": "devdetnsw.win"}, "nodes": ["dw0992scomdg01.dev.nsw.education"]}, "scomd_shared_predetnsw.win": {"service_name": "openmetrics_scomd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomd", "domain": "predetnsw.win"}, "nodes": ["qw0992scomdg01.pre.nsw.education"]}, "scomd_shared_uatdetnsw.win": {"service_name": "openmetrics_scomd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomd", "domain": "uatdetnsw.win"}, "nodes": ["tw0992scomdg01.test.nsw.education"]}, "scome_prod_detnsw.win": {"service_name": "openmetrics_scome", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "kristen johnson", "op_window": "24x7", "os_manager": "windows", "app_id": "scome", "domain": "detnsw.win"}, "nodes": ["pw0991scomem02.nsw.education", "pw0991scomem07.nsw.education", "pw0991scomem03.nsw.education", "pw0991scomem04.nsw.education", "pw0991scomem05.nsw.education", "pw0991scomem06.nsw.education", "pw0991scomem08.nsw.education", "pw0991scomem09.nsw.education", "pw0991scomem10.nsw.education", "pw0991scomem01.nsw.education"]}, "scomq_preprod_detnsw.win": {"service_name": "openmetrics_scomq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / ip - windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomq", "domain": "detnsw.win"}, "nodes": ["qw0991scomqm01.pre.nsw.education", "qw0992scomqm01.pre.nsw.education"]}, "scomq_shared_devdetnsw.win": {"service_name": "openmetrics_scomq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomq", "domain": "devdetnsw.win"}, "nodes": ["dw0992scomqg01.dev.nsw.education"]}, "scomq_shared_predetnsw.win": {"service_name": "openmetrics_scomq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomq", "domain": "predetnsw.win"}, "nodes": ["qw0992scomqg01.pre.nsw.education"]}, "scomq_shared_uatdetnsw.win": {"service_name": "openmetrics_scomq", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scomq", "domain": "uatdetnsw.win"}, "nodes": ["tw0992scomqg01.test.nsw.education"]}, "scord_preprod_predetnsw.win": {"service_name": "openmetrics_scord", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scord", "domain": "predetnsw.win"}, "nodes": ["qw0992scordms1.pre.nsw.education", "qw0992scordws1.pre.nsw.education"]}, "scord_shared_detnsw.win": {"service_name": "openmetrics_scord", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scord", "domain": "detnsw.win"}, "nodes": ["qw0992scordrd1.pre.nsw.education", "pw0992scordrs1.nsw.education", "pw0992scordrd1.nsw.education", "pw0991scordrs1.nsw.education", "pw0991scordrs2.nsw.education", "pw0992scordrs2.nsw.education"]}, "scord_shared_predetnsw.win": {"service_name": "openmetrics_scord", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scord", "domain": "predetnsw.win"}, "nodes": ["qw0992scordrs1.pre.nsw.education", "qw0991scordrs1.pre.nsw.education"]}, "scord_prod_detnsw.win": {"service_name": "openmetrics_scord", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "scord", "domain": "detnsw.win"}, "nodes": ["pw0992scordms1.nsw.education", "pw0991scordws1.nsw.education", "pw0992scordws1.nsw.education"]}, "score_preprod_predetnsw.win": {"service_name": "openmetrics_score", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "@it infraserv ddac - desktop platform - config mgr", "op_window": "24x7", "os_manager": "windows", "app_id": "score", "domain": "predetnsw.win"}, "nodes": ["qw0992scorems1.pre.nsw.education", "qw0992scorers1.pre.nsw.education", "qw0991scorers1.pre.nsw.education", "qw0992scorews1.pre.nsw.education", "qw0992scorerd1.pre.nsw.education"]}, "score_prod_detnsw.win": {"service_name": "openmetrics_score", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "@it infraserv ddac - desktop platform - config mgr", "op_window": "24x7", "os_manager": "windows", "app_id": "score", "domain": "detnsw.win"}, "nodes": ["pw0992scorers1.nsw.education", "pw0991scorers1.nsw.education", "pw0992scorews1.nsw.education", "pw0992scorerd1.nsw.education", "pw0992scorems1.nsw.education"]}, "sdetst_dev_detnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "sd&e team", "op_window": "12x5", "os_manager": "windows", "app_id": "sdetst", "domain": "detnsw.win"}, "nodes": ["dw0992sdetst02.nsw.education"]}, "sdetst_dev_devdetnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "sd&e team", "op_window": "12x5", "os_manager": "windows", "app_id": "sdetst", "domain": "devdetnsw.win"}, "nodes": ["dw0992sdetst01.dev.nsw.education", "dw0472sdetst01.dev.nsw.education"]}, "sdetst_preprod_predetnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "sd&e team", "op_window": "12x5", "os_manager": "windows", "app_id": "sdetst", "domain": "predetnsw.win"}, "nodes": ["qw0992sdetst01.pre.nsw.education"]}, "sdetst_test_detnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "sd&e team", "op_window": "12x5", "os_manager": "windows", "app_id": "sdetst", "domain": "detnsw.win"}, "nodes": ["tw0472sdetst01.nsw.education", "tw0992sdetst01.nsw.education"]}, "sdetst_test_uatdetnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "sd&e team", "op_window": "on_demand", "os_manager": "windows", "app_id": "sdetst", "domain": "uatdetnsw.win"}, "nodes": ["tw0475sdetst01.nsw.education", "tw0992sdetst02.test.nsw.education", "tw0472sdetst02.test.nsw.education"]}, "sdetst_prod_detnsw.win": {"service_name": "openmetrics_sdetst", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "sd&e team", "op_window": "12x5", "os_manager": "windows", "app_id": "sdetst", "domain": "detnsw.win"}, "nodes": ["pw0472sdetst01.nsw.education", "pw0991sdetst01.nsw.education"]}, "sedrms_dev_devdetnsw.win": {"service_name": "openmetrics_sedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "david weeda", "op_window": "24x7", "os_manager": "windows", "app_id": "sedrms", "domain": "devdetnsw.win"}, "nodes": ["cw0992sedrms01.nsw.education", "cw0992sedrms02.nsw.education", "dw0992sedrms01.nsw.education", "dw0992sedrms02.nsw.education"]}, "sedrms_preprod_detnsw.win": {"service_name": "openmetrics_sedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "david weeda", "op_window": "24x7", "os_manager": "windows", "app_id": "sedrms", "domain": "detnsw.win"}, "nodes": ["qw0991sedrms01.nsw.education", "qw0991sedrms02.nsw.education"]}, "sedrms_test_uatdetnsw.win": {"service_name": "openmetrics_sedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "david weeda", "op_window": "24x7", "os_manager": "windows", "app_id": "sedrms", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sedrms01.nsw.education", "tw0991sedrms02.nsw.education"]}, "sedrms_prod_detnsw.win": {"service_name": "openmetrics_sedrms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "david weeda", "op_window": "24x7", "os_manager": "windows", "app_id": "sedrms", "domain": "detnsw.win"}, "nodes": ["pw0991sedrms01.nsw.education", "pw0991sedrms02.nsw.education"]}, "seismo_prod_workgroup": {"service_name": "openmetrics_seismo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "seismo"}, "nodes": ["pl0991seismo01.nsw.education"]}, "sems_dev_workgroup": {"service_name": "openmetrics_sems", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "semmer sultan", "op_window": "12x5", "os_manager": "penguins", "app_id": "sems"}, "nodes": ["dl0991sems0001.nsw.education", "dl0991sems0002.nsw.education"]}, "serg_prod_detnsw.win": {"service_name": "openmetrics_serg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "fieldservices-oncall", "op_window": "24x7", "os_manager": "windows", "app_id": "serg", "domain": "detnsw.win"}, "nodes": ["pw0472serg0001.nsw.education"]}, "sfile_prod_detnsw.win": {"service_name": "openmetrics_sfile", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd - is - ddac - desktop platform - edge operations", "op_window": "12x5", "os_manager": "windows", "app_id": "sfile", "domain": "detnsw.win"}, "nodes": ["pw0992sfile001.nsw.education"]}, "sibi_test_uatdetnsw.win": {"service_name": "openmetrics_sibi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "simon ivers", "op_window": "24x7", "os_manager": "windows", "app_id": "sibi", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sibii001.nsw.education", "tw0992sibia001.nsw.education"]}, "sibi_prod_detnsw.win": {"service_name": "openmetrics_sibi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "simon ivers", "op_window": "24x7", "os_manager": "windows", "app_id": "sibi", "domain": "detnsw.win"}, "nodes": ["pw0992sibii001.nsw.education", "pw0991sibii001.nsw.education", "pw0992sibia001.nsw.education", "pw0991sibia001.nsw.education"]}, "siobi_preprod_predetnsw.win": {"service_name": "openmetrics_siobi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "si<PERSON>i", "domain": "predetnsw.win"}, "nodes": ["qw0992siobi001.nsw.education"]}, "siobi_test_uatdetnsw.win": {"service_name": "openmetrics_siobi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "mohan thakare", "op_window": "12x5", "os_manager": "windows", "app_id": "si<PERSON>i", "domain": "uatdetnsw.win"}, "nodes": ["tw0992siobi001.nsw.education"]}, "siobi_prod_detnsw.win": {"service_name": "openmetrics_siobi", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "si<PERSON>i", "domain": "detnsw.win"}, "nodes": ["pw0992siobi001.nsw.education"]}, "siop_preprod_predetnsw.win": {"service_name": "openmetrics_siop", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "siop", "domain": "predetnsw.win"}, "nodes": [{"name": "qw0991siop0004.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0991siop0002.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0992siop0002.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0992siop0001.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0991siop0001.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0992siop0003.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0991siop0003.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "qw0992siop0004.nsw.education", "meta": {"role": "FM WEB"}}]}, "siop_shared_detnsw.win": {"service_name": "openmetrics_siop", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "siop", "domain": "detnsw.win"}, "nodes": ["pw0992siop0005.nsw.education"]}, "siop_test_uatdetnsw.win": {"service_name": "openmetrics_siop", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "siop", "domain": "uatdetnsw.win"}, "nodes": [{"name": "tw0991siop0004.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "tw0991siop0002.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "tw0991siop0003.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "tw0991siop0001.nsw.education", "meta": {"role": "FM WEB"}}]}, "siop_prod_detnsw.win": {"service_name": "openmetrics_siop", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "mohan thakare", "op_window": "24x7", "os_manager": "windows", "app_id": "siop", "domain": "detnsw.win"}, "nodes": [{"name": "pw0991siop0003.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0991siop0004.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0991siop0002.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0992siop0001.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0992siop0004.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0992siop0003.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0991siop0001.nsw.education", "meta": {"role": "FM WEB"}}, {"name": "pw0992siop0002.nsw.education", "meta": {"role": "FM WEB"}}]}, "sip114_test_uatdetnsw.win": {"service_name": "openmetrics_sip114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sip114", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sip114h1.nsw.education"]}, "sis115_preprod_detnsw.win": {"service_name": "openmetrics_sis115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sis115", "domain": "detnsw.win"}, "nodes": ["qw0992sis115h1.nsw.education"]}, "sis115_prod_detnsw.win": {"service_name": "openmetrics_sis115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sis115", "domain": "detnsw.win"}, "nodes": ["pw0991sis115h1.nsw.education"]}, "slam_prod_detnsw.win": {"service_name": "openmetrics_slam", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "slam", "domain": "detnsw.win"}, "nodes": ["pw0992slam0101.ad.det.nsw.edu.au"]}, "smaw_shared_detnsw.win": {"service_name": "openmetrics_smaw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "student management", "op_window": "24x7", "os_manager": "windows", "app_id": "smaw", "domain": "detnsw.win"}, "nodes": ["pw0991smaw0001.nsw.education"]}, "smbps_prod_detnsw.win": {"service_name": "openmetrics_smbps", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "student management support", "op_window": "24x7", "os_manager": "windows", "app_id": "smbps", "domain": "detnsw.win"}, "nodes": ["pw0992smbps001.nsw.education", "pw0991smbps001.nsw.education", "pw0992smbps002.nsw.education"]}, "smcms_prod_detnsw.win": {"service_name": "openmetrics_smcms", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "student management support", "op_window": "24x7", "os_manager": "windows", "app_id": "smcms", "domain": "detnsw.win"}, "nodes": ["pw0991smcms001.nsw.education"]}, "smqa_preprod_predetnsw.win": {"service_name": "openmetrics_smqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "student management", "op_window": "24x7", "os_manager": "windows", "app_id": "smqa", "domain": "predetnsw.win"}, "nodes": ["qw0991smqas001.nsw.education", "qw0992smqas002.nsw.education", "qw0992smqas001.nsw.education", "qw0991smqas002.nsw.education"]}, "smqa_prod_detnsw.win": {"service_name": "openmetrics_smqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "student management", "op_window": "24x7", "os_manager": "windows", "app_id": "smqa", "domain": "detnsw.win"}, "nodes": ["pw0991smqas001.nsw.education", "pw0991smqas002.nsw.education", "pw0992smqas002.nsw.education", "pw0992smqas001.nsw.education"]}, "smtp4a_prod_workgroup": {"service_name": "openmetrics_smtp4a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ip-linux", "op_window": "24x7", "os_manager": "penguins", "app_id": "smtp4a"}, "nodes": ["pl0991imxs0001.nsw.education", "pl0992imxs0001.nsw.education", "pl0991imxs0002.nsw.education", "pl0992imxs0002.nsw.education", "pl0992imxr0002.nsw.education", "pl0992imxr0001.nsw.education", "pl0991imxr0001.nsw.education", "pl0991imxr0002.nsw.education", "pl0992imxr0003.nsw.education"]}, "smtp4o_prod_workgroup": {"service_name": "openmetrics_smtp4o", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ip-linux", "op_window": "24x7", "os_manager": "penguins", "app_id": "smtp4o"}, "nodes": ["pl0991imxs0003.nsw.education", "pl0991imxs0004.nsw.education", "pl0992imxs0003.nsw.education", "pl0992imxs0004.nsw.education"]}, "soapts_prod_detnsw.win": {"service_name": "openmetrics_soapts", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "soapts", "domain": "detnsw.win"}, "nodes": ["pw0000ruswfs01.file.det.nsw.edu.au", "pw0000metsfs01.file.det.nsw.edu.au", "pw0000regsfs01.file.det.nsw.edu.au", "pw0000rurnfs01.file.det.nsw.edu.au", "pw0000regnfs01.file.det.nsw.edu.au", "pw0000metnfs01.file.det.nsw.edu.au"]}, "spgps_prod_detnsw.win": {"service_name": "openmetrics_spgps", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "windows", "app_id": "spgps", "domain": "detnsw.win"}, "nodes": ["pw0472spgps001.nsw.education"]}, "splnk_dmz_workgroup": {"service_name": "openmetrics_splnk", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dmz", "support_team": "itd linux", "op_window": "24x7", "os_manager": "penguins", "app_id": "splnk"}, "nodes": ["pl0475splnk001.nsw.education", "pl0475splnk002.nsw.education"]}, "splnk_shared_workgroup": {"service_name": "openmetrics_splnk", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "itd linux", "op_window": "12x5", "os_manager": "penguins", "app_id": "splnk"}, "nodes": ["pl0991splnk080.nsw.education", "pl0992splnk080.nsw.education"]}, "splnk_prod_unset": {"service_name": "openmetrics_splnk", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "unset", "op_window": "unset", "os_manager": "penguins", "app_id": "splnk", "domain": "unset"}, "nodes": ["pl0992splf0001.nsw.education", "pl0991splf0001.nsw.education"]}, "splnk_prod_workgroup": {"service_name": "openmetrics_splnk", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd linux", "op_window": "24x7", "os_manager": "penguins", "app_id": "splnk"}, "nodes": ["pl0991splf0002.nsw.education", "pl0991splnk099.nsw.education"]}, "splnkc_prod_unset": {"service_name": "openmetrics_splnkc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "12x5", "os_manager": "penguins", "app_id": "splnkc", "domain": "unset"}, "nodes": ["pl0991splnkc003.nsw.education", "pl0991splnkc004.nsw.education", "pl0992splnkc01.nsw.education", "pl0992splnkc02.nsw.education", "pl0991splnkc002.nsw.education", "pl0992splnkc03.nsw.education"]}, "splt_prod_workgroup": {"service_name": "openmetrics_splt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "splt"}, "nodes": ["pu0992splt0001.hbm.det.nsw.edu.au"]}, "spsr_prod_detnsw.win": {"service_name": "openmetrics_spsr", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "spsr", "domain": "detnsw.win"}, "nodes": ["pw0000spaph001.apps.det.nsw.edu.au"]}, "smft1a_dev_unset": {"service_name": "openmetrics_smft1a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "smft1a", "cir_app_id": "smft1a", "domain": "unset"}, "nodes": ["tl0991smft9001.dmz.det.nsw.edu.au", "tl0992smft9001.dmz.det.nsw.edu.au"]}, "smft1a_prod_unset": {"service_name": "openmetrics_smft1a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "smft1a", "cir_app_id": "smft1a", "domain": "unset"}, "nodes": ["pl0991smft9001.dmz.det.nsw.edu.au", "pl0992smft9001.dmz.det.nsw.edu.au"]}, "sq7_bw_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "bw"}, "nodes": ["ql0992sq7va00.apps.pre.det.nsw.edu.au"]}, "sq7_bwportal_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "bwportal"}, "nodes": ["ql0992sq7ta00.apps.pre.det.nsw.edu.au"]}, "sq7_centralservices_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "centralservices"}, "nodes": ["ql0992sq7cs01.apps.pre.det.nsw.edu.au", "ql0992sq7cs02.apps.pre.det.nsw.edu.au", "ql0992sq7cs03.apps.pre.det.nsw.edu.au"]}, "sq7_erp_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "erp"}, "nodes": ["ql0992sq7na00.apps.pre.det.nsw.edu.au", "ql0992sq7na01.apps.pre.det.nsw.edu.au", "ql0992sq7nb00.apps.pre.det.nsw.edu.au", "ql0992sq7nb01.apps.pre.det.nsw.edu.au", "ql0992sq7nb02.apps.pre.det.nsw.edu.au", "ql0992sq7nb03.apps.pre.det.nsw.edu.au", "ql0992sq7nb04.apps.pre.det.nsw.edu.au", "ql0992sq7nb05.apps.pre.det.nsw.edu.au", "ql0992sq7nb06.apps.pre.det.nsw.edu.au", "ql0992sq7nb07.apps.pre.det.nsw.edu.au", "ql0992sq7nb08.apps.pre.det.nsw.edu.au", "ql0992sq7nb09.apps.pre.det.nsw.edu.au", "ql0992sq7nb10.apps.pre.det.nsw.edu.au", "ql0992sq7nb11.apps.pre.det.nsw.edu.au", "ql0992sq7nb12.apps.pre.det.nsw.edu.au", "ql0992sq7nb13.apps.pre.det.nsw.edu.au"]}, "sq7_fiori_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "fiori"}, "nodes": ["ql0992sq7ga00.apps.pre.det.nsw.edu.au"]}, "sq7_grc_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "grc"}, "nodes": ["ql0992sq7ua00.apps.pre.det.nsw.edu.au"]}, "sq7_po_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "po"}, "nodes": ["ql0992sq7oa00.apps.pre.det.nsw.edu.au"]}, "sq7_portal_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "portal"}, "nodes": ["ql0992sq7fa00.apps.pre.det.nsw.edu.au"]}, "sq7_srm_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "srm"}, "nodes": ["ql0992sq7pa00.apps.pre.det.nsw.edu.au"]}, "sq7_webdispatcher_workgroup": {"service_name": "openmetrics_sq7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq7", "host_group_id": "webdispatcher"}, "nodes": ["ql0992sq7wd01.apps.pre.det.nsw.edu.au", "ql0992sq7wd02.apps.pre.det.nsw.edu.au"]}, "sq8_centralservices_workgroup": {"service_name": "openmetrics_sq8", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq8", "host_group_id": "centralservices"}, "nodes": ["ql0992sq8cs01.apps.pre.det.nsw.edu.au", "ql0992sq8cs02.apps.pre.det.nsw.edu.au", "ql0992sq8cs03.apps.pre.det.nsw.edu.au", "ql0992sq8cs04.apps.pre.det.nsw.edu.au"]}, "sq8_erp_workgroup": {"service_name": "openmetrics_sq8", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq8", "host_group_id": "erp"}, "nodes": ["ql0992sq8na00.apps.pre.det.nsw.edu.au", "ql0992sq8na01.apps.pre.det.nsw.edu.au", "ql0992sq8na02.apps.pre.det.nsw.edu.au", "ql0992sq8na03.apps.pre.det.nsw.edu.au", "ql0992sq8na04.apps.pre.det.nsw.edu.au", "ql0992sq8na05.apps.pre.det.nsw.edu.au", "ql0992sq8na06.apps.pre.det.nsw.edu.au", "ql0992sq8na07.apps.pre.det.nsw.edu.au", "ql0992sq8na08.apps.pre.det.nsw.edu.au", "ql0992sq8na09.apps.pre.det.nsw.edu.au", "ql0992sq8na10.apps.pre.det.nsw.edu.au", "ql0992sq8na11.apps.pre.det.nsw.edu.au", "ql0992sq8na12.apps.pre.det.nsw.edu.au", "ql0992sq8na13.apps.pre.det.nsw.edu.au", "ql0992sq8na14.apps.pre.det.nsw.edu.au", "ql0992sq8na15.apps.pre.det.nsw.edu.au", "ql0992sq8na16.apps.pre.det.nsw.edu.au", "ql0992sq8na17.apps.pre.det.nsw.edu.au", "ql0992sq8nb00.apps.pre.det.nsw.edu.au", "ql0992sq8nb01.apps.pre.det.nsw.edu.au", "ql0992sq8nb02.apps.pre.det.nsw.edu.au", "ql0992sq8nb03.apps.pre.det.nsw.edu.au", "ql0992sq8nb04.apps.pre.det.nsw.edu.au", "ql0992sq8nb05.apps.pre.det.nsw.edu.au", "ql0992sq8nb06.apps.pre.det.nsw.edu.au", "ql0992sq8nb07.apps.pre.det.nsw.edu.au", "ql0992sq8nb08.apps.pre.det.nsw.edu.au", "ql0992sq8nb09.apps.pre.det.nsw.edu.au", "ql0992sq8nb10.apps.pre.det.nsw.edu.au", "ql0992sq8nb11.apps.pre.det.nsw.edu.au", "ql0992sq8nb12.apps.pre.det.nsw.edu.au", "ql0992sq8nb13.apps.pre.det.nsw.edu.au", "ql0992sq8nb14.apps.pre.det.nsw.edu.au", "ql0992sq8nb15.apps.pre.det.nsw.edu.au", "ql0992sq8nb16.apps.pre.det.nsw.edu.au", "ql0992sq8nb17.apps.pre.det.nsw.edu.au", "ql0992sq8nb18.apps.pre.det.nsw.edu.au", "ql0992sq8nb19.apps.pre.det.nsw.edu.au", "ql0992sq8nb20.apps.pre.det.nsw.edu.au", "ql0992sq8nb21.apps.pre.det.nsw.edu.au", "ql0992sq8nb22.apps.pre.det.nsw.edu.au"]}, "sq8_portal_workgroup": {"service_name": "openmetrics_sq8", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq8", "host_group_id": "portal"}, "nodes": ["ql0992sq8fa00.apps.pre.det.nsw.edu.au", "ql0992sq8fa01.apps.pre.det.nsw.edu.au"]}, "sq8_srm_workgroup": {"service_name": "openmetrics_sq8", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq8", "host_group_id": "srm"}, "nodes": ["ql0992sq8pa00.apps.pre.det.nsw.edu.au", "ql0992sq8pa01.apps.pre.det.nsw.edu.au", "ql0992sq8pa02.apps.pre.det.nsw.edu.au", "ql0992sq8pa03.apps.pre.det.nsw.edu.au"]}, "sq8_webdispatcher_workgroup": {"service_name": "openmetrics_sq8", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq8", "host_group_id": "webdispatcher"}, "nodes": ["ql0992sq8wd01.apps.pre.det.nsw.edu.au", "ql0992sq8wd02.apps.pre.det.nsw.edu.au", "ql0992sq8wd03.apps.pre.det.nsw.edu.au", "ql0992sq8wd04.apps.pre.det.nsw.edu.au"]}, "sqn_centralservices_workgroup": {"service_name": "openmetrics_sqn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqn", "host_group_id": "centralservices"}, "nodes": ["ql0992sqncs01.apps.pre.det.nsw.edu.au", "ql0992sqncs02.apps.pre.det.nsw.edu.au", "ql0992sqncs03.apps.pre.det.nsw.edu.au", "ql0992sqncs04.apps.pre.det.nsw.edu.au"]}, "sqn_erp_workgroup": {"service_name": "openmetrics_sqn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqn", "host_group_id": "erp"}, "nodes": ["ql0992sqnna00.apps.pre.det.nsw.edu.au", "ql0992sqnna01.apps.pre.det.nsw.edu.au", "ql0992sqnnb00.apps.pre.det.nsw.edu.au", "ql0992sqnnb01.apps.pre.det.nsw.edu.au", "ql0992sqnnb02.apps.pre.det.nsw.edu.au", "ql0992sqnnb03.apps.pre.det.nsw.edu.au"]}, "sqn_fiori_workgroup": {"service_name": "openmetrics_sqn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqn", "host_group_id": "fiori"}, "nodes": ["ql0992sqnga00.apps.pre.det.nsw.edu.au"]}, "sqn_portal_workgroup": {"service_name": "openmetrics_sqn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqn", "host_group_id": "portal"}, "nodes": ["ql0992sqnfa00.apps.pre.det.nsw.edu.au"]}, "sqn_webdispatcher_workgroup": {"service_name": "openmetrics_sqn", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqn", "host_group_id": "webdispatcher"}, "nodes": ["ql0992sqnwd01.apps.pre.det.nsw.edu.au", "ql0992sqnwd02.apps.pre.det.nsw.edu.au", "ql0992sqnwd03.apps.pre.det.nsw.edu.au", "ql0992sqnwd04.apps.pre.det.nsw.edu.au"]}, "sc0_dev_workgroup": {"service_name": "openmetrics_sc0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc0", "domain": "unset"}, "nodes": ["cl0992sc0cs01.apps.dev.det.nsw.edu.au", "cl0992sc0cs03.apps.dev.det.nsw.edu.au", "cl0992sc0fa00.apps.dev.det.nsw.edu.au", "cl0992sc0ga00.apps.dev.det.nsw.edu.au", "cl0992sc0la00.apps.dev.det.nsw.edu.au", "cl0992sc0na00.apps.dev.det.nsw.edu.au"]}, "spwl_dev_workgroup": {"service_name": "openmetrics_spwl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "spwl"}, "nodes": ["dl0991spwl11.apps.dev.det.nsw.edu.au", "dl0991spwl12.apps.dev.det.nsw.edu.au", "du-spwl-11.apps.dev.det.nsw.edu.au", "du-spwl-12.apps.dev.det.nsw.edu.au", "cl0991spwl01.apps.dev.det.nsw.edu.au"]}, "spwl_prod_workgroup": {"service_name": "openmetrics_spwl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "spwl"}, "nodes": ["pl0991spwl11.apps.det.nsw.edu.au", "pl0991spwl12.apps.det.nsw.edu.au", "pl0992spwl11.apps.det.nsw.edu.au", "pl0992spwl12.apps.det.nsw.edu.au", "pu-spwl-11.apps.det.nsw.edu.au", "pu-spwl-12.apps.det.nsw.edu.au", "pu-spwl-21.apps.det.nsw.edu.au", "pu-spwl-22.apps.det.nsw.edu.au"]}, "saprt_dev_workgroup": {"service_name": "openmetrics_saprt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "saprt"}, "nodes": ["dl0991srti01.apps.dev.det.nsw.edu.au", "dl0992srti01.apps.dev.det.nsw.edu.au"]}, "saprt_pre_workgroup": {"service_name": "openmetrics_saprt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "saprt"}, "nodes": ["ql0991srte01.dmz.det.nsw.edu.au", "ql0991srti01.apps.pre.det.nsw.edu.au"]}, "saprt_prod_workgroup": {"service_name": "openmetrics_saprt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "saprt"}, "nodes": ["pl0991srte01.dmz.det.nsw.edu.au", "pl0991srti01.apps.det.nsw.edu.au", "pl0992srte01.dmz.det.nsw.edu.au", "pl0992srti01.apps.det.nsw.edu.au", "tl0991srte01.dmz.det.nsw.edu.au"]}, "sd0_bpa_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "bpa"}, "nodes": ["dl0992sd0ka00.apps.dev.det.nsw.edu.au"]}, "sd0_bw_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "bw"}, "nodes": ["dl0992sd0va00.apps.dev.det.nsw.edu.au"]}, "sd0_bwportal_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "bwportal"}, "nodes": ["dl0992sd0ta00.apps.dev.det.nsw.edu.au"]}, "sd0_centralservices_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "centralservices"}, "nodes": ["dl0992sd0cs01.apps.dev.det.nsw.edu.au", "dl0992sd0cs02.apps.dev.det.nsw.edu.au", "dl0992sd0cs03.apps.dev.det.nsw.edu.au", "dl0992sd0cs04.apps.dev.det.nsw.edu.au"]}, "sd0_cps_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "cps"}, "nodes": ["dl0992sd0ja00.apps.dev.det.nsw.edu.au"]}, "sd0_erp_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "erp"}, "nodes": ["dl0992sd0na00.apps.dev.det.nsw.edu.au", "dl0992sd0na01.apps.dev.det.nsw.edu.au", "dl0992sd0nb00.apps.dev.det.nsw.edu.au", "dl0992sd0nb01.apps.dev.det.nsw.edu.au", "dl0992sd0nb02.apps.dev.det.nsw.edu.au", "dl0992sd0nb03.apps.dev.det.nsw.edu.au"]}, "sd0_fiori_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "fiori"}, "nodes": ["dl0992sd0ga00.apps.dev.det.nsw.edu.au"]}, "sd0_grc_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "grc"}, "nodes": ["dl0992sd0ua00.apps.dev.det.nsw.edu.au"]}, "sd0_po_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "po"}, "nodes": ["dl0992sd0oa00.apps.dev.det.nsw.edu.au"]}, "sd0_portal_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "portal"}, "nodes": ["dl0992sd0fa00.apps.dev.det.nsw.edu.au"]}, "sd0_srm_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "srm"}, "nodes": ["dl0992sd0pa00.apps.dev.det.nsw.edu.au"]}, "sd0_webdispatcher_workgroup": {"service_name": "openmetrics_sd0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd0", "host_group_id": "webdispatcher"}, "nodes": ["dl0992sd0wd01.apps.dev.det.nsw.edu.au", "dl0992sd0wd02.apps.dev.det.nsw.edu.au", "dl0992sd0wd03.apps.dev.det.nsw.edu.au", "dl0992sd0wd04.apps.dev.det.nsw.edu.au"]}, "sd1_bpa_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "bpa"}, "nodes": ["dl0991sd1ka00.apps.dev.det.nsw.edu.au"]}, "sd1_bw_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "bw"}, "nodes": ["dl0991sd1va00.apps.dev.det.nsw.edu.au"]}, "sd1_bwportal_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "bwportal"}, "nodes": ["dl0991sd1ta00.apps.dev.det.nsw.edu.au"]}, "sd1_centralservices_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "centralservices"}, "nodes": ["dl0991sd1cs01.apps.dev.det.nsw.edu.au", "dl0991sd1cs02.apps.dev.det.nsw.edu.au", "dl0991sd1cs03.apps.dev.det.nsw.edu.au", "dl0991sd1cs04.apps.dev.det.nsw.edu.au"]}, "sd1_cps_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "cps"}, "nodes": ["dl0991sd1ja00.apps.dev.det.nsw.edu.au"]}, "sd1_erp_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "erp"}, "nodes": ["dl0991sd1na00.apps.dev.det.nsw.edu.au", "dl0991sd1na01.apps.dev.det.nsw.edu.au", "dl0991sd1nb00.apps.dev.det.nsw.edu.au", "dl0991sd1nb01.apps.dev.det.nsw.edu.au", "dl0991sd1nb02.apps.dev.det.nsw.edu.au", "dl0991sd1nb03.apps.dev.det.nsw.edu.au"]}, "sd1_fiori_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "fiori"}, "nodes": ["dl0991sd1ga00.apps.dev.det.nsw.edu.au"]}, "sd1_grc_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "grc"}, "nodes": ["dl0991sd1ua00.apps.dev.det.nsw.edu.au"]}, "sd1_po_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "po"}, "nodes": ["dl0991sd1oa00.apps.dev.det.nsw.edu.au"]}, "sd1_portal_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "portal"}, "nodes": ["dl0991sd1fa00.apps.dev.det.nsw.edu.au"]}, "sd1_srm_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "srm"}, "nodes": ["dl0991sd1pa00.apps.dev.det.nsw.edu.au"]}, "sd1_webdispatcher_workgroup": {"service_name": "openmetrics_sd1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd1", "host_group_id": "webdispatcher"}, "nodes": ["dl0991sd1wd01.apps.dev.det.nsw.edu.au", "dl0991sd1wd02.apps.dev.det.nsw.edu.au", "dl0991sd1wd03.apps.dev.det.nsw.edu.au", "dl0991sd1wd04.apps.dev.det.nsw.edu.au"]}, "sd7_bw_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "bw"}, "nodes": ["dl0991sd7va00.apps.dev.det.nsw.edu.au"]}, "sd7_bwportal_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "bwportal"}, "nodes": ["dl0991sd7ta00.apps.dev.det.nsw.edu.au"]}, "sd7_centralservices_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "centralservices"}, "nodes": ["dl0991sd7cs01.apps.dev.det.nsw.edu.au", "dl0991sd7cs02.apps.dev.det.nsw.edu.au", "dl0991sd7cs03.apps.dev.det.nsw.edu.au"]}, "sd7_erp_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "erp"}, "nodes": ["dl0991sd7na00.apps.dev.det.nsw.edu.au", "dl0991sd7na01.apps.dev.det.nsw.edu.au", "dl0991sd7nb00.apps.dev.det.nsw.edu.au"]}, "sd7_fiori_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "fiori"}, "nodes": ["dl0991sd7ga00.apps.dev.det.nsw.edu.au"]}, "sd7_grc_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "grc"}, "nodes": ["dl0991sd7ua00.apps.dev.det.nsw.edu.au"]}, "sd7_po_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "po"}, "nodes": ["dl0991sd7oa00.apps.dev.det.nsw.edu.au"]}, "sd7_portal_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "portal"}, "nodes": ["dl0991sd7fa00.apps.dev.det.nsw.edu.au"]}, "sd7_srm_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "srm"}, "nodes": ["dl0991sd7pa00.apps.dev.det.nsw.edu.au"]}, "sd7_webdispatcher_workgroup": {"service_name": "openmetrics_sd7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sd7", "host_group_id": "webdispatcher"}, "nodes": ["dl0991sd7wd01.apps.dev.det.nsw.edu.au", "dl0991sd7wd02.apps.dev.det.nsw.edu.au"]}, "se1_bw_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "bw"}, "nodes": ["el0992se1va00.apps.train.det.nsw.edu.au"]}, "se1_bwportal_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "bwportal"}, "nodes": ["el0992se1ta00.apps.train.det.nsw.edu.au"]}, "se1_centralservices_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "centralservices"}, "nodes": ["el0992se1cs01.apps.train.det.nsw.edu.au", "el0992se1cs02.apps.train.det.nsw.edu.au", "el0992se1cs03.apps.train.det.nsw.edu.au", "el0992se1cs04.apps.train.det.nsw.edu.au"]}, "se1_erp_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "erp"}, "nodes": ["el0992se1na00.apps.train.det.nsw.edu.au", "el0992se1na01.apps.train.det.nsw.edu.au"]}, "se1_fiori_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "fiori"}, "nodes": ["el0992se1ga00.apps.train.det.nsw.edu.au", "el0992se1ga01.apps.train.det.nsw.edu.au"]}, "se1_po_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "po"}, "nodes": ["el0992se1oa00.apps.train.det.nsw.edu.au"]}, "se1_portal_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "portal"}, "nodes": ["el0992se1fa00.apps.train.det.nsw.edu.au"]}, "se1_srm_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "srm"}, "nodes": ["el0992se1pa00.apps.train.det.nsw.edu.au"]}, "se1_webdispatcher_workgroup": {"service_name": "openmetrics_se1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "se1", "host_group_id": "webdispatcher"}, "nodes": ["el0992se1wd01.apps.train.det.nsw.edu.au", "el0992se1wd02.apps.train.det.nsw.edu.au", "el0992se1wd03.apps.train.det.nsw.edu.au", "el0992se1wd04.apps.train.det.nsw.edu.au"]}, "ss0_bw_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "bw"}, "nodes": ["dl0992ss0va00.apps.dev.det.nsw.edu.au"]}, "ss0_bwportal_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "bwportal"}, "nodes": ["dl0992ss0ta00.apps.dev.det.nsw.edu.au"]}, "ss0_centralservices_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "centralservices"}, "nodes": ["dl0992ss0cs01.apps.dev.det.nsw.edu.au", "dl0992ss0cs02.apps.dev.det.nsw.edu.au", "dl0992ss0cs03.apps.dev.det.nsw.edu.au", "dl0992ss0cs04.apps.dev.det.nsw.edu.au"]}, "ss0_erp_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "erp"}, "nodes": ["dl0992ss0na00.apps.dev.det.nsw.edu.au"]}, "ss0_fiori_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "fiori"}, "nodes": ["dl0992ss0ga00.apps.dev.det.nsw.edu.au"]}, "ss0_po_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "po"}, "nodes": ["dl0992ss0oa00.apps.dev.det.nsw.edu.au"]}, "ss0_portal_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "portal"}, "nodes": ["dl0992ss0fa00.apps.dev.det.nsw.edu.au"]}, "ss0_srm_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "srm"}, "nodes": ["dl0992ss0pa00.apps.dev.det.nsw.edu.au"]}, "ss0_webdispatcher_workgroup": {"service_name": "openmetrics_ss0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "ss0", "host_group_id": "webdispatcher"}, "nodes": ["dl0992ss0wd01.apps.dev.det.nsw.edu.au", "dl0992ss0wd02.apps.dev.det.nsw.edu.au", "dl0992ss0wd03.apps.dev.det.nsw.edu.au", "dl0992ss0wd04.apps.dev.det.nsw.edu.au"]}, "st0_bpa_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "bpa"}, "nodes": ["tl0991st0ka00.apps.test.det.nsw.edu.au"]}, "st0_bw_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "bw"}, "nodes": ["tl0991st0va00.apps.test.det.nsw.edu.au"]}, "st0_bwportal_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "bwportal"}, "nodes": ["tl0991st0ta00.apps.test.det.nsw.edu.au"]}, "st0_centralservices_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "centralservices"}, "nodes": ["tl0991st0cs01.apps.test.det.nsw.edu.au", "tl0991st0cs02.apps.test.det.nsw.edu.au", "tl0991st0cs03.apps.test.det.nsw.edu.au", "tl0991st0cs04.apps.test.det.nsw.edu.au"]}, "st0_cps_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "cps"}, "nodes": ["tl0991st0ja00.apps.test.det.nsw.edu.au"]}, "st0_erp_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "erp"}, "nodes": ["tl0991st0na00.apps.test.det.nsw.edu.au", "tl0991st0na01.apps.test.det.nsw.edu.au", "tl0991st0nb00.apps.test.det.nsw.edu.au", "tl0991st0nb01.apps.test.det.nsw.edu.au", "tl0991st0nb02.apps.test.det.nsw.edu.au", "tl0991st0nb03.apps.test.det.nsw.edu.au"]}, "st0_fiori_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "fiori"}, "nodes": ["tl0991st0ga00.apps.test.det.nsw.edu.au"]}, "st0_grc_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "grc"}, "nodes": ["tl0991st0ua00.apps.test.det.nsw.edu.au"]}, "st0_po_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "po"}, "nodes": ["tl0991st0oa00.apps.test.det.nsw.edu.au"]}, "st0_portal_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "portal"}, "nodes": ["tl0991st0fa00.apps.test.det.nsw.edu.au"]}, "st0_srm_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "srm"}, "nodes": ["tl0991st0pa00.apps.test.det.nsw.edu.au"]}, "st0_webdispatcher_workgroup": {"service_name": "openmetrics_st0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st0", "host_group_id": "webdispatcher"}, "nodes": ["tl0991st0wd01.apps.test.det.nsw.edu.au", "tl0991st0wd02.apps.test.det.nsw.edu.au", "tl0991st0wd03.apps.test.det.nsw.edu.au", "tl0991st0wd04.apps.test.det.nsw.edu.au"]}, "st1_bpa_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "bpa"}, "nodes": ["tl0991st1ka00.apps.test.det.nsw.edu.au"]}, "st1_bw_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "bw"}, "nodes": ["tl0991st1va00.apps.test.det.nsw.edu.au"]}, "st1_bwportal_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "bwportal"}, "nodes": ["tl0991st1ta00.apps.test.det.nsw.edu.au"]}, "st1_centralservices_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "centralservices"}, "nodes": ["tl0991st1cs01.apps.test.det.nsw.edu.au", "tl0991st1cs02.apps.test.det.nsw.edu.au", "tl0991st1cs03.apps.test.det.nsw.edu.au", "tl0991st1cs04.apps.test.det.nsw.edu.au"]}, "st1_cps_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "cps"}, "nodes": ["tl0991st1ja00.apps.test.det.nsw.edu.au"]}, "st1_erp_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "erp"}, "nodes": ["tl0991st1na00.apps.test.det.nsw.edu.au", "tl0991st1na01.apps.test.det.nsw.edu.au", "tl0991st1nb00.apps.test.det.nsw.edu.au", "tl0991st1nb01.apps.test.det.nsw.edu.au", "tl0991st1nb02.apps.test.det.nsw.edu.au", "tl0991st1nb03.apps.test.det.nsw.edu.au"]}, "st1_fiori_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "fiori"}, "nodes": ["tl0991st1ga00.apps.test.det.nsw.edu.au"]}, "st1_grc_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "grc"}, "nodes": ["tl0991st1ua00.apps.test.det.nsw.edu.au"]}, "st1_po_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "po"}, "nodes": ["tl0991st1oa00.apps.test.det.nsw.edu.au"]}, "st1_portal_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "portal"}, "nodes": ["tl0991st1fa00.apps.test.det.nsw.edu.au"]}, "st1_srm_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "srm"}, "nodes": ["tl0991st1pa00.apps.test.det.nsw.edu.au"]}, "st1_webdispatcher_workgroup": {"service_name": "openmetrics_st1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st1", "host_group_id": "webdispatcher"}, "nodes": ["tl0991st1wd01.apps.test.det.nsw.edu.au", "tl0991st1wd02.apps.test.det.nsw.edu.au", "tl0991st1wd03.apps.test.det.nsw.edu.au", "tl0991st1wd04.apps.test.det.nsw.edu.au"]}, "st2_bwportal_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "bwportal"}, "nodes": ["tl0991st2ta00.apps.test.det.nsw.edu.au"]}, "st2_centralservices_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "centralservices"}, "nodes": ["tl0991st2cs01.apps.test.det.nsw.edu.au", "tl0991st2cs02.apps.test.det.nsw.edu.au", "tl0991st2cs03.apps.test.det.nsw.edu.au", "tl0991st2cs04.apps.test.det.nsw.edu.au"]}, "st2_erp_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "erp"}, "nodes": ["tl0991st2na00.apps.test.det.nsw.edu.au", "tl0991st2na01.apps.test.det.nsw.edu.au", "tl0991st2nb00.apps.test.det.nsw.edu.au", "tl0991st2nb01.apps.test.det.nsw.edu.au", "tl0991st2nb02.apps.test.det.nsw.edu.au", "tl0991st2nb03.apps.test.det.nsw.edu.au"]}, "st2_fiori_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "fiori"}, "nodes": ["tl0991st2ga00.apps.test.det.nsw.edu.au"]}, "st2_portal_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "portal"}, "nodes": ["tl0991st2fa00.apps.test.det.nsw.edu.au"]}, "st2_webdispatcher_workgroup": {"service_name": "openmetrics_st2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st2", "host_group_id": "webdispatcher"}, "nodes": ["tl0991st2wd01.apps.test.det.nsw.edu.au", "tl0991st2wd02.apps.test.det.nsw.edu.au", "tl0991st2wd03.apps.test.det.nsw.edu.au", "tl0991st2wd04.apps.test.det.nsw.edu.au"]}, "st7_bw_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "bw"}, "nodes": ["tl0991st7va00.apps.test.det.nsw.edu.au"]}, "st7_bwportal_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "bwportal"}, "nodes": ["tl0991st7ta00.apps.test.det.nsw.edu.au"]}, "st7_centralservices_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "centralservices"}, "nodes": ["tl0991st7cs01.apps.test.det.nsw.edu.au", "tl0991st7cs02.apps.test.det.nsw.edu.au", "tl0991st7cs03.apps.test.det.nsw.edu.au"]}, "st7_cps_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "cps"}, "nodes": ["tl0991st7ja00.apps.test.det.nsw.edu.au"]}, "st7_erp_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "erp"}, "nodes": ["tl0991st7na01.apps.test.det.nsw.edu.au", "tl0991st7nb00.apps.test.det.nsw.edu.au"]}, "st7_fiori_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "fiori"}, "nodes": ["tl0991st7ga00.apps.test.det.nsw.edu.au"]}, "st7_grc_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "grc"}, "nodes": ["tl0991st7ua00.apps.test.det.nsw.edu.au"]}, "st7_po_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "po"}, "nodes": ["tl0991st7oa00.apps.test.det.nsw.edu.au"]}, "st7_portal_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "portal"}, "nodes": ["tl0991st7fa00.apps.test.det.nsw.edu.au"]}, "st7_srm_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "srm"}, "nodes": ["tl0991st7pa00.apps.test.det.nsw.edu.au"]}, "st7_webdispatcher_workgroup": {"service_name": "openmetrics_st7", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "st7", "host_group_id": "webdispatcher"}, "nodes": ["tl0991st7wd01.apps.test.det.nsw.edu.au", "tl0991st7wd02.apps.test.det.nsw.edu.au"]}, "sc9_bpa_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "bpa"}, "nodes": ["cl0991sc9ka00.apps.dev.det.nsw.edu.au", "cl0991sc9ka01.apps.dev.det.nsw.edu.au"]}, "sc9_bw_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "bw"}, "nodes": ["cl0991sc9va00.apps.dev.det.nsw.edu.au", "cl0991sc9va01.apps.dev.det.nsw.edu.au", "cl0991sc9va02.apps.dev.det.nsw.edu.au", "cl0991sc9va03.apps.dev.det.nsw.edu.au", "cl0991sc9va04.apps.dev.det.nsw.edu.au", "cl0991sc9va05.apps.dev.det.nsw.edu.au", "cl0991sc9va06.apps.dev.det.nsw.edu.au", "cl0991sc9va07.apps.dev.det.nsw.edu.au"]}, "sc9_bwportal_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "bwportal"}, "nodes": ["cl0991sc9ta00.apps.dev.det.nsw.edu.au", "cl0991sc9ta01.apps.dev.det.nsw.edu.au"]}, "sc9_centralservices_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "centralservices"}, "nodes": ["cl0991sc9cs01.apps.dev.det.nsw.edu.au", "cl0991sc9cs02.apps.dev.det.nsw.edu.au", "cl0991sc9cs03.apps.dev.det.nsw.edu.au", "cl0991sc9cs04.apps.dev.det.nsw.edu.au"]}, "sc9_cps_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "cps"}, "nodes": ["cl0991sc9ja00.apps.dev.det.nsw.edu.au", "cl0991sc9ja01.apps.dev.det.nsw.edu.au"]}, "sc9_erp_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "erp"}, "nodes": ["cl0991sc9na00.apps.dev.det.nsw.edu.au", "cl0991sc9na01.apps.dev.det.nsw.edu.au", "cl0991sc9na02.apps.dev.det.nsw.edu.au", "cl0991sc9na03.apps.dev.det.nsw.edu.au", "cl0991sc9na04.apps.dev.det.nsw.edu.au", "cl0991sc9na05.apps.dev.det.nsw.edu.au", "cl0991sc9na06.apps.dev.det.nsw.edu.au", "cl0991sc9na07.apps.dev.det.nsw.edu.au", "cl0991sc9na08.apps.dev.det.nsw.edu.au", "cl0991sc9na09.apps.dev.det.nsw.edu.au", "cl0991sc9na10.apps.dev.det.nsw.edu.au", "cl0991sc9na11.apps.dev.det.nsw.edu.au", "cl0991sc9na12.apps.dev.det.nsw.edu.au", "cl0991sc9na13.apps.dev.det.nsw.edu.au", "cl0991sc9nb00.apps.dev.det.nsw.edu.au", "cl0991sc9nb01.apps.dev.det.nsw.edu.au", "cl0991sc9nb02.apps.dev.det.nsw.edu.au", "cl0991sc9nb03.apps.dev.det.nsw.edu.au", "cl0991sc9nb04.apps.dev.det.nsw.edu.au", "cl0991sc9nb05.apps.dev.det.nsw.edu.au", "cl0991sc9nb06.apps.dev.det.nsw.edu.au", "cl0991sc9nb07.apps.dev.det.nsw.edu.au", "cl0991sc9nb08.apps.dev.det.nsw.edu.au", "cl0991sc9nb09.apps.dev.det.nsw.edu.au", "cl0991sc9nb10.apps.dev.det.nsw.edu.au", "cl0991sc9nb11.apps.dev.det.nsw.edu.au", "cl0991sc9nb12.apps.dev.det.nsw.edu.au", "cl0991sc9nb13.apps.dev.det.nsw.edu.au", "cl0991sc9nb14.apps.dev.det.nsw.edu.au", "cl0991sc9nb15.apps.dev.det.nsw.edu.au"]}, "sc9_fiori_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "fiori"}, "nodes": ["cl0991sc9ga00.apps.dev.det.nsw.edu.au", "cl0991sc9ga01.apps.dev.det.nsw.edu.au", "cl0991sc9ga02.apps.dev.det.nsw.edu.au", "cl0991sc9ga03.apps.dev.det.nsw.edu.au"]}, "sc9_grc_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "grc"}, "nodes": ["cl0991sc9ua00.apps.dev.det.nsw.edu.au", "cl0991sc9ua01.apps.dev.det.nsw.edu.au"]}, "sc9_po_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "po"}, "nodes": ["cl0991sc9oa00.apps.dev.det.nsw.edu.au", "cl0991sc9oa01.apps.dev.det.nsw.edu.au"]}, "sc9_portal_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "portal"}, "nodes": ["cl0991sc9fa00.apps.dev.det.nsw.edu.au", "cl0991sc9fa01.apps.dev.det.nsw.edu.au", "cl0991sc9fa02.apps.dev.det.nsw.edu.au", "cl0991sc9fa03.apps.dev.det.nsw.edu.au", "cl0991sc9fa04.apps.dev.det.nsw.edu.au", "cl0991sc9fa05.apps.dev.det.nsw.edu.au", "cl0991sc9fa06.apps.dev.det.nsw.edu.au", "cl0991sc9fa07.apps.dev.det.nsw.edu.au"]}, "sc9_srm_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "srm"}, "nodes": ["cl0991sc9pa00.apps.dev.det.nsw.edu.au", "cl0991sc9pa01.apps.dev.det.nsw.edu.au", "cl0991sc9pa02.apps.dev.det.nsw.edu.au", "cl0991sc9pa03.apps.dev.det.nsw.edu.au", "cl0991sc9pa04.apps.dev.det.nsw.edu.au", "cl0991sc9pa05.apps.dev.det.nsw.edu.au"]}, "sc9_webdispatcher_workgroup": {"service_name": "openmetrics_sc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sc9", "host_group_id": "webdispatcher"}, "nodes": ["cl0991sc9wd01.apps.dev.det.nsw.edu.au", "cl0991sc9wd02.apps.dev.det.nsw.edu.au", "cl0991sc9wd03.apps.dev.det.nsw.edu.au", "cl0991sc9wd04.apps.dev.det.nsw.edu.au"]}, "sq0_bpa_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "bpa"}, "nodes": ["ql0991sq0ka00.apps.pre.det.nsw.edu.au", "ql0991sq0ka01.apps.pre.det.nsw.edu.au"]}, "sq0_bw_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "bw"}, "nodes": ["ql0991sq0va00.apps.pre.det.nsw.edu.au", "ql0991sq0va01.apps.pre.det.nsw.edu.au", "ql0991sq0va02.apps.pre.det.nsw.edu.au", "ql0991sq0va03.apps.pre.det.nsw.edu.au", "ql0991sq0va04.apps.pre.det.nsw.edu.au", "ql0991sq0va05.apps.pre.det.nsw.edu.au", "ql0991sq0va06.apps.pre.det.nsw.edu.au", "ql0991sq0va07.apps.pre.det.nsw.edu.au"]}, "sq0_bwportal_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "bwportal"}, "nodes": ["ql0991sq0ta00.apps.pre.det.nsw.edu.au", "ql0991sq0ta01.apps.pre.det.nsw.edu.au", "ql0991sq0ta02.apps.pre.det.nsw.edu.au", "ql0991sq0ta03.apps.pre.det.nsw.edu.au"]}, "sq0_centralservices_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "centralservices"}, "nodes": ["ql0991sq0cs01.apps.pre.det.nsw.edu.au", "ql0991sq0cs02.apps.pre.det.nsw.edu.au", "ql0991sq0cs03.apps.pre.det.nsw.edu.au", "ql0991sq0cs04.apps.pre.det.nsw.edu.au"]}, "sq0_cps_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "cps"}, "nodes": ["ql0991sq0ja00.apps.pre.det.nsw.edu.au", "ql0991sq0ja01.apps.pre.det.nsw.edu.au"]}, "sq0a_erp_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0a", "host_group_id": "erp"}, "nodes": ["ql0991sq0na00.apps.pre.det.nsw.edu.au", "ql0991sq0na01.apps.pre.det.nsw.edu.au", "ql0991sq0na02.apps.pre.det.nsw.edu.au", "ql0991sq0na03.apps.pre.det.nsw.edu.au", "ql0991sq0na04.apps.pre.det.nsw.edu.au", "ql0991sq0na05.apps.pre.det.nsw.edu.au", "ql0991sq0na06.apps.pre.det.nsw.edu.au", "ql0991sq0na07.apps.pre.det.nsw.edu.au", "ql0991sq0na08.apps.pre.det.nsw.edu.au", "ql0991sq0na09.apps.pre.det.nsw.edu.au", "ql0991sq0na10.apps.pre.det.nsw.edu.au", "ql0991sq0na11.apps.pre.det.nsw.edu.au", "ql0991sq0na12.apps.pre.det.nsw.edu.au", "ql0991sq0na13.apps.pre.det.nsw.edu.au"]}, "sq0b_erp_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0b", "host_group_id": "erp"}, "nodes": ["ql0991sq0nb00.apps.pre.det.nsw.edu.au", "ql0991sq0nb01.apps.pre.det.nsw.edu.au", "ql0991sq0nb02.apps.pre.det.nsw.edu.au", "ql0991sq0nb03.apps.pre.det.nsw.edu.au", "ql0991sq0nb04.apps.pre.det.nsw.edu.au", "ql0991sq0nb05.apps.pre.det.nsw.edu.au", "ql0991sq0nb06.apps.pre.det.nsw.edu.au", "ql0991sq0nb07.apps.pre.det.nsw.edu.au", "ql0991sq0nb08.apps.pre.det.nsw.edu.au", "ql0991sq0nb09.apps.pre.det.nsw.edu.au", "ql0991sq0nb10.apps.pre.det.nsw.edu.au", "ql0991sq0nb11.apps.pre.det.nsw.edu.au", "ql0991sq0nb12.apps.pre.det.nsw.edu.au", "ql0991sq0nb13.apps.pre.det.nsw.edu.au"]}, "sq0_fiori_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "fiori"}, "nodes": ["ql0991sq0ga00.apps.pre.det.nsw.edu.au", "ql0991sq0ga01.apps.pre.det.nsw.edu.au", "ql0991sq0ga02.apps.pre.det.nsw.edu.au", "ql0991sq0ga03.apps.pre.det.nsw.edu.au", "ql0991sq0ga04.apps.pre.det.nsw.edu.au", "ql0991sq0ga05.apps.pre.det.nsw.edu.au"]}, "sq0_grc_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "grc"}, "nodes": ["ql0991sq0ua00.apps.pre.det.nsw.edu.au", "ql0991sq0ua01.apps.pre.det.nsw.edu.au"]}, "sq0_po_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "po"}, "nodes": ["ql0991sq0oa00.apps.pre.det.nsw.edu.au", "ql0991sq0oa01.apps.pre.det.nsw.edu.au"]}, "sq0_portal_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "portal"}, "nodes": ["ql0991sq0fa00.apps.pre.det.nsw.edu.au", "ql0991sq0fa01.apps.pre.det.nsw.edu.au", "ql0991sq0fa02.apps.pre.det.nsw.edu.au", "ql0991sq0fa03.apps.pre.det.nsw.edu.au", "ql0991sq0fa04.apps.pre.det.nsw.edu.au", "ql0991sq0fa05.apps.pre.det.nsw.edu.au", "ql0991sq0fa06.apps.pre.det.nsw.edu.au", "ql0991sq0fa07.apps.pre.det.nsw.edu.au"]}, "sq0_srm_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "srm"}, "nodes": ["ql0991sq0pa00.apps.pre.det.nsw.edu.au", "ql0991sq0pa01.apps.pre.det.nsw.edu.au", "ql0991sq0pa02.apps.pre.det.nsw.edu.au", "ql0991sq0pa03.apps.pre.det.nsw.edu.au", "ql0991sq0pa04.apps.pre.det.nsw.edu.au", "ql0991sq0pa05.apps.pre.det.nsw.edu.au", "ql0991sq0pa06.apps.pre.det.nsw.edu.au", "ql0991sq0pa07.apps.pre.det.nsw.edu.au"]}, "sq0_webdispatcher_workgroup": {"service_name": "openmetrics_sq0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq0", "host_group_id": "webdispatcher"}, "nodes": ["ql0991sq0wd01.apps.pre.det.nsw.edu.au", "ql0991sq0wd02.apps.pre.det.nsw.edu.au", "ql0991sq0wd03.apps.pre.det.nsw.edu.au", "ql0991sq0wd04.apps.pre.det.nsw.edu.au"]}, "sq2_centralservices_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "centralservices"}, "nodes": ["ql0992sq2cs01.apps.pre.det.nsw.edu.au", "ql0992sq2cs02.apps.pre.det.nsw.edu.au", "ql0992sq2cs03.apps.pre.det.nsw.edu.au", "ql0992sq2cs04.apps.pre.det.nsw.edu.au"]}, "sq2_erp_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "erp"}, "nodes": ["ql0992sq2na00.apps.pre.det.nsw.edu.au", "ql0992sq2na01.apps.pre.det.nsw.edu.au", "ql0992sq2nb00.apps.pre.det.nsw.edu.au", "ql0992sq2nb01.apps.pre.det.nsw.edu.au", "ql0992sq2nb02.apps.pre.det.nsw.edu.au", "ql0992sq2nb03.apps.pre.det.nsw.edu.au"]}, "sq2_fiori_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "fiori"}, "nodes": ["ql0992sq2ga00.apps.pre.det.nsw.edu.au", "ql0992sq2ga01.apps.pre.det.nsw.edu.au"]}, "sq2_portal_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "portal"}, "nodes": ["ql0992sq2fa00.apps.pre.det.nsw.edu.au", "ql0992sq2fa01.apps.pre.det.nsw.edu.au"]}, "sq2_srm_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "srm"}, "nodes": ["ql0992sq2pa00.apps.pre.det.nsw.edu.au", "ql0992sq2pa01.apps.pre.det.nsw.edu.au"]}, "sq2_webdispatcher_workgroup": {"service_name": "openmetrics_sq2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sq2", "host_group_id": "webdispatcher"}, "nodes": ["ql0992sq2wd01.apps.pre.det.nsw.edu.au", "ql0992sq2wd02.apps.pre.det.nsw.edu.au", "ql0992sq2wd03.apps.pre.det.nsw.edu.au", "ql0992sq2wd04.apps.pre.det.nsw.edu.au"]}, "sqa_bpa_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "bpa"}, "nodes": ["ql0992sqaka00.apps.pre.det.nsw.edu.au", "ql0992sqaka01.apps.pre.det.nsw.edu.au"]}, "sqa_bw_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "bw"}, "nodes": ["ql0992sqava00.apps.pre.det.nsw.edu.au", "ql0992sqava01.apps.pre.det.nsw.edu.au", "ql0992sqava02.apps.pre.det.nsw.edu.au", "ql0992sqava03.apps.pre.det.nsw.edu.au", "ql0992sqava04.apps.pre.det.nsw.edu.au", "ql0992sqava05.apps.pre.det.nsw.edu.au", "ql0992sqava06.apps.pre.det.nsw.edu.au", "ql0992sqava07.apps.pre.det.nsw.edu.au"]}, "sqa_bwportal_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "bwportal"}, "nodes": ["ql0992sqata00.apps.pre.det.nsw.edu.au", "ql0992sqata01.apps.pre.det.nsw.edu.au"]}, "sqa_centralservices_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "centralservices"}, "nodes": ["ql0992sqacs01.apps.pre.det.nsw.edu.au", "ql0992sqacs02.apps.pre.det.nsw.edu.au", "ql0992sqacs03.apps.pre.det.nsw.edu.au", "ql0992sqacs04.apps.pre.det.nsw.edu.au"]}, "sqa_cps_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "cps"}, "nodes": ["ql0992sqaja00.apps.pre.det.nsw.edu.au", "ql0992sqaja01.apps.pre.det.nsw.edu.au"]}, "sqa_erp_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "erp"}, "nodes": ["ql0992sqana00.apps.pre.det.nsw.edu.au", "ql0992sqana01.apps.pre.det.nsw.edu.au", "ql0992sqana02.apps.pre.det.nsw.edu.au", "ql0992sqana03.apps.pre.det.nsw.edu.au", "ql0992sqana04.apps.pre.det.nsw.edu.au", "ql0992sqana05.apps.pre.det.nsw.edu.au", "ql0992sqana06.apps.pre.det.nsw.edu.au", "ql0992sqana07.apps.pre.det.nsw.edu.au", "ql0992sqana08.apps.pre.det.nsw.edu.au", "ql0992sqana09.apps.pre.det.nsw.edu.au", "ql0992sqana10.apps.pre.det.nsw.edu.au", "ql0992sqana11.apps.pre.det.nsw.edu.au", "ql0992sqana12.apps.pre.det.nsw.edu.au", "ql0992sqana13.apps.pre.det.nsw.edu.au", "ql0992sqanb00.apps.pre.det.nsw.edu.au", "ql0992sqanb01.apps.pre.det.nsw.edu.au", "ql0992sqanb02.apps.pre.det.nsw.edu.au", "ql0992sqanb03.apps.pre.det.nsw.edu.au", "ql0992sqanb04.apps.pre.det.nsw.edu.au", "ql0992sqanb05.apps.pre.det.nsw.edu.au", "ql0992sqanb06.apps.pre.det.nsw.edu.au", "ql0992sqanb07.apps.pre.det.nsw.edu.au", "ql0992sqanb08.apps.pre.det.nsw.edu.au", "ql0992sqanb09.apps.pre.det.nsw.edu.au", "ql0992sqanb10.apps.pre.det.nsw.edu.au", "ql0992sqanb11.apps.pre.det.nsw.edu.au", "ql0992sqanb12.apps.pre.det.nsw.edu.au", "ql0992sqanb13.apps.pre.det.nsw.edu.au"]}, "sqa_fiori_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "fiori"}, "nodes": ["ql0992sqaga00.apps.pre.det.nsw.edu.au", "ql0992sqaga01.apps.pre.det.nsw.edu.au", "ql0992sqaga02.apps.pre.det.nsw.edu.au", "ql0992sqaga03.apps.pre.det.nsw.edu.au", "ql0992sqaga04.apps.pre.det.nsw.edu.au", "ql0992sqaga05.apps.pre.det.nsw.edu.au"]}, "sqa_grc_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "grc"}, "nodes": ["ql0992sqaua00.apps.pre.det.nsw.edu.au", "ql0992sqaua01.apps.pre.det.nsw.edu.au"]}, "sqa_po_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "po"}, "nodes": ["ql0992sqaoa00.apps.pre.det.nsw.edu.au", "ql0992sqaoa01.apps.pre.det.nsw.edu.au"]}, "sqa_portal_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "portal"}, "nodes": ["ql0992sqafa00.apps.pre.det.nsw.edu.au", "ql0992sqafa01.apps.pre.det.nsw.edu.au", "ql0992sqafa02.apps.pre.det.nsw.edu.au", "ql0992sqafa03.apps.pre.det.nsw.edu.au", "ql0992sqafa04.apps.pre.det.nsw.edu.au", "ql0992sqafa05.apps.pre.det.nsw.edu.au", "ql0992sqafa06.apps.pre.det.nsw.edu.au", "ql0992sqafa07.apps.pre.det.nsw.edu.au"]}, "sqa_srm_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "srm"}, "nodes": ["ql0992sqapa00.apps.pre.det.nsw.edu.au", "ql0992sqapa01.apps.pre.det.nsw.edu.au", "ql0992sqapa02.apps.pre.det.nsw.edu.au", "ql0992sqapa03.apps.pre.det.nsw.edu.au", "ql0992sqapa04.apps.pre.det.nsw.edu.au", "ql0992sqapa05.apps.pre.det.nsw.edu.au"]}, "sqa_webdispatcher_workgroup": {"service_name": "openmetrics_sqa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sqa", "host_group_id": "webdispatcher"}, "nodes": ["ql0992sqawd01.apps.pre.det.nsw.edu.au", "ql0992sqawd02.apps.pre.det.nsw.edu.au", "ql0992sqawd03.apps.pre.det.nsw.edu.au", "ql0992sqawd04.apps.pre.det.nsw.edu.au"]}, "sp1_bpa_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "bpa"}, "nodes": ["pl0992sp1ka00.apps.det.nsw.edu.au", "pl0992sp1ka01.apps.det.nsw.edu.au"]}, "sp1_bw_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "bw"}, "nodes": ["pl0992sp1va00.apps.det.nsw.edu.au", "pl0992sp1va01.apps.det.nsw.edu.au", "pl0992sp1va02.apps.det.nsw.edu.au", "pl0992sp1va03.apps.det.nsw.edu.au", "pl0992sp1va04.apps.det.nsw.edu.au", "pl0992sp1va05.apps.det.nsw.edu.au", "pl0992sp1va06.apps.det.nsw.edu.au", "pl0992sp1va07.apps.det.nsw.edu.au"]}, "sp1_bwportal_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "bwportal"}, "nodes": ["pl0992sp1ta00.apps.det.nsw.edu.au", "pl0992sp1ta01.apps.det.nsw.edu.au", "pl0992sp1ta02.apps.det.nsw.edu.au", "pl0992sp1ta03.apps.det.nsw.edu.au"]}, "sp1_centralservices_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "centralservices"}, "nodes": ["pl0992sp1cs01.apps.det.nsw.edu.au", "pl0992sp1cs02.apps.det.nsw.edu.au", "pl0992sp1cs03.apps.det.nsw.edu.au", "pl0992sp1cs04.apps.det.nsw.edu.au"]}, "sp1_cps_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "cps"}, "nodes": ["pl0992sp1ja00.apps.det.nsw.edu.au", "pl0992sp1ja01.apps.det.nsw.edu.au"]}, "sp1_erp_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "erp"}, "nodes": ["pl0992sp1na00.apps.det.nsw.edu.au", "pl0992sp1na01.apps.det.nsw.edu.au", "pl0992sp1na02.apps.det.nsw.edu.au", "pl0992sp1na03.apps.det.nsw.edu.au", "pl0992sp1na04.apps.det.nsw.edu.au", "pl0992sp1na05.apps.det.nsw.edu.au", "pl0992sp1na06.apps.det.nsw.edu.au", "pl0992sp1na07.apps.det.nsw.edu.au", "pl0992sp1na08.apps.det.nsw.edu.au", "pl0992sp1na09.apps.det.nsw.edu.au", "pl0992sp1na10.apps.det.nsw.edu.au", "pl0992sp1na11.apps.det.nsw.edu.au", "pl0992sp1na12.apps.det.nsw.edu.au", "pl0992sp1na13.apps.det.nsw.edu.au", "pl0992sp1na14.apps.det.nsw.edu.au", "pl0992sp1na15.apps.det.nsw.edu.au", "pl0992sp1na16.apps.det.nsw.edu.au", "pl0992sp1na17.apps.det.nsw.edu.au", "pl0992sp1nb00.apps.det.nsw.edu.au", "pl0992sp1nb01.apps.det.nsw.edu.au", "pl0992sp1nb02.apps.det.nsw.edu.au", "pl0992sp1nb03.apps.det.nsw.edu.au", "pl0992sp1nb04.apps.det.nsw.edu.au", "pl0992sp1nb05.apps.det.nsw.edu.au", "pl0992sp1nb06.apps.det.nsw.edu.au", "pl0992sp1nb07.apps.det.nsw.edu.au", "pl0992sp1nb08.apps.det.nsw.edu.au", "pl0992sp1nb09.apps.det.nsw.edu.au", "pl0992sp1nb10.apps.det.nsw.edu.au", "pl0992sp1nb11.apps.det.nsw.edu.au", "pl0992sp1nb12.apps.det.nsw.edu.au", "pl0992sp1nb13.apps.det.nsw.edu.au", "pl0992sp1nb14.apps.det.nsw.edu.au", "pl0992sp1nb15.apps.det.nsw.edu.au", "pl0992sp1nb16.apps.det.nsw.edu.au", "pl0992sp1nb17.apps.det.nsw.edu.au", "pl0992sp1nb18.apps.det.nsw.edu.au", "pl0992sp1nb19.apps.det.nsw.edu.au", "pl0992sp1nb20.apps.det.nsw.edu.au", "pl0992sp1nb21.apps.det.nsw.edu.au", "pl0992sp1nb22.apps.det.nsw.edu.au"]}, "sp1_fiori_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "fiori"}, "nodes": ["pl0992sp1ga00.apps.det.nsw.edu.au", "pl0992sp1ga01.apps.det.nsw.edu.au", "pl0992sp1ga02.apps.det.nsw.edu.au", "pl0992sp1ga03.apps.det.nsw.edu.au", "pl0992sp1ga04.apps.det.nsw.edu.au", "pl0992sp1ga05.apps.det.nsw.edu.au"]}, "sp1_grc_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "grc"}, "nodes": ["pl0992sp1ua00.apps.det.nsw.edu.au", "pl0992sp1ua01.apps.det.nsw.edu.au"]}, "sp1_pi_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "pi"}, "nodes": ["pl0992sp1ia00.apps.det.nsw.edu.au", "pl0992sp1ia01.apps.det.nsw.edu.au"]}, "sp1_po_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "po"}, "nodes": ["pl0992sp1oa00.apps.det.nsw.edu.au", "pl0992sp1oa01.apps.det.nsw.edu.au"]}, "sp1_portal_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "portal"}, "nodes": ["pl0992sp1fa00.apps.det.nsw.edu.au", "pl0992sp1fa01.apps.det.nsw.edu.au", "pl0992sp1fa02.apps.det.nsw.edu.au", "pl0992sp1fa03.apps.det.nsw.edu.au", "pl0992sp1fa04.apps.det.nsw.edu.au", "pl0992sp1fa05.apps.det.nsw.edu.au", "pl0992sp1fa06.apps.det.nsw.edu.au", "pl0992sp1fa07.apps.det.nsw.edu.au"]}, "sp1_srm_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "srm"}, "nodes": ["pl0992sp1pa00.apps.det.nsw.edu.au", "pl0992sp1pa01.apps.det.nsw.edu.au", "pl0992sp1pa02.apps.det.nsw.edu.au", "pl0992sp1pa03.apps.det.nsw.edu.au", "pl0992sp1pa04.apps.det.nsw.edu.au", "pl0992sp1pa05.apps.det.nsw.edu.au", "pl0992sp1pa06.apps.det.nsw.edu.au", "pl0992sp1pa07.apps.det.nsw.edu.au"]}, "sp1_webdispatcher_workgroup": {"service_name": "openmetrics_sp1", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sp1", "host_group_id": "webdispatcher"}, "nodes": ["pl0991sp1wd01.apps.det.nsw.edu.au", "pl0991sp1wd02.apps.det.nsw.edu.au", "pl0991sp1wd03.apps.det.nsw.edu.au", "pl0991sp1wd04.apps.det.nsw.edu.au", "pl0992sp1wd01.apps.det.nsw.edu.au", "pl0992sp1wd02.apps.det.nsw.edu.au", "pl0992sp1wd03.apps.det.nsw.edu.au", "pl0992sp1wd04.apps.det.nsw.edu.au"]}, "sap_hana_dev_workgroup": {"service_name": "openmetrics_sapdh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapdh"}, "nodes": ["du0991sapdh101.dbs.dev.det.nsw.edu.au", "du0991sapdh301.dbs.dev.det.nsw.edu.au", "du0991sapdh501.dbs.dev.det.nsw.edu.au", "du0992sapdh201.dbs.dev.det.nsw.edu.au"]}, "sap_hana_train_workgroup": {"service_name": "openmetrics_sapdh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapdh"}, "nodes": ["eu0992sapdh201.dbs.train.det.nsw.edu.au"]}, "sap_hana_pre_workgroup": {"service_name": "openmetrics_sapdh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapdh"}, "nodes": ["qu0991sapdh101.dbs.pre.det.nsw.edu.au", "qu0991sapdh501.dbs.pre.det.nsw.edu.au", "qu0991sapdh701.dbs.pre.det.nsw.edu.au", "qu0991sapdh702.dbs.pre.det.nsw.edu.au", "qu0992sapdh201.dbs.pre.det.nsw.edu.au", "qu0992sapdh401.dbs.pre.det.nsw.edu.au", "qu0992sapdh601.dbs.pre.det.nsw.edu.au"]}, "sap_hana_prod_workgroup": {"service_name": "openmetrics_sapdh", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sapdh"}, "nodes": ["pu0991sapdh101.dbs.det.nsw.edu.au", "pu0991sapdh301.dbs.det.nsw.edu.au", "pu0992sapdh201.dbs.det.nsw.edu.au", "pu0992sapdh202.dbs.det.nsw.edu.au", "pu0992sapdh601.dbs.det.nsw.edu.au", "pu0992sapdh602.dbs.det.nsw.edu.au"]}, "sap_oracle_dev_workgroup": {"service_name": "openmetrics_sdbo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sdbo"}, "nodes": ["bl0991sdbo01.dbs.dev.det.nsw.edu.au", "bl0991sdbo02.dbs.dev.det.nsw.edu.au", "bl0991sdbo11.dbs.dev.det.nsw.edu.au", "bl0992sdbo01.dbs.dev.det.nsw.edu.au"]}, "sap_oracle_pre_workgroup": {"service_name": "openmetrics_sdbo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "pre", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sdbo"}, "nodes": ["ql0992sdbo01.dbs.pre.det.nsw.edu.au", "ql0992sdbo02.dbs.pre.det.nsw.edu.au", "ql0992sdbo11.dbs.pre.det.nsw.edu.au"]}, "sap_oracle_prod_workgroup": {"service_name": "openmetrics_sdbo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "sdbo"}, "nodes": ["pl0991sdbo01.dbs.det.nsw.edu.au", "pl0992sdbo01.dbs.det.nsw.edu.au", "pl0992sdbo02.dbs.det.nsw.edu.au"]}, "sqf019_preprod_predetnsw.win": {"service_name": "openmetrics_sqf019", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf019", "domain": "predetnsw.win"}, "nodes": ["qw0992sqf002n1.dbs.pre.det.nsw.edu.au", "qw0991sqf002n2.dbs.pre.det.nsw.edu.au"]}, "sqf03a_prod_detnsw.win": {"service_name": "openmetrics_sqf03a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf03a", "domain": "detnsw.win"}, "nodes": ["pw0992sqf003n2.dbs.det.nsw.edu.au", "pw0992sqf002n1.dbs.det.nsw.edu.au", "pw0991sqf003n1.dbs.det.nsw.edu.au", "pw0991sqf002n2.dbs.det.nsw.edu.au", "pw0991sqf003n3.dbs.det.nsw.edu.au"]}, "sqf03c_prod_detnsw.win": {"service_name": "openmetrics_sqf03c", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf03c", "domain": "detnsw.win"}, "nodes": ["pw0992sqf004n3.dbs.det.nsw.edu.au", "pw0992sqf004n1.dbs.det.nsw.edu.au", "pw0991sqf004n2.dbs.det.nsw.edu.au"]}, "sqf101_prod_detnsw.win": {"service_name": "openmetrics_sqf101", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf101", "domain": "detnsw.win"}, "nodes": ["pw0992sqf101n3.nsw.education", "pw0991sqf101n2.nsw.education", "pw0992sqf101n1.nsw.education"]}, "sqf102_preprod_predetnsw.win": {"service_name": "openmetrics_sqf102", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf102", "domain": "predetnsw.win"}, "nodes": ["qw0992sqf102n1.nsw.education", "qw0991sqf102n2.nsw.education"]}, "sqf104_preprod_detnsw.win": {"service_name": "openmetrics_sqf104", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "12x5", "os_manager": "windows", "app_id": "sqf104", "domain": "detnsw.win"}, "nodes": ["qw0991sqf104n1.nsw.education"]}, "sqf105_preprod_predetnsw.win": {"service_name": "openmetrics_sqf105", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "12x5", "os_manager": "windows", "app_id": "sqf105", "domain": "predetnsw.win"}, "nodes": ["qw0991sqf105n1.nsw.education", "qw0992sqf105n2.nsw.education"]}, "sqf106_preprod_predetnsw.win": {"service_name": "openmetrics_sqf106", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf106", "domain": "predetnsw.win"}, "nodes": ["qw0992sqf106n1.nsw.education", "qw0991sqf106n2.nsw.education"]}, "sqf106_prod_detnsw.win": {"service_name": "openmetrics_sqf106", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf106", "domain": "detnsw.win"}, "nodes": ["pw0991sqf106n1.nsw.education"]}, "sqf107_preprod_predetnsw.win": {"service_name": "openmetrics_sqf107", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf107", "domain": "predetnsw.win"}, "nodes": ["qw0991sqf107n1.nsw.education", "qw0992sqf107n2.nsw.education"]}, "sqf107_prod_detnsw.win": {"service_name": "openmetrics_sqf107", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf107", "domain": "detnsw.win"}, "nodes": ["pw0992sqf107n2.nsw.education", "pw0991sqf107n3.nsw.education", "pw0991sqf107n1.nsw.education"]}, "sqf108_preprod_predetnsw.win": {"service_name": "openmetrics_sqf108", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf108", "domain": "predetnsw.win"}, "nodes": ["qw0992sqf108n1.nsw.education", "qw0991sqf108n2.nsw.education"]}, "sqf110_prod_detnsw.win": {"service_name": "openmetrics_sqf110", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf110", "domain": "detnsw.win"}, "nodes": ["pw0992sqf110n2.nsw.education", "pw0991sqf110n3.nsw.education", "pw0991sqf110n1.nsw.education"]}, "sqf111_prod_detnsw.win": {"service_name": "openmetrics_sqf111", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqf111", "domain": "detnsw.win"}, "nodes": ["pw0991sqf111n2.nsw.education", "pw0992sqf111n1.nsw.education", "pw0992sqf111n3.nsw.education"]}, "sqfc01_dev_devdetnsw.win": {"service_name": "openmetrics_sqfc01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfc01", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqfc01n1.dev.nsw.education"]}, "sqfc01_prod_detnsw.win": {"service_name": "openmetrics_sqfc01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x5", "os_manager": "windows", "app_id": "sqfc01", "domain": "detnsw.win"}, "nodes": ["pw0991sqfc01h1.nsw.education"]}, "sqfc10_dev_devdetnsw.win": {"service_name": "openmetrics_sqfc10", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfc10", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqfc10n1.dev.nsw.education"]}, "sqfc10_prod_detnsw.win": {"service_name": "openmetrics_sqfc10", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x5", "os_manager": "windows", "app_id": "sqfc10", "domain": "detnsw.win"}, "nodes": ["pw0991sqfc10m1.nsw.education", "pw0991sqfc10m2.nsw.education", "pw0991sqfc10m3.nsw.education"]}, "sqfc2_prod_detnsw.win": {"service_name": "openmetrics_sqfc2", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x5", "os_manager": "windows", "app_id": "sqfc2", "domain": "detnsw.win"}, "nodes": ["pw0991sqfc20h1.nsw.education", "pw0991sqfc21h1.nsw.education", "pw0991sqfc22h1.nsw.education", "pw0991sqfc23h1.nsw.education", "pw0991sqfc24h1.nsw.education"]}, "sqfc3_prod_detnsw.win": {"service_name": "openmetrics_sqfc3", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x5", "os_manager": "windows", "app_id": "sqfc3", "domain": "detnsw.win"}, "nodes": ["pw0991sqfc30h1.nsw.education", "pw0991sqfc31h1.nsw.education", "pw0991sqfc32h1.nsw.education", "pw0991sqfc33h1.nsw.education", "pw0991sqfc34h1.nsw.education", "pw0991sqfc35h1.nsw.education", "pw0991sqfc36h1.nsw.education"]}, "sqfc9_prod_detnsw.win": {"service_name": "openmetrics_sqfc9", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "12x5", "os_manager": "windows", "app_id": "sqfc9", "domain": "detnsw.win"}, "nodes": ["pw0991sqfc90h1.nsw.education"]}, "sqfe01_prod_detnsw.win": {"service_name": "openmetrics_sqfe01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe01", "domain": "detnsw.win"}, "nodes": ["pw0992sqfe01n3.nsw.education", "pw0991sqfe01n2.nsw.education", "pw0992sqfe01n1.nsw.education"]}, "sqfe02_preprod_predetnsw.win": {"service_name": "openmetrics_sqfe02", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "dirk logemann", "op_window": "12x5", "os_manager": "windows", "app_id": "sqfe02", "domain": "predetnsw.win"}, "nodes": ["qw0991sqfe02n2.pre.nsw.education", "qw0992sqfe02n1.pre.nsw.education", "qw0992sqfe02n3.pre.nsw.education"]}, "sqfe02_prod_detnsw.win": {"service_name": "openmetrics_sqfe02", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe02", "domain": "detnsw.win"}, "nodes": ["pw0992sqfe02n3.nsw.education", "pw0992sqfe02n1.nsw.education", "pw0991sqfe02n2.nsw.education"]}, "sqfe03_preprod_predetnsw.win": {"service_name": "openmetrics_sqfe03", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe03", "domain": "predetnsw.win"}, "nodes": ["qw0992sqfe03n1.nsw.education", "qw0991sqfe03n2.nsw.education", "qw0992sqfe03n3.nsw.education"]}, "sqfe03_prod_detnsw.win": {"service_name": "openmetrics_sqfe03", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe03", "domain": "detnsw.win"}, "nodes": ["pw0992sqfe03n1.nsw.education", "pw0991sqfe03n2.nsw.education", "pw0992sqfe03n3.nsw.education"]}, "sqfe04_preprod_detnsw.win": {"service_name": "openmetrics_sqfe04", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe04", "domain": "detnsw.win"}, "nodes": ["qw0992sqfe04n1.nsw.education", "qw0992sqfe04n3.nsw.education", "qw0991sqfe04n2.nsw.education"]}, "sqfe05_prod_detnsw.win": {"service_name": "openmetrics_sqfe05", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe05", "domain": "detnsw.win"}, "nodes": ["pw0991sqfe05n3.nsw.education", "pw0991sqfe05n1.nsw.education", "pw0992sqfe05n2.nsw.education"]}, "sqfe06_prod_detnsw.win": {"service_name": "openmetrics_sqfe06", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x5", "os_manager": "unknown", "app_id": "sqfe06", "domain": "detnsw.win"}, "nodes": ["pw0991sqfe06n1.nsw.education", "pw0991sqfe06n3.nsw.education", "pw0992sqfe06n2.nsw.education"]}, "sqfe07_prod_detnsw.win": {"service_name": "openmetrics_sqfe07", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x5", "os_manager": "unknown", "app_id": "sqfe07", "domain": "detnsw.win"}, "nodes": ["pw0991sqfe07n1.nsw.education", "pw0991sqfe07n3.nsw.education", "pw0992sqfe07n2.nsw.education"]}, "sqfe09_preprod_predetnsw.win": {"service_name": "openmetrics_sqfe09", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe09", "domain": "predetnsw.win"}, "nodes": ["qw0991sqfe09n1.nsw.education", "qw0992sqfe09n2.nsw.education"]}, "sqfe20_prod_detnsw.win": {"service_name": "openmetrics_sqfe20", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfe20", "domain": "detnsw.win"}, "nodes": ["pw0991sqfe20n3.nsw.education", "pw0992sqfe20n2.nsw.education", "pw0991sqfe20n1.nsw.education"]}, "sqfe21_prod_detnsw.win": {"service_name": "openmetrics_sqfe21", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "12x5", "os_manager": "windows", "app_id": "sqfe21", "domain": "detnsw.win"}, "nodes": ["pw0992sqfe21h1.nsw.education"]}, "sqfe25_dev_devdetnsw.win": {"service_name": "openmetrics_sqfe25", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "viji kudanda", "op_window": "24x7", "os_manager": "unknown", "app_id": "sqfe25", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqfe25n1.dev.nsw.education"]}, "sqfe25_preprod_predetnsw.win": {"service_name": "openmetrics_sqfe25", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "viji kudanda", "op_window": "12x5", "os_manager": "unknown", "app_id": "sqfe25", "domain": "predetnsw.win"}, "nodes": ["qw0992sqfe25n1.pre.nsw.education"]}, "sqfe25_prod_detnsw.win": {"service_name": "openmetrics_sqfe25", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "viji kudanda", "op_window": "24x7", "os_manager": "unknown", "app_id": "sqfe25", "domain": "detnsw.win"}, "nodes": ["pw0991sqfe25n3.nsw.education", "pw0991sqfe25n1.nsw.education", "pw0992sqfe25n2.nsw.education"]}, "sqff_preprod_unknown": {"service_name": "openmetrics_sqff", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqff", "domain": "unknown"}, "nodes": ["qw0992sqffswh5.dbs.pre.det.nsw.edu.au", "qw0991sqffswh5.dbs.pre.det.nsw.edu.au"]}, "sqff_prod_detnsw.win": {"service_name": "openmetrics_sqff", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqff", "domain": "detnsw.win"}, "nodes": ["pw0992sqffswh1.dbs.det.nsw.edu.au", "pw0992sqsfswh1.dbs.det.nsw.edu.au", "pw0992sqpfswh1.dbs.det.nsw.edu.au", "pw0991sqpfswh1.dbs.det.nsw.edu.au", "pw0991sqffswh1.dbs.det.nsw.edu.au", "pw0991sqsfswh1.dbs.det.nsw.edu.au"]}, "sqff_prod_unknown": {"service_name": "openmetrics_sqff", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqff", "domain": "unknown"}, "nodes": ["pw0992sqffswh5.dbs.det.nsw.edu.au", "pw0991sqsfswh7.dbs.det.nsw.edu.au", "pw0991sqffswh5.dbs.det.nsw.edu.au"]}, "sqffsw_prod_detnsw.win": {"service_name": "openmetrics_sqffsw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqffsw", "domain": "detnsw.win"}, "nodes": ["pw0991sqffswh3.nsw.education", "pw0992sqffswh3.nsw.education"]}, "sqfl_preprod_detnsw.win": {"service_name": "openmetrics_sqfl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "is - databases sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqfl", "domain": "detnsw.win"}, "nodes": ["qw0991sqfl02n1.nsw.education", "qw0992sqfl03n2.nsw.education", "qw0991sqfl03n1.nsw.education", "qw0992sqfl01n2.nsw.education", "qw0991sqfl01n1.nsw.education", "qw0992sqfl02n2.nsw.education"]}, "sqld_prod_detnsw.win": {"service_name": "openmetrics_sqld", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqld", "domain": "detnsw.win"}, "nodes": ["pw0000sqlpe002.dbs.det.nsw.edu.au"]}, "sqlfsw_dev_devdetnsw.win": {"service_name": "openmetrics_sqlfsw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "is-db-sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlfsw", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqpfswh2.nsw.education", "dw0991sqpfswh2.nsw.education"]}, "sqlp_dev_devdetnsw.win": {"service_name": "openmetrics_sqlp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "mrinal vora", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqlp0001.nsw.education"]}, "sqlp11_dev_detnsw.win": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "detnsw.win"}, "nodes": ["dw0000lssql001.dbs.test.det.nsw.edu.au"]}, "sqlp11_dev_devdetnsw.win": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "devdetnsw.win"}, "nodes": ["dw0000lssql002.dbs.test.det.nsw.edu.au", "dw0991sqp001n1.dbs.dev.det.nsw.edu.au"]}, "sqlp11_preprod_predetnsw.win": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "predetnsw.win"}, "nodes": ["qw0992sqs002n1.dbs.pre.det.nsw.edu.au", "qw0991sqs001n1.dbs.pre.det.nsw.edu.au", "qw0000sqlqe015.hbm.det.nsw.edu.au"]}, "sqlp11_test_uatdetnsw.win": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqp002n1.dbs.dev.det.nsw.edu.au"]}, "sqlp11_prod_detnsw.win": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "detnsw.win"}, "nodes": ["pw0992sqf001n2.dbs.det.nsw.edu.au", "pw0000sqlpe128.dbs.det.nsw.edu.au", "pw0991sqf001n1.dbs.det.nsw.edu.au", "pw0000sqlpm024.dbs.det.nsw.edu.au"]}, "sqlp11_prod_unknown": {"service_name": "openmetrics_sqlp11", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlp11", "domain": "unknown"}, "nodes": ["pw0992sqs701n2.dbs.det.nsw.edu.au", "pw0991sqs701n3.dbs.det.nsw.edu.au", "pw0991sqs701n1.dbs.det.nsw.edu.au"]}, "sqlq4b_preprod_detnsw.win": {"service_name": "openmetrics_sqlq4b", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlq4b", "domain": "detnsw.win"}, "nodes": ["qw0000sqlqe009.apps.pre.det.nsw.edu.au"]}, "sqlq4b_prod_detnsw.win": {"service_name": "openmetrics_sqlq4b", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlq4b", "domain": "detnsw.win"}, "nodes": ["pw0000sqlpe111.apps.det.nsw.edu.au"]}, "sqlq7c_preprod_detnsw.win": {"service_name": "openmetrics_sqlq7c", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlq7c", "domain": "detnsw.win"}, "nodes": ["qw0991sqlqm005.pre.det.nsw.edu.au"]}, "sqls_prod_detnsw.win": {"service_name": "openmetrics_sqls", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqls", "domain": "detnsw.win"}, "nodes": ["pw0991sqs101n2.nsw.education", "pw0992sqs101n1.nsw.education", "pw0992sqs101n3.nsw.education", "pw0992sqsfswh2.nsw.education"]}, "sqlt_test_uatdetnsw.win": {"service_name": "openmetrics_sqlt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqlt", "domain": "uatdetnsw.win"}, "nodes": ["tw0000sqlte004.hbm.det.nsw.edu.au"]}, "sqmg_prod_detnsw.win": {"service_name": "openmetrics_sqmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqmg", "domain": "detnsw.win"}, "nodes": ["pw0991sqmgmth0.hbm.det.nsw.edu.au"]}, "sqmg_prod_unknown": {"service_name": "openmetrics_sqmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqmg", "domain": "unknown"}, "nodes": ["pw0991sqmgmth7.hbm.det.nsw.edu.au"]}, "sqmgmt_dev_devdetnsw.win": {"service_name": "openmetrics_sqmgmt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqmgmt", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqmgmth1.dev.nsw.education"]}, "sqmgmt_preprod_predetnsw.win": {"service_name": "openmetrics_sqmgmt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / enterprise database sql", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqmgmt", "domain": "predetnsw.win"}, "nodes": ["qw0472sqmgmth9.pre.nsw.education"]}, "sqmgmt_shared_detnsw.win": {"service_name": "openmetrics_sqmgmt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqmgmt", "domain": "detnsw.win"}, "nodes": ["pw0991sqmgmth1.nsw.education"]}, "sqmgmt_prod_detnsw.win": {"service_name": "openmetrics_sqmgmt", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "12x5", "os_manager": "windows", "app_id": "sqmgmt", "domain": "detnsw.win"}, "nodes": ["pw0472sqmgmth9.nsw.education"]}, "sqp03b_prod_detnsw.win": {"service_name": "openmetrics_sqp03b", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp03b", "domain": "detnsw.win"}, "nodes": ["pw0992sqp007n2.dbs.det.nsw.edu.au", "pw0000sqlpm004.dbs.det.nsw.edu.au", "pw0991sqp007n1.dbs.det.nsw.edu.au", "pw0991sqp007n3.dbs.det.nsw.edu.au", "pw0000sqlpm001.dbs.det.nsw.edu.au"]}, "sqp078_preprod_predetnsw.win": {"service_name": "openmetrics_sqp078", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp078", "domain": "predetnsw.win"}, "nodes": ["qw0991sqp001n1.dbs.pre.det.nsw.edu.au", "qw0991sqp002n2.dbs.pre.det.nsw.edu.au"]}, "sqp07a_dev_devdetnsw.win": {"service_name": "openmetrics_sqp07a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp07a", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqp003n4.dbs.dev.det.nsw.edu.au"]}, "sqp101_dev_devdetnsw.win": {"service_name": "openmetrics_sqp101", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jude d'souza", "op_window": "12x5", "os_manager": "windows", "app_id": "sqp101", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqp101n1.nsw.education", "dw0992sqp101n3.nsw.education", "dw0991sqp101n2.nsw.education"]}, "sqp101_preprod_predetnsw.win": {"service_name": "openmetrics_sqp101", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "12x5", "os_manager": "windows", "app_id": "sqp101", "domain": "predetnsw.win"}, "nodes": ["qw0992sqp101n1.nsw.education", "qw0991sqp101n2.nsw.education"]}, "sqp102_preprod_predetnsw.win": {"service_name": "openmetrics_sqp102", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "12x5", "os_manager": "windows", "app_id": "sqp102", "domain": "predetnsw.win"}, "nodes": ["qw0991sqp102n1.nsw.education", "qw0992sqp102n2.nsw.education"]}, "sqp103_dev_devdetnsw.win": {"service_name": "openmetrics_sqp103", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp103", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqp103n1.nsw.education"]}, "sqp103_test_uatdetnsw.win": {"service_name": "openmetrics_sqp103", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp103", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp103n1.nsw.education"]}, "sqp104_dev_devdetnsw.win": {"service_name": "openmetrics_sqp104", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp104", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqp104n1.nsw.education"]}, "sqp106_test_uatdetnsw.win": {"service_name": "openmetrics_sqp106", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp106", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp106n1.nsw.education"]}, "sqp108_test_uatdetnsw.win": {"service_name": "openmetrics_sqp108", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "a<PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp108", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp108n1.nsw.education"]}, "sqp108_prod_detnsw.win": {"service_name": "openmetrics_sqp108", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "a<PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp108", "domain": "detnsw.win"}, "nodes": ["pw0992sqp108n2.nsw.education", "pw0991sqp108n3.nsw.education", "pw0991sqp108n1.nsw.education"]}, "sqp109_dev_devdetnsw.win": {"service_name": "openmetrics_sqp109", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "a<PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp109", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqp109n1.nsw.education", "dw0472sqp10901.nsw.education"]}, "sqp109_preprod_predetnsw.win": {"service_name": "openmetrics_sqp109", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / enterprise database sql", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqp109", "domain": "predetnsw.win"}, "nodes": ["qw0472sqp10901.nsw.education"]}, "sqp109_test_uatdetnsw.win": {"service_name": "openmetrics_sqp109", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "a<PERSON><PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp109", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp109n1.nsw.education", "tw0472sqp10901.nsw.education"]}, "sqp109_prod_detnsw.win": {"service_name": "openmetrics_sqp109", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp109", "domain": "detnsw.win"}, "nodes": ["pw0991sqp109n2.nsw.education", "pw0992sqp109n1.nsw.education", "pw0992sqp109n3.nsw.education", "pw0472sqp109n1.nsw.education", "pw0472sqp109n3.nsw.education", "pw0472sqp109n2.nsw.education", "pw0472sqp10901.nsw.education"]}, "sqp111_dev_devdetnsw.win": {"service_name": "openmetrics_sqp111", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "<PERSON><PERSON> nguyen", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp111", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqp111n1.nsw.education"]}, "sqp112_dev_devdetnsw.win": {"service_name": "openmetrics_sqp112", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "<PERSON><PERSON> nguyen", "op_window": "12x5", "os_manager": "windows", "app_id": "sqp112", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqp112n1.nsw.education"]}, "sqp112_test_uatdetnsw.win": {"service_name": "openmetrics_sqp112", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "<PERSON><PERSON> nguyen", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp112", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp112n1.nsw.education"]}, "sqp113_test_uatdetnsw.win": {"service_name": "openmetrics_sqp113", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "<PERSON><PERSON> nguyen", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp113", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqp113n1.nsw.education"]}, "sqp114_test_uatdetnsw.win": {"service_name": "openmetrics_sqp114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp114", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp114n1.nsw.education", "tw0992sqp114h1.nsw.education"]}, "sqp115_test_uatdetnsw.win": {"service_name": "openmetrics_sqp115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp115", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqp115n1.nsw.education"]}, "sqp116_test_uatdetnsw.win": {"service_name": "openmetrics_sqp116", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp116", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp116n1.nsw.education"]}, "sqp118_test_uatdetnsw.win": {"service_name": "openmetrics_sqp118", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp118", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp118n1.nsw.education"]}, "sqp119_test_uatdetnsw.win": {"service_name": "openmetrics_sqp119", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqp119", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqp119n1.nsw.education"]}, "sqp120_dev_devdetnsw.win": {"service_name": "openmetrics_sqp120", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "it infraserv database sql", "op_window": "12x5", "os_manager": "windows", "app_id": "sqp120", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqp120n1.nsw.education"]}, "sqpe01_dev_devdetnsw.win": {"service_name": "openmetrics_sqpe01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "v<PERSON><PERSON><PERSON><PERSON><PERSON> kuda<PERSON>", "op_window": "12x5", "os_manager": "windows", "app_id": "sqpe01", "domain": "devdetnsw.win"}, "nodes": ["dw0991sqpe01n3.dev.nsw.education", "dw0991sqpe01n1.dev.nsw.education"]}, "sqpe01_preprod_predetnsw.win": {"service_name": "openmetrics_sqpe01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "v<PERSON><PERSON><PERSON><PERSON><PERSON> kuda<PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpe01", "domain": "predetnsw.win"}, "nodes": ["qw0992sqpe01n1.pre.nsw.education", "qw0991sqpe01n2.pre.nsw.education", "qw0992sqpe01n3.pre.nsw.education"]}, "sqpe01_test_uatdetnsw.win": {"service_name": "openmetrics_sqpe01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "v<PERSON><PERSON><PERSON><PERSON><PERSON> kuda<PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpe01", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqpe01n1.test.nsw.education", "tw0992sqpe01n3.test.nsw.education", "tw0991sqpe01n2.test.nsw.education"]}, "sqpe01_prod_detnsw.win": {"service_name": "openmetrics_sqpe01", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "v<PERSON><PERSON><PERSON><PERSON><PERSON> kuda<PERSON>", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpe01", "domain": "detnsw.win"}, "nodes": ["pw0992sqpe01n1.nsw.education", "pw0992sqpe01n3.nsw.education", "pw0991sqpe01n2.nsw.education"]}, "sqpe20_test_uatdetnsw.win": {"service_name": "openmetrics_sqpe20", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpe20", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqpe20n1.nsw.education"]}, "sqpe21_test_uatdetnsw.win": {"service_name": "openmetrics_sqpe21", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpe21", "domain": "uatdetnsw.win"}, "nodes": ["tw0992sqpe21n1.nsw.education"]}, "sqpfsw_prod_detnsw.win": {"service_name": "openmetrics_sqpfsw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqpfsw", "domain": "detnsw.win"}, "nodes": ["pw0991sqpfswh3.nsw.education", "pw0992sqpfswh3.nsw.education"]}, "sqpl_test_uatdetnsw.win": {"service_name": "openmetrics_sqpl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / enterprise database sql", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqpl", "domain": "uatdetnsw.win"}, "nodes": ["tw0472sqpl0001.test.nsw.education"]}, "sqs0_dev_devdetnsw.win": {"service_name": "openmetrics_sqs0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs0", "domain": "devdetnsw.win"}, "nodes": ["dw0992sqp002n1.dbs.dev.det.nsw.edu.au"]}, "sqs0_preprod_predetnsw.win": {"service_name": "openmetrics_sqs0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs0", "domain": "predetnsw.win"}, "nodes": ["qw0992sqs001n2.dbs.pre.det.nsw.edu.au"]}, "sqs0_test_uatdetnsw.win": {"service_name": "openmetrics_sqs0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs0", "domain": "uatdetnsw.win"}, "nodes": ["tw0991sqp001n1.dbs.dev.det.nsw.edu.au"]}, "sqs0_prod_detnsw.win": {"service_name": "openmetrics_sqs0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs0", "domain": "detnsw.win"}, "nodes": ["pw0992sqs001n2.dbs.det.nsw.edu.au", "pw0991sqs001n1.dbs.det.nsw.edu.au"]}, "sqs038_prod_detnsw.win": {"service_name": "openmetrics_sqs038", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs038", "domain": "detnsw.win"}, "nodes": ["pw0992sqs006n3.dbs.det.nsw.edu.au", "pw0992sqs006n1.dbs.det.nsw.edu.au", "pw0991sqs006n2.dbs.det.nsw.edu.au"]}, "sqs103_preprod_detnsw.win": {"service_name": "openmetrics_sqs103", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs103", "domain": "detnsw.win"}, "nodes": ["qw0991sqs103n1.nsw.education", "qw0992sqs103n2.nsw.education"]}, "sqs104_prod_detnsw.win": {"service_name": "openmetrics_sqs104", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs104", "domain": "detnsw.win"}, "nodes": ["pw0991sqs104n2.nsw.education", "pw0992sqs104n1.nsw.education", "pw0992sqs104n3.nsw.education"]}, "sqs105_preprod_predetnsw.win": {"service_name": "openmetrics_sqs105", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "danielle geor<PERSON>te", "op_window": "12x5", "os_manager": "windows", "app_id": "sqs105", "domain": "predetnsw.win"}, "nodes": ["qw0991sqs105n2.nsw.education", "qw0992sqs105n1.nsw.education"]}, "sqs105_prod_detnsw.win": {"service_name": "openmetrics_sqs105", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs105", "domain": "detnsw.win"}, "nodes": ["pw0992sqs105n2.nsw.education", "pw0991sqs105n3.nsw.education", "pw0991sqs105n1.nsw.education"]}, "sqs106_prod_detnsw.win": {"service_name": "openmetrics_sqs106", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs106", "domain": "detnsw.win"}, "nodes": ["pw0991sqs106n3.nsw.education", "pw0992sqs106n2.nsw.education", "pw0991sqs106n1.nsw.education"]}, "sqs112_prod_detnsw.win": {"service_name": "openmetrics_sqs112", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "danielle geor<PERSON>te", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs112", "domain": "detnsw.win"}, "nodes": ["pw0992sqs112n2.nsw.education", "pw0991sqs112n3.nsw.education", "pw0991sqs112n1.nsw.education"]}, "sqs114_preprod_detnsw.win": {"service_name": "openmetrics_sqs114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs114", "domain": "detnsw.win"}, "nodes": ["qw0992sqs114h1.nsw.education"]}, "sqs114_prod_detnsw.win": {"service_name": "openmetrics_sqs114", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs114", "domain": "detnsw.win"}, "nodes": ["pw0991sqs114h1.nsw.education", "pw0991sqs114h2.nsw.education"]}, "sqs115_prod_detnsw.win": {"service_name": "openmetrics_sqs115", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs115", "domain": "detnsw.win"}, "nodes": ["pw0992sqs115h1.nsw.education"]}, "sqs116_preprod_detnsw.win": {"service_name": "openmetrics_sqs116", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / enterprise database sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs116", "domain": "detnsw.win"}, "nodes": ["qw0472sqs116n1.nsw.education", "qw0472sqs116n2.nsw.education"]}, "sqs116_test_uatdetnsw.win": {"service_name": "openmetrics_sqs116", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / enterprise database sql", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqs116", "domain": "uatdetnsw.win"}, "nodes": ["tw0472sqs116n1.test.nsw.education"]}, "sqs116_prod_detnsw.win": {"service_name": "openmetrics_sqs116", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs116", "domain": "detnsw.win"}, "nodes": ["pw0472sqs116n1.nsw.education", "pw0472sqs116n2.nsw.education"]}, "sqs117_preprod_predetnsw.win": {"service_name": "openmetrics_sqs117", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / enterprise database sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs117", "domain": "predetnsw.win"}, "nodes": ["qw0472sqs117h1.pre.nsw.education"]}, "sqs118_preprod_predetnsw.win": {"service_name": "openmetrics_sqs118", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "itd-is / enterprise database sql", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqs118", "domain": "predetnsw.win"}, "nodes": ["qw0472sqs118h1.pre.nsw.education"]}, "sqs118_prod_detnsw.win": {"service_name": "openmetrics_sqs118", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / enterprise database sql", "op_window": "24x7", "os_manager": "windows", "app_id": "sqs118", "domain": "detnsw.win"}, "nodes": ["pw0472sqs118h1.nsw.education"]}, "sqse02_preprod_detnsw.win": {"service_name": "openmetrics_sqse02", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "dirk logemann", "op_window": "on_demand", "os_manager": "windows", "app_id": "sqse02", "domain": "detnsw.win"}, "nodes": ["qw0472sqse02n1.nsw.education", "qw0472sqse02n2.nsw.education"]}, "sqse02_prod_detnsw.win": {"service_name": "openmetrics_sqse02", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "dirk logemann", "op_window": "24x7", "os_manager": "windows", "app_id": "sqse02", "domain": "detnsw.win"}, "nodes": ["pw0472sqse02n1.nsw.education", "pw0472sqse02n2.nsw.education"]}, "sqsfsw_prod_detnsw.win": {"service_name": "openmetrics_sqsfsw", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "jude d'souza", "op_window": "24x7", "os_manager": "windows", "app_id": "sqsfsw", "domain": "detnsw.win"}, "nodes": ["pw0992sqsfswh3.nsw.education", "pw0991sqsfswh3.nsw.education"]}, "srpa_dev_devdetnsw.win": {"service_name": "openmetrics_srpa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "edconnect business process automation", "op_window": "24x7", "os_manager": "windows", "app_id": "srpa", "domain": "devdetnsw.win"}, "nodes": ["dw0992srpa0001.nsw.education"]}, "srpa_prod_detnsw.win": {"service_name": "openmetrics_srpa", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "edconnect business process automation", "op_window": "24x7", "os_manager": "windows", "app_id": "srpa", "domain": "detnsw.win"}, "nodes": ["pw0992srpa0001.nsw.education", "pw0992srpa0002.nsw.education", "pw0991srpa0002.nsw.education", "pw0991srpa0001.nsw.education"]}, "sshr_prod_detnsw.win": {"service_name": "openmetrics_sshr", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "sshr", "op_window": "24x7", "os_manager": "windows", "app_id": "sshr", "domain": "detnsw.win"}, "nodes": ["pw0000sshrwts1.nsw.education", "pw0000astpcrs1.apps.det.nsw.edu.au", "pw0991sshrwts1.nsw.education"]}, "ssis_preprod_detnsw.win": {"service_name": "openmetrics_ssis", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ssis", "domain": "detnsw.win"}, "nodes": ["qw0000ssisqe01.apps.pre.det.nsw.edu.au", "qw0000ssisqe02.apps.pre.det.nsw.edu.au"]}, "ssis_prod_detnsw.win": {"service_name": "openmetrics_ssis", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "ssis", "domain": "detnsw.win"}, "nodes": ["pw0000ssispe01.apps.det.nsw.edu.au"]}, "stmg_prod_detnsw.win": {"service_name": "openmetrics_stmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "@it storage", "op_window": "24x7", "os_manager": "windows", "app_id": "stmg", "domain": "detnsw.win"}, "nodes": ["pw0991stmgut01.nsw.education", "pw0992stmgut01.nsw.education"]}, "stmg_prod_unset": {"service_name": "openmetrics_stmg", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "storage team", "op_window": "24x7", "os_manager": "penguins", "app_id": "stmg", "domain": "unset"}, "nodes": ["pl0992stmgsn01.nsw.education", "pl0991stmgbs01.nsw.education"]}, "stpcc_prod_detnsw.win": {"service_name": "openmetrics_stpcc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "darryl bowden", "op_window": "24x7", "os_manager": "unset", "app_id": "stpcc", "domain": "detnsw.win"}, "nodes": ["pw0472stpcc001.nsw.education", "pw0472stpcc002.nsw.education"]}, "swrepo_prod_detnsw.win": {"service_name": "openmetrics_swrepo", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac desktop", "op_window": "24x7", "os_manager": "windows", "app_id": "swrepo", "domain": "detnsw.win"}, "nodes": ["pw0991swrepo01.nsw.education", "pw0992swrepo02.nsw.education"]}, "swstes_dev_workgroup": {"service_name": "openmetrics_swstes", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "<PERSON><PERSON><PERSON> na<PERSON>i", "op_window": "24x7", "os_manager": "penguins", "app_id": "swstes"}, "nodes": ["dl0475swstes01.nsw.education"]}, "swstes_test_workgroup": {"service_name": "openmetrics_swstes", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "<PERSON><PERSON><PERSON> na<PERSON>i", "op_window": "24x7", "os_manager": "penguins", "app_id": "swstes"}, "nodes": ["tl0475swstest01.nsw.education"]}, "syslog_dev_workgroup": {"service_name": "openmetrics_syslog", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "syslog"}, "nodes": ["dl0991syslog01.nsw.education"]}, "syslog_shared_workgroup": {"service_name": "openmetrics_syslog", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "syslog"}, "nodes": ["pl0991syslog01.nsw.education", "pl0992syslog01.nsw.education"]}, "syss_test_unset": {"service_name": "openmetrics_syss", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "linux team", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "syss", "domain": "unset"}, "nodes": ["tl0991syss0001.netmon.det.nsw.edu.au"]}, "syss_prod_unset": {"service_name": "openmetrics_syss", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "linux team", "op_window": "24x7", "os_manager": "penguins", "app_id": "syss", "domain": "unset"}, "nodes": ["pl0991syss0001.netmon.det.nsw.edu.au", "pl0992syss0001.netmon.det.nsw.edu.au"]}, "t4sc_preprod_detnsw.win": {"service_name": "openmetrics_t4sc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "t4sc", "domain": "detnsw.win"}, "nodes": ["qw0000t4scm001.vdr.pre.det.nsw.edu.au"]}, "t4sc_test_uatdetnsw.win": {"service_name": "openmetrics_t4sc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "t4sc", "domain": "uatdetnsw.win"}, "nodes": ["tw0000t4scm001.apps.test.det.nsw.edu.au"]}, "t4sc_prod_detnsw.win": {"service_name": "openmetrics_t4sc", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "t4sc", "domain": "detnsw.win"}, "nodes": ["pw0000t4scm001.vdr.det.nsw.edu.au"]}, "tifv_prod_detnsw.win": {"service_name": "openmetrics_tifv", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "tifv", "domain": "detnsw.win"}, "nodes": ["pw0000tifvms01.apps.det.nsw.edu.au"]}, "tism_prod_detnsw.win": {"service_name": "openmetrics_tism", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "tism", "domain": "detnsw.win"}, "nodes": ["pw0000tismtm01.appu.det.nsw.edu.au"]}, "tm1a_dev_uatdetnsw.win": {"service_name": "openmetrics_tm1a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "tm1a", "domain": "uatdetnsw.win"}, "nodes": ["tw0000tm1ap001.apps.test.det.nsw.edu.au"]}, "tm1a_prod_detnsw.win": {"service_name": "openmetrics_tm1a", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "tm1a", "domain": "detnsw.win"}, "nodes": ["pw0000tm1ap001.apps.det.nsw.edu.au"]}, "lutl_test_workgroup": {"service_name": "openmetrics_lutl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "lutl"}, "nodes": ["tl0991util42.nsw.education"]}, "lutl_prod_workgroup": {"service_name": "openmetrics_lutl", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "default_op_window_value", "os_manager": "penguins", "app_id": "lutl"}, "nodes": ["pl0991util42.nsw.education"]}, "util_dev_dev.uc.det.nsw.edu.au": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "dev.uc.det.nsw.edu.au"}, "nodes": ["dw0992util0501.messaging.test.det.nsw.edu.au"]}, "util_dev_devext.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "devext.win"}, "nodes": ["dw0992util0301.svcs.test.det.nsw.edu.au"]}, "util_dev_devidm.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "devidm.win"}, "nodes": ["dw0992util0201.svcs.test.det.nsw.edu.au"]}, "util_preprod_predetnsw.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "predetnsw.win"}, "nodes": ["qw0992util0101.ad.pre.det.nsw.edu.au"]}, "util_preprod_preext.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "preext.win"}, "nodes": ["qw0992util0301.svcs.pre.det.nsw.edu.au"]}, "util_preprod_preidm.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "preidm.win"}, "nodes": ["qw0992util0201.svcs.pre.det.nsw.edu.au"]}, "util_preprod_unknown": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "unknown"}, "nodes": ["qw0992util0501.messaging.pre.det.nsw.edu.au"]}, "util_test_uatext.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "uatext.win"}, "nodes": ["tw0992util0301.svcs.test.det.nsw.edu.au"]}, "util_test_uatidm.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "uatidm.win"}, "nodes": ["tw0992util0201.svcs.test.det.nsw.edu.au"]}, "util_test_unknown": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "unknown"}, "nodes": ["tw0992util0501.messaging.test.det.nsw.edu.au", "tw0992util0101.ad.test.det.nsw.edu.au"]}, "util_prod_detnsw.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "detnsw.win"}, "nodes": ["pw0992util0102.ad.det.nsw.edu.au", "pw0992util0101.ad.det.nsw.edu.au", "pw0992util0103.ad.det.nsw.edu.au", "pw0992util0104.ad.det.nsw.edu.au", "pw0991util0101.ad.det.nsw.edu.au", "pw0991util0102.ad.det.nsw.edu.au"]}, "util_prod_ext.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "ext.win"}, "nodes": ["pw0992util0301.svcs.det.nsw.edu.au"]}, "util_prod_idm.win": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "util", "domain": "idm.win"}, "nodes": ["pw0992util0201.svcs.det.nsw.edu.au", "pw0992util0202.svcs.det.nsw.edu.au"]}, "util_prod_unknown": {"service_name": "openmetrics_util", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "util", "domain": "unknown"}, "nodes": ["pw0992util0501.messaging.det.nsw.edu.au"]}, "vcutil_test_workgroup": {"service_name": "openmetrics_vcutil", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac - vc team", "op_window": "24x7", "os_manager": "windows", "app_id": "vcutil"}, "nodes": ["tw0991vcutil01.nsw.education"]}, "vcutil_prod_workgroup": {"service_name": "openmetrics_vcutil", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac - vc team", "op_window": "24x7", "os_manager": "windows", "app_id": "vcutil"}, "nodes": ["pw0991vcutil01.nsw.education", "pw0992vcutil02.nsw.education"]}, "vdibk_prod_detnsw.win": {"service_name": "openmetrics_vdibk", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "it desktop platforms - config mgr", "op_window": "12x5", "os_manager": "windows", "app_id": "vdibk", "domain": "detnsw.win"}, "nodes": ["pw0991vdibk001.nsw.education"]}, "viut_preprod_uatdetnsw.win": {"service_name": "openmetrics_viut", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "viut", "domain": "uatdetnsw.win"}, "nodes": ["qw0000viutl001.vi.det.nsw.edu.au"]}, "vlbe_preprod_detnsw.win": {"service_name": "openmetrics_vlbe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "vlbe", "domain": "detnsw.win"}, "nodes": ["qw0000vlbews01.apps.pre.det.nsw.edu.au"]}, "vlbe_prod_detnsw.win": {"service_name": "openmetrics_vlbe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "vlbe", "domain": "detnsw.win"}, "nodes": ["pw0000vlbews01.apps.det.nsw.edu.au"]}, "vlfe_preprod_detnsw.win": {"service_name": "openmetrics_vlfe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "vlfe", "domain": "detnsw.win"}, "nodes": ["qw0000vlfeis01.apps.pre.det.nsw.edu.au"]}, "vlfe_prod_detnsw.win": {"service_name": "openmetrics_vlfe", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "vlfe", "domain": "detnsw.win"}, "nodes": ["pw0000vlfeis01.apps.det.nsw.edu.au"]}, "wacd_shared_detnsw.win": {"service_name": "openmetrics_wacd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wacd", "domain": "detnsw.win"}, "nodes": ["pw0991wacd0001.nsw.education", "pw0992wacd0001.nsw.education"]}, "wamd_shared_detnsw.win": {"service_name": "openmetrics_wamd", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wamd", "domain": "detnsw.win"}, "nodes": ["pw0992wamd0001.nsw.education", "pw0991wamd0001.nsw.education"]}, "wame_test_uatdetnsw.win": {"service_name": "openmetrics_wame", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac desktop platforms", "op_window": "24x7", "os_manager": "windows", "app_id": "wame", "domain": "uatdetnsw.win"}, "nodes": []}, "wclas_dev_.uatdetnsw.win": {"service_name": "openmetrics_wclas", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "na", "op_window": "24x7", "os_manager": "unset", "app_id": "wclas", "domain": ".uatdetnsw.win"}, "nodes": ["dw0000ewdbpoc1.dbs.test.det.nsw.edu.au"]}, "web0_test_uatdetnsw.win": {"service_name": "openmetrics_web0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "web0", "domain": "uatdetnsw.win"}, "nodes": ["tw0992web00101.ad.test.det.nsw.edu.au", "tw0991web00101.ad.test.det.nsw.edu.au", "tw0991webc0101.ad.test.det.nsw.edu.au"]}, "web0_prod_detnsw.win": {"service_name": "openmetrics_web0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "windows", "app_id": "web0", "domain": "detnsw.win"}, "nodes": ["pw0992web00101.ad.det.nsw.edu.au", "pw0991webc0101.ad.det.nsw.edu.au"]}, "web0_prod_unknown": {"service_name": "openmetrics_web0", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "web0", "domain": "unknown"}, "nodes": ["pw0992web00701.ad.det.nsw.edu.au", "pw0991web00701.ad.det.nsw.edu.au"]}, "wlceds_prod_detnsw.win": {"service_name": "openmetrics_wlceds", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is-ddac-dp-edge", "op_window": "24x7", "os_manager": "windows", "app_id": "wlceds", "domain": "detnsw.win"}, "nodes": ["pw0991wlceds01.nsw.education", "pw0991wlceds02.nsw.education"]}, "wpmp_shared_detnsw.win": {"service_name": "openmetrics_wpmp", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "is-ip-windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wpmp", "domain": "detnsw.win"}, "nodes": ["pw0991wpmpws01.nsw.education", "pw0992wpmpws01.nsw.education"]}, "wscan_dev_workgroup": {"service_name": "openmetrics_wscan", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "dev", "support_team": "luke meijer", "op_window": "12x5", "os_manager": "windows", "app_id": "wscan"}, "nodes": ["dw0991wscan001.dev.nsw.education"]}, "wscan_shared_workgroup": {"service_name": "openmetrics_wscan", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "shared", "support_team": "luke meijer", "op_window": "24x7", "os_manager": "windows", "app_id": "wscan"}, "nodes": ["pw0991wscan002.nsw.education"]}, "wscan_prod_workgroup": {"service_name": "openmetrics_wscan", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "luke meijer", "op_window": "12x5", "os_manager": "windows", "app_id": "wscan"}, "nodes": ["pw0991wscan001.nsw.education"]}, "wsus_preprod_uatdetnsw.win": {"service_name": "openmetrics_wsus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "preprod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wsus", "domain": "uatdetnsw.win"}, "nodes": ["qw0000wsus0001.hbm.det.nsw.edu.au"]}, "wsus_prod_detnsw.win": {"service_name": "openmetrics_wsus", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wsus", "domain": "detnsw.win"}, "nodes": ["pw0992wsus0001.hbm.det.nsw.edu.au", "pw0991wsus0001.hbm.det.nsw.edu.au"]}, "wsus83_prod_detnsw.win": {"service_name": "openmetrics_wsus83", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "ddac", "op_window": "24x7", "os_manager": "ddac", "app_id": "wsus83", "domain": "detnsw.win"}, "nodes": ["pw0991wsus0102.hbm.det.nsw.edu.au"]}, "wsus95_prod_detnsw.win": {"service_name": "openmetrics_wsus95", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wsus95", "domain": "detnsw.win"}, "nodes": ["pw0000wsusr001.apps.det.nsw.edu.au"]}, "wsuse_test_uatdetnsw.win": {"service_name": "openmetrics_wsuse", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "test", "support_team": "itd-is / ip - windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wsuse", "domain": "uatdetnsw.win"}, "nodes": ["tw0472wsuse001.nsw.education"]}, "wsuse_prod_detnsw.win": {"service_name": "openmetrics_wsuse", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "itd-is / ip - windows", "op_window": "24x7", "os_manager": "windows", "app_id": "wsuse", "domain": "detnsw.win"}, "nodes": ["pw0472wsuse001.nsw.education"]}, "zkps_prod_workgroup": {"service_name": "openmetrics_zkps", "metrics_path": "/metrics", "port": 11055, "meta": {"env": "prod", "support_team": "penguins", "op_window": "24x7", "os_manager": "penguins", "app_id": "zkps"}, "nodes": ["pu0991zkps0001.apps.det.nsw.edu.au"]}}}