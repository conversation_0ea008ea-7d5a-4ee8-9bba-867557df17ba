
terraform {
  required_providers {
    consul = {
      source = "hashicorp/consul"
      version = "2.11.0"
    }
  }
}

provider "consul" {
  address = "tl0992obscol01.nsw.education:8500"
  datacenter = "dc-cir-un-test"
  token = "a5515cc3-0ae7-ff5d-84f5-676b8088eabf"
}

variable "groups" {
  type = map(object({
    port = number
    service_name = string
    // metric_path = string
    // tags = list(string)
    nodes = list(string)
    meta = map(string)
  }))
}

module "promtarget" {
  for_each = var.groups
  source = "./promtarget"
  service = each.value
}

//resource "consul_service" "https" {
//  for_each = toset(var.https)
//  name = "https"
//  node = split(":", each.value)[0]
//  port = split(":", each.value)[1]
//  tags = ["https"]
//
//  // Don't health check as we are using the ssl-exporter to get health information, we dont need to double hit these
//  // servers.
//}
