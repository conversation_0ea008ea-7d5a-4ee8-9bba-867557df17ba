{"groups": {"obs-ssl": {"service_name": "https", "port": 443, "meta": {"cir_app_id": "obs", "env": "test", "metrics_path": "/probe"}, "nodes": ["console.mtm.apps.test.det.nsw.edu.au:443", "console.mtm.schools.apps.test.det.nsw.edu.au:443", "grafana.mtm.apps.test.det.nsw.edu.au:443"]}, "obs": {"service_name": "openmetrics", "port": 11055, "meta": {"cir_app_id": "obs", "env": "test"}, "nodes": ["tu0992tcdnd001.hbm.det.nsw.edu.au", "tu0992tcdnd002.hbm.det.nsw.edu.au", "tu0992tcdnd003.hbm.det.nsw.edu.au", "tu0992tcdnd004.hbm.det.nsw.edu.au", "tu0992ttdbd001.hbm.det.nsw.edu.au", "tu0992tedbd001.hbm.det.nsw.edu.au", "tu0991tedbd001.hbm.det.nsw.edu.au", "tu0992ttdbs001.hbm.det.nsw.edu.au", "tu0992tedbs001.hbm.det.nsw.edu.au", "tu0991tedbs001.hbm.det.nsw.edu.au", "tu0992tamc0001.apps.test.det.nsw.edu.au", "tu0992tadb0001.apps.test.det.nsw.edu.au", "tu0992tagf0001.apps.test.det.nsw.edu.au", "tl0992obscol01.nsw.education", "tl0992obscol02.nsw.education", "tl0992obscol03.nsw.education"]}, "osecp-ssl": {"service_name": "https", "port": 443, "metrics_path": "/probe", "meta": {"cir_app_id": "osecp", "env": "test"}, "nodes": ["console.apps.d0.ocp.dev.education.nsw.gov.au:443", "api.d0.ocp.dev.education.nsw.gov.au:6443"]}, "kfka": {"service_name": "openmetrics", "port": 11055, "meta": {"cir_app_id": "kfka", "env": "test"}, "nodes": ["dl0991kfkab0001.nsw.education", "dl0991kfkab0002.nsw.education", "dl0991kfkab201.nsw.education", "dl0992kfkab202.nsw.education", "dl0991kfkab205.nsw.education", "dl0992kfkab206.nsw.education", "dl0991kfkaz0001.nsw.education", "dl0991kfkac0001.nsw.education", "dl0991kfkac0002.nsw.education", "dl0992kfkac201.nsw.education", "dl0992kfkac202.nsw.education", "dl0991kfkar0001.nsw.education", "dl0991kfkar0002.nsw.education", "dl0991kfkad0001.nsw.education", "dl0991kfkad0002.nsw.education", "dl0991kfkaz201.nsw.education", "dl0992kfkaz202.nsw.education", "dl0475kfkaz203.nsw.education"]}, "cyberark-privilege-cloud": {"service_name": "openmetrics", "port": 11055, "meta": {"cir_app_id": "cybpam", "env": "test"}, "nodes": ["tw0991cybpam01.test.nsw.education", "tw0992cybpam01.test.nsw.education"]}, "networks_zabbix_dev": {"service_name": "openmetrics", "port": 11055, "meta": {"cir_app_id": "nwzb", "env": "dev"}, "nodes": ["dl0991nwzb0052.netmon.det.nsw.edu.au", "dl0991nwpg0051.netmon.det.nsw.edu.au", "dl0991nwzb0051.netmon.det.nsw.edu.au", "dl0991nwzb0053.netmon.det.nsw.edu.au", "dl0991nwgf0001.netmon.det.nsw.edu.au"]}, "brik": {"service_name": "rubrik_api", "port": 19407, "meta": {"cir_app_id": "brik", "env": "test"}, "nodes": ["tl0992obscol01.nsw.education", "tl0992obscol01.nsw.education"]}}}