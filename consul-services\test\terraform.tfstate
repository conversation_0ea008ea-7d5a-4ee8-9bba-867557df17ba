{"version": 4, "terraform_version": "1.4.5", "serial": 7013, "lineage": "86450ed7-1812-1dd0-a04a-ad1faaff898b", "outputs": {}, "resources": [{"module": "module.promtarget[\"brik\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tl0992obscol01.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol01.nsw.education", "datacenter": "dc-cir-un-test", "id": "tl0992obscol01-tl0992obscol01.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "brik", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tl0992obscol01", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"brik\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tl0992obscol01.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol01.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tl0992obscol01.nsw.education:19407", "interval": "15m", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "rubrik_api", "meta": {"cir_app_id": "brik", "env": "test"}, "name": "rubrik_api", "namespace": null, "node": "tl0992obscol01", "port": 19407, "service_id": "rubrik_api", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"cyberark-privilege-cloud\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tw0991cybpam01.test.nsw.education", "schema_version": 0, "attributes": {"address": "tw0991cybpam01.test.nsw.education", "datacenter": "dc-cir-un-test", "id": "tw0991cybpam01-tw0991cybpam01.test.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "cybpam", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tw0991cybpam01", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tw0992cybpam01.test.nsw.education", "schema_version": 0, "attributes": {"address": "tw0992cybpam01.test.nsw.education", "datacenter": "dc-cir-un-test", "id": "tw0992cybpam01-tw0992cybpam01.test.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "cybpam", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tw0992cybpam01", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"cyberark-privilege-cloud\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tw0991cybpam01.test.nsw.education", "schema_version": 0, "attributes": {"address": "tw0991cybpam01.test.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tw0991cybpam01.test.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "cybpam", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tw0991cybpam01", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tw0992cybpam01.test.nsw.education", "schema_version": 0, "attributes": {"address": "tw0992cybpam01.test.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tw0992cybpam01.test.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "cybpam", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tw0992cybpam01", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"kfka\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "dl0475kfkaz203.nsw.education", "schema_version": 0, "attributes": {"address": "dl0475kfkaz203.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0475kfkaz203-dl0475kfkaz203.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0475kfkaz203", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkab0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab0001.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkab0001-dl0991kfkab0001.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkab0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkab0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab0002.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkab0002-dl0991kfkab0002.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkab0002", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkab201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab201.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkab201-dl0991kfkab201.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkab201", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkab205.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab205.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkab205-dl0991kfkab205.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkab205", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkac0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkac0001.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkac0001-dl0991kfkac0001.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkac0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkac0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkac0002.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkac0002-dl0991kfkac0002.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkac0002", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkad0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkad0001.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkad0001-dl0991kfkad0001.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkad0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkad0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkad0002.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkad0002-dl0991kfkad0002.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkad0002", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkar0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkar0001.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkar0001-dl0991kfkar0001.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkar0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkar0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkar0002.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkar0002-dl0991kfkar0002.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkar0002", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkaz0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkaz0001.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkaz0001-dl0991kfkaz0001.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkaz0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991kfkaz201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkaz201.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0991kfkaz201-dl0991kfkaz201.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0991kfkaz201", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0992kfkab202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkab202.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0992kfkab202-dl0992kfkab202.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0992kfkab202", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0992kfkab206.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkab206.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0992kfkab206-dl0992kfkab206.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0992kfkab206", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0992kfkac201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkac201.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0992kfkac201-dl0992kfkac201.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0992kfkac201", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0992kfkac202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkac202.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0992kfkac202-dl0992kfkac202.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0992kfkac202", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0992kfkaz202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkaz202.nsw.education", "datacenter": "dc-cir-un-test", "id": "dl0992kfkaz202-dl0992kfkaz202.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "kfka", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "dl0992kfkaz202", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"kfka\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "dl0475kfkaz203.nsw.education", "schema_version": 0, "attributes": {"address": "dl0475kfkaz203.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0475kfkaz203.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0475kfkaz203", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkab0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab0001.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkab0001.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkab0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkab0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab0002.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkab0002.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkab0002", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkab201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab201.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkab201.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkab201", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkab205.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkab205.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkab205.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkab205", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkac0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkac0001.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkac0001.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkac0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkac0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkac0002.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkac0002.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkac0002", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkad0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkad0001.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkad0001.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkad0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkad0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkad0002.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkad0002.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkad0002", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkar0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkar0001.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkar0001.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkar0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkar0002.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkar0002.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkar0002.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkar0002", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkaz0001.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkaz0001.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkaz0001.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkaz0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991kfkaz201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0991kfkaz201.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991kfkaz201.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0991kfkaz201", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0992kfkab202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkab202.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0992kfkab202.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0992kfkab202", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0992kfkab206.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkab206.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0992kfkab206.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0992kfkab206", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0992kfkac201.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkac201.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0992kfkac201.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0992kfkac201", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0992kfkac202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkac202.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0992kfkac202.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0992kfkac202", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0992kfkaz202.nsw.education", "schema_version": 0, "attributes": {"address": "dl0992kfkaz202.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0992kfkaz202.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "kfka", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "dl0992kfkaz202", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"networks_zabbix_dev\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "dl0991nwgf0001.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwgf0001.netmon.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "dl0991nwgf0001-dl0991nwgf0001.netmon.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "nwzb", "env": "dev", "external-node": "true", "external-probe": "true"}, "name": "dl0991nwgf0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991nwpg0051.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwpg0051.netmon.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "dl0991nwpg0051-dl0991nwpg0051.netmon.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "nwzb", "env": "dev", "external-node": "true", "external-probe": "true"}, "name": "dl0991nwpg0051", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991nwzb0051.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0051.netmon.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "dl0991nwzb0051-dl0991nwzb0051.netmon.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "nwzb", "env": "dev", "external-node": "true", "external-probe": "true"}, "name": "dl0991nwzb0051", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991nwzb0052.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0052.netmon.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "dl0991nwzb0052-dl0991nwzb0052.netmon.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "nwzb", "env": "dev", "external-node": "true", "external-probe": "true"}, "name": "dl0991nwzb0052", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "dl0991nwzb0053.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0053.netmon.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "dl0991nwzb0053-dl0991nwzb0053.netmon.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "nwzb", "env": "dev", "external-node": "true", "external-probe": "true"}, "name": "dl0991nwzb0053", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"networks_zabbix_dev\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "dl0991nwgf0001.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwgf0001.netmon.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991nwgf0001.netmon.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "nwzb", "env": "dev"}, "name": "openmetrics", "namespace": null, "node": "dl0991nwgf0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991nwpg0051.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwpg0051.netmon.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991nwpg0051.netmon.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "nwzb", "env": "dev"}, "name": "openmetrics", "namespace": null, "node": "dl0991nwpg0051", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991nwzb0051.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0051.netmon.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991nwzb0051.netmon.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "nwzb", "env": "dev"}, "name": "openmetrics", "namespace": null, "node": "dl0991nwzb0051", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991nwzb0052.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0052.netmon.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991nwzb0052.netmon.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "nwzb", "env": "dev"}, "name": "openmetrics", "namespace": null, "node": "dl0991nwzb0052", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "dl0991nwzb0053.netmon.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "dl0991nwzb0053.netmon.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://dl0991nwzb0053.netmon.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "nwzb", "env": "dev"}, "name": "openmetrics", "namespace": null, "node": "dl0991nwzb0053", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"obs\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tl0992obscol01.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol01.nsw.education", "datacenter": "dc-cir-un-test", "id": "tl0992obscol01-tl0992obscol01.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tl0992obscol01", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tl0992obscol02.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol02.nsw.education", "datacenter": "dc-cir-un-test", "id": "tl0992obscol02-tl0992obscol02.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tl0992obscol02", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tl0992obscol03.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol03.nsw.education", "datacenter": "dc-cir-un-test", "id": "tl0992obscol03-tl0992obscol03.nsw.education", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tl0992obscol03", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0991tedbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0991tedbd001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0991tedbd001-tu0991tedbd001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0991tedbd001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0991tedbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0991tedbs001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0991tedbs001-tu0991tedbs001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0991tedbs001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tadb0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tadb0001.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tadb0001-tu0992tadb0001.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tadb0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tagf0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tagf0001.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tagf0001-tu0992tagf0001.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tagf0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tamc0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tamc0001.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tamc0001-tu0992tamc0001.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tamc0001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tcdnd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tcdnd001-tu0992tcdnd001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tcdnd001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tcdnd002.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd002.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tcdnd002-tu0992tcdnd002.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tcdnd002", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tcdnd003.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd003.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tcdnd003-tu0992tcdnd003.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tcdnd003", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tcdnd004.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd004.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tcdnd004-tu0992tcdnd004.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tcdnd004", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tedbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tedbd001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tedbd001-tu0992tedbd001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tedbd001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992tedbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tedbs001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992tedbs001-tu0992tedbs001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992tedbs001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992ttdbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992ttdbd001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992ttdbd001-tu0992ttdbd001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992ttdbd001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "tu0992ttdbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992ttdbs001.hbm.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "tu0992ttdbs001-tu0992ttdbs001.hbm.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "tu0992ttdbs001", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"obs\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "tl0992obscol01.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol01.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tl0992obscol01.nsw.education:11055", "interval": "15m", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tl0992obscol01", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tl0992obscol02.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol02.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tl0992obscol02.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tl0992obscol02", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tl0992obscol03.nsw.education", "schema_version": 0, "attributes": {"address": "tl0992obscol03.nsw.education", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tl0992obscol03.nsw.education:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tl0992obscol03", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0991tedbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0991tedbd001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0991tedbd001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0991tedbd001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0991tedbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0991tedbs001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0991tedbs001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0991tedbs001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tadb0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tadb0001.apps.test.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tadb0001.apps.test.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tadb0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tagf0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tagf0001.apps.test.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tagf0001.apps.test.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tagf0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tamc0001.apps.test.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tamc0001.apps.test.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tamc0001.apps.test.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tamc0001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tcdnd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tcdnd001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tcdnd001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tcdnd002.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd002.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tcdnd002.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tcdnd002", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tcdnd003.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd003.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tcdnd003.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tcdnd003", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tcdnd004.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tcdnd004.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tcdnd004.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tcdnd004", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tedbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tedbd001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tedbd001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tedbd001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992tedbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992tedbs001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992tedbs001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992tedbs001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992ttdbd001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992ttdbd001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992ttdbd001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992ttdbd001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "tu0992ttdbs001.hbm.det.nsw.edu.au", "schema_version": 0, "attributes": {"address": "tu0992ttdbs001.hbm.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://tu0992ttdbs001.hbm.det.nsw.edu.au:11055", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "openmetrics", "meta": {"cir_app_id": "obs", "env": "test"}, "name": "openmetrics", "namespace": null, "node": "tu0992ttdbs001", "port": 11055, "service_id": "openmetrics", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"obs-ssl\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "console.mtm.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "console.mtm.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "console-console.mtm.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "console", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "console.mtm.schools.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "console.mtm.schools.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "console-console.mtm.schools.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "console", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "grafana.mtm.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "grafana.mtm.apps.test.det.nsw.edu.au", "datacenter": "dc-cir-un-test", "id": "grafana-grafana.mtm.apps.test.det.nsw.edu.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "obs", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "grafana", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"obs-ssl\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "console.mtm.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "console.apps.d0.ocp.dev.education.nsw.gov.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://console.mtm.apps.test.det.nsw.edu.au:443", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "https", "meta": {"cir_app_id": "obs", "env": "test", "metrics_path": "/probe"}, "name": "https", "namespace": null, "node": "console", "port": 443, "service_id": "https", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "console.mtm.schools.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "console.apps.d0.ocp.dev.education.nsw.gov.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://console.mtm.apps.test.det.nsw.edu.au:443", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "https", "meta": {"cir_app_id": "obs", "env": "test", "metrics_path": "/probe"}, "name": "https", "namespace": null, "node": "console", "port": 443, "service_id": "https", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "grafana.mtm.apps.test.det.nsw.edu.au:443", "schema_version": 0, "attributes": {"address": "grafana.mtm.apps.test.det.nsw.edu.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://grafana.mtm.apps.test.det.nsw.edu.au:443", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "https", "meta": {"cir_app_id": "obs", "env": "test", "metrics_path": "/probe"}, "name": "https", "namespace": null, "node": "grafana", "port": 443, "service_id": "https", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}, {"module": "module.promtarget[\"osecp-ssl\"]", "mode": "managed", "type": "consul_node", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "api.d0.ocp.dev.education.nsw.gov.au:6443", "schema_version": 0, "attributes": {"address": "api.d0.ocp.dev.education.nsw.gov.au", "datacenter": "dc-cir-un-test", "id": "api-api.d0.ocp.dev.education.nsw.gov.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "osecp", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "api", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}, {"index_key": "console.apps.d0.ocp.dev.education.nsw.gov.au:443", "schema_version": 0, "attributes": {"address": "console.apps.d0.ocp.dev.education.nsw.gov.au", "datacenter": "dc-cir-un-test", "id": "console-console.apps.d0.ocp.dev.education.nsw.gov.au", "meta": {"blackbox-icmp": "true", "cir_app_id": "osecp", "env": "test", "external-node": "true", "external-probe": "true"}, "name": "console", "token": null}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.promtarget[\"osecp-ssl\"]", "mode": "managed", "type": "consul_service", "name": "openmetrics", "provider": "provider[\"registry.terraform.io/hashicorp/consul\"]", "instances": [{"index_key": "api.d0.ocp.dev.education.nsw.gov.au:6443", "schema_version": 0, "attributes": {"address": "api.d0.ocp.dev.education.nsw.gov.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://api.d0.ocp.dev.education.nsw.gov.au:443", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "https", "meta": {"cir_app_id": "osecp", "env": "test"}, "name": "https", "namespace": null, "node": "api", "port": 443, "service_id": "https", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}, {"index_key": "console.apps.d0.ocp.dev.education.nsw.gov.au:443", "schema_version": 0, "attributes": {"address": "console.apps.d0.ocp.dev.education.nsw.gov.au", "check": [{"check_id": "service:openmetrics", "deregister_critical_service_after": "30s", "header": [], "http": "http://console.mtm.apps.test.det.nsw.edu.au:443", "interval": "15m0s", "method": "GET", "name": "OpenMetrics health check", "notes": "", "status": "passing", "tcp": "", "timeout": "5s", "tls_skip_verify": true}], "datacenter": "dc-cir-un-test", "enable_tag_override": false, "external": null, "id": "https", "meta": {"cir_app_id": "osecp", "env": "test"}, "name": "https", "namespace": null, "node": "console", "port": 443, "service_id": "https", "tags": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.promtarget.consul_node.openmetrics"]}]}], "check_results": null}