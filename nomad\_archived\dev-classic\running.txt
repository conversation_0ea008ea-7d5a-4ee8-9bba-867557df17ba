ID                                   Type     Priority  Status   Submit Date
collector-agent-aix                  service  50        running  2020-01-10T20:44:55+11:00
collector-agent-db-mssql             service  50        running  2020-01-10T20:45:03+11:00
collector-agent-db-pgsql             service  50        running  2020-01-10T20:45:10+11:00
collector-agent-linux-aws            service  50        running  2020-01-10T20:46:15+11:00
collector-agent-linux-azure          service  50        running  2020-01-10T20:46:18+11:00
collector-agent-linux-pcp            service  50        running  2020-01-10T20:38:54+11:00
collector-agent-windows-aws          service  50        running  2020-01-10T20:45:40+11:00
collector-agent-windows-azure        service  50        running  2020-01-10T20:45:51+11:00
collector-agent-windows-citrix       service  50        running  2020-01-10T20:45:18+11:00
collector-agent-windows-ebs          service  50        running  2020-01-10T20:45:54+11:00
collector-agent-windows-generic      service  50        running  2020-01-10T20:46:03+11:00
collector-agent-windows-generic-sch  service  50        running  2020-01-10T22:25:43+11:00
collector-agent-windows-odmetn       service  50        running  2020-01-10T23:00:48+11:00
collector-agent-windows-odmets       service  50        running  2020-01-10T23:07:19+11:00
collector-agent-windows-odregn       service  50        running  2020-01-10T23:00:34+11:00
collector-agent-windows-odregs       service  50        running  2020-01-10T23:00:41+11:00
collector-agent-windows-odrurn       service  50        running  2020-01-10T22:25:49+11:00
collector-agent-windows-odrursw      service  50        running  2020-01-10T22:25:55+11:00
collector-agent-windows-storage      service  50        running  2020-01-10T23:00:21+11:00
collector-generic-citrix             service  50        running  2020-01-10T22:36:16+11:00
collector-generic-ipmi               service  50        running  2020-01-10T20:45:24+11:00
collector-generic-middleware-jmx     service  50        running  2020-01-10T20:46:28+11:00
collector-generic-middleware-jmx6    service  50        running  2020-01-10T20:46:37+11:00
collector-generic-odmetn             service  50        running  2020-01-10T23:01:05+11:00
collector-generic-odmets             service  50        running  2020-01-10T23:01:08+11:00
collector-generic-odregn             service  50        running  2020-01-10T22:26:15+11:00
collector-generic-odregs             service  50        running  2020-01-10T22:28:17+11:00
collector-generic-odrurn             service  50        running  2020-01-10T22:26:02+11:00
collector-generic-odrursw            service  50        running  2020-01-10T22:26:09+11:00
collector-generic-purestorage        service  50        running  2020-01-10T23:00:27+11:00
collector-generic-snmptraps          service  50        dead     2020-01-10T20:45:32+11:00
collector-generic-vpoller            service  50        running  2020-01-10T22:47:19+11:00 <-- buggy



tcdnd 1 remaining
----
CONTAINER ID        IMAGE                                               COMMAND                  CREATED             STATUS              PORTS                                                     NAMES
59c7007787c4        collector-api-aggregator-pdu:latest                 "/usr/local/bin/en..."   13 months ago       Up 3 months                                                                   collector-api-aggregator-pdu-new
71aa627eeb56        collector-api-trapper-rubrik                        "/usr/local/bin/en..."   17 months ago       Up 3 months                                                                   collector-api-trapper-rubrik
f91a71a6f202        collector-custom-db4bix-oracle-mtm-det-nsw-edu-au   "/entrypoint.sh"         19 months ago       Up 3 months         0.0.0.0:10053->10052/tcp                                  collector-custom-db4bix-oracle
d8c85baafecd        collector-generic-snmptraps                         "/entrypoint.sh"         20 months ago       Up 3 months         162/udp, 0.0.0.0:162->162/tcp, 0.0.0.0:10065->10052/tcp   collector-generic-snmptraps
^^-- only needs proper volume setup for snmptraps.log to work, Ashish said we arent doing any SNMP traps.

tcdnd 2 remaining
----
CONTAINER ID        IMAGE                                              COMMAND             CREATED             STATUS              PORTS                      NAMES
DONE!


tcdnd 3 remaining
----
CONTAINER ID        IMAGE                                     COMMAND             CREATED             STATUS              PORTS                                 NAMES
6b62d7456c90        efe96070aab8                              "./entrypoint.sh"   15 months ago       Up 2 weeks          10051/tcp, 0.0.0.0:10054->10052/tcp   collector-generic-vpoller


tcdnd 4 remaining
----
CONTAINER ID        IMAGE                                                     COMMAND                  CREATED             STATUS              PORTS                                 NAMES
800debd99432        collector-api-trapper-nutanix-afs:latest                  "/usr/local/bin/en..."   10 months ago       Up 6 weeks                                                collector-api-trapper-nutanix-afs
^^-- replace w HTTP template
