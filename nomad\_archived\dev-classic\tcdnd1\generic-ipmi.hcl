job "generic-ipmi" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "du0992tcdnd001.hbm.det.nsw.edu.au"
    }


    task "zabbix-proxy" {
      driver = "docker"

      config {
        image = "artifacts.mtm.nsw.education/zabbix-proxy-doe:centos-4.2.8"
        hostname = "collector-generic-ipmi.mtm.dev.det.nsw.edu.au"

        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }
      }

      service {
        name = "generic-ipmi-passive"
        port = "zabbix_passive"
        tags = [
          "collector",
          "collector-db",
          "tcdnd001"]

        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "generic-ipmi"
        port = "zabbix_server"
        tags = [
          "collector",
          "collector-db",
          "tcdnd001"]

        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        "ZBX_HOSTNAME" = "collector-generic-ipmi.mtm.dev.det.nsw.edu.au"
        "ZBX_SERVER_HOST" = "dl0992obszbs01.nsw.education"
        "DB_SERVER_HOST" = "${NOMAD_IP_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_db_postgresql}"
        "ZBX_TIMEOUT" = "3"
        "ZBX_PROXYOFFLINEBUFFER" = "3"
        "ZBX_CONFIGFREQUENCY" = "600"
        "ZBX_STARTPOLLERS" = "32"
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        "ZBX_STARTTRAPPERS" = "15"
        "ZBX_STARTPINGERS" = "16"
        "ZBX_STARTDISCOVERERS" = "8"
        "ZBX_CACHESIZE" = "256M"
        "ZBX_HISTORYCACHESIZE" = "128M"
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        "ZBX_UNREACHABLEPERIOD" = "120"
        "ZBX_UNREACHABLEDELAY" = "30"
        "ZBX_LOGSLOWQUERIES" = "0"

        # "ZBX_PROXYMODE" = "0"
        # "ZBX_SERVER_PORT" = "10051"
        # "ZBX_LOADMODULE" = ""
        # "ZBX_DEBUGLEVEL" = "3"
        # "ZBX_JAVAGATEWAY_ENABLE" = "false"

        # "ZBX_PROXYLOCALBUFFER" = "0"
        # "ZBX_PROXYHEARTBEATFREQUENCY" = "60"
        # "ZBX_DATASENDERFREQUENCY" = "1"
        # "ZBX_IPMIPOLLERS" = "0"
        # "ZBX_STARTHTTPPOLLERS" = "1"

        # "ZBX_JAVAGATEWAY" = "zabbix-java-gateway"
        # "ZBX_JAVAGATEWAYPORT" = "10052"
        # "ZBX_STARTJAVAPOLLERS" = "0"

        # "ZBX_STARTVMWARECOLLECTORS" = "0"
        # "ZBX_VMWAREFREQUENCY" = "60"
        # "ZBX_VMWARECACHESIZE" = "8M"
        # "ZBX_ENABLE_SNMP_TRAPS" = "false"
        # "ZBX_HOUSEKEEPINGFREQUENCY" = "1"
        # "ZBX_STARTDBSYNCERS" = "4"
        # "ZBX_TRAPPERIMEOUT" = "300"
        # "ZBX_UNAVAILABLEDELAY" = "60"

        # "ZBX_TLSCONNECT" = "unencrypted"
        # "ZBX_TLSACCEPT" = "unencrypted"
        # "ZBX_TLSCAFILE" = ""
        # "ZBX_TLSCRLFILE" = ""
        # "ZBX_TLSSERVERCERTISSUER" = ""
        # "ZBX_TLSSERVERCERTSUBJECT" = ""
        # "ZBX_TLSCERTFILE" = ""
        # "ZBX_TLSKEYFILE" = ""
        # "ZBX_TLSPSKIDENTITY" = ""
        # "ZBX_TLSPSKFILE" = ""

        # "ZBX_ENABLEREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_LOGREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_HOSTNAMEITEM" = "system.hostname"
        # "ZBX_SOURCEIP" = ""
        # "ZBX_VMWAREPERFFREQUENCY" = "60"
        # "ZBX_VMWARETIMEOUT" = "10"
        # "ZBX_LISTENIP" = ""
      }

      resources {
        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10057
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "artifacts.mtm.nsw.education/postgres:12"

        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "collector-generic-ipmi-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        network {
          port "postgresql" {}
        }
      }
    }
  }
}