job "fabio_latest" {
  datacenters = ["dc1"]
  type = "system"

  group "fabio" {
    network {
          mbits = 20
          port "lb" {
            static = 9999
          }
          port "ui" {
            static = 9998
          }
          port "metrics" {
            static = 11055
          }
        }

    task "fabio" {
      driver = "docker"
      config {
        image = "fabiolb/fabio:latest"
        network_mode = "host"
        ports = ["lb","ui","metrics"]
        args = [
        "-registry.consul.addr=consul.spiti:8500",
        "-metrics.target=prometheus",
        "-insecure=true",
        "-proxy.addr=:9999",
        "-proxy.addr=:11055;proto=prometheus",
        "-registry.consul.register.tags=openmetrics"
        ]
      }

      resources {
        cpu    = 100
        memory = 64
        
      }
    }
  }
}
