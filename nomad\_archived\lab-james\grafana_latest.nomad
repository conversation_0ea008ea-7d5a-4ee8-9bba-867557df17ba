
job "grafana_latest" {
  datacenters = ["dc1"]
  type = "service"

  group "grafana" {
    count = 1

    network {
      port "http" {}
    }

    volume "vol_grafana" {
      type = "host"
      source = "vol_grafana"
      read_only = false
    }

    task "grafana" {
      driver = "docker"

      config {
        image = "https://docker.io/grafana/grafana:latest"
        ports = ["http"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.spiti:3100/loki/api/v1/push"
                }
            }
      }

      env {
        GF_LOG_LEVEL          = "DEBUG"
        GF_LOG_MODE           = "console"
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_http}"
        GF_INSTALL_PLUGINS = ""
        GF_PATHS_PROVISIONING = "/local/grafana/provisioning"
        GF_PATHS_DATA = "/data"
        GF_LIVE_ALLOWED_ORIGINS = "http://*"
        GF_FEATURE_TOGGLES_ENABLE = "ngalert"
      }

      service {
        name = "grafana"
        port = "http"
        check {
          type = "http"
          port = "http"
          path = "/api/health"
          interval = "5s"
          timeout = "2s"
          }
        tags = [  
          "urlprefix-/grafana",
          ]    
        }

      volume_mount {
        volume = "vol_grafana"
        destination = "/data"
        read_only = false
      }

      resources {
        cpu    = 100
        memory = 100
      }

      template {
        data        = <<EOTC
apiVersion: 1
datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    # It is necessary to use /api/prom legacy endpoint for Cortex Managed Alerts in Grafana 8.1
    url: http://prometheus.spiti:9090

EOTC
        destination = "/local/grafana/provisioning/datasources/ds.yaml"
      }
    }
  }
}
