##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

job "mimir_single_fs" {
  datacenters = ["dc1"]
  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }  

    group "mimir" {

        network {
            port "grpc" {
                static = 9095
                to = 9095
                }
            port "http" {
                static = 8080
                to = 8080
                }
        }
     
    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }    

    task "mimir" {
      driver = "docker"
        service {
            name = "mimir"
            port = "http"
            address_mode = "host"
        tags = [
            "urlprefix-/mimir"
        ]
        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }        
        }          
      resources {
        cpu = 500
        memory = 500
        }     
      config {
        image = "grafana/mimir:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.spiti:3100/loki/api/v1/push"
                }
            }        
        ports = ["http","grpc"]     
        args = [
            "-target=all",
            "-server.log-source-ips-enabled",
            "-log.level=debug",

            "-compactor.ring.store=consul",
            "-compactor.ring.prefix=collectors/",
            "-compactor.ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-compactor.ring.consul.hostname=consul.spiti:8500",
            "-compactor.ring.instance-addr=${NOMAD_IP_http}",
            "-compactor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-compactor.ring.instance-id=${node.unique.name}",

            "-distributor.ring.store=consul",
            "-distributor.ring.prefix=collectors/",
            "-distributor.ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-distributor.ring.consul.hostname=consul.spiti:8500",
            "-distributor.ring.instance-addr=${NOMAD_IP_http}",
            "-distributor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-distributor.ring.instance-id=${node.unique.name}",

            "-ingester.ring.store=consul",
            "-ingester.ring.prefix=collectors/",
            "-ingester.ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-ingester.ring.consul.hostname=consul.spiti:8500",
            "-ingester.ring.instance-addr=${NOMAD_IP_http}",
            "-ingester.ring.instance-port=${NOMAD_PORT_grpc}",
            "-ingester.ring.instance-id=${node.unique.name}",
            "-ingester.ring.replication-factor=1",

            "-store-gateway.sharding-ring.store=consul",
            "-store-gateway.sharding-ring.prefix=collectors/",
            "-store-gateway.sharding-ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-store-gateway.sharding-ring.consul.hostname=consul.spiti:8500",
            "-store-gateway.sharding-ring.replication-factor=1",
            "-store-gateway.sharding-ring.instance-addr=${NOMAD_IP_http}",
            "-store-gateway.sharding-ring.instance-port=${NOMAD_PORT_grpc}",
            "-store-gateway.sharding-ring.instance-id=${node.unique.name}",
            "-store-gateway.sharding-ring.replication-factor=1",

            "-blocks-storage.backend=filesystem",
            "-blocks-storage.filesystem.dir=/mimir/",
            "-blocks-storage.tsdb.dir=/mimir/tsdb/",
            "-blocks-storage.bucket-store.sync-dir=/mimir/tsdb-sync/",

            "-ruler.ring.store=consul",
            "-ruler.ring.prefix=collectors/",
            "-ruler.ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-ruler.ring.consul.hostname=consul.spiti:8500",
            
            "-ruler-storage.backend=filesystem", 

            "-alertmanager.sharding-ring.store=consul",
            "-alertmanager.sharding-ring.consul.acl-token=f56bf9ec-243c-94ae-4f91-8752628fbf8d",
            "-alertmanager.sharding-ring.consul.hostname=consul.spiti:8500",

            "-alertmanager-storage.backend=filesystem",
    ]
   }
  }
 }
}