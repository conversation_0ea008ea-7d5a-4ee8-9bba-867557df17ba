## Stolen example from https://github.com/leowmjw/nomad-box

job "node-red_latest" {
  datacenters = ["dc1"]
  type = "service"
  update {
    stagger = "10s"
    max_parallel = 2
  }
  group "control" {
    count = 1
    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }
    volume "vol_nodered" {
      type = "host"
      source = "vol_nodered"
      read_only = false
    }
    task "nodered" {
      driver = "docker"
      config {
        image = "nodered/node-red:latest"
        port_map {
          nodered = 1880
        }
       }
      volume_mount {
        volume      = "vol_nodered"
        destination = "/data"
        read_only   = false
      }
      resources {
        cpu    = 100
        memory = 100
        network {
          mbits = 10
          port "nodered" {
            to = 1880
          }
        }
      }
      service {
        name = "nodered-app"
        tags = [
          "urlprefix-/nodered"
        ]
        port = "nodered"
        check {
          	name     = "alive"          
            type     = "tcp"          
            interval = "10s"
          	timeout  = "2s"
        }                                                                              
      }
    }
  }
}