##https://learn.hashicorp.com/tutorials/nomad/prometheus-metrics

job "prometheus_latest" {
  datacenters = ["dc1"]
  type        = "service"
  group "monitoring" {
    count = 1
    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }

    volume "vol_prometheus" {
      type      = "host"
      read_only = false
      source    = "vol_prometheus"
    }
    task "prometheus" {
      template {
        change_mode = "noop"
        destination = "local/prometheus.yml"
        data = <<EOH
---
global:
  scrape_interval:     60s
  evaluation_interval: 30s
  query_log_file: /prometheus/query.log

  # Attach these labels to any time series or alerts when communicating with
  # external systems (federation, remote storage, Alertmanager).
  external_labels:
      monitor: 'docker-host-alpha'

# Load and evaluate rules in this file every 'evaluation_interval' seconds.
rule_files:
  - "alert.rules"

# A scrape configuration containing exactly one endpoint to scrape.
scrape_configs:
  - job_name: 'cadvisor'
    scrape_interval: 10s
    static_configs:
      - targets: [
        'tower.spiti:8080'
        ]

  - job_name: 'pihole'
    scrape_interval: 60s
    static_configs:
      - targets: [
        'tower.spiti:9617'
        ]

  
  - job_name: 'prometheus'
    scrape_interval: 30s
    static_configs:
      - targets: ['prometheus.spiti.pw'] #Fabio LB port for prometheus

  - job_name: 'telegraf'
    scrape_interval: 60s
    static_configs:
      - targets: [
        'tower.spiti:9273',
        'pihole.spiti:9273'
      ]

  - job_name: 'meters'
    scrape_interval: 10s
    static_configs:
      - targets: [
        'office-aircon-plug.spiti',
        'server-plug.spiti',
        'lounge-plug.spiti',
        'office-plug.spiti',
        'fridge-plug.spiti',
        'bedroom-aircon.spiti'
      ]

  - job_name: 'sensors'
    static_configs:
      - targets: [
        'server-cabinet.spiti',
        'office.spiti',
        'sebastians-room.spiti',
        'attic.spiti',
        'watertank.spiti',
        'bedroom.spiti'
      ]

  - job_name: 'mqtt_exporter'
    static_configs:
      - targets: [
        'tower.spiti:9344'
        ]

  - job_name: 'grafana'
    static_configs:
      - targets: [
        'grafana.spiti.pw'
        ]

  - job_name: 'promtail'
    static_configs:
      - targets: [
        'tower.spiti:9080'
        ]

  - job_name: 'loki'
    static_configs:
      - targets: [
        'loki.spiti:3100'
        ]
  - job_name: node
    static_configs:
      - targets: ['localhost:9100']
remote_write:
      - url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
        basic_auth:
          username: 624341
          password: eyJrIjoiMmI4ZWRiMTQxOWU3ZTBiZmE1Y2RjNGI4YTlhYjU0MWI4ZTZhZjcxZCIsIm4iOiJzcGl0aV9wcm9tZXRoZXVzIiwiaWQiOjIyMTc0M30=

EOH
      }
      driver = "docker"

     volume_mount {
      volume      = "vol_prometheus"
      destination = "/mnt/prometheus"
      read_only   = false
     } 

      config {
        image = "prom/prometheus:latest"
        logging {
          type = "loki"
          config {
              loki-url = "https://loki.service.dc1.consul/loki/api/v1/push"
              }
          }
        args = [
          "--config.file=/mnt/prometheus/prometheus.yml",
          "--storage.tsdb.path=/mnt/prometheus",
          "--storage.tsdb.retention=1y",
          ]
        volumes = [
          "local/prometheus.yml:/mnt/prometheus/prometheus.yml",
        ]
        port_map {
          prometheus_ui = 9090
        }
      }

      resources {
        cpu    = 500
        memory = 500
        network {
          mbits = 10
          port  "prometheus_ui"{}
        }
      }

      service {
        name = "prometheus"
        tags = [
            "urlprefix-/prometheus"
        ]
        port = "prometheus_ui"

        check {
          name     = "prometheus_ui port alive"
          type     = "http"
          path     = "/-/healthy"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}