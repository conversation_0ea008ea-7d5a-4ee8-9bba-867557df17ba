//docker run -it --rm -v /path/to/victoria-metrics-data:/victoria-metrics-data -p 8428:8428 victoriametrics/victoria-metrics

job "victoria_metrics" {
  datacenters = ["dc1"]
  type        = "service"
  group "monitoring" {
    count = 1
    restart {
      attempts = 2
      interval = "30m"
      delay    = "15s"
      mode     = "fail"
    }



    volume "vol_victoriametrics" {
      type      = "host"
      read_only = false
      source    = "vol_victoriametrics"
    }
    task "victoria-metrics" {
      driver = "docker"
     volume_mount {
      volume      = "vol_victoriametrics"
      destination = "/victoria-metrics-data/"
      read_only   = false
     } 

      config {
        image = "victoriametrics/victoria-metrics"
        args = []
        volumes = []
        port_map {
          victoriam_ui = 8428
        }
      }

      resources {
        cpu    = 500
        memory = 500
        network {
          mbits = 10
          port  "victoriam_ui"{
              static = 8428
          }
        }
      }

      service {
        name = "victoria-metrics"
        tags = []
        port = "victoriam_ui"

        check {
          name     = "victoriam_ui port alive"
          type     = "http"
          path     = "/-/healthy"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}
