# This is a configuration file for Zabbix Proxy process
# To get more information about Zabbix,
# visit http://www.zabbix.com

############ GENERAL PARAMETERS #################

### Option: ProxyMode
#	Proxy operating mode
#	0 - proxy in the active mode
#	1 - proxy in the passive mode
#
ProxyMode=0

### Option: Server
#	IP address (or hostname) of Zabbix server.
#	Active proxy will get configuration data from the server.
#	For a proxy in the passive mode this parameter will be ignored.
#
Server=pu0992tedbd001.hbm.det.nsw.edu.au

### Option: ServerPort
#	Port of Zabbix trapper on Zabbix server.
#	For a proxy in the passive mode this parameter will be ignored.
#
ServerPort=10051

### Option: Hostname
#	Unique, case sensitive Proxy name. Make sure the Proxy name is known to the server!
#	Value is acquired from HostnameItem if undefined.
#
Hostname=collector-generic-middleware-jmx.mtm.det.nsw.edu.au

### Option: HostnameItem
#	Item used for generating Hostname if it is undefined.
#	Ignored if Hostname is defined.
#
# Mandatory: no
# Default:
# HostnameItem=system.hostname

### Option: ListenPort
#	Listen port for trapper.
#
ListenPort=10052

### Option: SourceIP
#	Source IP address for outgoing connections.
#

### Option: LogFile
#	Name of log file.
#	If not set, syslog is used.
#
LogFile=/var/log/zabbix/zabbix_proxy.log

### Option: LogFileSize
#	Maximum size of log file in MB.
#	0 - disable automatic log rotation.
#
LogFileSize=10

### Option: DebugLevel
#	Specifies debug level
#	0 - no debug
#	1 - critical information
#	2 - error information
#	3 - warnings
#	4 - for debugging (produces lots of information)
#
DebugLevel=3

### Option: PidFile
#	Name of PID file.
#
PidFile=/var/run/zabbix/zabbix_proxy.pid

### Option: DBHost
#	Database host name.
#	If set to localhost, socket is used for MySQL.
#	If set to empty string, socket is used for PostgreSQL.
#
DBHost=localhost

### Option: DBName
#	Database name.
#	For SQLite3 path to database file must be provided. DBUser and DBPassword are ignored.
#	Warning: do not attempt to use the same database Zabbix server is using.
#
DBName=zabbix

### Option: DBSchema
#	Schema name. Used for IBM DB2.
#

### Option: DBUser
#	Database user. Ignored for SQLite.
#
DBUser=zabbix

### Option: DBPassword
#	Database password. Ignored for SQLite.
#	Comment this line if no password is used.
#
DBPassword=zabbix

### Option: DBSocket
#	Path to MySQL socket.
#

# Option: DBPort
#	Database port when not using local socket. Ignored for SQLite.
#
DBPort=5432

######### PROXY SPECIFIC PARAMETERS #############

### Option: ProxyLocalBuffer
#	Proxy will keep data locally for N hours, even if the data have already been synced with the server.
#	This parameter may be used if local data will be used by third party applications.
#
ProxyLocalBuffer=0

### Option: ProxyOfflineBuffer
#	Proxy will keep data for N hours in case if no connectivity with Zabbix Server.
#	Older data will be lost.
#
ProxyOfflineBuffer=3

### Option: HeartbeatFrequency
#	Frequency of heartbeat messages in seconds.
#	Used for monitoring availability of Proxy on server side.
#	0 - heartbeat messages disabled.
#	For a proxy in the passive mode this parameter will be ignored.
#
HeartbeatFrequency=60

### Option: ConfigFrequency
#	How often proxy retrieves configuration data from Zabbix Server in seconds.
#	For a proxy in the passive mode this parameter will be ignored.
#
ConfigFrequency=600

### Option: DataSenderFrequency
#	Proxy will send collected data to the Server every N seconds.
#	For a proxy in the passive mode this parameter will be ignored.
#
DataSenderFrequency=1

############ ADVANCED PARAMETERS ################

### Option: StartPollers
#	Number of pre-forked instances of pollers.
#
StartPollers=128

### Option: StartIPMIPollers
#	Number of pre-forked instances of IPMI pollers.
#
StartIPMIPollers=0

### Option: StartPollersUnreachable
#	Number of pre-forked instances of pollers for unreachable hosts (including IPMI).
#
StartPollersUnreachable=30

### Option: StartTrappers
#	Number of pre-forked instances of trappers.
#	Trappers accept incoming connections from Zabbix sender and active agents.
#
StartTrappers=15

### Option: StartPingers
#	Number of pre-forked instances of ICMP pingers.
#
StartPingers=64

### Option: StartDiscoverers
#	Number of pre-forked instances of discoverers.
#
StartDiscoverers=8

### Option: StartHTTPPollers
#	Number of pre-forked instances of HTTP pollers.
#
StartHTTPPollers=1

### Option: JavaGateway
#	IP address (or hostname) of Zabbix Java gateway.
#	Only required if Java pollers are started.
#
JavaGateway=127.0.0.1

### Option: JavaGatewayPort
#	Port that Zabbix Java gateway listens on.
#
JavaGatewayPort=12052

### Option: StartJavaPollers
#	Number of pre-forked instances of Java pollers.
#
StartJavaPollers=64

### Option: StartVMwareCollectors
#	Number of pre-forked vmware collector instances.
#
StartVMwareCollectors=0

### Option: VMwareFrequency
#	How often Zabbix will connect to VMware service to obtain a new data.
#
VMwareFrequency=60

### Option: VMwareCacheSize
#	Size of VMware cache, in bytes.
#	Shared memory size for storing VMware data.
#	Only used if VMware collectors are started.
#
VMwareCacheSize=8M

### Option: SNMPTrapperFile
#	Temporary file used for passing data from SNMP trap daemon to the proxy.
#	Must be the same as in zabbix_trap_receiver.pl or SNMPTT configuration file.
#
SNMPTrapperFile=/tmp/zabbix_traps.tmp

### Option: StartSNMPTrapper
#	If 1, SNMP trapper process is started.
#
StartSNMPTrapper=0

### Option: ListenIP
#	List of comma delimited IP addresses that the trapper should listen on.
#	Trapper will listen on all network interfaces if this parameter is missing.
#

### Option: HousekeepingFrequency
#	How often Zabbix will perform housekeeping procedure (in hours).
#	Housekeeping is removing unnecessary information from history, alert, and alarms tables.
#
HousekeepingFrequency=1

### Option: CacheSize
#	Size of configuration cache, in bytes.
#	Shared memory size, for storing hosts and items data.
#
CacheSize=512M

### Option: StartDBSyncers
#	Number of pre-forked instances of DB Syncers
#
StartDBSyncers=4

### Option: HistoryCacheSize
#	Size of history cache, in bytes.
#	Shared memory size for storing history data.
#
HistoryCacheSize=256M

### Option: HistoryIndexCacheSize
# Size of history index cache, in bytes.
# Shared memory size for indexing history cache.
#
# Mandatory: no
# Range: 128K-2G
# Default:
HistoryIndexCacheSize=128M


### Option: Timeout
#	Specifies how long we wait for agent, SNMP device or external check (in seconds).
#
Timeout=3

### Option: TrapperTimeout
#	Specifies how many seconds trapper may spend processing new data.
#
TrapperTimeout=300

### Option: UnreachablePeriod
#	After how many seconds of unreachability treat a host as unavailable.
#
UnreachablePeriod=120

### Option: UnavailableDelay
#	How often host is checked for availability during the unavailability period, in seconds.
#
UnavailableDelay=60

### Option: UnreachableDelay
#	How often host is checked for availability during the unreachability period, in seconds.
#
UnreachableDelay=30

### Option: ExternalScripts
#	Full path to location of external scripts.
#	Default depends on compilation options.
#
ExternalScripts=/usr/lib/zabbix/externalscripts

### Option: FpingLocation
#	Location of fping.
#	Make sure that fping binary has root ownership and SUID flag set.
#
FpingLocation=/usr/sbin/fping

### Option: Fping6Location
#	Location of fping6.
#	Make sure that fping6 binary has root ownership and SUID flag set.
#	Make empty if your fping utility is capable to process IPv6 addresses.
#
Fping6Location=/usr/sbin/fping6

### Option: SSHKeyLocation
#	Location of public and private keys for SSH checks and actions.
#

### Option: LogSlowQueries
#	How long a database query may take before being logged (in milliseconds).
#	Only works if DebugLevel set to 3 or 4.
#	0 - don't log slow queries.
#
LogSlowQueries=0

### Option: TmpDir
#	Temporary directory.
#
TmpDir=/tmp


### Option: Include
#	You may include individual files or all files in a directory in the configuration file.
#	Installing Zabbix will create include directory in /usr/local/etc, unless modified during the compile time.
#
Include=/etc/zabbix/zabbix_proxy.conf.d

####### LOADABLE MODULES #######


### Option: LoadModule
#	Module to load at proxy startup. Modules are used to extend functionality of the proxy.
#	Format: LoadModule=<module.so>
#	The modules must be located in directory specified by LoadModulePath.
#	It is allowed to include multiple LoadModule parameters.
#

####### TLS-RELATED PARAMETERS #######

### Option: TLSConnect
#       How the agent should connect to server or proxy. Used for active checks.
#       Only one value can be specified:
#               unencrypted - connect without encryption
#               psk         - connect using TLS and a pre-shared key
#               cert        - connect using TLS and a certificate
#
# Mandatory: yes, if TLS certificate or PSK parameters are defined (even for 'unencrypted' connection)
# Default:
# TLSConnect=unencrypted

### Option: TLSAccept
#       What incoming connections to accept.
#       Multiple values can be specified, separated by comma:
#               unencrypted - accept connections without encryption
#               psk         - accept connections secured with TLS and a pre-shared key
#               cert        - accept connections secured with TLS and a certificate
#
# Mandatory: yes, if TLS certificate or PSK parameters are defined (even for 'unencrypted' connection)
# Default:
# TLSAccept=unencrypted

### Option: TLSCAFile
#       Full pathname of a file containing the top-level CA(s) certificates for
#       peer certificate verification.
#
# Mandatory: no
# Default:
# TLSCAFile=

### Option: TLSCRLFile
#       Full pathname of a file containing revoked certificates.
#
# Mandatory: no
# Default:
# TLSCRLFile=

### Option: TLSServerCertIssuer
#      Allowed server certificate issuer.
#
# Mandatory: no
# Default:
# TLSServerCertIssuer=

### Option: TLSServerCertSubject
#      Allowed server certificate subject.
#
# Mandatory: no
# Default:
# TLSServerCertSubject=

### Option: TLSCertFile
#       Full pathname of a file containing the agent certificate or certificate chain.
#
# Mandatory: no
# Default:
# TLSCertFile=

### Option: TLSKeyFile
#       Full pathname of a file containing the agent private key.
#
# Mandatory: no
# Default:
# TLSKeyFile=

### Option: TLSPSKIdentity
#       Unique, case sensitive string used to identify the pre-shared key.
#
# Mandatory: no
# Default:
# TLSPSKIdentity=

### Option: TLSPSKFile
#       Full pathname of a file containing the pre-shared key.
#
# Mandatory: no
# Default:
# TLSPSKFile=