

job "custom-db4bix-oracle" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }

    task "db4bix" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/dbforbix:latest"
        hostname = "collector-custom-db4bix-oracle.mtm.det.nsw.edu.au"
      logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
      }

      env {
        "TZ" = "Australia/Sydney"
      }

      resources {
        cpu = 400
        memory = 1500
      }
    }
  }
}
