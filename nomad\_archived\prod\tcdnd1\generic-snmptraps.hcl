# Current NVPS: 78.61

job "generic-snmptraps" {
  # SNMPTRAPS should probably be completely different to the 3.4 implementation.
  # We should try to use the upstream container zabbix/zabbix-snmptraps


  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    # TODO: tcdnd001 ran out of reserved memory so moving to 005 for a bit
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd001.hbm.det.nsw.edu.au"
    }


    task "zabbix-proxy" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/zabbix-mibs.git"
        destination = "mibs"
        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      config {
        image = "https://artifacts.mtm.nsw.education/zabbix-proxy-doe:centos-5.0.3"
        hostname = "collector-generic-snmptraps.mtm.det.nsw.edu.au"
        dns_servers = ["************"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }

        # edit nomad.d/client.hcl
        # add old behaviour
        # plugin "docker" {
        #  config {
        #    volumes {
        #      enabled = true
        #    }
        #  }
        # }
        volumes = [
          "mibs:/var/lib/zabbix/mibs",
          "snmptraps:/var/lib/zabbix/snmptraps"
        ]
      }

      service {
        name = "generic-snmptraps-passive"
        port = "zabbix_passive"


        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "generic-snmptraps"
        port = "zabbix_server"


        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        "ZBX_HOSTNAME" = "collector-generic-snmptraps.mtm.det.nsw.edu.au"
        "ZBX_SERVER_HOST" = "pu0992tedbd001.hbm.det.nsw.edu.au"
        "DB_SERVER_HOST" = "${NOMAD_IP_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_db_postgresql}"

        "ZBX_PROXYOFFLINEBUFFER" = "3"
        "ZBX_STARTPOLLERS" = "32"
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        "ZBX_STARTTRAPPERS" = "15"
        "ZBX_STARTPINGERS" = "16"
        "ZBX_STARTDISCOVERERS" = "8"
        "ZBX_CACHESIZE" = "256M"
        "ZBX_HISTORYCACHESIZE" = "128M"
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        "ZBX_UNREACHABLEPERIOD" = "120"
        "ZBX_UNREACHABLEDELAY" = "30"


        "ZBX_ENABLE_SNMP_TRAPS" = "true"  # No need to specify trap directory as we use the defaults

      }

      resources {
        cpu = 1500
        memory = 1000

        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10065
          }
        }
      }
    }

    task "zabbix-snmptraps" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/zabbix-mibs.git"
        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }        
        destination = "mibs"
      }

      config {
        image = "artifacts.mtm.nsw.education/zabbix-snmptraps:centos-5.0.3"

        port_map {
          snmp = 161
          snmptrap = 162
        }

        volumes = [
          "mibs:/var/lib/zabbix/mibs",
          "snmptraps:/var/lib/zabbix/snmptraps"
        ]
      }

      service {
        name = "collector-generic-snmptraps-trap"
        port = "snmptrap"
      }

      env {

      }

      resources {
        cpu = 400
        memory = 1500

        network {
          port "snmptrap" {
            static = 162
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:pgsql12-novolume"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "collector-generic-snmptraps-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        cpu = 400
        memory = 1500

        network {
          port "postgresql" {}
        }
      }
    }
  }
}
