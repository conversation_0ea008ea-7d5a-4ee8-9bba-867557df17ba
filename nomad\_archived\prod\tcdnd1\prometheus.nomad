
// Classic tcdnd Prometheus - local monitoring of Nomad, remote write to ObsCol Mimir

// Agent-variant, no bother with local storage.

job "prometheus-tcdnd-prod" {

  type = "service"

  datacenters = ["dc-un-prod"]

  group "prometheus-tcdnd-prod" {

    count = 1

    # Absent traefik, it's easier to just bind this to a specific host
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd001.hbm.det.nsw.edu.au"
    }


    task "prometheus-tcdnd-prod" {


      driver = "docker"

      config {
        # ports = ["port_prometheus"]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }

#          port_map {
#            port_prometheus = 9090
#          }

        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          # "local/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:9090",
          # "--web.external-url=https://prometheus.hbm.det.nsw.edu.au",
          "--web.page-title=Prometheus on DoE Classic TCDND PROD cluster",

          # Disable all the go_collector_ metrics from prometheus
#          "--web.disable-exporter-metrics",

          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

#      service {
#        name = "prometheus"
#        port = "port_prometheus"
#
#        check {
#          type = "http"
#          port = "port_prometheus"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "20s"
#        }
#      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-tcdnd-prod

scrape_configs:
  # ObsCol instance - monitoring this job by itself effectively.
  - job_name: 'prometheus-tcdnd-prod'
    static_configs:
      - targets: [
         'pu0992tcdnd001.hbm.det.nsw.edu.au:9090',
      ]

#  - job_name: 'consul_metrics_tcdnd'
#    static_configs:
#      - targets: ['pu0992tcdnd001.hbm.det.nsw.edu.au:8500']
#    metrics_path: /v1/agent/metrics
#    params:
#      format: ['prometheus']

  - job_name: 'nomad_metrics_tcdnd'
    static_configs:
      - targets: [
        'pu0992tcdnd001.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd002.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd003.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd004.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd005.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd006.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd007.hbm.det.nsw.edu.au:4646',
        'pu0992tcdnd008.hbm.det.nsw.edu.au:4646',
        ]
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']





remote_write:
- name: mimir
  url: "https://mimir.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 1200
        network {
          port "port_prometheus" {
            static = 9090
          }
        }
      }

    }
  }
}
