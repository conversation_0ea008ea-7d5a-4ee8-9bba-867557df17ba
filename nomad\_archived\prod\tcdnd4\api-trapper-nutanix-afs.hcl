
job "api-trapper-nutanix-afs" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd004.hbm.det.nsw.edu.au"
    }


    task "zabbix-sender" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:collector-api-trapper-nutanix-afs"
        hostname = "collector-api-trapper-nutanix-afs.mtm.det.nsw.edu.au"
      }

      env {
        "NUTANIX_URL" = "https://ph0991ntxsc002.nutanix.det.nsw.edu.au:9440 https://ph0992ntxuc002.nutanix.det.nsw.edu.au:9440"
        "NUTANIX_USER" = "<EMAIL>"
        "NUTANIX_PASSWORD" = "2uGxZnppFPa5K8nPB@o&"
        "ZABBIX_COLLECTOR" = "collector-generic-snmptraps.mtm.det.nsw.edu.au"
        "http_proxy" = ""
        "https_proxy" = ""
      }

      resources {
        cpu = 400
        memory = 1500
      }
    }
  }
}
