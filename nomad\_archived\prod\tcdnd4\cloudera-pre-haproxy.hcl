job "cloudera-pre-haproxy" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "haproxy" {
    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd004.hbm.det.nsw.edu.au"
    }

    task "haproxy" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/haproxy:latest"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          cloudera = 7180
        }

        volumes = [
          "local/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg"
        ]
      }

      template {
        data = <<EOH
global
	maxconn 256
	log stdout format raw local0

defaults
	mode http
	timeout connect 5000ms
	timeout client 50000ms
	timeout server 50000ms
	log global

frontend http-in
	bind *:7180
	default_backend cloudera_mgmt

backend cloudera_mgmt
	server qu0000hdpj2001 qu0000hdpj2001.dbs.pre.det.nsw.edu.au:7180 maxconn 32

EOH
        destination = "local/haproxy.cfg"
      }



      resources {
        cpu = 200
        memory = 512

        network {
          port "cloudera" {
            static = 7180
          }
        }
      }
    }
  }
}
