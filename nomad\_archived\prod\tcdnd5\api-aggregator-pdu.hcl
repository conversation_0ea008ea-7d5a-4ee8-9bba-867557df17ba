
job "api-aggregator-pdu" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }


    task "zabbix-sender" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/prod/collector-api-aggregator-pdu:20200903"
        hostname = "collector-api-aggregator-pdu.mtm.det.nsw.edu.au"
                logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
      }

      env {
        "ZABBIX_CONSOLE" = "https://console.mtm.apps.det.nsw.edu.au/zabbix/"
        "ZABBIX_USER" = "sys-zbx-svc-dcapi"
        "ZABBIX_PASSWORD" = "UTAxczJaXmszJFI2NmZVQ3VEZwo="
        "ZABBIX_COLLECTOR" = "collector-generic-snmptraps.mtm.det.nsw.edu.au"
        "ZABBIX_COLLECTOR_PORT" = "10065"
        "PATH" = "/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
        "TERM" = "XTERM"
        "http_proxy" = "http://proxy.det.nsw.edu.au:80"
        "https_proxy" = "http://proxy.det.nsw.edu.au:80"
        "NO_PROXY" = "det.nsw.edu.au,nsw.education"
        "TZ" = "Australia/Sydney"
      }

      resources {
        cpu = 400
        memory = 1500
      }
    }
  }
}
