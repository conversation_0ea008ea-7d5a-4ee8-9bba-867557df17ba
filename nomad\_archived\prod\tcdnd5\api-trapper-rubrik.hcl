
job "api-trapper-rubrik" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }


    task "zabbix-sender" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/prod/collector-api-trapper-rubrik:20200903"
        hostname = "collector-api-trapper-rubrik.mtm.det.nsw.edu.au"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }         
      }

      env {
        "RUBRIK_HOST" = "https://ps0991brik1000.nsw.education https://ps0992brik1000.nsw.education "
        "RUBRIK_USER" = "svc-obs"
        "RUBRIK_PASSWORD" = "NQsQ?kYye#P0"
        "ZABBIX_COLLECTOR" = "pu0992tedbd001.hbm.det.nsw.edu.au"
        "http_proxy" = ""
        "https_proxy" = ""
      }

      resources {
        cpu = 400
        memory = 1500
      }
    }
  }
}
