# Current NVPS: 0.01

job "custom-externalscripts" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }


    task "zabbix-proxy" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:centos-5.0.2"
        hostname = "collector-custom-externalscripts.mtm.det.nsw.edu.au"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }
      }

      service {
        name = "custom-externalscripts-passive"
        port = "zabbix_passive"


//        check {
//          type = "tcp"
//          port = "zabbix_passive"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      service {
        name = "custom-externalscripts"
        port = "zabbix_server"


//        check {
//          type = "tcp"
//          port = "zabbix_server"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      env {
        "ZBX_HOSTNAME" = "collector-custom-externalscripts.mtm.det.nsw.edu.au"
        "ZBX_SERVER_HOST" = "pu0992tedbd001.hbm.det.nsw.edu.au"
        "DB_SERVER_HOST" = "${NOMAD_IP_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_db_postgresql}"

        "ZBX_PROXYOFFLINEBUFFER" = "3"
        "ZBX_STARTPOLLERS" = "32"
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        "ZBX_STARTTRAPPERS" = "15"
        "ZBX_STARTPINGERS" = "16"
        "ZBX_STARTDISCOVERERS" = "8"
        "ZBX_CACHESIZE" = "256M"
        "ZBX_HISTORYCACHESIZE" = "128M"
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        "ZBX_UNREACHABLEPERIOD" = "120"
        "ZBX_UNREACHABLEDELAY" = "30"

        "HTTP_PROXY" = "proxy.det.nsw.edu.au:80"
        "https_proxy" = "proxy.det.nsw.edu.au:80"
        "NO_PROXY" = ".det.nsw.edu.au,.nsw.education"
        "CITRIX_CLIENT_ID" = "caceffd6-26cd-4e5f-bc82-cd4a8bf78a68"
        "CITRIX_CLIENT_SECRET" = "FotGmTt-p5vYMLlr9c0L1Q=="
        "CITRIX_CUSTOMER_ID" = "xdp1c5"
        "ZABBIX_SERVER" = "collector-agent-linux-pcp.mtm.det.nsw.edu.au:10052"
      }

      resources {
        cpu = 400
        memory = 1500

        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10063
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:pgsql12-novolume"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "custom-externalscripts-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {

        cpu = 400
        memory = 1500

        network {
          port "postgresql" {}
        }
      }
    }
  }
}
