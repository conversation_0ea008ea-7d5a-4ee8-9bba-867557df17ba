# Current NVPS: 175.23

job "generic-jmx" {
  datacenters = ["dc-un-prod"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }


    task "generic_jmx_proxy" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:centos-5.0.3-odbc"
        hostname = "collector-generic-jmx.mtm.det.nsw.edu.au"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }
      }

      service {
        name = "generic-jmx-passive"
        port = "zabbix_passive"


        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "generic-jmx"
        port = "zabbix_server"


        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        "ZBX_HOSTNAME" = "collector-generic-jmx.mtm.det.nsw.edu.au"
        "ZBX_SERVER_HOST" = "pu0992tedbd001.hbm.det.nsw.edu.au"
        "DB_SERVER_HOST" = "${NOMAD_IP_generic_jmx_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_generic_jmx_db_postgresql}"

        "ZBX_PROXYOFFLINEBUFFER" = "3"

        "ZBX_STARTPOLLERS" = "16"
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        "ZBX_STARTTRAPPERS" = "15"
        "ZBX_STARTPINGERS" = "16"
        "ZBX_STARTDISCOVERERS" = "4"
        "ZBX_CACHESIZE" = "256M"
        "ZBX_HISTORYCACHESIZE" = "128M"
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        "ZBX_UNREACHABLEPERIOD" = "120"
        "ZBX_UNREACHABLEDELAY" = "30"


        # "ZBX_PROXYMODE" = "0"
        # "ZBX_SERVER_PORT" = "10051"
        # "ZBX_LOADMODULE" = ""
        # "ZBX_DEBUGLEVEL" = "3"
        "ZBX_JAVAGATEWAY_ENABLE" = "true"

        # "ZBX_PROXYLOCALBUFFER" = "0"
        # "ZBX_PROXYHEARTBEATFREQUENCY" = "60"
        # "ZBX_DATASENDERFREQUENCY" = "1"
        # "ZBX_IPMIPOLLERS" = "0"
        # "ZBX_STARTHTTPPOLLERS" = "1"

        "ZBX_JAVAGATEWAY" = "${NOMAD_IP_generic_jmx_java_gateway_zabbix_java}"
        "ZBX_JAVAGATEWAYPORT" = "${NOMAD_PORT_generic_jmx_java_gateway_zabbix_java}"
        "ZBX_STARTJAVAPOLLERS" = "16"

        # "ZBX_STARTVMWARECOLLECTORS" = "0"
        # "ZBX_VMWAREFREQUENCY" = "60"
        # "ZBX_VMWARECACHESIZE" = "8M"
        # "ZBX_ENABLE_SNMP_TRAPS" = "false"
        # "ZBX_HOUSEKEEPINGFREQUENCY" = "1"
        # "ZBX_STARTDBSYNCERS" = "4"
        # "ZBX_TRAPPERIMEOUT" = "300"
        # "ZBX_UNAVAILABLEDELAY" = "60"

        # "ZBX_TLSCONNECT" = "unencrypted"
        # "ZBX_TLSACCEPT" = "unencrypted"
        # "ZBX_TLSCAFILE" = ""
        # "ZBX_TLSCRLFILE" = ""
        # "ZBX_TLSSERVERCERTISSUER" = ""
        # "ZBX_TLSSERVERCERTSUBJECT" = ""
        # "ZBX_TLSCERTFILE" = ""
        # "ZBX_TLSKEYFILE" = ""
        # "ZBX_TLSPSKIDENTITY" = ""
        # "ZBX_TLSPSKFILE" = ""

        # "ZBX_ENABLEREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_LOGREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_HOSTNAMEITEM" = "system.hostname"
        # "ZBX_SOURCEIP" = ""
        # "ZBX_VMWAREPERFFREQUENCY" = "60"
        # "ZBX_VMWARETIMEOUT" = "10"
        # "ZBX_LISTENIP" = ""
      }

            resources {
        cpu = 400
        memory = 1000

        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10059
          }
        }
      }
    }

    task "generic_jmx_java_gateway" {
      driver = "docker"

      config {
        image = "artifacts.mtm.nsw.education/zabbix-java-gateway-doe:centos-5.0-latest"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          zabbix_java = 10052
        }

        # todo: /usr/share/zabbix-java-gateway/lib/jboss-client.jar
        # /usr/sbin/zabbix_java/ext_lib
      }

      service {
        name = "generic-jmx-java"
        port = "zabbix_java"
      }

      env {
        "ZBX_START_POLLERS" = "32"

        "ZBX_DEBUGLEVEL" = "info"
        # Available since Java JDK 8u192
        # zabbix 3.4.15 java-gateway runs openjdk version "1.8.0_181"
        # TODO: Adjust logback.xml to remove "WARN  org.jboss.remotingjmx.Util - The protocol 'remoting-jmx' is deprecated"
        "JAVA_OPTIONS" = "-XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap -XX:MaxRAMFraction=2"
      }

      resources {
        cpu = 400
        memory = 1500

        network {
          port "zabbix_java" {}
        }
      }
    }

    task "generic_jmx_db" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:pgsql12-novolume"

        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "generic-jmx-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

     resources {
       cpu = 400
       memory = 1500

        network {
          port "postgresql" {}
        }
      }
    }
  }
}
