job "synthetics-minion-grafana" {
  type = "service"
  datacenters = ["dc-un-prod"]

  group "synthetics-minion-grafana" {
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }
    task "synthetics-minion-grafana" {
      driver = "docker"

      config {
        image = "grafana/synthetic-monitoring-agent"
        dns_servers = ["************"]
        logging {   
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            }
            }
      args = [
        "-verbose=true",
        "-debug=true",
        "-api-insecure=false",
        "-api-server-address=${API_SERVER}",
        "-api-token=${API_TOKEN}"
      ]            
      }

      env {
        # See https://grafana.com/docs/grafana-cloud/synthetic-monitoring/private-probes/#deploy-the-agent-using-docker
        # 
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        API_SERVER  = "synthetic-monitoring-grpc-au-southeast.grafana.net:443"
        #API_SERVER  = "https://synthetic-monitoring-api-au-southeast.grafana.net"
        API_TOKEN   = "PBweG+XMlHKs72aVZzgYb9QtybBDd3dESBwbrUGHagN5W9IBMajSNdEXqgmM7rdrrebh3OiPHcIMoNbuY/gI6IXhnoPQaDzLXAWi3Ixl6NRn35xp6nS/Roj8S5CgGb4JVdoGdd1BU3eUCQZ/6e7T5UYP8DuyZSVRDm2P7W3QKxs="
        } 
      resources {
        cpu = 250
        memory = 250
      }
    }
  }
}
