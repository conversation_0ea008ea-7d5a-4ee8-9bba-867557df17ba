job "synthetics-minion" {
  type = "service"
  datacenters = ["dc-un-prod"]

  group "synthetics-minion" {
    # MINION NAME = Central Ave 2
    # Private Location ID = 318084-central_ave_2-177
 
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd005.hbm.det.nsw.edu.au"
    }
    task "synthetics-minion" {
      driver = "docker"

      config {
        image = "https://quay.io/newrelic/synthetics-minion:latest"
        dns_servers = ["************"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }        
        volumes = [
          "local/tmp:/tmp:rw",
          "/var/run/docker.sock:/var/run/docker.sock:rw"
        ]
      }

      env {
        # See https://docs.newrelic.com/docs/synthetics/synthetic-monitoring/private-locations/containerized-private-minion-cpm-configuration/

        # Registered 26/09/2021
        MINION_PRIVATE_LOCATION_KEY = "NRSP-us0180CBEF5865F3BD74C5E92A9DD76265D"

        # Variables available to synthetic script via env
        # MINION_USER_DEFINED_VARIABLES
        # Can be used as $env.USER_DEFINED_VARIABLES.MY_VARIABLE

        MINION_API_PROXY = "proxy.det.nsw.edu.au:80"

      }

      resources {
        cpu = 800
        memory = 2500
      }
    }
  }
}

