# Current NVPS: 59.75

job "agent-windows-odrursw" {
  datacenters = ["dc-un-prod-schools"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pu0992tcdnd006.hbm.det.nsw.edu.au"
    }

    constraint {
      attribute    = "${meta.zabbix_platform}"
      set_contains = "school"
    }

    restart {
      attempts         = 30
      delay            = "30s"
      interval         = "20m"
      mode             = "delay"
    }

    task "agent_windows_odrursw_proxy" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:centos-5.0.2"
        hostname = "collector-agent-windows-odrursw.mtm.det.nsw.edu.au"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }
      }

      service {
        name = "agent-windows-odrursw-passive"
        port = "zabbix_passive"


        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "agent-windows-odrursw"
        port = "zabbix_server"


        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        "ZBX_HOSTNAME" = "collector-agent-windows-odrursw.mtm.det.nsw.edu.au"
        "ZBX_SERVER_HOST" = "pu0992tedbs001.hbm.det.nsw.edu.au"
        "DB_SERVER_HOST" = "${NOMAD_IP_agent_windows_odrursw_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_agent_windows_odrursw_db_postgresql}"

        "ZBX_PROXYOFFLINEBUFFER" = "3"

        "ZBX_STARTPOLLERS" = "32"
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        "ZBX_STARTTRAPPERS" = "15"
        "ZBX_STARTPINGERS" = "16"
        "ZBX_STARTDISCOVERERS" = "8"
        "ZBX_CACHESIZE" = "256M"
        "ZBX_HISTORYCACHESIZE" = "128M"
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        "ZBX_UNREACHABLEPERIOD" = "120"
        "ZBX_UNREACHABLEDELAY" = "30"
      }

      resources {
        cpu = 1000
        # 2023-11-21 jedd - bump from 2gb to 4gb
        memory = 4000

        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10057
          }
        }
      }
    }

    task "agent_windows_odrursw_db" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/collectors-zabbix:pgsql12-novolume"
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        } 
        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "agent-windows-odrursw-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        cpu = 1000
        memory = 2000

        network {
          port "postgresql" {}
        }
      }
    }
  }
}
