## https://www.consul.io/docs/agent/options.html#client_addr
## https://www.consul.io/docs/agent/options.html#_client

# 2021-05 jedd
# client_addr="0.0.0.0"
# client = "0.0.0.0"
# bind_addr = "0.0.0.0"
# bind = "0.0.0.0"

# bind_addr = "************, 127.0.0.1"
bind_addr = "**************"
# client_addr = "************"
client_addr = "127.0.0.1 {{ GetPrivateIPs }}"
# client_addr = "************"


## https://www.consul.io/docs/agent/options.html#retry_join
# retry_join = ["consul.service.consul", "royksopp"]
# retry_join = ["dg-pan-01.int.jeddi.org", "jarre.int.jeddi.org"]
retry_join = ["dg-pan-01.int.jeddi.org"]
