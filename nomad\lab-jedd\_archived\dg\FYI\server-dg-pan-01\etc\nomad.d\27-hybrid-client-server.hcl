# Nomad client & Server configuration

data_dir = "/var/lib/nomad/"
plugin_dir = "/usr/lib/nomad"

## https://www.nomadproject.io/docs/agent/configuration/index.html#log_level
## [WARN|INFO|DEBUG]
log_level = "WARN"

datacenter = "DG"

client {
  enabled = true
  options {
    "docker.volumes.enabled" = "true"
  }

  ### Dual-node - bit flakey
  # servers = ["**************", "*************"]
  ### Single node
  servers = ["**************"]

  host_volume "vol_flask" {
    path = "/opt/flask"
    read_only = false
  }

  host_volume "loki" {
    path = "/opt/loki"
    read_only = false
  }

  host_volume "promvol" {
    path = "/opt/prometheus"
    read_only = false
  }

  host_volume "promconfvol" {
    path = "/opt/prometheus-configuration"
    read_only = false
  }

  host_volume "vol_bookstack" {
    path = "/opt/bookstack"
    read_only = false
  }

  host_volume "vol_cortex" {
    path = "/opt/cortex"
    read_only = false
  }

  host_volume "vol_dashy" {
    path = "/opt/dashy"
    read_only = false
  }

  host_volume "vol_mariadb" {
    path = "/opt/mariadb"
    read_only = false
  }

  host_volume "vol_mimir" {
    path = "/opt/mimir"
    read_only = false
  }

  host_volume "vol_minio" {
    path = "/opt/minio"
    read_only = false
  }

  host_volume "vol_tempo" {
    path = "/opt/tempo"
    read_only = false
  }

  host_volume "vol_nodered" {
    path = "/opt/nodered"
    read_only = false
  }

  host_volume "vol_postgresql" {
    path = "/opt/postgresql"
    read_only = false
  }

  host_volume "vol_timescaledb" {
    path = "/opt/timescaledb"
    read_only = false
  }

  host_volume "vol_var_log" {
    path = "/var/log"
    read_only = true
  }

  host_volume "vol_plausible_postgres" {
    path = "/opt/plausible_postgres"
    read_only = false
  }


  # For cadvisor - needs a few mappings, but can all be read-only
  # --volume=/:/rootfs:ro \
  # --volume=/var/run:/var/run:ro \
  # --volume=/sys:/sys:ro \
  # --volume=/var/lib/docker/:/var/lib/docker:ro \
  # --volume=/dev/disk/:/dev/disk:ro \

  host_volume "vol_var_run" {
    path = "/var/run"
    read_only = true
  }
  host_volume "vol_rootfs" {
    path = "/"
    read_only = true
  }
  host_volume "vol_sys" {
    path = "/sys"
    read_only = true
  }
  host_volume "vol_var_lib_docker" {
    path = "/var/lib/docker"
    read_only = true
  }
  host_volume "vol_dev_disk" {
    path = "/dev/disk"
    read_only = true
  }



}

bind_addr = "0.0.0.0"

advertise {
  # This should be the IP of THIS MACHINE and must be routable by every node
  # in your cluster

  rpc = "**************:4647"
}

server {
  enabled          = true
  # bootstrap_expect = the number of servers to wait for before bootstrapping - typically
  # an odd number - but in a (dangerous) two-node cluster, one can be 1 (jarre), the other 0 (pan).
  bootstrap_expect = 1
}

telemetry {
  collection_interval = "10s"
  disable_hostname = true
  prometheus_metrics = true
  publish_allocation_metrics = true
  publish_node_metrics = true
}

#plugin "docker" {
#  config {
#    # allow_privileged = true
#    allow_caps = [ "net_raw" ]
#    }
#  }
#}

## plugin "docker" {
##   config {
##     gc {
##       image  =  false
##       image_delay = "3m"
##     }
##   }
## }



