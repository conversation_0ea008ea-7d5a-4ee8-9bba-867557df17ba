# Nomad client & Server configuration

data_dir = "/var/lib/nomad/"

datacenter = "DG"

client {
  enabled = true
  options {
    "docker.volumes.enabled" = "true"
  }
  servers = ["**************"]
  host_volume "promvol" {
    path = "/opt/prometheus-LTS"
    read_only = false
  }
}

bind_addr = "0.0.0.0"

advertise {
  # This should be the IP of THIS MACHINE and must be routable by every node
  # in your cluster

  rpc = "**************:4647"
}

server {
  enabled          = true
  bootstrap_expect = 0
  # docker.volumes.enabled = true
}

telemetry {
  collection_interval = "10s"
  disable_hostname = true
  prometheus_metrics = true
  publish_allocation_metrics = true
  publish_node_metrics = true
}
