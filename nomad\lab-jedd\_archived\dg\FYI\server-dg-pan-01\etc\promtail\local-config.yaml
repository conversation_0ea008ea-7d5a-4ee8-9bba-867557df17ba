server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/promtail-positions.yaml

clients:
  - url: http://loki.int.jeddi.org:3100/loki/api/v1/push


scrape_configs:
- job_name: system

  static_configs:
  - targets:
      - localhost
    labels:
      job: varlog-logs
      host: dg-pan-01
      __path__: /var/log/*log 

  - targets:
      - localhost
    labels:
      job: varlog-messages
      host: dg-pan-01
      __path__: /var/log/messages

#  relabel_configs:
#    - source_labels: ['__syslog_message_hostname']
#      target_label: 'host'


- job_name: system
  journal:
    json: true
    max_age: 12h
    labels:
      job: journal
      host: dg-pan-01
  relabel_configs:
###    - source_labels: [__journal__comm]
###      action: keep
###      regex: syncthing
###    - source_labels: [__journal_message]
###      action: keep
###      regex: Finished\ syncing

    - source_labels: [__journal__comm, __journal_message]
      separator: ;
      action: keep
      regex: ^syncthing;.*Finished.*

# Note - if you get errors like this:
##  promtail: level=error caller=journaltarget.go:187 msg="received error during sdjournal follow" err="Timeout expired"
##  promtail: level=error caller=journaltarget.go:190 msg="unable to follow journal" err="Timeout expired"
# .. then you need this setting -- default of 10s can sometimes be insufficient on even moderately loaded hosts

timeout: 30s



