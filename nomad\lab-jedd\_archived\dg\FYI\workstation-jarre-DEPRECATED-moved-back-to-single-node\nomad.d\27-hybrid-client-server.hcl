# Nomad client & Server configuration

data_dir = "/var/lib/nomad/"

datacenter = "DG"

client {
  enabled = true
  options {
    "docker.volumes.enabled" = "true"
  }
  servers = ["*************", "**************"]
}

bind_addr = "0.0.0.0"

advertise {
  # This should be the IP of THIS MACHINE and must be routable by every node
  # in your cluster

  rpc = "*************:4647"
}

server {
  enabled          = true
  # bootstrap_expect = the number of servers to wait for before bootstrapping - typically
  # an odd number - but in a (dangerous) two-node cluster, one (jarre) can be 1, the other (pan) 0
  bootstrap_expect = 1
}

telemetry {
  collection_interval = "10s"
  disable_hostname = true
  prometheus_metrics = true
  publish_allocation_metrics = true
  publish_node_metrics = true
}
