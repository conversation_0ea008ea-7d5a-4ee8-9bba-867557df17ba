This is the configuration <PERSON><PERSON>'s using in his lab box.

My lab configuration files (consul, nomad, docker) are under this repo at nomad/lab-jedd-dg/FYI/                                                                                                                                                                               

I have two hosts - server (dg-pan-01) and a workstation (jarre) - both running Debian GNU/Linux.                                                                                                                                                                                 

The .nomad files track the current preferences / styles we're using in
the obscol (prod), but applied to a subset (obviously) of hosts that
are available locally.

Some additions are here for reference under the FYI directory - for example
under FYI/nomad.d shows what configuration I've got for nomad.   Volume
mounting (such as the NFS vol we'll need for prometheus long term storage)
that requires a client (nomad config) change.

Note that I'm using Debian bullseye (stable/testing) with current versions 
of nomad (1.1.2) and consul (1.10.0) at time of writing this README.


# = = = = = = = = =
promtail-system - there's two ways of pulling 'natively tagged' logs from docker containers.
First - add boilerplate to every (nomad) job - this is reliable but inelegant.
Second - rely on a file that contains a lookup for these data that you generate 
  outside of nomad/docker - this is unreliable but slightly more elegant.

My promtail(s) are using the latter method, pinched from:

Command to generate the file is:
  docker ps --format '- targets: ["{{.ID}}"]\n  labels:\n    container_name: "{{.Names}}"' > /etc/promtail/promtail-targets.yaml
  (don't send to /tmp - as it'll crap itself out on the far end after a reboot and the file does not exist)


# = = = = = = = = =
Oracle 
Using the image:  https://hub.docker.com/r/oracleinanutshell/oracle-xe-11g
Once running, create our service account using the standard (wiki documented) process
Note that these won't persist.

    SQL> create user  srvmtmzbx  identified by bigsecretpassword default tablespace users temporary tablespace temp;         
    SQL> grant create session, select_catalog_role to srvmtmzbx;

To get there, you should docker exec into the container, and then you can run 'sqlplus', using:
    u:  system
    p:  oracle

Apex is disabled (port clash with traefik, and not hugely useful) but can be re-enabled
from the nomad recipe provided - then the url is:  :8888/apex/apex_admin with credentials:
    u:  ADMIN
    p:  admin

Note - don't go to just :8888/ -- you'll get an htauth request, which is NOT what you want for Apex.


# = = = = = = = = =
Logging with Loki -- 
  https://atodorov.me/2021/07/09/logging-on-nomad-and-log-aggregation-with-loki/
  -- has a good write up on some options around this.

Originally we were disinclined to do additional stanzas per container to grab logs, and tried to
find ways to have this automated (it's certainly doable) for any new nomad job that was set up.

Subsequently, we're looking to use the native logging / type=loki feature for nomad jobs, as
we're not spinning up huge numbers of ephemeral jobs, and customising extra labels etc is
something we should probably take some care around per job.

Note that on each candidate server we need to do this first:

docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions

The alias means that in the task { config { logging { type =    section, we can specify 'loki' rather than
the protracted name.



