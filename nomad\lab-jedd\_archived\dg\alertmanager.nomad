// jedd lab - alertmanager - talking to prometheus-LTS nomad 

job "alertmanager" {
  type = "service"
  datacenters = ["DG"]

  group "alertmanager" {

    network {
      port "http" {
        static = 9093
      }
    }

    task "alertmanager" {
      driver = "docker"

      config {
        ports = ["http"]
        image = "https://docker.io/prom/alertmanager:v0.22.2"
        dns_servers = ["192.168.27.123"]
        volumes = [
          "local/alertmanager.yaml:/etc/alertmanager/alertmanager.yml",
          "local/prometheus-configuration/dg-pan-01/alertmanager/templates:/etc/alertmanager/template",
        ]
        args = [
          "--web.external-url=https://alertmanager.int.jeddi.org",
          # For reasons that aren't clear I need this specified - prod-obscol does not
          "--config.file=/etc/alertmanager/alertmanager.yml"
        ]
      }

      service {
        name = "alertmanager"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.alertmanager.rule=Host(`alertmanager.int.jeddi.org`)",
#          "traefik.http.routers.alertmanager.tls=false",
#        ]
      }

      artifact {
        # @TODO go-getter - hashicorp underlying agnostic utility to retrieve git repos - ONLY works with
        #       a username of 'git' - which obviously breaks with gitolite, which uses a user of 'gitolite'.
        # source = "git::ssh://<EMAIL>/prometheus-configuration.git"
        # source = "git::<EMAIL>:prometheus-configuration.git"

        source = "git::ssh://<EMAIL>/prometheus-configuration"
        destination = "local/prometheus-configuration"

        options {
          # sshkey = "{{ key "sshkey/gitolite-prometheus-configuration" }}"
          sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }



      template {
        data = <<EOH
templates:
- '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 3h
  receiver: xmatters-jeddi-prom-alertmanager

receivers:
- name: xmatters-jeddi-prom-alertmanager
  webhook_configs:
  - url: https://jeddi.xmatters.com/api/integration/1/functions/481b90d5-6cf9-4232-9e34-723eca61edbe/triggers?apiKey=53e976f3-28a3-44d7-aea0-25784a446e76&recipients=xmatters


EOH
        destination = "local/alertmanager.yaml"
      }
    }
  }
}
