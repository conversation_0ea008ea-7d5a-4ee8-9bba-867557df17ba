
executable_name: "/usr/bin/nomad"
# executable_name: "nomad"
# open_port: 4646
  # system_wide: true

service_name: nomad
log_level: DEBUG

print_traces: true

ebpf:
  wakeup_len: 100

#otel_traces:
#  endpoint: https://otlp-gateway-prod-eu-west-0.grafana.net/otlp

prometheus_export:
  # port: 8999
  # {{ env "NOMAD_PORT_grpc" }}    

  port:  {{ env "NOMAD_PORT_port_beyla_prom" }}    
  path: /metrics

internal_metrics:
  port:  {{ env "NOMAD_PORT_port_beyla_prom" }}    
  path: /metrics

