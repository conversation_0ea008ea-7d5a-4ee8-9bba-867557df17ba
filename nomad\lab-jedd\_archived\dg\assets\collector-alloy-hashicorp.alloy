// 2024-12-06 jedd - configuration dump from jeddii.grafana.net


// token name:   dg-hac
// obtained 2024-12-07

//  ARCH="amd64"
//  GCLOUD_HOSTED_METRICS_URL="https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push"
//  GCLOUD_HOSTED_METRICS_ID="624363"
//  GCLOUD_SCRAPE_INTERVAL="60s"
//  GCLOUD_HOSTED_LOGS_URL="https://logs-prod-004.grafana.net/loki/api/v1/push"
//  GCLOUD_HOSTED_LOGS_ID="311152"
//  GCLOUD_RW_API_KEY="glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="
//  /bin/sh -c "$(curl -fsSL https://storage.googleapis.com/cloud-onboarding/alloy/scripts/install-linux.sh)"


// FLEET management token name:  dg-hac
// FLEET management token :  glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWZsZWV0LW1hbmFnZW1lbnQtYXBpLWRnLWhhYyIsImsiOiJMODAyWWU2QmkzRzNuMUJXMHpLQjhGcTEiLCJtIjp7InIiOiJhdSJ9fQ==
// FLEET management token :  glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWZsZWV0LW1hbmFnZW1lbnQtYXBpLWRnLWhhYyIsImsiOiJMODAyWWU2QmkzRzNuMUJXMHpLQjhGcTEiLCJtIjp7InIiOiJhdSJ9fQ==



// Actual configuration = = = = = = = = = = = = = = = = = = = = = = = =

prometheus.exporter.self "integrations_alloy" { }

logging {
    write_to = [loki.process.logs_integrations_integrations_alloy_health.receiver]
    // level can be one of:  error, warn, info, debug
    level  = "warn"
    format = "logfmt"
}

tracing {
  sampling_fraction = 0.1
  write_to          = [otelcol.exporter.otlphttp.grafanacloud.input]
}







// 2024-12-06 jedd - using credentials from jeddii.grafana.net integration
otelcol.exporter.otlphttp "grafanacloud" {
  client {
    // 2024-12-10 claude suggested changing this to reflect error hints
    //endpoint = "https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp/v1/traces"
    endpoint = "https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp"

    // 2024-12-10 claude suggested headers embedment
    headers = {
      "Authorization" = "Bearer glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ==",
      "X-Scope-OrgID" = "624363",

    }


    // 2024-12-10 claude suggests a new auth header option (rather than auth basic)
    //auth = otelcol.auth.basic.grafanacloud.handler
    // auth = otelcol.auth.header.grafanacloud.handler
  }
}








// 2024-12-06 jedd - using credentials from jeddii.grafana.net integration
otelcol.auth.basic "grafanacloud" {

  // 2024-12-10 jedd - claude suggests removing username:
  //username = "624363"
  //password = "glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="
  username = ""  // Leave empty since we're using bearer token
  password = "glc_yeJvIjoiOTc9OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="


}


//otelcol.auth.header "grafanacloud" {
//  header = "Authorization"
 // value = "Bearer glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="
//}






discovery.relabel "integrations_alloy" {
  targets = prometheus.exporter.self.integrations_alloy.targets

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "alloy_hostname"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "namespace"
    replacement  = "dg-hac"
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy-check"
  }

  rule {
    target_label = "env"
    replacement  = "prod"
  }  
}

prometheus.exporter.self "integrations_alloy_health" {}

discovery.relabel "integrations_alloy_health" {
  targets = prometheus.exporter.self.integrations_alloy_health.targets

  rule {
    replacement = constants.hostname
    target_label  = "instance"
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy"
  }
}

prometheus.scrape "integrations_alloy_health" {
  targets    = discovery.relabel.integrations_alloy_health.output
  forward_to = [prometheus.relabel.integrations_alloy_health.receiver]
  job_name   = "{{env "NOMAD_JOB_NAME"}}"
}

prometheus.relabel "integrations_alloy_health" {
  forward_to = [prometheus.remote_write.metrics_service.receiver]

  rule {
    source_labels = ["__name__"]
    regex         = "alloy_build_info|alloy_component_controller_evaluating|alloy_component_controller_running_components|alloy_component_dependencies_wait_seconds|alloy_component_dependencies_wait_seconds_bucket|alloy_component_evaluation_seconds|alloy_component_evaluation_seconds_bucket|alloy_component_evaluation_seconds_count|alloy_component_evaluation_seconds_sum|alloy_component_evaluation_slow_seconds|alloy_config_hash|alloy_resources_machine_rx_bytes_total|alloy_resources_machine_tx_bytes_total|alloy_resources_process_cpu_seconds_total|alloy_resources_process_resident_memory_bytes|cluster_node_gossip_health_score|cluster_node_gossip_proto_version|cluster_node_gossip_received_events_total|cluster_node_info|cluster_node_lamport_time|cluster_node_peers|cluster_node_update_observers|cluster_transport_rx_bytes_total|cluster_transport_rx_packet_queue_length|cluster_transport_rx_packets_failed_total|cluster_transport_rx_packets_total|cluster_transport_stream_rx_bytes_total|cluster_transport_stream_rx_packets_failed_total|cluster_transport_stream_rx_packets_total|cluster_transport_stream_tx_bytes_total|cluster_transport_stream_tx_packets_failed_total|cluster_transport_stream_tx_packets_total|cluster_transport_streams|cluster_transport_tx_bytes_total|cluster_transport_tx_packet_queue_length|cluster_transport_tx_packets_failed_total|cluster_transport_tx_packets_total|exporter_send_failed_spans_ratio_total|exporter_sent_spans_ratio_total|go_gc_duration_seconds_count|go_goroutines|go_memstats_heap_inuse_bytes|processor_batch_batch_send_size_ratio_bucket|processor_batch_metadata_cardinality_ratio|processor_batch_timeout_trigger_send_ratio_total|prometheus_remote_storage_bytes_total|prometheus_remote_storage_highest_timestamp_in_seconds|prometheus_remote_storage_metadata_bytes_total|prometheus_remote_storage_queue_highest_sent_timestamp_seconds|prometheus_remote_storage_samples_failed_total|prometheus_remote_storage_samples_retried_total|prometheus_remote_storage_samples_total|prometheus_remote_storage_sent_batch_duration_seconds_bucket|prometheus_remote_storage_sent_batch_duration_seconds_count|prometheus_remote_storage_sent_batch_duration_seconds_sum|prometheus_remote_storage_shards|prometheus_remote_storage_shards_max|prometheus_remote_storage_shards_min|prometheus_remote_write_wal_samples_appended_total|prometheus_remote_write_wal_storage_active_series|receiver_accepted_spans_ratio_total|receiver_refused_spans_ratio_total|rpc_server_duration_milliseconds_bucket|scrape_duration_seconds|up"
    action        = "keep"
  }
}



prometheus.remote_write "metrics_service" {
  endpoint {
    url = "https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push"
    // proxy_from_environment = true
    basic_auth {
      username = "624363"
      password = "glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="
    }
  }
}





//2024-12-06 jedd - probably need this
//loki.write "grafana_cloud_loki" {
//  endpoint {
//    # url = "https://logs-prod-004.grafana.net/loki/api/v1/push"
//    url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
//    # proxy_from_environment = true
//    basic_auth {
//      username = "382995"
//      password = "glc_yeJvIjoiMTM2NzU2IiwibiI6InN0YWNrLTUzMzYxMi1pbnRlZ3JhdGlvbi1ncmFmYW5hX2FsbG95X3Rlc3Rfb2JzY29sX2luc3RhbmNlIiwiayI6IjM0ckZpNU03eTJ6SWg5ZVM4YTd6Wng4NCIsIm0iOnsiciI6ImF1In19"
//    }
//  }
//}

// 2024-12-06 jedd - using credentials from jeddii.grafana.net integration
loki.write "grafana_cloud_loki" {
  endpoint {
    url = "https://logs-prod-004.grafana.net/loki/api/v1/push"
    basic_auth {
      username = "311152"
      password = "glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ=="
    }
  }
}





prometheus.exporter.consul "integrations_consul_exporter" {
  server = "{{ env "node.unique.name"}}.obs.int.jeddi.org:8500"
}

discovery.relabel "integrations_consul_exporter" {
  targets = prometheus.exporter.consul.integrations_consul_exporter.targets

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }
      rule {
    target_label = "env"
    replacement  = "prod"
  }
}

prometheus.scrape "integrations_consul_exporter" {
  targets    = discovery.relabel.integrations_consul_exporter.output
  forward_to = [prometheus.remote_write.metrics_service.receiver]
  job_name   = "integrations/consul_exporter"
}

prometheus.exporter.cadvisor "integrations_cadvisor" {
    docker_only = true
}
discovery.relabel "integrations_cadvisor" {
    targets = prometheus.exporter.cadvisor.integrations_cadvisor.targets

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }
      rule {
    target_label = "env"
    replacement  = "prod"
  }
}
prometheus.scrape "integrations_cadvisor" {
    targets    = discovery.relabel.integrations_cadvisor.output
    forward_to = [prometheus.remote_write.metrics_service.receiver]
}


discovery.relabel "metrics_integrations_integrations_nomad" {
  targets = concat(
    [{
      __address__ = "{{ env "node.unique.name" }}.int.jeddi.org:4646",
    }],      
  )

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }
  rule {
    target_label = "DC"
    replacement  = "DG"
  }
  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }
//  rule {
//    target_label = "env"
//    replacement  = "prod"
//  }     
}

prometheus.scrape "metrics_integrations_integrations_nomad" {
  targets    = discovery.relabel.metrics_integrations_integrations_nomad.output
  forward_to = [prometheus.remote_write.metrics_service.receiver]
  job_name   = "integrations/nomad"
  params     = {
    format = ["prometheus"],
  }
  metrics_path = "/v1/metrics"
}

discovery.relabel "metrics_integrations_integrations_traefik" {
  targets = concat(
    [{
      __address__ = "{{ env "node.unique.name" }}.int.jeddi.org:8081",
    }],     
  )

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }
  rule {
    target_label = "DC"
    replacement  = "DG"
  }
//  rule {
//    target_label = "env"
//    replacement  = "prod"
//  }    
  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }     
}

// Traefik is the vast bulk of my metrics, mostly because of the insane
// volume of histogram buckets for every type of http query / route / endpoint.
//prometheus.scrape "metrics_integrations_integrations_traefik" {
//  targets    = discovery.relabel.metrics_integrations_integrations_traefik.output
//  forward_to = [prometheus.remote_write.metrics_service.receiver]
//  job_name   = "integrations/traefik"
//}

loki.process "logs_integrations_integrations_alloy_health" {
  forward_to = [loki.relabel.logs_integrations_integrations_alloy_health.receiver]

  stage.regex {
    expression = "(level=(?P<log_level>[\\s]*debug|warn|info|error))"
  }
  
  stage.labels {
    values = {
      level = "log_level",
    }
  }
}

loki.relabel "logs_integrations_integrations_alloy_health" {

  forward_to = [loki.write.grafana_cloud_loki.receiver]

  rule {
    replacement = constants.hostname
    target_label  = "instance"
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy"
  }
}



// 2024-12-10 jedd - trying to get remotecfg working sanely
// From jeddii.grafana.net, I'm told:
// collector api is:
//     https://fleet-management-prod-004.grafana.net/collector.v1.CollectorService/
// pipeline api is:
//     https://fleet-management-prod-004.grafana.net/pipeline.v1.PipelineService/
// api authentication:
//     461795￼

// BUT documentation uses different endpoint and variable names, of course:
// grafana.net fleet management  page says:
//   "The Fleet Management API supports Basic Auth where Instance ID 
//    is the username and API Token is the password."
// So the fleet token NAME is 'dg-hac' again (as per alloy 'receiver' proper)
// and the username is the api authentication above ( 461795￼)

remotecfg {
    // url = <SERVICE_URL>
    url = "https://fleet-management-prod-004.grafana.net/collector.v1.CollectorService/"

    basic_auth {
        // username      = <USERNAME>
        // password_file = <PASSWORD_FILE>
        username = "461795"
        password = "glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWZsZWV0LW1hbmFnZW1lbnQtYXBpLWRnLWhhYyIsImsiOiJMODAyWWU2QmkzRzNuMUJXMHpLQjhGcTEiLCJtIjp7InIiOiJhdSJ9fQ=="
    }

    id             = constants.hostname
    attributes     = {"cluster" = "dg-hac", "namespace" = "otlp-hac"}
    poll_frequency = "5m"
}

