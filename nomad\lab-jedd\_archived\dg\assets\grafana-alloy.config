prometheus.exporter.self "integrations_alloy" { }

logging {
  level  = "info"
  format = "logfmt"
}

// tracing {
//   sampling_fraction = 0.1
//   write_to          = [otelcol.exporter.otlp.grafanacloud.input]
// }

// otelcol.exporter.otlp "grafanacloud" {
//   client {
//     endpoint = "tempo-prod-03-au-southeast-0.grafana.net:443"
//     auth = otelcol.auth.basic.grafanacloud.handler
//   }
// }

// otelcol.auth.basic "grafanacloud" {
//   username = "379508"
//   password = "********************************************************************************************************************************************************"
// }

discovery.relabel "integrations_alloy" {
  targets = prometheus.exporter.self.integrations_alloy.targets

  rule {
    target_label = "instance"
    replacement  = constants.hostname
  }

  rule {
    target_label = "alloy_hostname"
    replacement  = constants.hostname
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy-check"
  }
}

// prometheus.scrape "integrations_alloy" {
//   targets    = discovery.relabel.integrations_alloy.output
//   forward_to = [prometheus.relabel.integrations_alloy.receiver]  
// 
//   scrape_interval = "60s"
// }

// prometheus.relabel "integrations_alloy" {
//   forward_to = [prometheus.remote_write.metrics_service.receiver]
// 
//   rule {
//     source_labels = ["__name__"]
//     regex         = "(prometheus_target_sync_length_seconds_sum|prometheus_target_scrapes_.*|prometheus_target_interval.*|prometheus_sd_discovered_targets|alloy_build.*|prometheus_remote_write_wal_samples_appended_total|process_start_time_seconds)"
//     action        = "keep"
//   }
// }

// prometheus.remote_write "metrics_service" {
//   endpoint {
//     url = "https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push"
//     proxy_from_environment = true
//     basic_auth {
//       username = "768052"
//       password = "********************************************************************************************************************************************************"
//     }
//   }
// }

// loki.write "grafana_cloud_loki" {
//   endpoint {
//     url = "https://logs-prod-004.grafana.net/loki/api/v1/push"
//     proxy_from_environment = true
//     basic_auth {
//       username = "382995"
//       password = "********************************************************************************************************************************************************"
//     }
//   }
// }

