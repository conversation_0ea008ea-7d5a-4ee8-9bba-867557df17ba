
# Migrated from <PERSON>' 2025-04-01 instance for loki Version 3.4.x

# <PERSON>' notes / gotchas:
# - RWB is easier with a simplier config, I believe g<PERSON><PERSON><PERSON><PERSON> has set suitable defaults
#
# - Compactor address needs to use the http://loki.obs.nsw.education endpoint
#
# - Backend task needs a router other than loki.obs.nsw.education otherwise reads dont work, 
#   similar to how mimir-rwb is setup in Traefik.
#
# - Flushing ingestors on shutdown to s3 can take minutes -- which causes canary type updates 
#   to fail, as there is still a process accessing files on the NFS mount.
#
# - Read & Write nomad tasks can scale as we need, but backend can remain at 1
#   @TODO need to test in a patching cycle to understand if running multiple backends 
#   in a ring impacts the performance of (f.e.) queries.

auth_enabled: false


server:
  log_level: warn
  grpc_server_max_recv_msg_size: 58152281 
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

common:
  <PERSON> notes this is a gotcha .. but not why.
  compactor_address: http://loki-rwb.obs.int.jeddi.org

  # path_prefix is poorly documented, but seems to be file system related
  path_prefix: /loki

  replication_factor: 2

  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}

  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc "}}
    kvstore:
      store: consul
      # kvstore.prefix - is where (in Consul) the variables are stored for
      #  ingester, compactor ring, etc.  Must end in a forward slash ( / ).
      prefix: loki-rwb/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  wal:
    enabled: true
    dir: /loki/tsdb/{{ env "node.unique.name" }}/data/wal
    # This flush can take minutes to push wal contents to blob storage (s3), but with
    # persistent (local file system) storage it is not needed.
    flush_on_shutdown: false 
    #replay_memory_ceiling: "1G"

schema_config:
  configs:
  # This section not really needed, as we're starting fresh with tsdb store in 2025-04
  #- from: 2022-05-15
  #  store: boltdb-shipper
  #  object_store: s3
  #  schema: v13
  #  index:
  #    prefix: index_
  #    period: 24h

  # Allows loki to know that after this data the schema in the object store is tsdb
  - from: 2025-01-01
    store: tsdb
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h

storage_config:
  # 'Modern' versions of loki use tsdb, not boltdb.
  tsdb_shipper: 
    # Historically we would have something like a path (on the volume mounted, nfs say)
    # with a unique sub-directory element per host (task), such as:
    # But - with cache being moved back to ephemeral storage (mostly because NFS is a
    # bottleneck at work) this isn't needed - we're entirely in Alloc Ephemeral.
    # cache_location: /loki/tsdb/{{ env "node.unique.name" }}/data/data/index-cache
    cache_location: {{ env "NOMAD_ALLOC_DIR" }}/data/index-cache
    active_index_directory: {{ env "NOMAD_ALLOC_DIR" }}/data/index
    cache_ttl: 2h
  aws:
    s3: https://garage-s3.obs.int.jeddi.org/loki-rwb
    bucketnames: loki-rwb
    region: garage
    # Retrieve S3 (garage) key & secret from Consul. 
    access_key_id: {{ key "loki-rwb/access_key_id" }}
    secret_access_key: {{ key "loki-rwb/secret_access_key" }}
    s3forcepathstyle: true

limits_config:
  # Do not allow Loki to ingest old data.
  reject_old_samples: false 

  # Specifically 7 days
  reject_old_samples_max_age: 168h 
  volume_enabled: true 
  s3_sse_type: AES256

  # allow_structured_metadata triggers enablement of OTLP - labels will be applied
  # in the OTLP collector, implies some OTLP-specific label indexing in Loki.
  # allow_structured_metadata: true 

  # Global ingest rates - much more modest in the home lab.
  ingestion_rate_mb: 50
  ingestion_burst_size_mb: 50
  per_stream_rate_limit: 6MB

  # Ask loki to delete its own data.  Refer compactor.retention_enabled.  (672 h = 28 days)
  retention_period: 672h

compactor:
  working_directory: /loki/tsdb/{{ env "node.unique.name" }}/data/compactor
  
  # Default compaction interval is 10 minutes - we can probably run much slower.
  # apply_retention_interval of 0 says to stay in sync with compaction interval.
  compaction_interval: 30m
  apply_retention_interval: 0s

  # Another poorly documented configuration item - 
  delete_request_store: s3

  # If retention_enabled is false, we _shouldn't need_ 'delete_request_store' configured.
  # Normally we'd set this to 'true', configure delete_request_store to s3, hand off the task of
  # deleting old blocks to S3 expiration policy.  Otherwise we'd want retention_enabled to true,
  # AND deletion_mode set to `filter-only` or `filter-and-delete`.
  # With retention_enabled set to false, the Compactor will _only_ compact tables - it won't
  # delete data. (Crikey.)  Also, retention is only available IF index period is 24h.
  # retention_enabled: false

  # Ask loki to delete its own data.  Refer limits_config.retention_period.
  retention_enabled: true

  # @TODO  2025-04-29 - work out if I want to revisit retention / deletion via Loki, or
  # handle it via garage lifecycle configuration.
  # Though the decision may be made for me - it looks like S3 versioning has been on
  # the backlog for a while:  https://git.deuxfleurs.fr/Deuxfleurs/garage/issues/166
  #
  # So if I want to keep _some_ subset of log data, say SyncThing, I should put that
  # into a separate tenant, and use an `overrides` section with longer retention period.
  # Refer:
  # https://grafana.com/docs/loki/latest/operations/storage/retention/#configuring-the-retention-period


analytics:
  reporting_enabled: false
