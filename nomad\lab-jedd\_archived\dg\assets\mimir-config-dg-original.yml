multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  log_level: "info"
  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  http_tls_config:
    client_auth_type: "VerifyClientCertIfGiven"
    client_ca_file: "/secrets/certs/CA.pem"
    cert_file: "/secrets/certs/cert.pem"
    key_file: "/secrets/certs/key.pem"
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_tls_config:
    client_auth_type: "RequireAndVerifyClientCert"
    client_ca_file: "/secrets/certs/CA.pem"
    cert_file: "/secrets/certs/cert.pem"
    key_file: "/secrets/certs/key.pem"

distributor:
  ha_tracker:
    enable_ha_tracker: true
    kvstore:
      store: "consul"
      prefix: "mimir/ha-tracker/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
  ring:
    instance_id: {{ env "NOMAD_ALLOC_ID" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester_client:
  grpc_client_config:
    grpc_compression: "snappy"
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"

ingester:
  ring:
    replication_factor: 1
    instance_id: {{ env "NOMAD_ALLOC_ID" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_availability_zone: {{ env "node.unique.name" }}
    zone_awareness_enabled: true
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

querier:
  store_gateway_client:
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"

frontend_worker:
  scheduler_address: "mimir-query-scheduler.service.consul:8096"
  id: {{ env "NOMAD_ALLOC_ID" }}
  grpc_client_config:
    grpc_compression: "snappy"
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"

frontend:
  scheduler_address: "mimir-query-scheduler.service.consul:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"

ruler:
  enable_api: true
  rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/
  external_url: "https://mimir-rules.service.consul"
  poll_interval: "1m"
  evaluation_interval: "1m"
  # alertmanager_url: "dns:///alertmanager.service.consul:9876"
  alertmanager_url: "https://alertmanager.service.consul:9876"
  ruler_client:
    grpc_compression: "snappy"
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"
  query_frontend:
    address: "dns:///mimir-querier-frontend.service.consul"
    grpc_client_config:
      grpc_compression: "snappy"
      tls_enabled: true
      tls_ca_path: "/secrets/certs/CA.pem"
      tls_cert_path: "/secrets/certs/cert.pem"
      tls_key_path: "/secrets/certs/key.pem"

ruler_storage:
  backend: "s3"
  s3:
    endpoint: "s3.nahsi.dev"
    region: "syria"
    bucket_name: "mimir-rules"
    access_key_id: "${S3_ACCESS_KEY_ID}"
    secret_access_key: "${S3_SECRET_ACCESS_KEY}"

compactor:
  data_dir: {{ env "NOMAD_ALLOC_DIR" }}/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "NOMAD_ALLOC_ID" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway:
  sharding_ring:
    replication_factor: 1
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "NOMAD_ALLOC_ID" }}
    instance_availability_zone: {{ env "node.unique.name" }}
    zone_awareness_enabled: true
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

query_scheduler:
  grpc_client_config:
    grpc_compression: "snappy"
    tls_enabled: true
    tls_ca_path: "/secrets/certs/CA.pem"
    tls_cert_path: "/secrets/certs/cert.pem"
    tls_key_path: "/secrets/certs/key.pem"

blocks_storage:
  backend: s3
  s3:
    endpoint: "s3.nahsi.dev"
    region: "syria"
    bucket_name: "mimir"
    access_key_id: "${S3_ACCESS_KEY_ID}"
    secret_access_key: "${S3_SECRET_ACCESS_KEY}"
  bucket_store:
    sync_dir: {{ env "NOMAD_ALLOC_DIR" }}/data/tsdb-sync
  tsdb:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/tsdb
    flush_blocks_on_shutdown: true

limits:
  compactor_blocks_retention_period: "25w"
  accept_ha_samples: true
