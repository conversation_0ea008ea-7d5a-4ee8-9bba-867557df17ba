
# mimir rwb read-write-backend configuration for jedd-lab (dg)

multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "warn"

  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

alertmanager:
  sharding_ring:
    kvstore:
      store: "consul"
      prefix: "alertmanagers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  data_dir: /mimir/alertmanager/

alertmanager_storage:
  backend: "filesystem"
  local:
    path: "/mimir/alertmanager/"

distributor:
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    replication_factor: 3 

    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

# frontend_worker:
#   scheduler_address: "mimir-rwb-backend.service.dc-cir-un-prod.collectors.obs.nsw.education:8096"
#   id: {{ env "node.unique.name" }}
#   grpc_client_config:
#     grpc_compression: "snappy"

frontend:
#  results_cache:
#    backend: redis
#    redis:
#      endpoint: redis.obs.int.jeddi.org:6379
#      db: 4
#  cache_results: true
  # scheduler_address: "mimir-rwb-backend.service.dc-cir-un-prod.collectors.obs.nsw.education:8096"
  scheduler_address: "mimir-rwb-backend.service.dg.obs.int.jeddi.org:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  port: {{ env "NOMAD_PORT_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"

    # jedd - disable tls at home
    tls_enabled: false


query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  ring:
    kvstore:
      store: "consul"
      prefix: "rulers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "http://mimir-rwb-read.obs.int.jeddi.org/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  alertmanager_url: "http://alertmanager.obs.int.jeddi.org"
  query_frontend:
    address: "http://mimir-rwb-read.obs.int.jeddi.org"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

querier:
  #query_ingesters_within: 13h0m0s
  #query_store_after: 8h0m0s
  #query_store_after: 6h # Setting to 6 hours to go to the store(compacted blocks) sooner
  #query_ingesters_within: 7h # 

compactor:
  data_dir: /mimir/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway:

  sharding_ring:
    replication_factor: 3 # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage:
  backend: s3
  bucket_store:
    #ignore_blocks_within: 10h  # Going to remove this advanced option
    sync_dir: /mimir/tsdb-sync/

    # index_cache defaults to inmemory
    #index_cache:
    #  backend: redis
    #  redis:
    #    endpoint: redis.obs.nsw.education:6379
    #    db: 0

    # chunks_cache can only be memcached or redis
    #chunks_cache:
    #  backend: redis
    #  redis:
    #    endpoint: redis.obs.nsw.education:6379
    #    db: 1

    # metadata_cache can only be memcached or redis
    #metadata_cache:
    #  backend: redis
    #  redis:
    #    endpoint: redis.obs.nsw.education:6379
    #    db: 2

  tsdb: #Required to be persistent between restarts
    dir: /mimir/tsdb/
    #retention_period: 13h0m0s Dropping this to help ingester start up faster
    retention_period: 8h0m0s

    # If flush_blocks_on_shutdown is false then incomplete blocks will be used on startup
    flush_blocks_on_shutdown: false 

    #(experimental) if memory_snapshot_on_shutdown is true, enable snapshotting of in-memory 
    # TSDB data on disk when shutting down.
    memory_snapshot_on_shutdown: false 

common:
  storage:
    backend: s3
    s3:
      # minio access key + secret for mimir-rwb
      access_key_id: 482QfgDnhb7egeygvrh0
      secret_access_key: gEVnFvRDRU9tXkeKU3tlhQs9kmQERtipSowA6lsA

      bucket_name: mimir-rwb
      # for work - endpoint: s3.ap-southeast-2.amazonaws.com
      # for loki we did s3: http://loki:<EMAIL>.:9888/loki-rw
      endpoint: dg-pan-01.int.jeddi.org:9888
      region: au-east

      # jedd - disable tls at home
      insecure: true

limits:
  accept_ha_samples: true
  max_global_series_per_user: 0
  max_label_names_per_series: 200
  out_of_order_time_window:  5m
  ingestion_rate: 200000
  ingestion_burst_size: 3000000
  compactor_blocks_retention_period: 1y
  compactor_block_upload_enabled: true
