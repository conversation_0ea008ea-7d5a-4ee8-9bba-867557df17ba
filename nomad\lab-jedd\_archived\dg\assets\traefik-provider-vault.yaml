http:
  routers:
    vault:
      entryPoints:
        - "http"
      rule: "Host(`vault.obs.int.jeddi.org`)"
      service: vault-service
  
  services:
    vault-service:
      loadBalancer:
        servers:
#          - url: "http://LOCALHOST:8000"
#          - url: "{{ env "NOMAD_IP_port_vault:NOMAD_PORT_port_vault" }}"
#          - url: "http://{{ env "NOMAD_IP_port_vault:NOMAD_PORT_port_vault" }}"
#          - url: http://dg-hac-01.int.jeddi.org:8000
          - url: "http://{{ env "NOMAD_IP_port_vault" }}:{{ env "NOMAD_PORT_port_vault" }}"
