[entryPoints]
  [entryPoints.http]
  address = ":80"

  [entryPoints.https]
  address = ":443"

  [entryPoints.traefik]
  address = ":8081"

[api]
  dashboard = true
  insecure  = true

# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
  prefix           = "traefik"
  exposedByDefault = true

  [providers.consulCatalog.endpoint]
    address = "**************:8500"
    scheme  = "http"

# Add file provider for static configurations
[providers.file]
  directory = "/etc/traefik/dynamic"
  watch = true

# Metrics configuration
[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]
