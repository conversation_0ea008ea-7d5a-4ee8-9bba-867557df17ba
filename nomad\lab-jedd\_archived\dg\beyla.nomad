
// grafana beyla - jedd-lab dg

job "beyla" {
  datacenters = ["DG"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "beyla" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[2]"
    }

    network {
      port "port_beyla"  {
      }
      port "port_beyla_prom"  {
      }

      port "port_goblog"  {
      }
      port "port_goblog_https"  {
      }
    }

    # TASK:  grafana beyla = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "beyla" {

      driver = "docker"

      config {
        image = "grafana/beyla:0.2.0"

        ports = ["port_beyla", "port_beyla_prom"]

        privileged = true

        logging  {
          type = "loki"
          config {
            loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [ 
          "local/beyla.yml:/etc/beyla.yml",
        ]

        command = "/beyla"

        args = [
          "-config", "/etc/beyla.yml"
        ]

      }

      env = {
#        "OPEN_PORT" = "${NOMAD_PORT_port_beyla}"
#        "PRINT_TRACES" = "true"

      }

      template {
        data = file("assets/beyla-config.yml")
        destination = "local/beyla.yml"
      }

      resources {
        cpu    = 512
        memory = 512
        memory_max = 1024
      }

      service {
        name = "beyla"
        port = "port_beyla"

#        check {
#          name     = "Beyla healthcheck"
#          port     = "port_beyla"
#          type     = "http"
#          path     = "/ready"
#          interval = "20s"
#          timeout  = "5s"
#          check_restart {
#            limit           = 3
#            grace           = "60s"
#            ignore_warnings = false
#          }
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.beyla.rule=Host(`beyla.int.jeddi.org`)",
          "traefik.http.routers.beyla.tls=false",
        ]
      }
    }


    # TASK:  grafana beyla = = = = = = = = = = = = = = = = = = = = = = = = = =
#    task "goblog" {
#
#      driver = "docker"
#
#      config {
#        image = "mariomac/goblog:dev"
#
#        ports = ["port_goblog", "port_goblog_https"]
#
#        logging  {
#          type = "loki"
#          config {
#            loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
#            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
#          }
#        }
#
#        args = [
#
#        ]
#
#      }
#
#      env = {
#        "GOBLOG_HTTP_PORT" = "${NOMAD_PORT_port_goblog}"
#        "GOBLOG_HTTPS_PORT" = "${NOMAD_PORT_port_goblog_https}"
#        "GOBLOG_DOMAIN" = "${NOMAD_IP_port_goblog}"
#      }
#
#      resources {
#        cpu    = 120
#        memory = 120
#        memory_max = 512
#      }
#
#      service {
#        name = "goblog"
#        port = "port_goblog"
#
##        check {
##          name     = "Goblog healthcheck"
##          port     = "port_goblog"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }
#
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.goblog.rule=Host(`goblog.int.jeddi.org`)",
#          "traefik.http.routers.goblog.tls=false",
#        ]
#      }
#    }



  }
}

