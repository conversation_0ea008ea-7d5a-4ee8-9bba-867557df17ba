
# ObservIQ BindPlane for <PERSON><PERSON>'s lab (DG) 
#
#
# @TODO persistent volume and/or postgresql task

# NOTE - run this command on dg-hac-03 to register agent:
#  sh -c "$(curl -fsSlL https://github.com/observiq/observiq-otel-collector/releases/download/v1.27.0/install_unix.sh)" install_unix.sh -e ws://bindplane.obs.int.jeddi.org:80/v1/opamp -s 38f6b093-ed43-457d-9564-1b55006f66b2 -v 1.27.0
#  agent install string provided in GUI gives wrong endpoint (probably a config param we can provide for server address)


job "bindplane" {

  datacenters = ["DG"]

  type = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
  }

  group "bindplane" {

    network {
      port "port_bindplane" {
        to = 3001
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

#    volume "vol_bindplane" {
#      type = "host"
#      source = "vol_bindplane"
#      read_only = false
#    }

    ephemeral_disk {
      size = 300
    }

    task "bindplane" {
      driver = "docker"

#      volume_mount {
#        volume      = "vol_bindplane"
#        destination = "/data"
#        read_only   = false
#      }

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
      }

      config {
        image = "observiq/bindplane"

        ports = ["port_bindplane"]

        volumes = [
          "local/config.yaml:/config.yaml"
        ]


        args = [
          # "--config", "/config.yaml",
          "--username", "jedd",
          "--password", "bigsecret",

          "--tracing-otlp-endpoint", "otlp.obs.int.jeddi.org",

        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      resources {
        cpu    = 500 # 500 MHz
        memory = 256 # 256MB
      }

      template {
        data = <<EOH
name: default
apiVersion: bindplane.observiq.com/v1
auth:
  username: jedd
  password: bigsecret

network:
  host: 0.0.0.0
  port: "3001"
  # remoteURL: http://**********:3001

tracing:
  otlp:
    endpoint:  otlp.obs.int.jeddi.org

#store:
#  type: postgres
#  postgres:
#    host: postgres-bindplane.obs.int.jeddi.org
#    port: "5432"
#    database: bindplane
#    sslmode: disable
#    username: postgres
#    password: password
#    maxConnections: 5

EOH
        destination = "local/config.yaml"
        perms = "644"
      }

      service {
        name = "bindplane"
        port = "port_bindplane"

        tags = [
           "traefik.enable=true",
           "traefik.http.routers.bindplane.rule=Host(`bindplane.obs.int.jeddi.org`)",
           "traefik.http.routers.bindplane.tls=false",
           "traefik.http.routers.bindplane.entrypoints=http",
        ]

#        check {
#          name     = "alive"
#          type     = "tcp"
#          # name     = "http"
#          # type     = "http"
#          # path     = "/admin"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
