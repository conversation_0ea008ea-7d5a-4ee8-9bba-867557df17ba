
// EDIT -- split into TWO jobs - may reconcile to one later one
// EDIT -- split into TWO jobs - may reconcile to one later one
// EDIT -- split into TWO jobs - may reconcile to one later one
// NOT - grafana ALLOY job - monitoring two resource types via two tasks: hashicorp & OTLP
// in JEDD LAB

// 2024-12-10 - notes from claude - mostly changing the assets / configuration file
//    (some of these were self-owns and then recovers, so it's mostly the 4th item)
//
//  Corrected the OTLP endpoint URL structure
//  Switched from basic auth to direct header-based authentication
//  Added proper HCL syntax with commas in the headers map
//  Included both the Authorization Bearer token and X-Scope-OrgID headers







// ACTUAL - just alloy-OLTP


// Local repo:
// skopeo copy --dest-tls-verify=false  docker://registry.hub.docker.com/grafana/alloy:v1.5.0 docker://dg-pan-01.int.jeddi.org:5000/alloy:v1.5.0


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_alloy = "dg-pan-01.int.jeddi.org:5000/alloy:v1.5.0"
}

# JOB  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-alloy-hashicorp" {

  datacenters = ["DG"]

  type        = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
  }


  # Group hashicorp  = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "alloy-group-hashicorp" {
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    count = 3

    ephemeral_disk {
      migrate = true
      sticky = true
      size = 120
    }
    
    network {
        port "http" {
        }



    }

    task "alloy-task-hashicorp" {
      driver = "docker"

      config {
        privileged = true

        image = "${var.image_alloy}"

        ports = ["http"]

        entrypoint = [
            "/bin/alloy",
            "run",
            "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
            "--server.http.enable-pprof=true",
            "--server.http.memory-addr=0.0.0.0:${NOMAD_PORT_http}",

            "--stability.level=experimental",

            "/local/config.alloy"
            ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro",
           "/var/run/docker.sock:/var/run/docker.sock"
         ]
      }

      env = {
        "GCLOUD_HOSTED_METRICS_URL" = "https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push",
        "GCLOUD_HOSTED_METRICS_ID" = "624363",
        "GCLOUD_SCRAPE_INTERVAL" = "60s",
        "GCLOUD_HOSTED_LOGS_URL" = "https://logs-prod-004.grafana.net/loki/api/v1/push",
        "GCLOUD_HOSTED_LOGS_ID" = "311152",
        "GCLOUD_RW_API_KEY" = "glc_yeJvIjoiOTc5OTEiLCJuIjoic3RhY2stNDYxNzk1LWFsbG95LWRnLWhhYyIsImsiOiJDSzllMDBpVDlNMFFJcTZ3bzhZSDNIMzYiLCJtIjp7InIiOiJhdSJ9fQ==",

        # Fleet management option
        "GCLOUD_FM_URL"="https://fleet-management-prod-004.grafana.net"
        "GCLOUD_FM_POLL_FREQUENCY"="60s"
        "GCLOUD_FM_HOSTED_ID"="461795" 

      }

      resources {
        cpu = 100
        memory = 500
      }

      service {
        name = "grafana-alloy-collector-hashicorp-metrics"
        port = "http"
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-alloy-hashicorp.rule=Host(`collector-alloy-hashicorp.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-alloy-hashicorp.tls=false",
          "traefik.http.routers.collector-alloy-hashicorp.entrypoints=https,http",
        ]        
      } 

      template {
        data        = file("assets/collector-alloy-hashicorp.alloy")
        destination = "local/config.alloy"
      }

    }   // end-task hashi
  }  // end-group hashi

} // end-job

