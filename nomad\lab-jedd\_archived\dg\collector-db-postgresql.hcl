
# postgresql opentelemetry collector - jedd lab (dg)

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "otel/opentelemetry-collector-contrib:latest"
}

job "collector-opentelemetry-db-postgresql" {

  datacenters = ["DG"]

  type = "service"

  group "otel-db-postgresql" {
    count = 1

    network {
      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "pprof" {
        to = 1777
      }
    }

    service {
      name     = "otel-db-postgresql-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-db-postgresql-metrics.entrypoints=http",
        "traefik.http.routers.otel-db-postgresql-metrics.rule=Host(`otel-db-postgresql.obs.int.jeddi.org`) && Path(`/metrics`)",
        "traefik.http.routers.otel-db-postgresql-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-db-postgresql-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-db-postgresql-healthcheck.entrypoints=http",
        "traefik.http.routers.otel-db-postgresql-healthcheck.rule=Host(`otel-db-postgresql.obs.int.jeddi.org`) && Path(`/health/status`)",
        "traefik.http.routers.otel-db-postgresql-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-db-postgresql-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-db-postgresql-pprof.entrypoints=http",
        "traefik.http.routers.otel-db-postgresql-pprof.rule=Host(`otel-db-postgresql.obs.int.jeddi.org`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-db-postgresql-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-db-postgresql-otlphttp"
      port     = "otlphttp"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-db-postgresql.entrypoints=http",
        "traefik.http.routers.otel-db-postgresql.rule=Host(`otel-db-postgresql.obs.int.jeddi.org`)",
        "traefik.http.routers.otel-db-postgresql.tls=false"
      ]
    }

    task "otel-db-postgresql" {
      driver = "docker"
      env = {
        POSTGRESQL_PASSWORD = "bigsecret"
      }

      config {
        image = var.otel_image

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
            "otlp",
            "otlphttp",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 200
        memory = 350
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      grpc:
      http:

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-db-postgresql'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-db-postgresql.obs.int.jeddi.org']

  postgresql:
    endpoint: tu0992tedbs001.hbm.det.nsw.edu.au:5432
    transport: tcp
    username: pgwatch2
    password: {{env "POSTGRESQL_PASSWORD"}}
    databases:
      - zabbixtstdb_s
    collection_interval: 60s

processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.test.nsw.education/otlp"
    headers:
      X-Scope-ORGID: test

# loki onprem
  loki/onpremloki:
    endpoint: "https://loki.obs.nsw.education/loki/api/v1/push"

# tempo on-prem for otlp traces
  otlphttp/onpremtempo:
    endpoint: https://traces.obs.test.nsw.education
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    metrics:
      receivers: [otlp,prometheus,postgresql]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud]

    logs:
      receivers: [otlp]
      processors: [attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki,otlphttp/grafanacloud]

  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-db-postgresql
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
