
# collector-dfs - jedd lab instance

# Combined [[ standalone prometheus + blackbox exporter + python for continuous csv processing ]]
# for Digital Field Services.
# refer:  https://jira.education.nsw.gov.au/browse/OBS-573


job "collector-dfs" {

  type = "service"

  datacenters = ["DG"]

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-dfs" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
      port "port_blackbox" { 
        # to = 9115
      }
  	}

    volume "obs-app-dfs"  {
      type = "host"
      source = "obs-app-dfs"
      read_only = false
    }

    
    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK python container for conversion script  = = = = = = = = = =

    task "task-dfs-csv-converter" {
      driver = "docker"

      volume_mount {
        volume = "obs-app-dfs"
        destination = "/obs-app-dfs"
        read_only = false
      }

      config {
        image = "python:3.9-bullseye"

        dns_servers = ["************23"]
        # dns_servers = [ "************" ]

        command = "/local/looper.sh"
      
#        volumes = [ 
#          "/opt/obs-app-dfs:/obs-app-dfs"
#        ]

        network_mode = "host"

        ports = [ ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          "--log.level",      "debug"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu    = 200
        memory = 256
      }

      service {
        name = "collector-dfs-csv-converter"
        meta {
          cir_app_id = "obs"
          env = "test"
        }
      }


      #  FILE:   looper.sh
      #  this is our entry point - shell control script that runs the csv -> yaml conversion, sleeps, git pulls, repeat
      template {
        data = <<EOH
#! /usr/bin/env bash

cd /obs-app-dfs

while [ 1 ]
do
   ./obs-app-dfs-csv-converter.py
   sleep 60m
done



EOH
        destination = "local/looper.sh"
        perms = "755"
      }


    }  // end-task python-csv-converter


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-prometheus" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=https://collector-dfs-prometheus.obs.int.jeddi.org",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Digital Field Services Collector",
        ]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        logging {
          type = "loki"
          config {
            loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/ultimo.yaml:/etc/prometheus/ultimo.yaml",
          # "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]
      }

      resources {
        cpu    = 256
        memory = 256
      }

      service {
        name = "collector-dfs-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-prometheus.rule=Host(`collector-dfs-prometheus.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-dfs-prometheus.tls=false",
          "traefik.http.routers.collector-dfs-prometheus.entrypoints=http",
        ]

#        check {
#          type = "http"
#          port = "port_prometheus"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "10s"
#        }
      }

#      artifact {
#        source = "git::ssh://<EMAIL>/prometheus-configuration"
#        destination = "local/prometheus-configuration"
#
#        options {
#          sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
#        }
#      }

      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-dfs"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-dfs'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:{{ env "NOMAD_PORT_port_prometheus" }}']

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-dfs'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:{{ env "NOMAD_PORT_port_blackbox" }}']

  - job_name: 'dfs-pings-sd'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 1m
      files: 
      - "/etc/prometheus/ultimo.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: dg-pan-01.int.jeddi.org:{{env "NOMAD_PORT_port_blackbox" }}

  - job_name: 'dfs-pings-static'
    metrics_path: /probe
    params:
      module: ["icmp"]
    static_configs:

      - targets: 
        - dg-esx-01.int.jeddi.org
        - dg-pan-01.int.jeddi.org
        - jarre.int.jeddi.org
        labels:
          site: servers

      - targets: 
        - tosca
        - ************
        - ************
        - ************
        labels:
          site: network and windows

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: dg-pan-01.int.jeddi.org:{{env "NOMAD_PORT_port_blackbox" }}

remote_write:
  - name: mimir
    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"
#    headers: 
#      X-Scope-OrgID: test
#    tls_config:
#      insecure_skip_verify: true


EOH

        destination = "local/prometheus.yaml"
      }


      template {
        data = file("assets/collector-dfs-file-sd-config.yaml")
        destination = "local/ultimo.yaml"
      }

    }


    # TASK blackbox for dfs   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          "--log.level",      "debug"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu    = 200
        memory = 256
      }


      service {
        name = "collector-dfs-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-blackbox.rule=Host(`collector-dfs-blackbox.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-dfs-blackbox.tls=false",
          "traefik.http.routers.collector-dfs-blackbox.entrypoints=http",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
        }

#        check {
#          type = "http"
#          port = "port_blackbox"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "10s"
#        }

      }


      template {
        data = <<EOH
modules:
  http_with_proxy:
    prober: http
    http:
      # proxy_url: "http://proxy.det.nsw.edu.au:80"
      fail_if_body_not_matches_regexp:
        - "1 results loaded."
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # = = = = = = = = = = = = = = = = = = = = = = = =
  # Web checks - bespoke, for Oliver School Library System (SLS) 
  # >>> requires a 200 and a string 'results found' on the target page.

  webcheck_generic:
    prober: http
    timeout: 20s
    http:
      # proxy_url: "http://proxy.det.nsw.edu.au:80"
      valid_status_codes: [200]
      # fail_if_not_ssl: true
      # fail_if_body_not_matches_regexp:
      #   - results found
      preferred_ip_protocol: "ip4" 

EOH
        destination = "local/config.yml"
      }


    }



  }  // end-group
} // end-job


