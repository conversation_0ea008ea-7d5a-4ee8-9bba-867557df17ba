# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "otel/opentelemetry-collector:0.53.0"
}

job "collector-opentelemetry" {

  datacenters = ["DG"]

  type        = "service"

  group "otel-collector" {
    count = 1

    network {
      port "metrics" {
        static = 8888
      }

      # Receivers
      port "grpc" {
        to = 4317
      }

      port "jaeger-grpc" {
        to = 14250
      }

      port "jaeger-thrift-http" {
        static = 14268
      }

      port "zipkin" {
        to = 9411
      }

      # Extensions
      port "zpages" {
        to = 55679
      }
    }

    service {
      name     = "otel-collector"
      port     = "grpc"
      tags     = ["grpc"]
      provider = "nomad"
    }

    task "otel-collector" {
      driver = "docker"

      config {
        image = var.otel_image

        entrypoint = [
          "/otelcol",
          "--config=local/config/otel-collector-config.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
          "metrics",
          "grpc",
          "jaeger-grpc",
          "jaeger-thrift-http",
          "zipkin",
          "zpages",
        ]
      }

      resources {
        cpu    = 256
        memory = 1024
      }

      template {
        data = <<EOF

receivers:
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 2m
        static_configs:
        - targets: ['**************:8888']
  otlp:
    protocols:
      grpc:
      http:


#  oracledb:
#    datasource:  "oracle://system:<EMAIL>:1521/XE"

processors:
  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
  zpages: {}
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 333

exporters:
  logging:
    loglevel: debug
  prometheusremotewrite:
    endpoint: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"
    tls:
      insecure: true
  jaeger:
    # endpoint: "dg-pan-01.int.jeddi.org:14268/api/v1/push"
    endpoint: "http://dg-pan-01.int.jeddi.org:14268/api/v1/push"
    tls:
      insecure: true

service:
  extensions: [zpages, memory_ballast]
  pipelines:
    traces/1:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [jaeger]
    metrics:
      receivers: [otlp, prometheus]
      # processors: [otlp, prometheus]
      # exporters: [otlp, prometheus]
      exporters: [prometheusremotewrite]

EOF

        destination = "local/config/otel-collector-config.yaml"
      }
    }
  }
}

