
// OpenTelemetry logging-only collector contrib - jedd-lab (DG)

// Refer:
//    https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  image = "otel/opentelemetry-collector-contrib:latest"
  cluster = "obs.int.jeddi.org"
}


job "collector-opentelemetry-logging" {
  datacenters = ["DG"]
  type        = "service"

  group "collector-opentelemetry-logging" {
    count = 1
  
#    update {
#      auto_revert = true        # if job does not go healthy nomad will revert to previous version
#      # health_check = "checks"   # specifies the allocation should be considered healthy
#      healthy_deadline = "1m"   # Time allowed for job to report as healthy
#      max_parallel = 2          # allows for there to be at least two instance running avoiding data dropouts
#      min_healthy_time = "5s"   # Time to wait before checking job health
#    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-hac-03"
    }

    network {

      port "port_health_check" {
        to = 13133
      }

      port "port_log_http" {
        to = 3500
        # static = 3500
      }
      port "port_log_grpc" {
        to = 3600
        # static = 3600
      }

      port "metrics" {
        to = 8888
      }

      port "otlp" {
        to = 4317
      }

      port "otlphttp" {
        to = 4318
      }

      port "port_zpages" {
        to = 55679
      }

      port "port_pprof" {
        to = 1777
      }

    }

    service {
      name = "logging"
      port = "port_log_http"

      tags = [
        "traefik.http.routers.log.entrypoints=http",
        # "traefik.http.routers.log.rule=Host(`log.${var.cluster}`)",
        "traefik.http.routers.log.rule=Host(`log.${var.cluster}`) && Path(`/loki/api/v1/push`)",
        "traefik.http.routers.log.tls=false",
        "traefik.enable=true",
      ]
    }

    service {
      port = "port_log_grpc"

      tags = [
        "traefik.http.routers.log-grpc.entrypoints=http",
        "traefik.http.routers.log-grpc.rule=Host(`log-grpc.${var.cluster}`)",
        "traefik.http.routers.log-grpc.tls=false",
        "traefik.enable=true",
      ]
    }

    service {
      name     = "healthcheck"
      port     = "port_health_check"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.health_check.entrypoints=http",
        "traefik.http.routers.health_check.rule=Host(`health_check.${var.cluster}`)",
        "traefik.http.routers.health_check.tls=false"
      ]
    }

    service {
      name     = "zpages"
      port     = "port_zpages"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.zpages.entrypoints=http",
        "traefik.http.routers.zpages.rule=Host(`zpages.${var.cluster}`)",
        "traefik.http.routers.zpages.tls=false"
      ]
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http",
        "traefik.http.routers.otel.rule=Host(`otel.${var.cluster}`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

      service {
        tags = [
          "traefik.http.routers.otel-collector-http.entrypoints=http",
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.${var.cluster}`)",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
        port = "otlphttp"
      }

    task "otel-collector" {
      driver = "docker"

      config {
        image = var.image

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]

        logging {
          type = "loki"
          config {
            # loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-url =   "http://log.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
        "otlphttp",
        "metrics",
        "otlp",
        "port_log_http",
        "port_log_grpc",
        "port_zpages",
        "port_health_check",
        "port_pprof"
        ]
      }

      resources {
        cpu    = 300
        memory = 512
      }

      template {
        data = <<EOF
receivers:

  loki:
    protocols:
      # defaults - http: 3500, grpc: 3600
      http:
        # endpoint: 0.0.0.0:3500
      # grpc:
        # endpoint: 0.0.0.0:3600
    use_incoming_timestamp: true

  otlp:
    protocols:
      grpc:
      http:


#    labels:
#      provenance: otel



processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test

  attributes/logs:
    actions:

      - action: insert
        key: log_file_name
        from_attribute: log.file.name

      - action: insert
        key: loki.attribute.labels
        value: log_file_name

      - action: insert
        key: container_name
        value: nomad.container.name

extensions:
  zpages:
    endpoint: 0.0.0.0:55679
  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777

exporters:
  logging:
    # loglevel: debug
    loglevel: info


  # loki onprem
  loki/onpremloki:
    endpoint: "http://loki.int.jeddi.org:3100/loki/api/v1/push"


service:
  # extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  extensions: [health_check,zpages,pprof]

  pipelines:

    logs:
      receivers: [otlp,loki]
      processors: [attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki]

  telemetry:
    logs:
      level: info
      initial_fields:
        service: obs-int-jeddi
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
