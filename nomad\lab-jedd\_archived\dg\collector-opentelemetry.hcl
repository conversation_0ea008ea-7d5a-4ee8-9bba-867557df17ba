# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0


#  2023-05-30T08:43:25.411Z	error	exporterhelper/queued_retry.go:391	Exporting failed. The error is not retryable. Dropping data.
#  {"service": "obscol-test",
#   "kind": "exporter",
#   "data_type": "metrics",
#   "name": "otlphttp/onpremmimir", 
#   "error": "Permanent error: error exporting items, request to http://dg-pan-01.int.jeddi.org:19009/api/v1/push/v1/metrics responded with HTTP Status Code 404",
#   "dropped_items": 5}


# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "otel/opentelemetry-collector-contrib:latest"
}

job "collector-opentelemetry" {

  datacenters = ["DG"]

  type        = "service"

  namespace = "collectors"

  group "otel-collector" {

    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "dg-hac-0[123]"
      value = "dg-hac-03"
    }
  
    update {
      max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
      health_check = "checks" #specifies the allocation should be considered healthy
      auto_revert = true #if job does not go healthy nomad will revert to previous version
      healthy_deadline = "5m" #Time allowed for job to report as healthy
      min_healthy_time = "5s" #Time to wait before checking job health
    }

    network {

      port "healthcheck" {
        to = 13133
      }
      port "jaeger-grpc" {
        to = 14250
      }
      port "jaeger-thrift-http" {
        to = 14268
      }
      port "metrics" {
        to = 8888
      }
      port "influxdb"{
        to = 8086
      }
      port "loki-receiver" {
        to = 3500
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "zipkin" {
        to = 9411
      }
      port "zpages" {
        to = 55679
      }
      port "pprof" {
        to = 1777
      }
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http,https",
        "traefik.http.routers.otel.rule=Host(`otel.obs.int.jeddi.org`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

    service {
      name     = "loki-receiver"
      port     = "loki-receiver"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-loki.entrypoints=http,https",
        "traefik.http.routers.otel-loki.rule=Host(`otel.obs.int.jeddi.org`) && Path(`/loki/api/v1/push`)",
        "traefik.http.routers.otel-loki.tls=false"
      ]
    }

    service {
      name     = "influxdb-receiver"
      port     = "influxdb"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-influxdb.entrypoints=http,https",
        "traefik.http.routers.otel-influxdb.rule=Host(`otel-influxdb.obs.int.jeddi.org`) && Path(`/write`)",
        "traefik.http.routers.otel-influxdb.tls=false"
      ]
    }

    service {
      name     = "healthcheck"
      port     = "healthcheck"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-healthcheck.rule=Host(`otel.obs.int.jeddi.org`) && Path(`/healthcheck`)",
        "traefik.http.routers.otel-healthcheck.tls=false"
      ]
    }

    service {
      name     = "jaeger-thrift"
      port     = "jaeger-thrift-http"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger-thrift.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger-thrift.rule=Host(`otel-jaeger-thrift.obs.int.jeddi.org`)",
        "traefik.http.routers.otel-jaeger-thrift.tls=false"
      ]
    }

    service {
       name = "otlphttp"
       port = "otlphttp"
       provider = "consul"

       tags = [
         "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.int.jeddi.org`)",
         "traefik.http.routers.otel-collector-http.entrypoints=http",
         "traefik.http.routers.otel-collector-http.tls=false",
         "traefik.enable=true",
       ]
    }

    service {
      name     = "pprof"
      port     = "pprof"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-pprof.entrypoints=http,https",
        "traefik.http.routers.otel-pprof.rule=Host(`otel.obs.int.jeddi.org`) && Path(`/net/http/pprof`)",
        "traefik.http.routers.otel-pprof.tls=false"
      ]
    }

    task "collector-opentelemetry-task" {
      driver = "docker"

      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
          type = "loki"
          config {
            # loki-url = "https://otel.obs.int.jeddi.org/loki/api/v1/push"
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
        ports = [
        "otlphttp",
        "zipkin",
        "zpages",
        "healthcheck",
        "jaeger-grpc",
        "jaeger-thrift-http",
        "metrics",
        "otlp",
        "influxdb",
        "loki-receiver",
        "pprof"
        ]
      }

      resources {
        cpu    = 500
        memory = 1800
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      http:
  jaeger:
    protocols:
      thrift_compact:
      thrift_binary:
      thrift_http:
  zipkin:
  opencensus:
  influxdb:
#    endpoint: 0.0.0.0:8086
  loki:
    protocols:
      http:
    use_incoming_timestamp: true

  hostmetrics:
    scrapers:
      cpu:
      disk:
      filesystem:
      load:
      memory:
      network:
      process:
      processes:
      paging:

#### Collect own metrics ####
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 60s
        static_configs:
        - targets: ['"0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"']
  filelog:
    include: [/var/log/*.log]

processors:
  resourcedetection/system:
    # Modify the list of detectors to match the cloud environment
    detectors: [env, system, gcp, ec2, azure]
    timeout: 2s
    override: false

  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test
      - key: service_instance_id
        action: update
        value: "{{env "NOMAD_TASK_NAME"}}"

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name
      - action: insert
        key: loki.format
        value: raw

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

  probabilistic_sampler:
    sampling_percentage: 1

extensions:
### basic auth to grafanacloud OTLP gateway ###
#  basicauth/otlp:
#    client_auth:
#      username: 533612
#      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 256

exporters:
  logging:
    loglevel: info

### NewRelic ###

### GrafanaCloud has a simple gateway ##
#  otlphttp/grafanacloud:
#    auth:
#      authenticator: basicauth/otlp
#    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

### mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers ###
  otlphttp/onpremmimir:
    # endpoint: "https://mimir-distributor.obs.int.jeddi.org/otlp"
    # endpoint: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"
    endpoint: "http://dg-pan-01.int.jeddi.org:19009"
    headers:
      X-Scope-ORGID: otlp

### loki onprem ###
  loki/onpremloki:
    endpoint: "https://loki.obs.int.jeddi.org/loki/api/v1/push"

### tempo on-prem for jaeger traces ###
  jaeger_thrift/onpremtempo:
    endpoint: http://tempo-jaeger.obs.int.jeddi.org/api/traces
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast]
  pipelines:
    traces:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [probabilistic_sampler,resourcedetection/system,attributes/env,memory_limiter,batch]
      exporters: [jaeger_thrift/onpremtempo]

    traces/local:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [resourcedetection/system,attributes/env,memory_limiter,batch]
      exporters: [jaeger_thrift/onpremtempo]

    metrics:
      receivers: [otlp,prometheus,influxdb]
      processors: [resourcedetection/system,attributes/env,batch]
      exporters: [otlphttp/onpremmimir]

    logs:
      receivers: [filelog,otlp,loki]
      processors: [resourcedetection/system,attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki]

  telemetry:
    logs:
      level: error
      initial_fields:
        service: obscol-test
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
