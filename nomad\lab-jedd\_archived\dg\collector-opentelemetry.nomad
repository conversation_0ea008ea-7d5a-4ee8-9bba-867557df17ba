
// OpenTelemetry collector contrib - jedd-lab (DG)

// Refer:
//    https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  image = "otel/opentelemetry-collector-contrib:latest"
  cluster = "obs.int.jeddi.org"
}


job "collector-opentelemetry" {
  datacenters = ["DG"]
  type        = "service"

  group "otel-collector" {
    count = 1
  
#    update {
#      auto_revert = true        # if job does not go healthy nomad will revert to previous version
#      # health_check = "checks"   # specifies the allocation should be considered healthy
#      healthy_deadline = "1m"   # Time allowed for job to report as healthy
#      max_parallel = 2          # allows for there to be at least two instance running avoiding data dropouts
#      min_healthy_time = "5s"   # Time to wait before checking job health
#    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-hac-03"
    }

    network {

      port "healthcheck" {
        to = 13133
      }
      port "jaeger-grpc" {
        to = 14250
      }
      port "jaeger-thrift-http" {
        to = 14268
      }
      port "promtail" {
        # to = 8006
        static = 8006
      }
      port "metrics" {
        to = 8888
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "zipkin" {
        to = 9411
      }
      port "zpages" {
        to = 55679
      }
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http",
        "traefik.http.routers.otel.rule=Host(`otel.${var.cluster}`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

    service {
      name     = "jaeger"
      port     = "jaeger-grpc"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger.rule=Host(`otel-jaeger.${var.cluster}`)",
        "traefik.http.routers.otel-jaeger.tls=false"
      ]
    }

    service {
      name     = "jaeger-thrift"
      port     = "jaeger-thrift-http"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger-thrift.entrypoints=http",
        "traefik.http.routers.otel-jaeger-thrift.rule=Host(`otel-jaeger-thrift.${var.cluster}`)",
        "traefik.http.routers.otel-jaeger-thrift.tls=false"
      ]
    }

      service {
        tags = [
          "traefik.http.routers.otel-collector-http.entrypoints=http",
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.${var.cluster}`)",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
        port = "otlphttp"
      }

    task "otel-collector" {
      driver = "docker"

      config {
        image = var.image

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
        "otlphttp",
        "zipkin",
        "zpages",
        "healthcheck",
        "jaeger-grpc",
        "jaeger-thrift-http",
        "metrics",
        "otlp"
        ]
      }

      resources {
        cpu    = 300
        memory = 512
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      grpc:
      http:
  jaeger:
    protocols:
      thrift_compact:
      thrift_binary:
      thrift_http:
  zipkin:
  opencensus:

  hostmetrics:
    scrapers:
      cpu:
      disk:
      filesystem:
      load:
      memory:
      network:
      process:
      processes:
      paging:

  loki:
    protocols:
      http:
        endpoint: 0.0.0.0:8006

#    labels:
#      provenance: otel


#### Collect own metrics ####
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 60s
        static_configs:
        - targets: ['0.0.0.0:8888']
  filelog:
    include: [/var/log/*.log]

processors:
  resourcedetection/system:
    # Modify the list of detectors to match the cloud environment
    detectors: [env, system, gcp, ec2, azure]
    timeout: 2s
    override: false

  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name


  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 500
    # 25% of limit up to 2G
    spike_limit_mib: 240
    check_interval: 5s

  probabilistic_sampler:
    sampling_percentage: 15

extensions:
### basic auth to grafanacloud OTLP gateway ###
#  basicauth/otlp:
#    client_auth:
#      username: 533612
#      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

#  health_check:
#  pprof:
#  memory_ballast:
#    # Memory Ballast size should be max 1/3 to 1/2 of memory.
#    size_mib: 683

exporters:
  logging:
    loglevel: error


### GrafanaCloud has a simple gateway ##
#  otlphttp/grafanacloud:
#    auth:
#      authenticator: basicauth/otlp
#    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

### mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers ###
  otlphttp/onpremmimir:
#    endpoint: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"
    endpoint: "http://dg-pan-01.int.jeddi.org:19009/otlp"
#     endpoint: "https://mimir-distributor.obs.test.nsw.education/otlp"
#    headers:
#      X-Scope-ORGID: test

### loki onprem ###
  loki/onpremloki:
    endpoint: "http://loki.int.jeddi.org:3100/loki/api/v1/push"

### tempo on-prem for jaeger traces ###
#  jaeger_thrift/onpremtempo:
#    endpoint: http://tempo-jaeger.obs.int.jeddi.org/api/traces
#    tls:
#      insecure: true

service:
  # extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
#    traces:
#      receivers: [otlp,jaeger,zipkin,opencensus]
#      processors: [probabilistic_sampler,resourcedetection/system,attributes/env,memory_limiter,batch]
#      exporters: [jaeger_thrift/onpremtempo,otlphttp/grafanacloud,otlp/newrelic]

#    traces/local:
#      receivers: [otlp,jaeger,zipkin,opencensus]
#      processors: [resourcedetection/system,attributes/env,memory_limiter,batch]
#      exporters: [jaeger_thrift/onpremtempo,otlphttp/grafanacloud,otlp/newrelic]

    metrics:
      receivers: [otlp,prometheus]
      processors: [resourcedetection/system,attributes/env,batch]
      # exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud,otlp/newrelic]
      exporters: [otlphttp/onpremmimir]

    logs:
      receivers: [filelog,otlp]
      processors: [resourcedetection/system,attributes/env,attributes/logs]
      # exporters: [logging,loki/onpremloki,otlphttp/grafanacloud,otlp/newrelic]
      exporters: [logging,loki/onpremloki]

  telemetry:
    logs:
      level: error
      initial_fields:
        service: obs-int-jeddi
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
