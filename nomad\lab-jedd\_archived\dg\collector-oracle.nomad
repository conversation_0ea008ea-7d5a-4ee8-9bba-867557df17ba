 
// Collector-Oracle - prometheus + exporter pair


job "collector-oracle" {
  type = "service"
  datacenters = ["DG"]

  group "collector-oracle" {
    
    # Shared persistent storage for all tasks in this collector
    #volume "vol_collector_oracle"  {
    #  type = "host"
    #  source = "vol_collector_oracle"
    #  read_only = false
    #  }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {

      port "port_prometheus" {
      }

      port "port_oracle_exporter" {
      }

  	}




    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      #volume_mount {
      #  volume = "vol_collector_oracle"
      #  destination = "/data"
      #  read_only = false
      #}

      # %%% - can probably ditch with this configuration (no alerts, no dynamic config)
      #volume_mount {
      #  volume = "promconfvol"
      #  destination = "/prometheus-configuration"
      #  read_only = false
      #  }

      config {
        image = "https://docker.io/prom/prometheus:v2.41.0"

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Starlink Collector",
          # "--enable-feature=agent",
          "--config.file=/etc/prometheus/prometheus.yml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          # "local/prometheus-configuration/dg-pan-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-oracle-prometheus"
        port = "port_prometheus"
        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}

  scrape_interval: 1m

  # This creates a log of *all* queries, and will show up in /metrics as 
  #     prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus_oracle'
    metrics_path: /metrics
    static_configs:
      - targets: [ "{{NOMAD_IP_port_prometheus}}:{{ env "NOMAD_PORT_port_prometheus" }}" ]

  - job_name: 'oracle_exporter'
    metrics_path: /metrics
    static_configs:
      - targets: [ "{{NOMAD_IP_port_oracle_exporter}}:{{ env "NOMAD_PORT_port_oracle_exporter" }}" ]

#remote_write:
#  - name: mimir
#    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }


    # TASK exporter oracle   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "exporter-oracle" {
      driver = "docker"

      env = {
        # "DATA_SOURCE_NAME" = "system/oracle@//**************:1521"
        "DATA_SOURCE_NAME" = "system/oracle@//**************:1521"
      }

      config {
        ports = [ "port_oracle_exporter" ]

        image = "iamseth/oracledb_exporter"

        args = [

        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        


      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-oracle-exporter"
        port = "port_oracle_exporter"

        check {
          type = "http"
          port = "port_oracle_exporter"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

    }

  }

}


