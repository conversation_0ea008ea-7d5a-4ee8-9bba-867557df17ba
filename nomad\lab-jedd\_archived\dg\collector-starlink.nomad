 
// Collector-starlink

// Combined standalone prometheus + exporter (starlink)

// SNAPSHOT 2024-11 - production release relocated to jedd's git repo


job "collector-starlink" {
  type = "service"
  datacenters = ["DG"]

  group "collector-starlink" {
    
    # Shared persistent storage for all tasks in this collector
    volume "vol_collector_starlink"  {
      type = "host"
      source = "vol_collector_starlink"
      read_only = false
      }

    # %%%
    # Prometheus and exporter configuration comes from this repo:
    #    obs/prometheus-configuation
    volume "promconfvol"  {
      type = "host"
      source = "promconfvol"
      read_only = false
      }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {

      port "port_prometheus" {
      }

      port "port_starlink" {
      }

  	}




    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      volume_mount {
        volume = "vol_collector_starlink"
        destination = "/data"
        read_only = false
      }

      # %%% - can probably ditch with this configuration (no alerts, no dynamic config)
      volume_mount {
        volume = "promconfvol"
        destination = "/prometheus-configuration"
        read_only = false
        }

      config {
        image = "https://docker.io/prom/prometheus:v2.41.0"

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Starlink Collector",
          "--enable-feature=agent",
          "--config.file=/etc/prometheus/prometheus.yml"
        ]

        dns_servers = ["**************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/dg-pan-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-starlink-prometheus"
        port = "port_prometheus"
        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      artifact {
      source = "git::ssh://<EMAIL>/prometheus-configuration"
      destination = "local/prometheus-configuration"
      options {
        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}

  scrape_interval: 1m

  # This creates a log of *all* queries, and will show up in /metrics as 
  #     prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus_starlink'
    metrics_path: /metrics
    static_configs:
      - targets: [ "dg-pan-01.int.jeddi.org:{{ env "NOMAD_PORT_port_prometheus" }}" ]

  - job_name: 'starlink_exporter'
    metrics_path: /metrics
    static_configs:
      - targets: [ "dg-pan-01.int.jeddi.org:{{ env "NOMAD_PORT_port_starlink" }}" ]

remote_write:
  - name: mimir
    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }


    # TASK exporter starlink   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "exporter-starlink" {
      driver = "docker"

      config {
        ports = [ "port_starlink" ]

        image = "danopstech/starlink_exporter"

        args = [
          "-address",   "*************:9200",
          "-port",      "${NOMAD_PORT_port_starlink}",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        


      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-starlink-exporter"
        port = "port_starlink"

        check {
          type = "http"
          port = "port_starlink"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

    }

  }

}


