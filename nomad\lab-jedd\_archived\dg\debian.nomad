
# generic debian container for <PERSON><PERSON>'s lab (DG) 

# Needs the command / args to sleep indefinitely otherwise it bombs out immediately.

# Stolen then heavily modified from https://github.com/leowmjw/nomad-box


job "debian" {

  datacenters = ["DG"]

  type = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
    # value = "dg-hac-0[2]"
  }

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "control" {

    network {
      port "port_debian" {
        static = 2222
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "debian" {
      driver = "docker"


      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
      }

      config {
        # image = "debian:stable"
        image = "someguy123/net-tools"
        ports = ["port_debian"]
        command = "/bin/sleep"
        args = [" infinity"]
        }

      resources {
        cpu    = 500   # 500 MHz
        memory = 2048  # 2048M
      }

#      artifact {
#        # Nomad uses go-getter library, and requires ssh: type, plus git:: prefix if not using a 'git@' username
#        source = "git::ssh://<EMAIL>/prometheus-configuration"
#        destination = "local/prometheus-configuration"
#
#        options {
##           sshkey = "{{ key "sshkey/gitolite-prometheus-configuration" }}"
#
#          #  sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
#
#
#        }
#
#      }

      service {
        name = "debian-app"
        port = "port_debian"

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}
