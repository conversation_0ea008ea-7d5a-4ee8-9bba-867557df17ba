
// jedd-lab - OTEL Astronomy Shop Demo
//
// WIP attempt to get the OTEL demo suite running in Nomad.
//
// <PERSON><PERSON> has two posts about this:
//    OpenTelemetry.io  2022-12 :  https://opentelemetry.io/blog/2022/otel-demo-app-nomad/
//    her Medium site   2021-12 :  https://storiesfromtheherd.com/just-in-time-nomad-running-the-opentelemetry-collector-on-hashicorp-nomad-with-hashiqube-4eaf009b8382
//
// The newer one starts with the suggestion it's her first attempt at setting this up, and both
// instances rely on bespoke traefik, grafana, jaeger, OTEL collector etc - plus they rely on
// HashiQube, which we don't want to touch.  She tends to run them all as separate nomad jobs,
// relying on traefik to get them talking to each other.  There's some divergence in the approaches,
// the older version uses honeycomb.  In any case, despite being the only attempt on the net I could
// find, I'm mostly going to be starting from scratch.  Insert generic grumble aboue people that don't
// put ANY FLIPPING COMMENTS in their code / jobs / etc.

// Consistency is the hobgoblin of small minds .. yada yada.  I like the consistency of using port name
// of 'containerport' everywhere (though I note <PERSON>a doesn't do that for the grafana task - there the
// port is just called 'http') but on the other hand I abhor the overloading of variables that are hard
// to track down later if you're not breathing Nomad HCL, and specifically a foreign recipe, daily.  And
// obviously with lots of tasks in one big group (or a handful of groups with many tasks in each, as we'll
// probably end up with here) this naming scheme obviously wouldn't work.
//
// At work we've adopted a prefix of port_ everywhere for port names - it makes NOMAD_PORT_port_... look
// a bit redundant, but it's *obvious* later on when you see it with or without context.
//
// Similarly our job, group, and task names need to make sense when reviewing Loki logs, which pick up
// from the Loki driver for docker.  I've added lots of loki-exporting stanzas here.


// Gotcha 1 - you will likely get some inotify failures causing docker to bomb out, this is related to
// the sysctl settable value for user.max_inotify_instances -- has a default of 128, which is way too
// small apparently.
// exact error:  Unhandled exception. System.IO.IOException: The configured user limit (128) on the number of inotify instances has been reached, or the per-process limit on the number of open file descriptors has been reached

// Gotcha 2 - Loki driver - read up: https://grafana.com/docs/loki/latest/clients/docker-driver/
// This means you need to run:
// docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
// alternatively - comment out all the logging stanzas below.

// Gotcha 3 - I run my own prometheus, grafana, and otel collector - so duplicating all that again
// in a phat job with ephemeral-by-default storage, etc, is not ideal.  Initial cut of this play
// will include everything from Adriana's jobs, taken in turn from the upstream helmchart, and then
// can be stripped back to use local resources.  I hope it's a safe assumption that if you're playing
// with this you already have grafana, at least, and probably a prometheus in play.

// Gotcha 4 - usual constraints - service names max 63 chars and can't contain underscores. (@TODO confirm 
// what can't contain hyphens but needs underscores instead? - why did we start using underscores in ports?)




variables  {
  # These make it easier to adjust this to local resource names.
  loki = {
    url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
  }
}


job "astronomy-shop" {
  datacenters = ["DG"]
  type = "service"
  group "astronomy-shop" {
    # We want this running on the traefik-friendly 3-node cluster only
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {

      port "port_adservice" { 
        to = 9555
      }

      port "port_cartservice" { 
        to = 7070
        static = 7070
      }

      port "port_checkoutservice" { 
        to = 9555
      }

      port "port_currencyservice" { 
        to = 7001
      }

      port "port_emailservice" { 
        to = 6060
      }

      # featureflagservice
      port "port_ffs_http" { 
        to = 8081
      }
      port "port_ffs_grpc" { 
        to = 50053
      }

      port "port_postgres" { 
        to = 5432
      }

      port "port_frontend" { 
        to = 8080
      }

      port "port_frontendproxy" { 
        to = 8080
      }

      port "port_grafana" { 
        to = 3000
      }

      port "port_jaeger_frontend" {
        to = 16686
      }
      port "port_jaeger_collector" {
        to = 4317
      }

      port "port_loadgenerator" {
        to = 8089
      }

      # otel collector has A LOT
      port "port_otel_collector_healthcheck" {
        to = 13133
      }
      port "port_otel_collector_jaeger_compact" {
        to = 6831
        // UDP???
      }
      port "port_otel_collector_jaeger_grpc" {
        to = 14250
      }
      port "port_otel_collector_jaeger_thrift" {
        to = 14268
      }
      port "port_otel_collector_metrics" {
        to = 8888
      }
      port "port_otel_collector_otlp" {
        to = 4317
      }
      port "port_otel_collector_otlp_http" {
        to = 4318
      }
      port "port_otel_collector_prometheus" {
        to = 9464
      }
      port "port_otel_collector_zipkin" {
        to = 9411
      }

      port "port_paymentservice" {
        to = 5051
      }

      port "port_productioncatalogservice" {
        to = 3550
      }

      port "port_otel_prometheus_ui" {
      }

      port "port_quoteservice" {
        to = 8090
      }

      port "port_recommendationservice" {
        to = 9001
        static = 9001
      }

      port "port_redis" {
        # @TODO fix this to redirect sanely
        # static = 6379
        to = 6379
      }

      port "port_shippingservice" {
        to = 50050
      }

    }


    # TASK -- adservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-adservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-adservice"
        ports = ["port_adservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        AD_SERVICE_PORT = "${NOMAD_PORT_port_adservice}"
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "adservice"
      }      

      service {
        name = "adservice"
        port = "port_adservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.adservice.rule=Host(`adservice.obs.int.jeddi.org`)",
          "traefik.http.routers.adservice.tls=false",
          "traefik.http.routers.adservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_adservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        memory = 100
        # memory = 250
        memory_max = 800
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-adservice"


    # TASK -- cartservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-cartservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-cartservice"
        ports = ["port_cartservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        ASPNETCORE_URLS = "http://*:${NOMAD_PORT_port_cartservice}"
        CART_SERVICE_PORT = "${NOMAD_PORT_port_cartservice}"
        OTEL_SERVICE_NAME = "cartservice"
      }      

      service {
        name = "cartservice"
        port = "port_cartservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.cartservice.rule=Host(`cartservice.obs.int.jeddi.org`)",
          "traefik.http.routers.cartservice.tls=false",
          "traefik.http.routers.cartservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_cartservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        # memory = 250
        memory = 150
        memory_max = 500
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "redis-service" }}
REDIS_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-cartservice"




    # TASK -- checkoutservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-checkoutservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-checkoutservice"
        ports = ["port_checkoutservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        CHECKOUT_SERVICE_PORT = "${NOMAD_PORT_port_checkoutservice}"
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "checkoutservice"
      }      

      service {
        name = "checkoutservice"
        port = "port_checkoutservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.checkoutservice.rule=Host(`checkoutservice.obs.int.jeddi.org`)",
          "traefik.http.routers.checkoutservice.tls=false",
          "traefik.http.routers.checkoutservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_checkoutservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 30
        # memory = 400
        memory = 100
        memory_max = 800
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "cartservice" }}
CART_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "currencyservice" }}
CURRENCY_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "emailservice" }}
EMAIL_SERVICE_ADDR = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "paymentservice" }}
PAYMENT_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "productcatalogservice" }}
PRODUCT_CATALOG_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "shippingservice" }}
SHIPPING_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_METRICS_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-checkoutservice"




    # TASK -- currencyservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-currencyservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-currencyservice"
        ports = ["port_currencyservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        CURRENCY_SERVICE_PORT = "${NOMAD_PORT_port_currencyservice}"
        OTEL_RESOURCE_ATTRIBUTES = "service.name=currencyservice"
      }      

      service {
        name = "currencyservice"
        port = "port_currencyservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.currencyservice.rule=Host(`currencyservice.obs.int.jeddi.org`)",
          "traefik.http.routers.currencyservice.tls=false",
          "traefik.http.routers.currencyservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_currencyservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
        memory_max = 250
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-currencyservice"





    # TASK -- emailservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-emailservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-emailservice"
        ports = ["port_emailservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        APP_ENV = "production"
        EMAIL_SERVICE_PORT = "${NOMAD_PORT_port_emailservice}"
        OTEL_SERVICE_NAME = "emailservice"
      }      

      service {
        name = "emailservice"
        port = "port_emailservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.emailservice.rule=Host(`emailservice.obs.int.jeddi.org`)",
          "traefik.http.routers.emailservice.tls=false",
          "traefik.http.routers.emailservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_emailservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        memory_max = 250
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "otelcol-http" }}
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}/v1/traces"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-emailservice"




    # TASK -- featureflagservice  = = = = = = = = = = = = = = = = = = = = 
    task "demo-featureflagservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-featureflagservice"
        ports = ["port_ffs_http", "port_ffs_grpc"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        FEATURE_FLAG_GRPC_SERVICE_PORT = "${NOMAD_PORT_port_ffs_grpc}"
        FEATURE_FLAG_SERVICE_PATH_ROOT = "\"/feature\""
        FEATURE_FLAG_SERVICE_PORT = "${NOMAD_PORT_port_ffs_http}"
        OTEL_EXPORTER_OTLP_TRACES_PROTOCOL = "grpc"
        OTEL_SERVICE_NAME = "featureflagservice"
      }      

      service {
        name = "featureflagservice-http"
        port = "port_ffs_http"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.featureflagservice.rule=Host(`featureflagservice.obs.int.jeddi.org`)",
          "traefik.http.routers.featureflagservice.tls=false",
          "traefik.http.routers.featureflagservice.entrypoints=http",
        ]

        check {
          type = "tcp"
          port = "port_ffs_http"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "featureflagservice-grpc"
        port = "port_ffs_grpc"

        check {
          type = "tcp"
          port = "port_ffs_grpc"
          interval = "20s"
          timeout = "10s"
        }
      }

      resources {
        cpu = 60
        # memory = 250
        memory = 150
        memory_max = 400
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "ffspostgres-service" }}
DATABASE_URL = "ecto://ffs:ffs@{{ .Address }}:{{ .Port }}/ffs"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-featureflagservice"




    # TASK -- ffspostgres  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-ffspostgres" {
      driver = "docker"

      config {
        image = "postgres:14"
        ports = ["port_postgres"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        POSTGRES_DB = "ffs"
        POSTGRES_PASSWORD = "ffs"
        POSTGRES_USER = "ffs"
        OTEL_SERVICE_NAME = "ffspostgres"
      }      

      service {
        name = "ffspostgres-service"
        port = "port_postgres"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.ffspostgres.rule=Host(`ffspostgres.obs.int.jeddi.org`)",
          "traefik.http.routers.ffspostgres.tls=false",
          "traefik.http.routers.ffspostgres.entrypoints=http",
        ]

        check {
          interval = "10s"
          timeout  = "5s"
          type     = "script"
          command  = "pg_isready"
          args     = [
            "-d", "ffs",
            "-h", "${NOMAD_IP_port_postgres}",
            "-p", "${NOMAD_PORT_port_postgres}",
            "-U", "ffs"
          ]
        }

      }

      resources {
        cpu = 55
        # memory = 300
        memory = 150
        memory_max = 500
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-ffspostgres"




    # TASK -- frontend  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-frontend" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-frontend"
        ports = ["port_frontend"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        ENV_PLATFORM = "local"
        # FRONTEND_ADDR = "frontend.localhost"
        FRONTEND_ADDR = "frontend.obs.int.jeddi.org"
        OTEL_RESOURCE_ATTRIBUTES = "service.name=frontend"
        OTEL_SERVICE_NAME = "frontend"
        PORT = "${NOMAD_PORT_port_frontend}"
      }      

      service {
        name = "frontend"
        port = "port_frontend"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.frontend.rule=Host(`frontend.obs.int.jeddi.org`)",
          "traefik.http.routers.frontend.tls=false",
          "traefik.http.routers.frontend.entrypoints=http",
        ]

        check {
          type = "tcp"
          port = "port_frontend"
          interval = "20s"
          timeout = "10s"
        }
      }

      resources {
        cpu = 55
        memory = 128
        # memory = 512
        memory_max = 512
        # memory_max = 1024
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF

{{ range service "adservice" }}
AD_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "cartservice" }}
CART_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "checkoutservice" }}
CHECKOUT_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "currencyservice" }}
CURRENCY_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "productcatalogservice" }}
PRODUCT_CATALOG_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "recommendationservice" }}
RECOMMENDATION_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "shippingservice" }}
SHIPPING_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-http" }}
PUBLIC_OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}/v1/traces"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-frontend"



    # TASK -- frontendproxy  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-frontendproxy" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-frontendproxy"
        ports = ["port_frontendproxy"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        ENVOY_PORT = "${NOMAD_PORT_port_frontendproxy}"
        ENVOY_UID = "0"
        // GRAFANA_SERVICE_HOST = "grafana.localhost"
        // GRAFANA_SERVICE_PORT = "80"
        // JAEGER_SERVICE_HOST = "jaeger.localhost"
        // JAEGER_SERVICE_PORT = "80"
      }      

      service {
        name = "frontendproxy"
        port = "port_frontendproxy"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.frontendproxy.rule=Host(`frontendproxy.obs.int.jeddi.org`)",
          "traefik.http.routers.frontendproxy.tls=false",
          "traefik.http.routers.frontendproxy.entrypoints=http",
        ]

        check {
          type = "tcp"
          port = "port_frontend"
          interval = "20s"
          timeout = "10s"
        }
      }

      resources {
        cpu = 55
        memory = 128
        # memory = 512
        memory_max = 512
        # memory_max = 1024
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF

{{ range service "grafana" }}
GRAFANA_SERVICE_HOST = "{{ .Address }}"
GRAFANA_SERVICE_PORT = "{{ .Port }}"
{{ end }}

{{ range service "jaeger-frontend" }}
JAEGER_SERVICE_HOST = "{{ .Address }}"
JAEGER_SERVICE_PORT = "{{ .Port }}"
{{ end }}

{{ range service "featureflagservice-http" }}
FEATURE_FLAG_SERVICE_HOST = "{{ .Address }}"
FEATURE_FLAG_SERVICE_PORT = "{{ .Port }}"
{{ end }}

{{ range service "frontend" }}
FRONTEND_HOST = "{{ .Address }}"
FRONTEND_PORT = "{{ .Port }}"
{{ end }}

{{ range service "loadgenerator" }}
LOCUST_WEB_HOST = "{{ .Address }}"
LOCUST_WEB_PORT = "{{ .Port }}"
{{ end }}

{{ range service "otelcol-http" }}
PUBLIC_OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}/v1/traces"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-frontendproxy"



    # TASK -- grafana  = = = = = = = = = = = = = = = = = = = = = = = =

    # @TODO adjust the whole play to talk to local extant grafana instance, but there's some
    # ease of use benefits to pulling in an ephemeral grafana and populating it with datasource
    # and dashboard (git)

    task "demo-grafana" {
      driver = "docker"

      config {
        image = "grafana/grafana:9.1.0"
        ports = ["port_grafana"]
        volumes = [ 
          "local/config:/etc/grafana",
          "local/provisioning:/etc/grafana/provisioning",
        ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        GF_AUTH_ANONYMOUS_ENABLED  = "true"
        GF_AUTH_ANONYMOUS_ORG_ROLE = "Editor"
        GF_SERVER_HTTP_PORT        = "${NOMAD_PORT_port_grafana}"

        GF_PATHS_DATA = "/var/lib/grafana/"
        GF_PATHS_LOGS = "/var/log/grafana"
        GF_PATHS_PLUGINS = "/var/lib/grafana/plugins"
        GF_LOG_LEVEL = "DEBUG"
        GF_LOG_MODE = "console"
        GF_PATHS_PROVISIONING = "/etc/grafana/provisioning"
      }      

      service {
        name = "grafana"
        port = "port_grafana"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.int.jeddi.org`)",
          "traefik.http.routers.grafana.tls=false",
          "traefik.http.routers.grafana.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_grafana"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        memory = 100
        memory_max = 150
      }

      artifact {
        source      = "github.com/open-telemetry/opentelemetry-demo/src/grafana/provisioning/dashboards"
        destination = "local/provisioning/dashboards"
      }

      template {
        data = <<EOH
[analytics]
check_for_updates = true
[auth]
disable_login_form = true
[auth.anonymous]
enabled = true
org_name = Main Org.
org_role = Admin
[grafana_net]
url = https://grafana.net
[log]
mode = console
[paths]
data = /var/lib/grafana/
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
;provisioning = /etc/grafana/provisioning
[server]
protocol = http
domain = otel-demo.localhost
http_port = 80
root_url = %(protocol)s://%(domain)s:%(http_port)s/grafana
serve_from_sub_path = true
EOH
        destination = "local/config/grafana.ini"
      }

      template {
        data = <<EOH
apiVersion: 1
datasources:
- editable: true
  isDefault: true
  name: Prometheus
  type: prometheus
  uid: webstore-metrics
  url: http://{{ range service "prometheus" }}{{ .Address }}:{{ .Port }}{{ end }}
- editable: true
  isDefault: false
  name: Jaeger
  type: jaeger
  uid: webstore-traces
  url: http://{{ range service "jaeger-collector" }}{{ .Address }}:{{ .Port }}{{ end }}
EOH

        destination = "local/provisioning/datasources/datasources.yaml"
      }

    } // end-task "demo-grafana"



    # TASK -- jaeger  = = = = = = = = = = = = = = = = = = = = = = = =

    # @TODO rip this out - it's been mostly deprecated upstream, and we can probably live without it.

    task "demo-jaeger" {

      driver = "docker"

      config {
        image = "jaegertracing/all-in-one:latest"
        ports = ["port_jaeger_frontend", "port_jaeger_collector"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        COLLECTOR_OTLP_ENABLED = "true"
      }      

      service {
        name = "jaeger-collector"
        port = "port_jaeger_collector"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger.rule=Host(`jaeger.obs.int.jeddi.org`)",
          "traefik.http.routers.jaeger.tls=false",
          "traefik.http.routers.jaeger.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_jaeger_collector"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      service {
        name = "jaeger-frontend"
        port = "port_jaeger_frontend"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger-ui.rule=Host(`jaeger-ui.obs.int.jeddi.org`)",
          "traefik.http.routers.jaeger-ui.tls=false",
          "traefik.http.routers.jaeger-ui.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_jaeger_frontend"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        # memory_max = 250
      }

    } // end-task "demo-jaeger"




    # TASK -- loadgenerator  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-loadgenerator" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-loadgenerator"
        ports = ["port_loadgenerator"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        LOCUST_AUTOSTART = "true"
        LOCUST_HEADLESS = "false"
        LOCUST_USERS = "10"
        LOCUST_WEB_PORT = "${NOMAD_PORT_port_loadgenerator}"
        LOADGENERATOR_PORT = "${NOMAD_PORT_port_loadgenerator}"
        OTEL_SERVICE_NAME = "loadgenerator"
        PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION = "python"
      }

      service {
        name = "loadgenerator"
        port = "port_loadgenerator"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loadgenerator.rule=Host(`loadgenerator.obs.int.jeddi.org`)",
          "traefik.http.routers.loadgenerator.tls=false",
          "traefik.http.routers.loadgenerator.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_loadgenerator"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 48
        memory = 256
        # memory_max = 1024
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "frontend" }}
FRONTEND_ADDR = "{{ .Address }}:{{ .Port }}"
LOCUST_HOST = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-loadgenerator"




    # TASK -- otel-collector  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-otel-collector" {
      driver = "docker"

      config {
        image = "otel/opentelemetry-collector-contrib:0.64.1"

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/config/otel-collector-config.yaml",
        ]

        ports = [
          "port_otel_collector_healthcheck" ,
          "port_otel_collector_jaeger_compact" ,
          "port_otel_collector_jaeger_grpc" ,
          "port_otel_collector_jaeger_thrift" ,
          "port_otel_collector_metrics" ,
          "port_otel_collector_otlp" ,
          "port_otel_collector_otlp_http" ,
          "port_otel_collector_prometheus",
          "port_otel_collector_zipkin" 
        ]

        volumes = [ ]

        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        HOST_DEV = "/hostfs/dev"
        HOST_ETC = "/hostfs/etc"
        HOST_PROC = "/hostfs/proc"
        HOST_RUN = "/hostfs/run"
        HOST_SYS = "/hostfs/sys"
        HOST_VAR = "/hostfs/var"
      }      


      service {
        name = "otel-collector-healthcheck" 
        port = "port_otel_collector_healthcheck" 
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "5s"
        }
      }
      service {
        name = "otel-collector-jaeger-compact"
        port = "port_otel_collector_jaeger_compact" 
        tags = ["jaeger-compact"]
      }
      service {
        name = "otel-collector-jaeger-grpc" 
        port = "port_otel_collector_jaeger_grpc" 
        tags = ["jaeger-grpc"]
      }
      service {
        name = "otel-collector-jaeger-thrift" 
        port = "port_otel_collector_jaeger_thrift" 
        tags = ["jaeger-thrift"]
      }
      service {
        name = "otel-collector-metrics" 
        port = "port_otel_collector_metrics" 
        tags = ["metrics"]
      }
      service {
        name = "otel-collector-otlp" 
        port = "port_otel_collector_otlp" 
        tags = [
          "traefik.tcp.routers.otel-collector-grpc.rule=HostSNI(`*`)",
          "traefik.tcp.routers.otel-collector-grpc.entrypoints=grpc",
          "traefik.enable=true",
        ]        
      }
      service {
        name = "otel-collector-otlp-http" 
        port = "port_otel_collector_otlp_http" 
        tags = [
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.int.jeddi.org`)",
          "traefik.http.routers.otel-collector-http.entrypoints=web",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
      }
      service {
        name = "otel-collector-prometheus"
        port = "port_otel_collector_prometheus"
        tags = ["prometheus"]
      }
      service {
        name = "otel-collector-zipkin" 
        port = "port_otel_collector_zipkin" 
        tags = ["zipkin"]
      }


      resources {
        cpu = 60
        memory = 150
        memory_max = 300
      }


      template {
        data = <<EOH
receivers:
  otlp:
    protocols:
      grpc:
      http:
        endpoint: "0.0.0.0:4318"

processors:
  batch:
    timeout: 10s
  spanmetrics:
    metrics_exporter: prometheus

  memory_limiter:
    # 75% of maximum memory up to 4G
    limit_mib: 1536
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

exporters:
  logging:
    verbosity: detailed

  prometheus:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_otel_collector_prometheus" }}"

  otlp:
    endpoint: '{{ range service "jaeger-collector" }}{{ .Address }}:{{ .Port }}{{ end }}'
    tls:
      insecure: true

extensions:
  health_check:
    endpoint: 0.0.0.0:{{ env "NOMAD_PORT_port_otel_collector_healthcheck" }}

service:
  extensions: [health_check]
  pipelines:
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [prometheus, logging]
    traces:
      receivers: [otlp]
      processors: [spanmetrics, batch]
      exporters: [logging, otlp]

EOH

        change_mode   = "restart"
        destination = "local/config/otel-collector-config.yaml"
      }


    } // end-task "demo-otel-collector" 




    # TASK -- paymentservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-paymentservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-paymentservice"
        ports = ["port_paymentservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "paymentservice"
        PAYMENT_SERVICE_PORT = "${NOMAD_PORT_port_paymentservice}"
      }      

      service {
        name = "paymentservice"
        port = "port_paymentservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.paymentservice.rule=Host(`paymentservice.obs.int.jeddi.org`)",
          "traefik.http.routers.paymentservice.tls=false",
          "traefik.http.routers.paymentservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_paymentservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 250
        # memory_max = 400
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_METRICS_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-paymentservice"




    # TASK -- productioncatalogservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-soncatalogice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-productcatalogservice"
        ports = ["port_productioncatalogservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_SERVICE_NAME = "productcatalogservice"
        PRODUCT_CATALOG_SERVICE_PORT = "${NOMAD_PORT_port_productioncatalogservice}"
      }      

      service {
        name = "productioncatalogservice"
        port = "port_productioncatalogservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.productioncatalogservice.rule=Host(`productioncatalogservice.obs.int.jeddi.org`)",
          "traefik.http.routers.productioncatalogservice.tls=false",
          "traefik.http.routers.productioncatalogservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_productioncatalogservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        # memory_max = 250
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "featureflagservice-grpc" }}
FEATURE_FLAG_GRPC_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-productioncatalogservice"




    # TASK -- prometheus  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-prometheus" {
      driver = "docker"

      config {
        image = "prom/prometheus:v2.38.0"
        ports = ["port_otel_prometheus_ui"]

        volumes = [
          "local/config:/etc/config",
        ]

        args = [ 
          "--config.file=/etc/config/prometheus.yml",
          "--storage.tsdb.path=/prometheus",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_otel_prometheus_ui}",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          "--web.enable-lifecycle"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
      }      

      service {
        name = "otelprometheus"
        port = "port_otel_prometheus_ui"
      
        # @NOTE we need to watch for contention for 'prometheus' in consul or DNS
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.otel_prometheus.rule=Host(`otel_prometheus.obs.int.jeddi.org`)",
          "traefik.http.routers.otel_prometheus.tls=false",
          "traefik.http.routers.otel_prometheus.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_otel_prometheus_ui"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 80
        memory = 250
        # memory_max = 800
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
# ---
global:
  evaluation_interval: 30s
  scrape_interval: 5s
scrape_configs:
- job_name: otel
  static_configs:
  - targets:
    - '{{ range service "otelcol-prometheus" }}{{ .Address }}:{{ .Port }}{{ end }}'
- job_name: otel-collector
  static_configs:
  - targets:
    - '{{ range service "otelcol-metrics" }}{{ .Address }}:{{ .Port }}{{ end }}'

EOF
        destination = "local/config/prometheus.yml"
        env         = true
      }

      # @TODO jedd - perhaps rip these out - they don't appear useful, and shouldn't be required to exist.
      template {
        data = <<EOH
{}
EOH
        destination = "local/config/recording_rules.yml"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/rules"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/alerting_rules.yml"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/alerts"
      }


    } // end-task "demo-prometheus"





    # TASK -- quoteservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-quoteservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-quoteservice"
        ports = ["port_quoteservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_TRACES_PROTOCOL = "http/protobuf"
        OTEL_PHP_TRACES_PROCESSOR = "simple"
        OTEL_SERVICE_NAME = "quoteservice"
        OTEL_TRACES_EXPORTER = "otlp"
        OTEL_TRACES_SAMPLER = "parentbased_always_on"
        QUOTE_SERVICE_PORT = "${NOMAD_PORT_port_quoteservice}"
      }


      service {
        name = "quoteservice"
        port = "port_quoteservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.quoteservice.rule=Host(`quoteservice.obs.int.jeddi.org`)",
          "traefik.http.routers.quoteservice.tls=false",
          "traefik.http.routers.quoteservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_quoteservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "otelcol-http" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-quoteservice"






    # TASK -- recommendationservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-recommendationservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-recommendationservice"
        ports = ["port_recommendationservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_METRICS_EXPORTER = "otlp"
        OTEL_PYTHON_LOG_CORRELATION = "true"
        OTEL_SERVICE_NAME = "recommendationservice"
        OTEL_TRACES_EXPORTER = "otlp"
        PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION = "python"
        RECOMMENDATION_SERVICE_PORT = "${NOMAD_PORT_port_recommendationservice}"
      }


      service {
        name = "recommendationservice"
        port = "port_recommendationservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.recommendationservice.rule=Host(`recommendationservice.obs.int.jeddi.org`)",
          "traefik.http.routers.recommendationservice.tls=false",
          "traefik.http.routers.recommendationservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_recommendationservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "featureflagservice-grpc" }}
FEATURE_FLAG_GRPC_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "productcatalogservice" }}
PRODUCT_CATALOG_SERVICE_ADDR = "{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-recommendationservice"




    # TASK -- redis  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-redis" {
      driver = "docker"

      config {
        image = "redis:alpine"
        ports = ["port_redis"]
        volumes = [ ]

        args = [
          "-h", "${NOMAD_IP_port_redis}",
          "-p", "${NOMAD_PORT_port_redis}",
          "PING"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
      }

      service {
        name = "redis"
        port = "port_redis"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.redis.rule=Host(`redis.obs.int.jeddi.org`)",
          "traefik.http.routers.redis.tls=false",
          "traefik.http.routers.redis.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_redis"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
      }

    } // end-task "demo-redis"





    # TASK -- shippingservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-shippingservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-shippingservice"
        ports = ["port_shippingservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_SERVICE_NAME = "shippingservice"
        SHIPPING_SERVICE_PORT = "${NOMAD_PORT_port_shippingservice}"
      }


      service {
        name = "shippingservice"
        port = "port_shippingservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.shippingservice.rule=Host(`shippingservice.obs.int.jeddi.org`)",
          "traefik.http.routers.shippingservice.tls=false",
          "traefik.http.routers.shippingservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_shippingservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 75
      }

      template {
        # https://developer.hashicorp.com/nomad/docs/job-specification/template
        # range service assumes 'otelcol-grpc' is in Consul, I think
        # the job 'otel-collector' (not the lightstep variant) defines these services.
        data = <<EOF
{{ range service "quoteservice" }}
QUOTE_SERVICE_ADDR = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

{{ range service "otelcol-grpc" }}
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT = "http://{{ .Address }}:{{ .Port }}"
{{ end }}

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-quoteservice"








  }
}

