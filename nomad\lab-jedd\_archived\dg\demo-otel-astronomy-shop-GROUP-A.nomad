
// jedd-lab - OTEL Astronomy Shop Demo - GROUP A
//
// WIP attempt to get the OTEL demo suite running in Nomad.
//
// <PERSON><PERSON> has two posts about this:
//    OpenTelemetry.io  2022-12 :  https://opentelemetry.io/blog/2022/otel-demo-app-nomad/
//    her Medium site   2021-12 :  https://storiesfromtheherd.com/just-in-time-nomad-running-the-opentelemetry-collector-on-hashicorp-nomad-with-hashiqube-4eaf009b8382
//
// The newer one starts with the suggestion it's her first attempt at setting this up, and both
// instances rely on bespoke traefik, grafana, jaeger, OTEL collector etc - plus they rely on
// HashiQube, which we don't want to touch.  She tends to run them all as separate nomad jobs,
// relying on traefik to get them talking to each other.  There's some divergence in the approaches,
// the older version uses honeycomb.  In any case, despite being the only attempt on the net I could
// find, I'm mostly going to be starting from scratch.  Insert generic grumble aboue people that don't
// put ANY FLIPPING COMMENTS in their code / jobs / etc.

// Consistency is the hobgoblin of small minds .. yada yada.  I like the consistency of using port name
// of 'containerport' everywhere (though I note <PERSON>a doesn't do that for the grafana task - there the
// port is just called 'http') but on the other hand I abhor the overloading of variables that are hard
// to track down later if you're not breathing Nomad HCL, and specifically a foreign recipe, daily.  And
// obviously with lots of tasks in one big group (or a handful of groups with many tasks in each, as we'll
// probably end up with here) this naming scheme obviously wouldn't work.
//
// At work we've adopted a prefix of port_ everywhere for port names - it makes NOMAD_PORT_port_... look
// a bit redundant, but it's *obvious* later on when you see it with or without context.
//
// Similarly our job, group, and task names need to make sense when reviewing Loki logs, which pick up
// from the Loki driver for docker.  I've added lots of loki-exporting stanzas here.


// Gotcha 1 - you will likely get some inotify failures causing docker to bomb out, this is related to
// the sysctl settable value for user.max_inotify_instances -- has a default of 128, which is way too
// small apparently.
// exact error:  Unhandled exception. System.IO.IOException: The configured user limit (128) on the number of inotify instances has been reached, or the per-process limit on the number of open file descriptors has been reached

// Gotcha 2 - Loki driver - read up: https://grafana.com/docs/loki/latest/clients/docker-driver/
// This means you need to run:
// docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
// alternatively - comment out all the logging stanzas below.

// Gotcha 3 - I run my own prometheus, grafana, and otel collector - so duplicating all that again
// in a phat job with ephemeral-by-default storage, etc, is not ideal.  Initial cut of this play
// will include everything from Adriana's jobs, taken in turn from the upstream helmchart, and then
// can be stripped back to use local resources.  I hope it's a safe assumption that if you're playing
// with this you already have grafana, at least, and probably a prometheus in play.

// Gotcha 4 - usual constraints - service names max 63 chars and can't contain underscores. (@TODO confirm 
// what can't contain hyphens but needs underscores instead? - why did we start using underscores in ports?)

// Gotcha 5 -- ordering is important, and Nomad has only some very basic task-based ordering (refer:
// https://developer.hashicorp.com/nomad/docs/job-specification/lifecycle ) - which facilitates only
// three categories - pre, post-start, and post-stop, which won't help us here, as our dependency graph
// is a bit more complicated than that.
//
// Raw ordering / dependencies taken from docker-compose.yml :
//      
//  C     adservice - depends on otelcol
//      
//  C     cartservice - depends on otelcol, redis-cart
//      
//  D     checkoutservice - depends on cartservice, currencyservice, emailservice, otelcol, paymentservice,
//                        productcatalogservice, shippingservice
//      
//  C     currencyservice - depends on otelcol
//      
//  C     emailservice - depends on  otelcol
//      
//  B     featureflagservice - depends on ffs_postgres
//
//  A     ffs_postgres - no dependencies
//      
//  E     frontend - depends on adservice, cartservice, checkoutservice, currencyservice, otelcol, 
//                 productcatalogservice, quoteservice, recommendationservice, shippingservice
//      
//  G     frontendproxy - depends on  featureflagservice, frontend, grafana, loadgenerator
//
//  A     grafana - no dependencies
//
//  A     jaeger - no dependencies
//      
//  F     loadgenerator - depends on frontend
//      
//  B     otelcol - depends on jaeger
//      
//  C     paymentservice - depends on otelcol
//      
//  C     productcatalogservice - depends on otelcol
//
//  A     prometheus - no dependencies
//      
//  C     quoteservice - depends on otelcol
//      
//  D     recommendationservice - depends on featureflagservice, otelcol, productcatalogservice
//      
//  A     redis-cart - no dependencies
//      
//  C     shippingservice - depends on otelcol
//      
// Interpreting the above, since docker-compose-viz failed to satisfy:
//      a) ffs_postgres , grafana , jaeger , prometheus , redis-cart
//      b) featureflagservice , otelcol 
//      c) adservice , cartservice , currencyservice , emailservice , paymentservice
//         productcatalogservice , quoteservice , shippingservice
//      d) checkoutservice , recommendationservice
//      e) frontend
//      f) loadgenerator
//      g) frontendproxy


variables  {
  # These make it easier to adjust this to local resource names.
  loki = {
    url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
  }

  domain = {
    obs = "obs.int.jeddi.org"
  }
}


job "astronomy-shop-group-a" {
  #  Group A =  ffs_postgres , grafana, jaeger , prometheus , redis

  datacenters = ["DG"]
  type = "service"
  group "astronomy-shop-group-a" {
    # We want this running on the traefik-friendly 3-node cluster only
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {

      port "port_postgres" { 
        to = 5432
      }

      port "port_grafana" { 
        to = 3000
      }

      port "port_jaeger_frontend" {
        to = 16686
      }
      port "port_jaeger_collector" {
        to = 4317
      }

      port "port_otel_prometheus_ui" {
      }

      port "port_redis" {
        to = 6379
      }

    }


    # TASK -- ffspostgres  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-ffspostgres" {
      driver = "docker"

      config {
        image = "postgres:14"
        ports = ["port_postgres"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        POSTGRES_DB = "ffs"
        POSTGRES_PASSWORD = "ffs"
        POSTGRES_USER = "ffs"
        OTEL_SERVICE_NAME = "ffspostgres"
      }      

      service {
        name = "ffspostgres-service"
        port = "port_postgres"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.ffspostgres.rule=Host(`ffspostgres.${var.domain.obs}`)",
          "traefik.http.routers.ffspostgres.tls=false",
          "traefik.http.routers.ffspostgres.entrypoints=http",
        ]

        #check {
        #  interval = "10s"
        #  timeout  = "5s"
        #  type     = "script"
        #  command  = "pg_isready"
        #  args     = [
        #    "-d", "ffs",
        #    "-h", "${NOMAD_IP_port_postgres}",
        #    "-p", "${NOMAD_PORT_port_postgres}",
        #    "-U", "ffs"
        #  ]
        #}

      }

      resources {
        cpu = 55
        # memory = 300
        memory = 150
        memory_max = 500
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_ENDPOINT = "http://otel-collector-otlp.{{ env "var.domain.obs" }}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-ffspostgres"


    # TASK -- grafana  = = = = = = = = = = = = = = = = = = = = = = = =

    # @TODO adjust the whole play to talk to local extant grafana instance, but there's some
    # ease of use benefits to pulling in an ephemeral grafana and populating it with datasource
    # and dashboard (git)

    task "demo-grafana" {
      driver = "docker"

      config {
        image = "grafana/grafana:9.1.0"
        ports = ["port_grafana"]
        volumes = [ 
          "local/config:/etc/grafana",
          "local/provisioning:/etc/grafana/provisioning",
        ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        GF_AUTH_ANONYMOUS_ENABLED  = "true"
        GF_AUTH_ANONYMOUS_ORG_ROLE = "Editor"
        GF_SERVER_HTTP_PORT        = "${NOMAD_PORT_port_grafana}"

        GF_PATHS_DATA = "/var/lib/grafana/"
        GF_PATHS_LOGS = "/var/log/grafana"
        GF_PATHS_PLUGINS = "/var/lib/grafana/plugins"
        GF_LOG_LEVEL = "DEBUG"
        GF_LOG_MODE = "console"
        GF_PATHS_PROVISIONING = "/etc/grafana/provisioning"
      }      

      service {
        name = "grafana"
        port = "port_grafana"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.rule=Host(`grafana.${var.domain.obs}`)",
          "traefik.http.routers.grafana.tls=false",
          "traefik.http.routers.grafana.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_grafana"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        memory = 100
        memory_max = 150
      }

      artifact {
        source      = "github.com/open-telemetry/opentelemetry-demo/src/grafana/provisioning/dashboards"
        destination = "local/provisioning/dashboards"
      }

      template {
        data = <<EOH
[analytics]
check_for_updates = true
[auth]
disable_login_form = true
[auth.anonymous]
enabled = true
org_name = Main Org.
org_role = Admin
[grafana_net]
url = https://grafana.net
[log]
mode = console
[paths]
data = /var/lib/grafana/
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
;provisioning = /etc/grafana/provisioning
[server]
protocol = http
domain = otel-demo.localhost
http_port = 80
root_url = %(protocol)s://%(domain)s:%(http_port)s/grafana
serve_from_sub_path = true
EOH
        destination = "local/config/grafana.ini"
      }

      template {
        data = <<EOH
apiVersion: 1
datasources:
- editable: true
  isDefault: true
  name: Prometheus
  type: prometheus
  uid: webstore-metrics
  url: http://prometheus.{{ env "var.domain.obs" }}:80
- editable: true
  isDefault: false
  name: Jaeger
  type: jaeger
  uid: webstore-traces
  url: http://jaeger.{{ env "var.domain.obs" }}:80
EOH

        destination = "local/provisioning/datasources/datasources.yaml"
      }

    } // end-task "demo-grafana"


    # TASK -- jaeger  = = = = = = = = = = = = = = = = = = = = = = = =

    # @TODO rip this out - it's been mostly deprecated upstream, and we can probably live without it.

    task "demo-jaeger" {

      driver = "docker"

      config {
        image = "jaegertracing/all-in-one:latest"
        ports = ["port_jaeger_frontend", "port_jaeger_collector"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        COLLECTOR_OTLP_ENABLED = "true"
      }      

      service {
        name = "jaeger-collector"
        port = "port_jaeger_collector"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger.rule=Host(`jaeger.${var.domain.obs}`)",
          "traefik.http.routers.jaeger.tls=false",
          "traefik.http.routers.jaeger.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_jaeger_collector"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      service {
        name = "jaeger-frontend"
        port = "port_jaeger_frontend"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger-ui.rule=Host(`jaeger-ui.${var.domain.obs}`)",
          "traefik.http.routers.jaeger-ui.tls=false",
          "traefik.http.routers.jaeger-ui.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_jaeger_frontend"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        # memory_max = 250
      }

    } // end-task "demo-jaeger"


    # TASK -- prometheus  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-prometheus" {
      driver = "docker"

      config {
        image = "prom/prometheus:v2.38.0"
        ports = ["port_otel_prometheus_ui"]

        volumes = [
          "local/config:/etc/config",
        ]

        args = [ 
          "--config.file=/etc/config/prometheus.yml",
          "--storage.tsdb.path=/prometheus",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_otel_prometheus_ui}",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          "--web.enable-lifecycle"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
      }      

      service {
        name = "otelprometheus"
        port = "port_otel_prometheus_ui"
      
        # @NOTE we need to watch for contention for 'prometheus' in consul or DNS
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.otel_prometheus.rule=Host(`otel_prometheus.${var.domain.obs}`)",
          "traefik.http.routers.otel_prometheus.tls=false",
          "traefik.http.routers.otel_prometheus.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_otel_prometheus_ui"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 80
        memory = 250
        # memory_max = 800
      }

      template {
        data = <<EOF
global:
  # evaluation_interval: 30s
  scrape_interval: 10s

scrape_configs:
  - job_name: 'otel'
    static_configs:
    - targets: [ 'prometheus.{{ env "var.domain.obs" }}:80' ]

  - job_name: 'otel-collector'
    static_configs:
    - targets: [ 'otel-collector-metrics:80' ]

EOF
        destination = "local/config/prometheus.yml"
        ## We use this everywhere, but if set it breaks - prometheus will error out:
        ## Template failed to read environment variables error parsing env template missing =
        # env         = true
      }

      # @TODO jedd - perhaps rip these out - they don't appear useful, and shouldn't be required to exist.
      template {
        data = <<EOH
{}
EOH
        destination = "local/config/recording_rules.yml"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/rules"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/alerting_rules.yml"
      }

      template {
        data = <<EOH
{}
EOH
        destination = "local/config/alerts"
      }
    } // end-task "demo-prometheus"


    # TASK -- redis  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-redis" {
      driver = "docker"

      config {
        image = "redis:alpine"
        ports = ["port_redis"]
        volumes = [ 
          "local/redis.conf:/etc/redis.conf"
        ]

        args = [
          # "--name" , "redis",
          # "-h", "${NOMAD_IP_port_redis}",
          # "-p", "${NOMAD_PORT_port_redis}",
          # "PING"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
      }

      service {
        name = "redis"
        port = "port_redis"
      
        tags = [
          # "traefik.enable=true",
          # "traefik.http.routers.redis.rule=Host(`redis.obs.int.jeddi.org`)",
          # "traefik.http.routers.redis.tls=false",
          # "traefik.http.routers.redis.entrypoints=http,redis",

          # "traefik.enable=true",
          # "traefik.tcp.routers.redis.rule=HostSNI(`redis.obs.int.jeddi.org`)",
          # "traefik.tcp.routers.redis.tls=false",
          # "traefik.tcp.routers.redis.entrypoints=grpc",

          "traefik.enable=true",

          "traefik.tcp.routers.redis.rule=HostSNI(`redis.obs.int.jeddi.org`)",
          "traefik.tcp.routers.redis.service=redis",
          "traefik.tcp.routers.redis.tls=false",
          "traefik.tcp.routers.redis.entrypoints=redis",

          "traefik.tcp.services.redis.loadlbalancer.server.port=${NOMAD_PORT_port_redis}",
          # "traefik.tcp.services.redis.loadlbalancer.server.port=6001",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_redis"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 200
      }

      template {
        data = <<EOF
port {{ env "NOMAD_PORT_port_redis" }}

EOF
        destination = "local/redis.conf"
        # env         = true
      }

    } // end-task "demo-redis"

  }  // end-group "astronomy-shop"

}

