
// jedd-lab - OTEL Astronomy Shop Demo - GROUP C
//
// WIP attempt to get the OTEL demo suite running in Nomad.
//
// <PERSON><PERSON> has two posts about this:
//    OpenTelemetry.io  2022-12 :  https://opentelemetry.io/blog/2022/otel-demo-app-nomad/
//    her Medium site   2021-12 :  https://storiesfromtheherd.com/just-in-time-nomad-running-the-opentelemetry-collector-on-hashicorp-nomad-with-hashiqube-4eaf009b8382
//
// The newer one starts with the suggestion it's her first attempt at setting this up, and both
// instances rely on bespoke traefik, grafana, jaeger, OTEL collector etc - plus they rely on
// HashiQube, which we don't want to touch.  She tends to run them all as separate nomad jobs,
// relying on traefik to get them talking to each other.  There's some divergence in the approaches,
// the older version uses honeycomb.  In any case, despite being the only attempt on the net I could
// find, I'm mostly going to be starting from scratch.  Insert generic grumble aboue people that don't
// put ANY FLIPPING COMMENTS in their code / jobs / etc.

// Consistency is the hobgoblin of small minds .. yada yada.  I like the consistency of using port name
// of 'containerport' everywhere (though I note <PERSON>a doesn't do that for the grafana task - there the
// port is just called 'http') but on the other hand I abhor the overloading of variables that are hard
// to track down later if you're not breathing Nomad HCL, and specifically a foreign recipe, daily.  And
// obviously with lots of tasks in one big group (or a handful of groups with many tasks in each, as we'll
// probably end up with here) this naming scheme obviously wouldn't work.
//
// At work we've adopted a prefix of port_ everywhere for port names - it makes NOMAD_PORT_port_... look
// a bit redundant, but it's *obvious* later on when you see it with or without context.
//
// Similarly our job, group, and task names need to make sense when reviewing Loki logs, which pick up
// from the Loki driver for docker.  I've added lots of loki-exporting stanzas here.


// Gotcha 1 - you will likely get some inotify failures causing docker to bomb out, this is related to
// the sysctl settable value for user.max_inotify_instances -- has a default of 128, which is way too
// small apparently.
// exact error:  Unhandled exception. System.IO.IOException: The configured user limit (128) on the number of inotify instances has been reached, or the per-process limit on the number of open file descriptors has been reached

// Gotcha 2 - Loki driver - read up: https://grafana.com/docs/loki/latest/clients/docker-driver/
// This means you need to run:
// docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
// alternatively - comment out all the logging stanzas below.

// Gotcha 3 - I run my own prometheus, grafana, and otel collector - so duplicating all that again
// in a phat job with ephemeral-by-default storage, etc, is not ideal.  Initial cut of this play
// will include everything from Adriana's jobs, taken in turn from the upstream helmchart, and then
// can be stripped back to use local resources.  I hope it's a safe assumption that if you're playing
// with this you already have grafana, at least, and probably a prometheus in play.

// Gotcha 4 - usual constraints - service names max 63 chars and can't contain underscores. (@TODO confirm 
// what can't contain hyphens but needs underscores instead? - why did we start using underscores in ports?)

// Gotcha 5 -- ordering is important, and Nomad has only some very basic task-based ordering (refer:
// https://developer.hashicorp.com/nomad/docs/job-specification/lifecycle ) - which facilitates only
// three categories - pre, post-start, and post-stop, which won't help us here, as our dependency graph
// is a bit more complicated than that.
//
// Raw ordering / dependencies taken from docker-compose.yml :
//      
//  C     adservice - depends on otelcol
//      
//  C     cartservice - depends on otelcol, redis-cart
//      
//  D     checkoutservice - depends on cartservice, currencyservice, emailservice, otelcol, paymentservice,
//                        productcatalogservice, shippingservice
//      
//  C     currencyservice - depends on otelcol
//      
//  C     emailservice - depends on  otelcol
//      
//  B     featureflagservice - depends on ffs_postgres
//
//  A     ffs_postgres - no dependencies
//      
//  E     frontend - depends on adservice, cartservice, checkoutservice, currencyservice, otelcol, 
//                 productcatalogservice, quoteservice, recommendationservice, shippingservice
//      
//  G     frontendproxy - depends on  featureflagservice, frontend, grafana, loadgenerator
//
//  A     grafana - no dependencies
//
//  A     jaeger - no dependencies
//      
//  F     loadgenerator - depends on frontend
//      
//  B     otelcol - depends on jaeger
//      
//  C     paymentservice - depends on otelcol
//      
//  C     productcatalogservice - depends on otelcol
//
//  A     prometheus - no dependencies
//      
//  C     quoteservice - depends on otelcol
//      
//  D     recommendationservice - depends on featureflagservice, otelcol, productcatalogservice
//      
//  A     redis-cart - no dependencies
//      
//  C     shippingservice - depends on otelcol
//      
// Interpreting the above, since docker-compose-viz failed to satisfy:
//      a) ffs_postgres , grafana , jaeger , prometheus , redis-cart
//      b) featureflagservice , otelcol 
//      c) adservice , cartservice , currencyservice , emailservice , paymentservice
//         productcatalogservice , quoteservice , shippingservice
//      d) checkoutservice , recommendationservice
//      e) frontend
//      f) loadgenerator
//      g) frontendproxy


variables  {
  # These make it easier to adjust this to local resource names.
  loki = {
    url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
  }

  domain = {
    obs = "obs.int.jeddi.org"
  }
}


job "astronomy-shop-group-c" {
  # Group C =  adservice , cartservice , currencyservice , emailservice , 
  #            paymentservice , productcatalogservice , quoteservice , 
  #            shippingservice

  datacenters = ["DG"]
  type = "service"
  group "astronomy-shop-group-c" {
    # We want this running on the traefik-friendly 3-node cluster only
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {

      port "port_adservice" { 
        to = 9555
      }

      port "port_cartservice" { 
        to = 7070
        static = 7070
      }

      port "port_currencyservice" { 
        to = 7001
      }

      port "port_emailservice" { 
        to = 6060
      }

      port "port_paymentservice" {
        to = 5051
      }

      port "port_productcatalogservice" {
        to = 3550
      }

      port "port_quoteservice" {
        to = 8090
      }

      port "port_shippingservice" {
        to = 50050
      }

    }


    # TASK -- adservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-adservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-adservice"
        ports = ["port_adservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        AD_SERVICE_PORT = "${NOMAD_PORT_port_adservice}"
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "adservice"
      }      

      service {
        name = "adservice"
        port = "port_adservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.adservice.rule=Host(`adservice.${var.domain.obs}`)",
          "traefik.http.routers.adservice.tls=false",
          "traefik.http.routers.adservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_adservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        memory = 100
        # memory = 250
        memory_max = 800
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_ENDPOINT = "http://otel-collector-otlp.${var.domain.obs}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-adservice"


    # TASK -- cartservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-cartservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-cartservice"
        ports = ["port_cartservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        ASPNETCORE_URLS = "http://*:${NOMAD_PORT_port_cartservice}"
        CART_SERVICE_PORT = "${NOMAD_PORT_port_cartservice}"
        OTEL_SERVICE_NAME = "cartservice"
      }      

      service {
        name = "cartservice"
        port = "port_cartservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.cartservice.rule=Host(`cartservice.${var.domain.obs}`)",
          "traefik.http.routers.cartservice.tls=false",
          "traefik.http.routers.cartservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_cartservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        # memory = 250
        memory = 150
        memory_max = 500
      }

      template {
        data = <<EOF

REDIS_ADDR = "redis.{{ env "var.domain.obs" }}:80"

OTEL_EXPORTER_OTLP_ENDPOINT = "http://otel-collector-otlp.{{ env "var.domain.obs" }}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-cartservice"


    # TASK -- currencyservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-currencyservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-currencyservice"
        ports = ["port_currencyservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        CURRENCY_SERVICE_PORT = "${NOMAD_PORT_port_currencyservice}"
        OTEL_RESOURCE_ATTRIBUTES = "service.name=currencyservice"
      }      

      service {
        name = "currencyservice"
        port = "port_currencyservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.currencyservice.rule=Host(`currencyservice.${var.domain.obs}`)",
          "traefik.http.routers.currencyservice.tls=false",
          "traefik.http.routers.currencyservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_currencyservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
        memory_max = 250
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.${var.domain.obs}"
EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-currencyservice"


    # TASK -- emailservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-emailservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-emailservice"
        ports = ["port_emailservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        APP_ENV = "production"
        EMAIL_SERVICE_PORT = "${NOMAD_PORT_port_emailservice}"
        OTEL_SERVICE_NAME = "emailservice"
      }      

      service {
        name = "emailservice"
        port = "port_emailservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.emailservice.rule=Host(`emailservice.${var.domain.obs}`)",
          "traefik.http.routers.emailservice.tls=false",
          "traefik.http.routers.emailservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_emailservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        memory_max = 250
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.{{ env "var.domain.obs" }}"


EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-emailservice"


    # TASK -- paymentservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-paymentservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-paymentservice"
        ports = ["port_paymentservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "paymentservice"
        PAYMENT_SERVICE_PORT = "${NOMAD_PORT_port_paymentservice}"
      }      

      service {
        name = "paymentservice"
        port = "port_paymentservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.paymentservice.rule=Host(`paymentservice.${var.domain.obs}`)",
          "traefik.http.routers.paymentservice.tls=false",
          "traefik.http.routers.paymentservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_paymentservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 250
        # memory_max = 400
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_METRICS_ENDPOINT = "http://otel-collector-otlp.{{env "var.domain.obs" }}"
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.{{env "var.domain.obs" }}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-paymentservice"


    # TASK -- productcatalogservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-productcatalogservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-productcatalogservice"
        ports = ["port_productcatalogservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_SERVICE_NAME = "productcatalogservice"
        PRODUCT_CATALOG_SERVICE_PORT = "${NOMAD_PORT_port_productcatalogservice}"
      }      

      service {
        name = "productcatalogservice"
        port = "port_productcatalogservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.productcatalogservice.rule=Host(`productcatalogservice.${var.domain.obs}`)",
          "traefik.http.routers.productcatalogservice.tls=false",
          "traefik.http.routers.productcatalogservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_productcatalogservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 150
        # memory_max = 250
      }

      template {
        data = <<EOF

FEATURE_FLAG_GRPC_SERVICE_ADDR = "featureflagservice-grpc.{{env "var.domain.obs" }}:80"

OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.{{env "var.domain.obs" }}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-productcatalogservice"


    # TASK -- quoteservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-quoteservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-quoteservice"
        ports = ["port_quoteservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_TRACES_PROTOCOL = "http/protobuf"
        OTEL_PHP_TRACES_PROCESSOR = "simple"
        OTEL_SERVICE_NAME = "quoteservice"
        OTEL_TRACES_EXPORTER = "otlp"
        OTEL_TRACES_SAMPLER = "parentbased_always_on"
        QUOTE_SERVICE_PORT = "${NOMAD_PORT_port_quoteservice}"
      }

      service {
        name = "quoteservice"
        port = "port_quoteservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.quoteservice.rule=Host(`quoteservice.${var.domain.obs}`)",
          "traefik.http.routers.quoteservice.tls=false",
          "traefik.http.routers.quoteservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_quoteservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 100
      }

      template {
        data = <<EOF

OTEL_EXPORTER_OTLP_ENDPOINT = "http://otel-collector-http.{{env "var.domain.obs" }}:80"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-quoteservice"


    # TASK -- shippingservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-shippingservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-shippingservice"
        ports = ["port_shippingservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_SERVICE_NAME = "shippingservice"
        SHIPPING_SERVICE_PORT = "${NOMAD_PORT_port_shippingservice}"
      }


      service {
        name = "shippingservice"
        port = "port_shippingservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.shippingservice.rule=Host(`shippingservice.${var.domain.obs}`)",
          "traefik.http.routers.shippingservice.tls=false",
          "traefik.http.routers.shippingservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_shippingservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 75
      }

      template {
        data = <<EOF

QUOTE_SERVICE_ADDR = "http://quoteservice.{{env "var.domain.obs" }}:80"

OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.{{env "var.domain.obs" }}"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-shippingservice"

  }  // end-group "astronomy-shop"

}

