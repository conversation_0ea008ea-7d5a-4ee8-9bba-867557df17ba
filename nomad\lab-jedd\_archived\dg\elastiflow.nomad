// jedd lab - elastiflow (next-gen) 
//
// adapted from:  https://docs.elastiflow.com/docs/install_docker

job "elastiflow" {
  datacenters = ["DG"]
  type        = "service"
  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }
  group "elastiflow" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port "port_elastiflow"  {
        static = 2055
      }
    }

    volume "loki" {
      type      = "host"
      read_only = false
      source    = "loki"
    }

    task "elastiflow" {
      driver = "docker"
      config {
        image = "elastiflow/flow-collector:5.1.7"
        ports = ["port_elastiflow"]
      }

#      volume_mount {
#        volume      = "vol_elastiflow"
#        destination = "/elastiflow"
#        read_only   = false
#      }

#      template {
#        data = <<EOH
#
#EOH
#        destination = "local/loki/local-config.yaml"
#      }

      resources {
        cpu    = 512
        memory = 512
      }

      service {
        name = "elastiflow"
        port = "port_elastiflow"
        check {
          name     = "Elastiflow healthcheck"
          port     = "port_elastiflow"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }


      }
    }
  }
}
