
# Excalidraw for <PERSON><PERSON>'s lab (DG)


job "excalidraw" {
  type        = "service"

  datacenters = ["DG"]

  group "excalidraw" {
    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    ephemeral_disk {
      migrate = true
      size    = 300
      sticky  = true
    }

    network {
      port "port_excalidraw" {
        static = 5000
        }
      }

    task "excalidraw" {
      driver = "docker"

      config {
        image = "excalidraw/excalidraw"
        ports = ["port_excalidraw"]
      }

      env = {
      }

      resources {
        cpu    = 128
        memory = 512
      }

      service {
        name = "excalidraw"
        port = "port_excalidraw"

#        check {
#          type     = "tcp"
#          interval = "120s"
#          timeout  = "10s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.excalidraw.entrypoints=http",
          "traefik.http.routers.excalidraw.tls=false",
          "traefik.http.routers.excalidraw.rule=Host(`excalidraw.obs.int.jeddi.org`)",
        ]
      }

    }
  }
}



