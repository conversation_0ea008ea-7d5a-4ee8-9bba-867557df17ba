// jedd lab - prometheus exporter third party for oracle
//
// docker commandline is:
//   docker run   -p 9161:9161 -e DATA_SOURCE_NAME="system/oracle@//**************:1521" iamseth/oracledb_exporter
//
// minimalistic docker hub page:
//   https://hub.docker.com/r/iamseth/oracledb_exporter

// github project page:
//   https://github.com/iamseth/oracledb_exporter


job "exporter-oracle" {
  datacenters = ["DG"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "prometheus-oracle" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {
      port "port_exporter"  {
        static = 9161
        to     = 9161
      }
    }

    task "oracle" {
      driver = "docker"

      env = {
        "DATA_SOURCE_NAME" = "system/oracle@//**************:1521"
      }

      config {
        image = "iamseth/oracledb_exporter"

        ports = ["port_exporter"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      #resources {
      #  cpu    = 512
      #  memory = 512
      #}

      service {
        name = "exporter-oracle"
        port = "port_exporter"
        check {
          name     = "Oracle exporter healthcheck"
          port     = "port_exporter"
          type     = "tcp"
          interval = "60s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }

    }
  }
}
