
//  exporter-redfish for prometheus - for jedd's lab (dg)
//
//  redfish is the API used by Dell iDRAC OOB systems, amongst others
//
//  Similar to snmp exporter, the target hostname is supplied to this exporter as a
//  URL parameter, for example:
//
//      curl http://localhost:xxx/redfish?target=servername.fqdn&job=redfish/jobname
//
//      or explicitly on <PERSON><PERSON>'s lab:
//
//      curl "http://dg-pan-01.int.jeddi.org:9610/redfish?target=funky.fqdn&job=redfish"
//
//  Refer:   
//        https://hub.docker.com/r/solipsist01/redfish-exporter
//        https://github.com/sapcc/redfish-exporter

job "exporter-redfish" {
  type = "service"
  datacenters = ["DG"]

  group "exporter-redfish" {
    network {
      port "port_redfish" {
        # static = 9218 
        static = 9610
      }
    }

    task "exporter-redfish" {
      driver = "docker"

      config {
        ports = [ 
          "port_redfish" 
          ]
        # command = "/bin/sleep"
        image = "solipsist01/redfish-exporter"

        dns_servers = [
          "**************"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      service {
        name = "exporter-redfish"
        port = "port_redfish"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.redfish.rule=Host(`redfish.int.jeddi.org`)",
          "traefik.http.routers.redfish.tls=false",
        ]

#        check {
#          type = "http"
#          port = "port_redfish"
#          path = "/"
#          interval = "20s"
#          timeout = "10s"
#        }

      }

      template {
        data = <<EOH
# listen_port: 9218
listen_port: 9610
username: usernameTBC
password: passwordTBC
timeout:  10
job: 'opentel/redfish'

EOH
        destination = "local/redfish.yaml"
      }

      resources {
        cpu    = 100
        memory = 256
      }

    }

  }
}
