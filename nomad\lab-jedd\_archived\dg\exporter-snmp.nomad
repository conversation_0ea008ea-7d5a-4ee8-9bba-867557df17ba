
//  snmp-exporter for prometheus - for jedd's lab (dg)

//  snapshot 2025-07-31 - original moved to jgit:nomad

job "exporter-snmp2" {
  type = "service"
  datacenters = ["DG"]

  group "exporter-snmp" {

    network {
      port "port-exporter-snmp" {
        static = 9116 
      }
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    task "exporter-snmp" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = [
          "port-exporter-snmp"
          ]
        dns_servers = [
          "**************", "************"]
        volumes = [
          # "local/snmp_exporter.yaml:/etc/snmp_exporter.yaml"
          "local/prometheus-configuration/dg-pan-01/snmp/snmp.yml:/etc/snmp_exporter/snmp.yml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      service {
        name = "exporter-snmp"
        port = "port-exporter-snmp"
//
//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      artifact {

        source = "git::ssh://<EMAIL>/prometheus-configuration"
        destination = "local/prometheus-configuration"

        options {
          sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }

#      template {
#        data = <<EOH
#modules:
#  prober: https
#  timeout: 10s
#
#EOH
#        destination = "local/snmp_exporter.yaml"
#      }

      resources {
        cpu    = 256
        memory = 128
      }

    }

  }
}
