
// Jedd lab - python flask - basic instance (precursor to ad hoc app implementation)

variables {
  consul_hostname = "dg-pan-01.int.jeddi.org:8500"
}

job "flask"  {
  datacenters = ["DG"]
  type = "service"

  group "flask-basic" {
    network {
      port "port_http" {
        # static = 19009
        static = 8088
      }
    }

    volume "vol_flask"  {
      type = "host"
      source = "vol_flask"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    task "flask-basic" {
      driver = "docker"

      # user = "root:root"
      user = "root"
      # user = "nginx:nginx"

      volume_mount {
        volume = "vol_flask"
        destination = "/mnt/flask"
        read_only = false
      }

      env = {
        "LISTEN_PORT" = "8088"
        "FLASK_APP" = "main.py",
        "FLASK_DEBUG" = "1",
        "RUN" = "flask run --host=0.0.0.0 --port 8088"
      }

      config {
        image = "tiangolo/uwsgi-nginx-flask"
        dns_servers = ["**************"]
        ports = ["port_http"]
        volumes = [
          "local/prestart.sh:/app/prestart.sh",
          "local/main.py:/app/main.py",
        ]
        command = "/local/prestart.sh"
        network_mode = "host"

        # errors at runtime logs include:
        # 
        # " /usr/lib/python2.7/dist-packages/supervisor/options.py:461: UserWarning: Supervisord is running as root and it is 
        #  searching for its configuration file in default locations (including its current working directory); you probably
        #  want to specify a "-c" argument specifying an absolute path to a configuration file for improved security.
        #
        # 'Supervisord is running as root and it is searching '
        # 2022/05/03 13:34:44 [emerg] 12#12: chown("/var/cache/nginx/client_temp", 101) failed (1: Operation not permitted)
        # nginx: [emerg] chown("/var/cache/nginx/client_temp", 101) failed (1: Operation not permitted)

#        args = [
#          "-config.file=/local/mimir.yml",
#          "-config.expand-env",
#          "-consul.hostname=dg-pan-01.int.jeddi.org:8500",
#           "-log.level=warn",
#          "server.http_listen-address=**************",
#          "server.grpc_listen-address=**************"
#        ]

      }

      resources {
        cpu = 500
        memory = 2048
      }

      service {
        name = "mimir"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.flask.rule=Host(`flask.int.jeddi.org`)",
          "traefik.http.routers.flask.tls=false",
        ]



      }

      service {
        name = "flask-web"
        port = "port_http"
        tags = ["traefik.enable=true"]

#        check {
#          type = "http"
#          port = "port_http"
#          path = "/services"
#          interval = "30s"
#          timeout = "5s"
#        }
      }

      template {
        data = <<EOH
from flask import Flask
app = Flask(__name__)

@app.route("/")
def hello():
    return "Hello World from Flask"

if __name__ == "__main__":
    # Only for debugging while developing
    app.run(host='0.0.0.0', debug=True, port=8088)

EOH
        destination = "local/main.py"

      }

      template {
        data = <<EOH
#! /usr/bin/env bash



python3 /local/main.py

EOH
        destination = "local/prestart.sh"
        perms = "755"
      }


    }
  }
}
