
# Notes on setting up the nomad job

note that garage == distributed object storage (s3 or minio alike)

3-node cluster using nomad job - separate directories on /opt/sharednfs/garage/ - based on hostname (dg-hac-0[123])


# Forming the 3 jobs into a cluster

A lot of the following comes from the quickstart guide:
https://garagehq.deuxfleurs.fr/documentation/quick-start/


The 3 instances come up without knowing about each other.

On each host you can use the built-in 'garage' in the docker container this way.  Note that there's no sh or bash in these things. : (

jedd@dg-hac-01:~$ docker ps | grep garage
dbdb344438f1   dxflrs/garage:v0.9.1                  "/garage server"         7 minutes ago   Up 7 minutes            *************:3900-3904->3900-3904/tcp, *************:3900-3904->3900-3904/udp                                                             garage-task-9c7350e3-a914-876c-00e3-0b6c6334eb62

use the container id to:

jedd@dg-hac-01:~$ alias garage="docker exec -ti dbdb3 /garage"


Then you can run 'garage' command natively.

On first host - dg-hac-01 - run:

jedd@dg-hac-01:~$ garage node id
0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

To instruct a node to connect to this node, run the following command on that node:
    garage [-c <config file path>] node connect 0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

(There's some 'tell them to connect to me' instructions, but it' easier to visit the other two nodes)

Connect to 02 and 03, and use the same:   docker ps // alias garage sequence and then the command as above:

    garage  node connect 0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

Run 'garage status' on any of the hosts, once the above's done (the hosts share consensus, so you only need to tell 2 and 3 about 1)

jedd@dg-hac-03:~$ garage status
==== HEALTHY NODES ====
ID                Hostname      Address                      Tags              Zone  Capacity  DataAvail
0d77307affe8d4c9  dbdb344438f1  *************:3901           NO ROLE ASSIGNED
f06e1b2a7f28af20  9356f61fbf59  [::ffff:*************]:3901  NO ROLE ASSIGNED
a164b1fc6f69d576  ae633f23ef0b  *************:3901           NO ROLE ASSIGNED

It seem to flip between vanilla ipv4 and that weird ipv6 style prefix

Data is stored in the data-dir - in dg job that means /opt/sharednfs/garage/{hostname}/data

From any host in the cluster, using the aliased garage command:


# Review health of the cluster

jedd@dg-hac-03:~$ garage status
==== HEALTHY NODES ====
ID                Hostname      Address                      Tags              Zone  Capacity  DataAvail
0d77307affe8d4c9  dbdb344438f1  *************:3901           NO ROLE ASSIGNED
f06e1b2a7f28af20  9356f61fbf59  [::ffff:*************]:3901  NO ROLE ASSIGNED
a164b1fc6f69d576  ae633f23ef0b  *************:3901           NO ROLE ASSIGNED

# Create a cluster layout - assign disk to garage

jedd@dg-hac-03:~$ df -h /opt/sharednfs/
Filesystem                    Type  Size  Used Avail Use% Mounted on
**************:/opt/sharednfs nfs4  108G   30G   74G  29% /opt/sharednfs

jedd@dg-hac-03:~$ garage layout assign 0d77 -z dg -c 10G    
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout assign f06e -z dg -c 10G 
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout assign a164 -z dg -c 10G 
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout show
==== CURRENT CLUSTER LAYOUT ====
No nodes currently have a role in the cluster.
See `garage status` to view available nodes.

Current cluster layout version: 0

==== STAGED ROLE CHANGES ====
ID                Tags  Zone  Capacity
0d77307affe8d4c9        dg    10.0 GB
a164b1fc6f69d576        dg    10.0 GB
f06e1b2a7f28af20        dg    10.0 GB


==== NEW CLUSTER LAYOUT AFTER APPLYING CHANGES ====
ID                Tags  Zone  Capacity  Usable capacity
0d77307affe8d4c9        dg    10.0 GB   10.0 GB (100.0%)
a164b1fc6f69d576        dg    10.0 GB   10.0 GB (100.0%)
f06e1b2a7f28af20        dg    10.0 GB   10.0 GB (100.0%)

Zone redundancy: maximum

==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     39.1 MB
Usable capacity / total cluster capacity:   30.0 GB / 30.0 GB (100.0 %)
Effective capacity (replication factor 3):  10.0 GB

dg                  Tags  Partitions        Capacity  Usable capacity
  0d77307affe8d4c9        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  a164b1fc6f69d576        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  f06e1b2a7f28af20        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  TOTAL                   768 (256 unique)  30.0 GB   30.0 GB (100.0%)


To enact the staged role changes, type:

    garage layout apply --version 1

You can also revert all proposed changes with: garage layout revert --version 1


jedd@dg-hac-03:~$ garage layout apply --version 1
==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     39.1 MB
Usable capacity / total cluster capacity:   30.0 GB / 30.0 GB (100.0 %)
Effective capacity (replication factor 3):  10.0 GB

dg                  Tags  Partitions        Capacity  Usable capacity
  0d77307affe8d4c9        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  a164b1fc6f69d576        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  f06e1b2a7f28af20        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  TOTAL                   768 (256 unique)  30.0 GB   30.0 GB (100.0%)


New cluster layout with updated role assignment has been applied in cluster.
Data will now be moved around between nodes accordingly.




# Create key and bucket on the cluster

Do this from any host - after running the 'alias garage ...' (docker) command above.

Create a bucket:
  garage bucket create loki-ha

Show all buckets:
  garage bucket list

Show details about this newly created bucket:
  garage bucket info loki-ha

Create an API key:
  garage key create loki-ha-key

This provides key-id (access key) and Secret Key

Confirm things look sensible:
  garage key list

Details about this key:
  garage key info loki-ha-key
Note Authorized buckets section will be empty

Assign key to bucket:
  garage bucket allow --read --write --owner loki-ha --key loki-ha-key

Running `garage bucket info loki-ha` now has the 'RWO' perms and authorized key of 'loki-ha-key' listed

# Using the 'aws' CLI tool - on debian it's just 'awscli' package, and provides /usr/bin/aws

dg-pan-01:~$ export AWS_ACCESS_KEY_ID=GKfa249bae7d21cc4a1a5a05b4
dg-pan-01:~$ export AWS_SECRET_ACCESS_KEY=dabe3d073411f4a1f25a04af0f21a1cef4e7ee647720b97f9752106b59c1d6d9
dg-pan-01:~$ export AWS_DEFAULT_REGION='garage'
dg-pan-01:~$ export AWS_ENDPOINT_URL='http://dg-hac-01.int.jeddi.org:3900'

Noting on dg-pan-01 I get version awscli version 2.9.19 - and AWS_ENDPOINT_URL isn't respected until 2.13
consequently commands need to have --endpoint-url=http://dg-hac-01.int.jeddi.org:3900

eg.

   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900
2024-02-18 22:31:39 loki-ha

Diving into a specific bucket with a similar command:
   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900 loki-ha/
   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900 loki-ha/index/

... and so on.








