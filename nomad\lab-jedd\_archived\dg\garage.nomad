
# garage - cluster mode - 3 nodes (should really be 4, but meh) for DG, running on dg-hac-0[123]
#
# PoC - obviously NOT actual ha, as it all runs on the same vmware host, sharing the same platters.

# garage == distributed object storage (s3 or minio alike)

# Refer:
#   https://garagehq.deuxfleurs.fr/documentation/quick-start/

variables {
  # image_garage = "dxflrs/garage:v0.9.1"
  image_garage = "dg-pan-01.int.jeddi.org:5000/garage:v1.0.0"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "garage" {

  datacenters = ["DG"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
   }

# Group garage  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "garage" {

    count = 3
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    network {
      # We don't use Traefik - instead we use static ports, and static task-per-host.
      port "port_3900" {
        static = 3900
      }
      port "port_3901" {
        static = 3901
      }
      port "port_3902" {
        static = 3902
      }
      port "port_3903" {
        static = 3903
      }
      port "port_3904" {
        static = 3904
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "garage-task" {
      driver = "docker"

      env = {
      }

      config {
        image = var.image_garage

        # ports = ["port_api", "port_console"]
        ports = ["port_3900", "port_3901", "port_3902", "port_3903", "port_3904"]

        args = [ 
#          "-c",   "/etc/garage.toml",
#          "server", 
#          "--address",           "0.0.0.0:${NOMAD_HOST_PORT_port_api}",
#          "--console-address",   "0.0.0.0:${NOMAD_HOST_PORT_port_console}",
#          # "/data",
#          "http://dg-hac-0{1...3}.obs.int.jeddi.org:${NOMAD_PORT_port_api}/data",

        ]

        volumes = [
          "local/garage-cluster.toml:/etc/garage.toml",
          "/opt/sharednfs/garage:/persistent/"
        ]

        logging {
          type = "loki"
          config {
            # loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-url = "http://lokiwrite.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      resources {
        cpu = 200
        memory = 200
        # memory_max = 800
      }



      template {
        data = <<EOH
# metadata_dir = "/var/lib/garage/meta"
# data_dir = "/var/lib/garage/data"

metadata_dir = "/persistent/{{ env "attr.unique.hostname" }}/meta"
data_dir = "/persistent/{{ env "attr.unique.hostname" }}/data"

db_engine = "lmdb"

replication_mode = "3"

compression_level = 2

rpc_bind_addr = "[::]:3901"
#rpc_public_addr = "<this node's public IP>:3901"
rpc_public_addr = "{{ env "NOMAD_IP_port_3901" }}:3901"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage"
index = "index.html"

EOH
        destination = "local/garage-cluster.toml"
        perms = "755"
      }



      # garage-single -- unused, in preference to the cluster version above
      template {
        data = <<EOH
metadata_dir = "/tmp/meta"
data_dir = "/tmp/data"
db_engine = "lmdb"

replication_mode = "none"

rpc_bind_addr = "[::]:3901"
rpc_public_addr = "127.0.0.1:3901"
# rpc_secret = "$(openssl rand -hex 32)"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage.localhost"
index = "index.html"

[k2v_api]
api_bind_addr = "[::]:3904"

[admin]
api_bind_addr = "0.0.0.0:3903"
admin_token = "$(openssl rand -base64 32)"


EOH
        destination = "local/garage-single.toml"
        perms = "755"
      }


      service {
        name = "garage"
        port = "port_3902"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.garage.rule=Host(`garage.obs.int.jeddi.org`)",
          "traefik.http.routers.garage.entrypoints=http",
          "traefik.http.routers.garage.tls=false",
        ]

#        check {
#          name     = "garage_alive"
#          type     = "http"
#          port     = "port_console"
#          path     = "/m_aliveinio/health/live"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
