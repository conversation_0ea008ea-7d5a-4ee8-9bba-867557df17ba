// jedd lab - grafana agent hcl2  - 2021-10 experiment for metrics

job "grafana-agent" {
  type = "service"
  datacenters = ["DG"]

  group "grafana-agent" {

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {
      port "port-grafana-agent" {
        static = 12345
      }
  	}

    task "grafana-agent" {
      driver = "docker"

      config {
        image = "https://docker.io/grafana/agent"
        dns_servers = ["**************"]

        volumes = [ 
          "local/agent.yaml:/etc/agent/agent.yaml"
        ]

        network_mode = "host"
      }

      service {
        name = "grafana-agent-http"
        port = "port-grafana-agent"

       check {
         type = "http"
         port = "port-grafana-agent"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }

      }

      template {
        data = <<EOH

server:
  log_level: info
  http_listen_port: 12345
  # grpc default listen is 9095 which clashes with cortex (single instance) default
  grpc_listen_port:  9096

prometheus:

  global:
    scrape_interval: 1m

  configs:
    - name: test
      host_filter: false
      scrape_configs:
        - job_name: local_scrape
          static_configs:
            - targets: ['127.0.0.1:12345']
              labels:
                cluster: 'localhost'
                provenance: 'grafana-agent'

        - job_name: test_dedup_hassio
          static_configs:
            - targets: [ 'dg-hassio-01.int.jeddi.org:9273']
              labels:
                cluster: 'hassio'
                provenance: 'grafana-agent'

      remote_write:
        # - url: http://localhost:9009/api/prom/push
        - url: 'http://dg-pan-01.int.jeddi.org:9009/api/v1/push'

EOH
        destination = "local/agent.yaml"
      }
    }
  }
}
