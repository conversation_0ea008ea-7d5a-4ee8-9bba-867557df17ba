
// jedd lab - grafana alloy - 2024-04 experiment, lifed from <PERSON>' prod copy

job "grafana-alloy" {
  type        = "service"
  datacenters = ["DG"]

  group "grafana-alloy" {
    count = 1

    #ephemeral_disk {
    #  migrate = true
    #  size    = 500
    #  sticky  = true
    #}
    
    network {
      port "http" { }
      port "cluster" { }
      port "otlp" {
        to = 4317
      } 
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123456]"
    }

    task "grafana-alloy-task" {
      driver = "docker"

      config {
        image = "grafana/alloy"

        ports = [
          "http", 
          "otlp", 
          "cluster"
        ]

        entrypoint = [
          "/bin/alloy",
          "run",
          "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
          "--server.http.enable-pprof=true",
          "--cluster.advertise-address=${attr.unique.network.ip-address}:${NOMAD_PORT_cluster}",
          "--cluster.node-name=${attr.unique.hostname}",
          "/local/config.alloy"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro"
         ]
      }

      resources {
        cpu    = 500
        memory = 256

      }

      service {
        name = "grafana-alloy"
        port = "http"

        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy.rule=Host(`grafana-alloy.obs.int.jeddi.org`)",
          "traefik.http.routers.grafana-alloy.tls=false",
          "traefik.http.routers.grafana-alloy.entrypoints=http",
        ]        

      }
      service {
        name = "grafana-alloy-collector-otlp"
        port = "otlp"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-otlp.rule=Host(`grafana-alloy-otlp.obs.int.jeddi.org`)",
          "traefik.http.routers.grafana-alloy-otlp.tls=false",
          "traefik.http.routers.grafana-alloy-otlp.entrypoints=http",
        ]        
      }      
      template {
        data        = file("assets/grafana-alloy.config")
        destination = "local/config.alloy"
      }
    }
  }
}
