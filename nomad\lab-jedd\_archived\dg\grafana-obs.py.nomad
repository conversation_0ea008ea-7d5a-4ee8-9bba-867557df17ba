
// Grafana for jedd-lab - with bundled postgresql

// Part of the 2023-06 migration of Grafana from Classic to ObsCol 


# @TODO sort out https certs problems - might need copy of certs locally
# @TODO sso - probably just via GUI once it's stable (stored in postgresql)
# @TODO postgresql task - the grafana task should wait for this to be running, otherwise we have at least one failed grafana allocation at each job-start as it gets ahead of postgresql readiness
# @TODO sanitise residual configuration (environment) variables below - put as many into the assets/grafana-obs.ini file as possible
# @TODO work out whether WARN about /usr/share/grafana/plugins-bundled should be / can be suppressed or resolved - it's related to one of the PATHs we can define, but probably should NOT be redefined from default (internal to container) as the variable defines bin/grafana and other key paths - but the docker container does NOT ship with an empty plugins-bundled directory for some reason. Perhaps a pre-start mkdir would suppress safely

# Requirements = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# CONSUL variables must exist:
#     grafana-obs/POSTGRES_DB
#     grafana-obs/POSTGRES_USER
#     grafana-obs/POSTGRES_PASSWORD
# @TODO Once Vault exists, get these into that.


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_grafana    =  "grafana/grafana:9.5.6"
  image_grafana     =  "grafana/grafana-enterprise:9.5.6"
  image_postgresql  =  "postgres:12"

  #POSTGRES_DB       =  {{key "grafana-obs/POSTGRES_DB" }}
  #POSTGRES_USER     =  "{{key \"grafana-obs/POSTGRES_USER\" }}"
  #POSTGRES_PASSWORD =  "{{key \"grafana-obs/POSTGRES_PASSWORD\" }}"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-obs" {
  datacenters = ["PY"]

  # Constraint all tasks in this job to HACluster
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "*-hac-0[123]"
    # value = "dg-hac-0[3]"
  }

  type = "service"

  group "grafana-obs" {

    restart {
      interval = "10m"
      attempts = 30
      delay    = "10s"
    }

    network {
      port "port_grafana" {
        to = 3000
      }

      port "port_postgresql"  {
        to = 5432
      }
    }


    # task postgresql  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      # user = "postgres:postgres"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = "${var.image_postgresql}"

        # userns does not work - but privileged does - to solve the 'cannot change permission on files' error
        # userns_mode = "host"
        # privileged = true

        ports = ["port_postgresql"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          # "local/var-run:/var/run",
          # "/persistent/postgresql:/var/lib/postgresql",
          # "/etc/passwd:/etc/passwd",
        ]

      }

      env = {
        #"POSTGRES_DB"      = "{{ key \"grafana-obs/POSTGRES_DB\" }}",
        #"POSTGRES_USER"    = "{{ key \"grafana-obs/POSTGRES_USER\" }}",
        #"POSTGRES_PASSWORD"= "{{ key \"grafana-obs/POSTGRES_PASSWORD\" }}",
        ## "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
        #"PGPORT"            = "5432",
        #"PGDATA"            = "/persistent/postgresql/data",

        "POSTGRES_DB"       = "grafana",
        "POSTGRES_USER"     = "grafana",
        "POSTGRES_PASSWORD" = "password",
        # "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
        "PGPORT"            = 5432
        "PGDATA"            = "/persistent/postgresql/data"

        # This SHOULD do something functionally equivalent to:
        #   echo "host all all all $POSTGRES_HOST_AUTH_METHOD" >> pg_hba.conf
        # Which SHOULD resolve our 'no pg_hba.conf entry for ************' error - but does not
        "POSTGRES_HOST_AUTH_METHOD" = "md5"

        # We should be able to brute force this with 'trust' but that doesn't work either
        # "POSTGRES_HOST_AUTH_METHOD" = "trust"
      }

      resources {
        cpu    = 500
        memory = 1024
      }

      service {
        name = "postgresql"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-postgresql.entrypoints=http",
          "traefik.http.routers.grafana-postgresql.rule=Host(`grafana-postgresql.obs.int.jeddi.org`)",
        ]      

        port = "port_postgresql"

##        check {
##          name     = "PostgreSQL for Grafana healthcheck"
##          port     = "port_postgresql"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }

      }
    }    #  end-task  postgresql


    # task grafana  = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "grafana" {
      driver = "docker"

      kill_signal = "SIGTERM"      

      # This sidecar approach should force Grafana to 'post start' after PostgreSQL - though
      # in practice Grafana should start fine without PostgreSQL answering immediately, and
      # just retry until the DB is ready.
      #lifecycle {
      #  hook = "poststart"
      #  sidecar = true
      #}

      # user = "root:root"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = var.image_grafana

        # privileged = true

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        # dns_servers = ["192.168.27.123"]

        ports = ["port_grafana"]

        volumes = [
          # "/persistent/grafana/etc/grafana:/etc/grafana",
          # "/persistent/grafana/plugins:/var/lib/grafana/plugins/",
          "/opt/sharednfs/grafana-OBS/var-lib-grafana:/var/lib/grafana",
          "local/grafana.ini:/etc/grafana/grafana.ini",
          "local/license.jwt:/var/lib/grafana/license.jwt",
        ]

      }

      env {
        # Expose Grafana on this port - as with other ObsObs jobs we're sticking with standard ports for each job.
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_port_grafana}"

        # This points to /grafana is our persistent mount point - this directory defaults to /var/lib/grafana, 
        # and would contain several sub-directories: alerting/, csv/, plugins/, and png/
        # as well as the default location for the sqlite database file:   grafana.db
        # Note - plugins/ does NOT get created here at instantiation - but is at /var/lib/grafana/plugins internally - needs more experimentation

        # 2023-10-23 disabling to force vol mount to /var/lib/grafana
        #GF_PATHS_DATA         = "/persistent/grafana/DATA"

        # Usually points to /usr/share/grafana - contains plugins-bundled and other distro / shipped files - we may want more control over this.
        # DISABLING - as this is about 300MB on grafana.mtm.test and contains lots of things we actually don't want to replicate .. probably
        # GF_PATHS_HOME         = "/persistent/grafana/usr-share"

        # This defaults to /var/log/grafana - we may have better ways of extracting logs via nomad (docker/loki) but
        # for troubleshooting it should be convenient. Frustratingly Grafana employees like to pluralise everythings.
        GF_PATHS_LOGS         = "/grafana/log"

        # GF_LOG_LEVEL “debug”, “info”, “warn”, “error”, and “critical”. Default is info
        # GF_LOG_LEVEL = "warn"

        # We can send logs to console (captured by loki above), or file (dumped to GF_PATHS_LOGS above), or both with "console file"
        GF_LOG_MODE           = "console"

        # We probably want this set so we can call in dashboards from grafana.com
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTP_PROXY  = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY    = "10.0.0.0/8,172.0.0.0/8,192.168/16.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.consul"

        # Pass the plugins you want installed to Docker with the GF_INSTALL_PLUGINS environment variable as a 
        # comma-separated list. This sends each plugin name to grafana-cli plugins install ${plugin} and installs 
        # them when Grafana starts.
        # GF_INSTALL_PLUGINS = ""

        # We can load this up via the template (below) to autogenerate two datasources, though we prefer to keep
        # those in the SQLite database in GF_PATHS_DATA (/opt/grafana in host) so we can modify them, and more
        # conveniently add new items.  Commented out (also the template below) and create from within the UI.
        # GF_PATHS_PROVISIONING = "/local/grafana/provisioning"

        # I can't even find a reference to this on the googles in 2022-11
        # GF_LIVE_ALLOWED_ORIGINS = "http://*"

        # This is another poorly documented feature, and I'm doubtful that we need it.
        # GF_FEATURE_TOGGLES_ENABLE = "ngalert"

        # original grafana-test.mtm credentials
        #GF_DATABASE_NAME = "grafana_db_admin"
        #GF_DATABASE_USER = "grafanadb_t"
        #GF_DATABASE_PASSWORD = "aHgz8zW3UCszCgu2"

        # Database configuration - should probably not be in grafana.ini as this way we can utilise
        # the same credentials and secrets from Consul that are then used by the postgresql-backup task below.
        GF_DATABASE_TYPE = "postgres"

        GF_DATABASE_HOST = "${NOMAD_ADDR_port_postgresql}"
        # GF_DATABASE_NAME = {{ key "grafana-obs/POSTGRES_DB" }}
        # GF_DATABASE_USER = {{ key "grafana-obs/POSTGRES_USER" }}
        # GF_DATABASE_PASSWORD = {{ key "grafana-obs/POSTGRES_PASSWORD" }}

        GF_DATABASE_NAME = "grafana"
        GF_DATABASE_USER = "grafana"
        GF_DATABASE_PASSWORD = "password"

        #GF_DATABASE_NAME = "{{ key \"grafana-obs/POSTGRES_DB\" }}"
        # GF_DATABASE_NAME = "{{ key "grafana-obs/POSTGRES_DB" }}"

        #GF_DATABASE_USER = "{{ key \"grafana-obs/POSTGRES_USER\" }}"
        # GF_DATABASE_USER =  {{ key "grafana-obs/POSTGRES_USER" }}
        
        #GF_DATABASE_PASSWORD = "{{ key \"grafana-obs/POSTGRES_PASSWORD\" }}"
        # GF_DATABASE_PASSWORD =  {{ key "grafana-obs/POSTGRES_PASSWORD" }}

        # With Postgresql can be: "require" (default), "verify-full", "verify-ca", and "disable"
        GF_DATABASE_SSL_MODE = "disable"

        # GF_DATABASE_LOG_QUERIES = "true"

        # GF_SERVER_ROOT_URL = "http://grafana.obs.int.jeddi.org"

      }

      service {
        name = "grafana"
        port = "port_grafana"

#        check {
#          type = "http"
#          port = "port_grafana"
#          path = "/api/health"
#          interval = "5s"
#          timeout = "2s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.entrypoints=http",
          "traefik.http.routers.grafana.tls=false",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.int.jeddi.org`)",
        ]      
      }

      resources {
        cpu    = 200
        memory = 1000
      }

#### Disabling this entirely as we will create the data sources and persist them in the /grafana
#### storage mapped above onto host's /opt/grafana structure.  

# @TODO explore failure states when provisioning extant datasource, and/or provision fails (remote
# end not available) for single source, and/or editing of an existing datasource originally auto-
# provisioned, etc.  Persistence is where we are coming from, and may better suit operational and
# upgrade processes.

      template {
        # We have a modified version of the original grafana.mtm.test (tu0992tagf0001)
        # grafana.ini that we're starting with.  
        data = file("./assets/grafana-obs.ini")
        destination = "local/grafana.ini"
      }

      template {
        # Licence license file is keyed to fqdn hostname and uses american spelling of the noun.
        # Because DATA is pointing to persistent/grafana/DATA - we can keep license key on sharednfs, probably
        data = file("./assets/grafana-obs.license.jwt")
        destination = "local/license.jwt"
      }

    } // end-task grafana


    # task postgresql-backup = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql-backup" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      user = "postgres:postgres"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = "${var.image_postgresql}"

        command = "/backup-looper.sh"

        volumes = [
          "local/backup-looper.sh:/backup-looper.sh",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=test"
          }
        }

      }

      env = {
        # It's just easier if we have local timezone inside the container.
        "TZ" = "Australia/Sydney",
      }

      resources {
        cpu    = 512
        memory = 1024
      }

      service {
        name = "postgresql-backup"
      }

      #  FILE:   backup-looper.sh
      #  This is our over-ridden entry point - we're just here for pg_dump but use the same 
      #  postgresql *server* image as we've already got it in cache on this host AND we get
      #  guaranteed client / server version alignment for free.
      template {
        data = <<EOH
#! /usr/bin/env bash

# Heavily opinionated backup script for small PostgreSQL database
#
# Sleep regularly, wake up to detect if we're in one of the right windows for the
# day (typically once a day, but can be adjusted below).  If so, perform a db dump
# then return to sleep.

TARGETDIR=/persistent/BACKUPS

if [ !  -d ${TARGETDIR} ]
then
  mkdir -p ${TARGETDIR}
fi

# Feeding a password to pg_dump is easier if we just use the ~/.pgpass convention
# in format:  hostname:port:database:username:password
echo {{ env "NOMAD_ADDR_port_postgresql" }}:{{ key "grafana-obs/POSTGRES_DB" }}:{{ key "grafana-obs/POSTGRES_USER" }}:{{ key "grafana-obs/POSTGRES_PASSWORD" }} > ~/.pgpass

# Must be set to limited rights or else it ignores the file.
chmod 600 ~/.pgpass

while [ 1 ]
do
  # Sleep first, as the database is typically not ready on instantiation anyway
  sleep 1h

  HOUR=`date "+%H"`
  TARGETFILE=grafana-obs-postgresql-dump-`date "+%a-%H"`H.sql

  # Multi-value alternative:
  # if [ ${HOUR} -eq 08 ] || [ ${HOUR} -eq 16 ] || [ ${HOUR} -eq 23 ] 

  # Daily option:
  if [ ${HOUR} -eq 23 ]
  then
    # First - remove the 1-week old archive
    rm ${TARGETDIR}/${TARGETFILE}.gz

    # pg_dump requires the following params despite them being in pgpass - pgpass is a pattern
    # matching file only, and password is retrieved when user/db/addr matches.
    pg_dump -f ${TARGETDIR}/${TARGETFILE}                    \
            -Fc                                              \
            -d {{ key "grafana-obs/POSTGRES_DB" }}           \
            -U {{ key "grafana-obs/POSTGRES_USER" }}         \
            -h {{ env "NOMAD_HOST_IP_port_postgresql" }}     \
            -p {{ env "NOMAD_HOST_PORT_port_postgresql" }}       

    # The -Fc format is recommended - ostensibly it is compressed but in practice not optimally,
    # so we compress properly with gzip as the final step.
    gzip --best ${TARGETDIR}/${TARGETFILE}
  fi
done

EOH
        destination = "local/backup-looper.sh"
        perms = "755"
      }
    }    #  end-task "postgresql-backup"

  }
}
