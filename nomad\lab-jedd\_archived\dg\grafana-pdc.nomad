
# Grafana PDC Private Data Connect agent - jedd-lab

# 2024-07-16 - original moved to jgit:nomad repository

# Commandline provided by grafana.net to configure to my server is:

# Review processes within the container:
#
# proc 1 - pdc agent proper:
#   /usr/bin/pdc 
#     -token ********************************************************************************************************************************
#     -cluster prod-au-southeast-0 
#     -gcloud-hosted-grafana-id 461795

# proc 15 - ssh tunnel:
#   ssh 
#     -i /home/<USER>/.ssh/grafana_pdc
#     <EMAIL>
#     -p 22
#     -R 0
#     -o CertificateFile=/home/<USER>/.ssh/grafana_pdc-cert.pub
#     -o ConnectTimeout=1
#     -o ServerAliveInterval=15
#     -o UserKnownHostsFile=/home/<USER>/.ssh/grafana_pdc_known_hosts 
#     -vv

### pdc agent takes these parameters (0.0.20)
#
#    Usage of pdc:
#      -cluster string
#            the PDC cluster to connect to use
#      -domain string
#            the domain of the PDC cluster (default "grafana.net")
#      -gcloud-hosted-grafana-id string
#            The ID of the Hosted Grafana instance to connect to
#      -h    Print help
#      -log-level int
#            The level of log verbosity. The maximum is 3. (default 2)
#      -log.level string
#            "debug", "info", "warn" or "error" (default "info")
#      -network string
#            DEPRECATED: The name of the PDC network to connect to
#      -ssh-flag value
#            Additional flags to be passed to ssh. Can be set more than once.
#      -ssh-key-file string
#            The path to the SSH key file. (default "/home/<USER>/.ssh/grafana_pdc")
#      -token string
#            The token to use to authenticate with Grafana Cloud. It must have the pdc-signing:write scope
#    
#    If pdc-agent is run with SSH flags, it will pass all arguments directly through to the "ssh" binary. This is deprecated behaviour.





# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_grafana_pdc = "grafana/pdc-agent:0.0.20"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-pdc" {
  type        = "service"

  datacenters = ["DG"]

  group "grafana-pdc" {
    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    #ephemeral_disk {
    #  migrate = true
    #  size    = 300
    #  sticky  = true
    #}

#    network {
      #port "port_grafana_pdc" {
      #  to = 12345
      #  }
      #}

    task "grafana-pdc" {
      driver = "docker"

      config {
        image = "${var.image_grafana_pdc}"

      
        # command = "/bin/sleep 15m"

        args = [ 
          "-token", "********************************************************************************************************************************",
          "-cluster", "prod-au-southeast-0",
          "-gcloud-hosted-grafana-id", "461795",
          "-ssh-flag", "ProxyCommand nc --proxy proxy.det.nsw.edu.au:80 %h %p",
        ]

        volumes = [ 
          # "local/ssh-config:/home/<USER>/.ssh/config"
          # "local/ssh-config:/home/<USER>/ssh-config-file-replication-test"
        ]

        # ports = ["port_grafana_pdc"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      env = {
      }

      resources {
        cpu    = 128
        memory = 512
      }


      template {
        data = <<EOH
# Host bitbucket.org
#   Hostname altssh.bitbucket.org
#   Port 443
#   ProxyCommand nc --proxy proxy.det.nsw.edu.au:80 %h %p

Host       private-datasource-connect-prod-au-southeast-0.grafana.net
  Hostname private-datasource-connect-prod-au-southeast-0.grafana.net
  User     461795
  Port     443
  ProxyCommand nc --proxy proxy.det.nsw.edu.au:80 %h %p

EOH
        destination = "local/ssh-config"
        # perms = "755"
        uid = 30000
        gid = 30000
      }

      service {
        name = "grafana-pdc"

        # port = "port_grafana_pdc"

#        check {
#          type     = "tcp"
#          interval = "120s"
#          timeout  = "10s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-pdc.entrypoints=http",
          "traefik.http.routers.grafana-pdc.tls=false",
          "traefik.http.routers.grafana-pdc.rule=Host(`grafana-pdc.obs.int.jeddi.org`)",
        ]
      }

    } // end-task grafana-pdc

  } // end-group grafana-pdc

} // end-job grafana-pdc



