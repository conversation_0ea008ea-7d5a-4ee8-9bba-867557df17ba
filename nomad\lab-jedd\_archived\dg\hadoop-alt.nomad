
// Jedd lab - hadoop alt

job "hadoop"  {
  datacenters = ["DG"]
  type = "service"

  group "hadoop-basic" {
    network {
      port "port_hadoop" {
        static = 9000
      }
    }

#    volume "vol_hadoop"  {
#      type = "host"
#      source = "vol_python39"
#      read_only = false
#    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    task "hadoop-basic" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_python39"
#        destination = "/mnt/python39"
#        read_only = false
#      }

      config {
        image = "apache/hadoop:3"

        ports = ["port_hadoop"]

        # command = "/local/prestart.sh"

        volumes = [ ]

        network_mode = "host"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      resources {
        cpu = 1024
        memory = 4400
      }

      service {
        name = "hadoop"
        port = "port_hadoop"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.hadoop.rule=Host(`hadoop.int.jeddi.org`)",
          "traefik.http.routers.hadoop.tls=false",
        ]

      }

    }
  }
}
