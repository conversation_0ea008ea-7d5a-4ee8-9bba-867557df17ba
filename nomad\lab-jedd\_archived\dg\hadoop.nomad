
# Hadoop for <PERSON><PERSON>'s lab (DG)


job "hadoop" {
  type        = "service"

  datacenters = ["DG"]

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
  }

  group "hadoop" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    ephemeral_disk {
      migrate = true
      size    = 300
      sticky  = true
    }

    network {
      port "port_hadoop" {
        to = 9000
        }
      }

    task "hadoop" {
      driver = "docker"

      config {
        image = "apache/hadoop:3"

        ports = ["port_hadoop"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }


      env = {
        "PORT" = "9000"
      }

      resources {
        cpu    = 500
        memory = 2048
        memory_max = 3000

      }

      service {
        name = "hadoop"
        port = "port_hadoop"

        check {
          type     = "tcp"
          interval = "120s"
          timeout  = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.hadoop.entrypoints=http",
          "traefik.http.routers.hadoop.rule=Host(`hadoop.obs.int.jeddi.org`)",
        ]      

      }
    }
  }
}

