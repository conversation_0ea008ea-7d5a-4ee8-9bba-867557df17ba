job "harbor" {
  meta {
    DATA_VOLUME="/harbor/nomad/data"
    CONFIG_VOLUME="/harbor/nomad/common/config"
  }
  type = "service"
  region = "zhejiang"
  datacenters = ["dc-01"]
  constraint {
        attribute = "${attr.kernel.name}"
        value     = "linux"
      }
  constraint {
    attribute = "${attr.unique.hostname}"
    value     = "harbor1"
  } 
  update {
    max_parallel = 1
    min_healthy_time = "10s"
    healthy_deadline = "3m"
    auto_revert = false
    canary = 0
  }
  group "harbor" {
    # task1: log
    task "log" {
      driver = "docker"
      config {
        image = "goharbor/harbor-log:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "DAC_OVERRIDE",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_CONFIG_VOLUME}/log/:/etc/logrotate.d/",
          "/var/log/harbor/:/var/log/docker"
        ]
        port_map {
          log_port = 10514
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "log_port" {
            static = 1514
          }
        }
      }
      service {
        name = "harbor-log"
        tags = ["urlprefix-/log"]
        port = "log_port"
      }
    }
    
    # task2: redis
    task "redis" {
      driver = "docker"
      config {
        image = "goharbor/redis-photon:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/redis:/var/lib/redis"
        ]
        port_map {
          redis_port = 6379
        } 
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "redis"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "redis_port" {
            static = 6379
          }
        }
      }
      service {
        name = "harbor-redis"
        tags = ["urlprefix-/redis"]
        port = "redis_port"
      }
    }

    # task3: registry 
    task "registry" {
      driver = "docker"
      config {
        image = "goharbor/registry-photon:v2.7.1-patch-2819-v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/registry:/storage:z",
          "${NOMAD_META_CONFIG_VOLUME}/registry/:/etc/registry/:z",
          "${NOMAD_META_DATA_VOLUME}/secret/registry/root.crt:/etc/registry/root.crt"
        ]
        port_map {
          registry_port = 5000 
        } 
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "registry"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "registry_port" {
            static = 5000 
          }
        }
      }
      service {
        name = "harbor-registry"
        tags = ["urlprefix-/registry"]
        port = "registry_port"

      }
    }

    # task4: registryctl 
    task "registryctl" {
      env {
        CORE_SECRET="DhFBnijfb7yme36s"
        JOBSERVICE_SECRET="VXSUB9VLbv00iDHI"
      }
      driver = "docker"
      config {
        image = "goharbor/harbor-registryctl:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/registry:/storage:z",
          "${NOMAD_META_CONFIG_VOLUME}/registry/:/etc/registry/:z",
          "${NOMAD_META_CONFIG_VOLUME}/registryctl/config.yml:/etc/registryctl/config.yml"
        ]
        port_map {
          registryctl_port = 8080 
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "registryctl"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "registryctl_port" {
            static = 8080
          }
        }
      }
      service {
        name = "harbor-registryctl"
        tags = ["urlprefix-/registryctl"]
        port = "registryctl_port"
      }
    }
    # task5: postgresql 
    task "postgresql" {
      env {
        POSTGRES_PASSWORD="root123"
      }
      driver = "docker"
      config {
        image = "goharbor/harbor-db:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "DAC_OVERRIDE",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/database:/var/lib/postgresql/data:z",
        ]
        port_map {
          db_port = 5432 
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "postgresql"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "db_port" {
            static = 5432 
          }
        }
      }
      service {
        name = "harbor-postgresql"
        tags = ["urlprefix-/postgresql"]
        port = "db_port"

      }
    }
    # task6: core 
    task "core" {
      meta {
          CORE_HOST_PORT=18080
          JOBSERVICE_HOST_PORT=28080
      }
      env {
        CONFIG_PATH="/etc/core/app.conf"
        UAA_CA_ROOT="/etc/core/certificates/uaa_ca.pem"
        _REDIS_URL="${attr.unique.network.ip-address}:6379,100,"
        SYNC_REGISTRY=false
        CHART_CACHE_DRIVER="redis"
        _REDIS_URL_REG="redis://${attr.unique.network.ip-address}:6379/1"

        PORT=8080
        LOG_LEVEL="info"
        EXT_ENDPOINT="https://${attr.unique.network.ip-address}"
        DATABASE_TYPE="postgresql"
        POSTGRESQL_HOST="${attr.unique.network.ip-address}"
        POSTGRESQL_PORT=5432
        POSTGRESQL_USERNAME="postgres"
        POSTGRESQL_PASSWORD="root123"
        POSTGRESQL_DATABASE="registry"
        POSTGRESQL_SSLMODE="disable"
        REGISTRY_URL="http://${attr.unique.network.ip-address}:5000"
        TOKEN_SERVICE_URL="http://${attr.unique.network.ip-address}:${NOMAD_META_CORE_HOST_PORT}/service/token"
        HARBOR_ADMIN_PASSWORD="Harbor12345"
        MAX_JOB_WORKERS=10
        CORE_SECRET="DhFBnijfb7yme36s"
        JOBSERVICE_SECRET="VXSUB9VLbv00iDHI"
        ADMIRAL_URL=""
        WITH_NOTARY=true
        WITH_CLAIR=true
        CLAIR_DB_PASSWORD="root123"
        CLAIR_DB_HOST="${attr.unique.network.ip-address}"
        CLAIR_DB_PORT=5432
        CLAIR_DB_USERNAME="postgres"
        CLAIR_DB="postgres"
        CLAIR_DB_SSLMODE="disable"
        CORE_URL="http://${attr.unique.network.ip-address}:${NOMAD_META_CORE_HOST_PORT}"
        JOBSERVICE_URL="http://${attr.unique.network.ip-address}:${NOMAD_META_JOBSERVICE_HOST_PORT}"
        CLAIR_URL="http://${attr.unique.network.ip-address}:6060"
        NOTARY_URL="http://${attr.unique.network.ip-address}:4443"
        REGISTRY_STORAGE_PROVIDER_NAME="filesystem"
        READ_ONLY=false
        RELOAD_KEY=""
        CHART_REPOSITORY_URL="http://${attr.unique.network.ip-address}:9999"
        REGISTRY_CONTROLLER_URL="http://${attr.unique.network.ip-address}:8080"
        WITH_CHARTMUSEUM=false
      }
      driver = "docker"
      config {
        image = "goharbor/harbor-core:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/ca_download/:/etc/core/ca/:z",
          "${NOMAD_META_DATA_VOLUME}/ca_download/ca.crt:/etc/ssl/certs/ca.crt:z",
          "${NOMAD_META_DATA_VOLUME}/psc/:/etc/core/token/:z",
          "${NOMAD_META_DATA_VOLUME}/:/data/:z",
          "${NOMAD_META_CONFIG_VOLUME}/core/certificates/:/etc/core/certificates/:z",
          "${NOMAD_META_CONFIG_VOLUME}/core/app.conf:/etc/core/app.conf",
          "${NOMAD_META_DATA_VOLUME}/secret/core/private_key.pem:/etc/core/private_key.pem",
          "${NOMAD_META_DATA_VOLUME}/secret/keys/secretkey:/etc/core/key"
        ]
        port_map {
          core_port = 8080 
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "core"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "core_port" {
            static = "18080"
          }
        }
      }
      service {
        name = "harbor-core"
        tags = ["urlprefix-/core"]
        port = "core_port"
      }
    }
    # task7: jobservice 
    task "jobservice" {
      env {
        CORE_SECRET="DhFBnijfb7yme36s"
        JOBSERVICE_SECRET="VXSUB9VLbv00iDHI"
        CORE_URL="http://${attr.unique.network.ip-address}:18080"
      }
      driver = "docker"
      config {
        image = "goharbor/harbor-jobservice:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID"
        ]
        volumes = [
          "${NOMAD_META_DATA_VOLUME}/ca_download/ca.crt:/etc/ssl/certs/ca.crt:z",
          "${NOMAD_META_DATA_VOLUME}/job_logs:/var/log/jobs:z",
          "${NOMAD_META_CONFIG_VOLUME}/jobservice/config.yml:/etc/jobservice/config.yml"
        ]
        port_map {
          jobservice_port = 8080 
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "jobservice"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "jobservice_port" {
            static = "28080"
          }
        }
      }
      service {
        name = "harbor-jobservice"
        tags = ["urlprefix-/jobservice"]
        port = "jobservice_port"
      }
    }
    # task8: portal 
    task "portal" {
      driver = "docker"
      config {
        image = "goharbor/harbor-portal:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "NET_BIND_SERVICE",
          "SETUID"
        ]
        port_map {
          portal_port = 80 
        }
        dns_servers = ["${attr.unique.network.ip-address}"]
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "portal"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "portal_port" {
            static = "10080"
          }
        }
      }
      service {
        name = "harbor-portal"
        tags = ["urlprefix-/portal"]
        port = "portal_port"
      }
    }
    # task9: proxy
    task "proxy" {
      driver = "docker"
      config {
        image = "goharbor/nginx-photon:v1.8.1"
        cap_drop = [
          "ALL",
        ]
        cap_add = [
          "CHOWN",
          "SETGID",
          "SETUID",
          "NET_BIND_SERVICE"
        ]
        volumes = [
          "${NOMAD_META_CONFIG_VOLUME}/nginx:/etc/nginx",
          "${NOMAD_META_DATA_VOLUME}/cert/harbor.service.consul.key:/etc/cert/server.key",
          "${NOMAD_META_DATA_VOLUME}/cert/harbor.service.consul.crt:/etc/cert/server.crt"
        ]
        dns_servers = ["${attr.unique.network.ip-address}"]
        port_map {
          http = 80
          https = 443
        }
        logging {
          type = "syslog"
          config {
            syslog-address = "tcp://${attr.unique.network.ip-address}:1514"
            tag = "proxy"
          }
        }
      }
      resources {
        cpu    = 100 # 100 MHz
        memory = 128 # 128 MB
        network {
          mbits = 10
          port "http" {
              static = 80
          }
          port "https" {
              static = 443
          }
        }
      }
      service {
        name = "harbor"
        tags = [ "nginx", "web", "urlprefix-/nginx" ]
        port = "http"
        check {
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }    
    restart {
      attempts = 3
      delay    = "20s"
    }
  }
}
