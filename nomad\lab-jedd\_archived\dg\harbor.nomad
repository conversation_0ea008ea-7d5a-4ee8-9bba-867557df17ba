
// jedd lab (DG) - harbor hcl 

// Refer full list of parameters per module:
//    https://github.com/bitnami/charts/tree/main/bitnami/harbor

variables {
  # use image = "${var.image}"
  image_registry      = "bitnami/harbor-registry:2"
  image_registry_ctl  = "bitnami/harbor-registryctl:2"
  image_postgresql    = "docker.io/bitnami/postgresql:13"
  image_harbor_core   = "docker.io/bitnami/harbor-core:2"
  image_harbor_portal =  "docker.io/bitnami/harbor-portal:2"
  image_harbor_jobservice =  "docker.io/bitnami/harbor-jobservice:2"
  image_harbor_redis  = "docker.io/bitnami/redis:7"
  image_harbor_nginx  = "docker.io/bitnami/nginx:1.23"
  image_harbor_chartmuseum = "docker.io/bitnami/chartmuseum:0"

}


job "harbor" {
  type = "service"
  datacenters = ["DG"]

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    # value = "dg-hac-0[123]"
    value = "dg-hac-0[3]"
  }

  group "harbor" {


    # For long-term-storage (LTS) of registry artifacts
#    volume "vol_harbor"  {
#      type = "host"
#      source = "vol_harbor"
#      read_only = false
#      }


    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }


    network {
      port "port_harbor_registry" {
        static = 5000
      }

      port "port_harbor_registry_metrics" {
        static = 5001
      }

      port "port_harbor_registryctl" {
        static = 5002
      }

      port "port_harbor_portal" {
        static = 5003
      }

      port "port_harbor_postgresql" {
        to = 5432
      }

      port "port_harbor_nginx" {
      }

      port "port_harbor_redis" {
        static = 6379
      }


      port "port_harbor_jobservice" {
      }

  	}


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "harbor-registry" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_harbor"
#        destination = "/harbor"
#        read_only = false
#      }


      config {

        image = "${var.image_registry}"

#        args = [
#          "--storage.tsdb.retention.time=1y" ,
#          "--enable-feature=examplar-storage"
#        ]

        dns_servers = ["*************"]

        # env = {
        #   "REGISTRY_HTTP_SECRET=xyz"
        # }

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/config.yaml:/etc/registry/config.yml",
          "local/htpasswd:/etc/registry/htpasswd",
#           "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      service {
        name = "harbor-http"
        port = "port_harbor_registry"

       check {
         type = "http"
         port = "port_harbor_registry"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }



      template {
        data = <<EOH

version: 0.1
log:
  fields:
    service: registry
    environment: dev

storage:
  cache:
    blobdescriptor: inmemory
  filesystem:
    rootdirectory: /var/lib/registry

http:
  addr: :5000
  headers:
    X-Content-Type-Options: [nosniff]
  debug:
    addr: :5001
    prometheus:
      enabled: true
      path: /metrics

auth:
  none
#  htpasswd:
#    realm: basic-realm
#    path: /etc/registry/htpasswd

health:
  storagedriver:
    enabled: true
    interval: 10s
    threshold: 3

EOH
        destination = "local/config.yaml"
      }

      template {
        data = <<EOH

jedd:$apr1$uZZ0kKCb$VeQBiLwQHFjc7mr3Z6/q1.
admin:$apr1$MpEuq1T.$zriFBV5G6F.Me83gVDHNf1

EOH
        destination = "local/htpasswd"
      }


    }
    

    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "harbor-registryctl" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_harbor"
#        destination = "/harbor"
#        read_only = false
#      }


      config {

        image = "${var.image_registry_ctl}"

#        args = [
#          "--storage.tsdb.retention.time=1y" ,
#          "--enable-feature=examplar-storage"
#        ]

        dns_servers = ["*************"]

        # env = "REGISTRY_HTTP_SECRET=xyz"
        #env = {
        #  "CORE_SECRET"="CHANGEME"
        #  "JOBSERVICE_SECRET"="CHANGEME"
        #  "REGISTRY_HTTP_SECRET"="CHANGEME"
        #}


        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/config.yaml:/etc/registryctl/config.yml",
          # "local/htpasswd:/etc/registry/htpasswd",
#           "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      service {
        name = "harbor-registryctl"
        port = "port_harbor_registryctl"

       check {
         type = "http"
         port = "port_harbor_registryctl"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }


      template {
        data = <<EOH

registry.controller.service.ports.http: 80
protocol: tcp

EOH
        destination = "local/config.yaml"
      }


    }
    



    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-postgresql" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_harbor"
#        destination = "/harbor"
#        read_only = false
#      }


      config {

        image = "${var.image_postgresql}"

#        args = [
#          "--storage.tsdb.retention.time=1y" ,
#          "--enable-feature=examplar-storage"
#        ]

        dns_servers = ["*************"]



        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          # "local/config.yaml:/etc/registryctl/config.yml",
          # "local/htpasswd:/etc/registry/htpasswd",
#           "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      env = {
         "POSTGRESQL_PASSWORD" = "bitnami"
         "POSTGRESQL_DATABASE" = "registry"
      }

      service {
        name = "harbor-postgresql"
        port = "port_harbor_postgresql"

       check {
         type = "http"
         port = "port_harbor_postgresql"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

      template {
        data = <<EOH

EOH
        destination = "local/config.yaml"
      }

    }
    


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-core" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_harbor"
#        destination = "/harbor"
#        read_only = false
#      }

      env = {
        "CORE_KEY" = "change-this-key"
        "_REDIS_URL_CORE" = "redis://redis:6379/0"
        "SYNC_REGISTRY" = "false"
        "CHART_CACHE_DRIVER" = "redis"
        "_REDIS_URL_REG" = "redis://redis:6379/1"
        "PORT" = "8080"
        "LOG_LEVEL" = "info"
        "EXT_ENDPOINT" = "http://reg.mydomain.com"
        "DATABASE_TYPE" = "postgresql"
        "REGISTRY_CONTROLLER_URL" = "http://registryctl:8080"
        "POSTGRESQL_HOST" = "postgresql"
        "POSTGRESQL_PORT" = "5432"
        "POSTGRESQL_DATABASE" = "registry"
        "POSTGRESQL_USERNAME" = "postgres"
        "POSTGRESQL_PASSWORD" = "bitnami"
        "POSTGRESQL_SSLMODE" = "disable"
        "REGISTRY_URL" = "http://registry:5000"
        "TOKEN_SERVICE_URL" = "http://core:8080/service/token"
        "HARBOR_ADMIN_PASSWORD" = "bitnami"
        "CORE_SECRET" = "CHANGEME"
        "JOBSERVICE_SECRET" = "CHANGEME"
        "ADMIRAL_URL" = ""
        "WITH_NOTARY" = "False"
        "WITH_CHARTMUSEUM" = "True"
        "CHART_REPOSITORY_URL" = "http://chartmuseum:8080"
        "CORE_URL" = "http://core:8080"
        "JOBSERVICE_URL" = "http://jobservice:8080"
        "REGISTRY_STORAGE_PROVIDER_NAME" = "filesystem"
        "REGISTRY_CREDENTIAL_USERNAME" = "harbor_registry_user"
        "REGISTRY_CREDENTIAL_PASSWORD" = "harbor_registry_password"
        "READ_ONLY" = "false"
        "RELOAD_KEY" = ""
      }


      config {

        image = "${var.image_harbor_core}"

#        args = [
#          "--storage.tsdb.retention.time=1y" ,
#          "--enable-feature=examplar-storage"
#        ]

        dns_servers = ["*************"]


        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/app.conf:/etc/core/app.conf",
        ]

        network_mode = "host"
      }

      service {
        name = "harbor-postgresql"
        port = "port_harbor_postgresql"

       check {
         type = "http"
         port = "port_harbor_postgresql"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

      template {
        data = <<EOH

CONFIG_PATH: /etc/core/app.conf
core.service.ports.http: 80
httpport: 80

EOH
        destination = "local/app.conf"
      }


      template {
        data = <<EOH

EOH
        destination = "local/config.yaml"
      }

    }
    





    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-portal" {
      driver = "docker"

      config {

        image = "${var.image_harbor_portal}"

        dns_servers = ["*************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        network_mode = "host"
      }

      service {
        name = "harbor-portal"
        port = "port_harbor_portal"

       check {
         type = "http"
         port = "port_harbor_portal"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

    }
    


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-jobservice" {
      driver = "docker"

      env = {
        "CORE_SECRET" = "CHANGEME"
        "JOBSERVICE_SECRET" = "CHANGEME"
        "CORE_URL" = "http://core:8080"
        "REGISTRY_CONTROLLER_URL" = "http://registryctl:8080"
        "REGISTRY_CREDENTIAL_USERNAME" = "harbor_registry_user"
        "REGISTRY_CREDENTIAL_PASSWORD" = "harbor_registry_password"
      }

      config {

        image = "${var.image_harbor_jobservice}"

        dns_servers = ["*************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/config.yaml:/etc/jobservice/config.yml",
        ]

        network_mode = "host"
      }

      service {
        name = "harbor-jobservice"
        port = "port_harbor_jobservice"

       check {
         type = "http"
         port = "port_harbor_jobservice"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

      template {
        data = <<EOH

protocol: tcp

EOH
        destination = "local/config.yaml"
      }



    }
    

    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-redis" {
      driver = "docker"

      config {

        image = "${var.image_harbor_redis}"

        dns_servers = ["*************"]


        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        network_mode = "host"
      }

      env = {
        "ALLOW_EMPTY_PASSWORD" = "yes"
      }

      service {
        name = "harbor-redis"
        port = "port_harbor_redis"

       check {
         type = "http"
         port = "port_harbor_redis"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

    }
    


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-nginx" {
      driver = "docker"

      config {

        image = "${var.image_harbor_nginx}"

        dns_servers = ["*************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        network_mode = "host"
      }

      env = {
        "ALLOW_EMPTY_PASSWORD" = "yes"
      }

      service {
        name = "harbor-redis"
        port = "port_harbor_redis"

       check {
         type = "http"
         port = "port_harbor_redis"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

    }
    


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

    task "harbor-chartmuseum" {
      driver = "docker"

      config {

        image = "${var.image_harbor_chartmuseum}"

        dns_servers = ["*************"]


        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        network_mode = "host"
      }

      env = {
        "CACHE" = "redis"
        "CACHE_REDIS_ADDR" = "redis:6379"
        "CACHE_REDIS_DB" = "1"
        "DEPTH" = "1"
        "PORT" = "8080"
        "STORAGE" = "local"
        "STORAGE_LOCAL_ROOTDIR" = "/bitnami/data"
        "ALLOW_OVERWRITE" = "true"
        "INDEX_LIMIT" = "0"
      }

      service {
        name = "harbor-redis"
        port = "port_harbor_redis"

       check {
         type = "http"
         port = "port_harbor_redis"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

    }
    





  }
}
