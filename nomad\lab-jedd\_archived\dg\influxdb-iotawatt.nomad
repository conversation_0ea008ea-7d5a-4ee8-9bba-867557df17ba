
// influxdb-iotawatt - data store for both iotawatt devices


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_influxdb = "influxdb:latest"
  image_influxdb = "dg-pan-01.int.jeddi.org:5000/influxdb:2.7.6"
  }


# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "influxdb-iotawatt" {

  datacenters = ["DG"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    # Can't run on dg-hac as the iotawatt's don't seem to pick up DHCP's DNS,
    # or at least they're not resolving the wildcard to obs for influx - so we'll
    # lock it to dg-pan host and a fixed high port.
    # operator = "regexp"
    # value = "dg-hac-0[123]"
    value = "dg-pan-01"
   }

  group "influxdb-iotawatt" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }


    network {
      port "port_influxdb"  {
        # Default with influx v2 is tcp/9999 - we'll pick a static high port
        # and lock to dg-pan-01 (see above) so the iotawatt's can find us.
        static = 29999
      }
    }

    task "influxdb" {

      driver = "docker"

      # Try to give Influx a moment to terminate sanely.
      kill_timeout = "60s"
      kill_signal = "SIGTERM"

      config {
        image = "${var.image_influxdb}"

        ports = ["port_influxdb"]

        volumes = [
          "/opt/sharednfs/influxdb-iotawatt:/var/lib/influxdb2"
        ]

        logging  {
            type = "loki"
            config {
              loki-url = "http://dg-pan-01.int.jeddi.org:3100/loki/api/v1/push"
              loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
            }
          }

        args = [
          # "--log-level", "debug"
          "--log-level", "warn"
        ]
      }

      env = {
        "DOCKER_INFLUXDB_INIT_MODE"       = "setup",
        "DOCKER_INFLUXDB_INIT_USERNAME"   = "admin",
        "DOCKER_INFLUXDB_INIT_PASSWORD"   = "bigsecret",
        "DOCKER_INFLUXDB_INIT_ORG"        = "jeddi",
        "DOCKER_INFLUXDB_INIT_BUCKET"     = "iotawatt",
        "DOCKER_INFLUXDB_INIT_RETENTION"  = "3650",

        # "INFLUXD_HTTP_BIND_ADDRESS"       = ":9999"
        "INFLUXD_HTTP_BIND_ADDRESS"     = ":${NOMAD_HOST_PORT_port_influxdb}"
      }

      template {
        data = file("assets/influxdb-iotawatt-config.yml")
        destination = "local/influxdb.yaml"
      }

      resources {
        cpu    = 512
        memory = 512
        memory_max = 1024
      }

      service {
        name = "influxdb-iotawatt"
        port = "port_influxdb"

#        check {
#          name     = "Influxdb-iotawatt healthcheck"
#          port     = "port_influxdb"
#          type     = "http"
#          path     = "/ready"
#          interval = "20s"
#          timeout  = "5s"
#          check_restart {
#            limit           = 3
#            grace           = "60s"
#            ignore_warnings = false
#          }
#        }

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.influxdb-iotawatt.entrypoints=http,https",
#          "traefik.http.routers.influxdb-iotawatt.rule=Host(`influxdb-iotawatt.obs.int.jeddi.org`)",
#          "traefik.http.routers.influxdb-iotawatt.tls=false",
#        ]

      }

    }
  }
}

