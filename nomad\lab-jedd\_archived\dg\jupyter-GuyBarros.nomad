job "jupyter" {
  datacenters = ["DG"]

  group "jupyter-notebook" {
    count = 1
   network {
          port  "http"  {
            to = 8888
          }
        }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # 2023-09-01 jedd - constrain to one vm - the image is about 1.2GB so it's a pain to move around
      # value = "dg-hac-0[123]"
      value = "dg-hac-0[2]"
    }

    task "scipy" {
       
      driver = "docker"
      config {
        image = "jupyter/scipy-notebook"
        ports = ["http"]
      }

      logs {
        max_files     = 5
        max_file_size = 15
      }
      resources {
        cpu = 1000
        memory = 1024
      }
      
    }
    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }
   service {
        name = "jupyter-scipy"
        tags = ["urlprefix-/jupyter-scipy strip=/jupyter-scipy"]
        port = "http"

        check {
          name     = "alive"
          port = "http"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
     }
  }
}
