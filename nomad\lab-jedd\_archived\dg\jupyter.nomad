
// Jedd lab - <PERSON><PERSON><PERSON> Docker Stack

// https://jupyter-docker-stacks.readthedocs.io/en/latest/index.html

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_jupyter = "jupyter/scipy-notebook:2023-07-25"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "jupyter"  {
  datacenters = ["DG"]
  type = "service"

  update {
    # default is 5m, but the 1.2gb takes a while to pull down and then unpack,
    # and health must be < progress.
    healthy_deadline = "10m"
    progress_deadline = "11m"
  }

  group "jupyter" {
    network {
      port "port_jupyter" {
        to = 8888
      }
    }

#    volume "vol_jupyter"  {
#      type = "host"
#      source = "vol_jupyter"
#      read_only = false
#    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # 2023-09-01 jedd - constrain to one vm - the image is about 1.2GB so it's a pain to move around
      # value = "dg-hac-0[123]"
      value = "dg-hac-0[2]"
    }

    task "jupyter" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_jupyter"
#        destination = "/mnt/jupyter"
#        read_only = false
#      }

      config {
        image = "${var.image_jupyter}"

        # dns_servers = ["************23", "************"]

        ports = ["port_jupyter"]

        # command = "/local/prestart.sh"
        # args  = [ "3000" ]

        volumes = [ 
#          "local/prestart.sh:/prestart.sh",
#          "local/main.py:/main.py",
#          "local/main.py:/main.py",
        ]

        network_mode = "host"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      resources {
        cpu = 500
        memory = 512
        memory_max = 1500
      }

      service {
        name = "jupyter"
        port = "port_jupyter"

        tags = [
          # "urlprefix-/jupyter-scipy strip=/jupyter-scipy",
         "traefik.enable=true",
         # "traefik.http.routers.jupyter.rule=Host(`jupyter.obs.int.jeddi.org`)",
         "traefik.http.routers.jupyter.rule=Host(`jupyter.obs.int.jeddi.org`) &&  PathPrefix(`/jupyter{regex:$$|/.*})",
         "traefik.http.routers.jupyter.tls=false",
         "traefik.http.routers.jupyter.entrypoints=http",

        ]

      }


    }
  }
}

