
# localstack - an aws-alike for some basic services

# refer:  https://github.com/localstack/localstack



job "localstack" {

  datacenters = ["DG"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "localstack" {

    network {
      port "port_localstack_edge" {
        static = 4566
      }
      port "port_localstack_elastic" {
        static = 4571
      }
      port "port_localstack_debugpy" {
        static = 5678
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    ephemeral_disk {
      size = 300
    }

    task "localstack" {
      driver = "docker"

      env = {
        "SERVICES" = "s3,lambda"
      }

      config {
        image = "localstack/localstack"
        ports = [ "port_localstack_edge", "port_localstack_elastic", "port_localstack_debugpy" ]
        }

      resources {
        cpu    = 500 # 500 MHz
        memory = 256 # 256MB
      }

      service {
        name = "service-localstack"
        port = "port_localstack_edge"
        check {
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }

    }

  }
}
