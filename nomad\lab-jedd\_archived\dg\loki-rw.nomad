
# Loki-rw on <PERSON><PERSON>'s lab (DG)


variables {
  image_loki = "grafana/loki:2.9.4"
  }


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "loki-rw" {
  datacenters = ["DG"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
    # value = "dg-pan-01"
   }

# Group READ = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-read" {
    count = 3
    # count = 1

#    update {
#      max_parallel     = 1
#      min_healthy_time = "10s"
#      healthy_deadline = "2m"
#      canary = 1
#      auto_promote = true
#      auto_revert = true
#    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    network {
      port "port_http" {
      }
      port "port_grpc" {
      }
    }

# TASK loki-read = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "loki-read" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki

        volumes = [
          "/opt/sharednfs/loki-rw:/loki"
        ]

        logging {
          type = "loki"

          config {
            loki-url = "http://lokiwrite.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
          "port_http",
          "port_grpc",
        ]

        args = [
          "-target=read",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          # This dumps configuration, in reverse order (so it reads sensibly in grafana/loki log view) at startup.
          "-log-config-reverse-order",
        ]
      }

      template {
        data        = file("assets/loki-rw.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://dg-pan-01.int.jeddi.org:8500"
      }
      
      service {
        name = "lokiread"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.lokiread.rule=Host(`lokiread.obs.int.jeddi.org`)",
          "traefik.http.routers.lokiread.tls=false",
          "traefik.http.routers.lokiread.entrypoints=http",
        ]

#        check {
#          name     = "Loki read"
#          port     = "port_http"
#          type     = "http"
#          path     = "/ready"
#          interval = "20s"
#          timeout  = "1s"
#
#          initial_status = "passing"
#        }

      }

      resources {
        cpu = 400
        memory = 300
        memory_max = 1000
      }
    }
  }

# Group WRITE = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-write" {
    count = 3
    # count = 1
    
#    update {
#      max_parallel     = 1
#      min_healthy_time = "10s"
#      healthy_deadline = "2m"
#      canary = 1
#      auto_promote = true
#      auto_revert = true
#    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    network {
      port "port_http" {
      }
      port "port_grpc" {
      }
    }

# TASK loki-write = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "loki-write" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki

        volumes = [
          "/opt/sharednfs/loki-rw:/loki"
        ]

        logging {
          type = "loki"

          config {
            loki-url = "http://lokiwrite.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
        
        ports = [
          "port_http",
          "port_grpc",
        ]

        args = [
          "-target=write",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          # This dumps configuration, in reverse order (so it reads sensibly in grafana/loki log view) at startup.
          "-log-config-reverse-order",
        ]
      }

      template {
        data        = file("assets/loki-rw.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://dg-pan-01.int.jeddi.org:8500"
      }

      service {
        name = "lokiwrite"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.lokiwrite.entrypoints=http",
          "traefik.http.routers.lokiwrite.rule=Host(`lokiwrite.obs.int.jeddi.org`) && Path(`/loki/api/v1/push`)",
          "traefik.http.routers.lokiwrite.tls=false"
        ]

#        check {
#          name     = "Loki write"
#          port     = "port_http"
#          type     = "http"
#          path     = "/ready"
#          interval = "20s"
#          timeout  = "1s"
#
#          initial_status = "passing"
#        }

      }

      resources {
        cpu = 400
        memory = 300
        memory_max = 1000
      }
    }
  }
}
