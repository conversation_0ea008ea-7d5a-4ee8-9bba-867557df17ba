
// jedd lab (dg) - loki nomad job -- with persistent storage.
// adapted from:  https://atodorov.me/2021/07/09/logging-on-nomad-and-log-aggregation-with-loki/

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_loki    =  "grafana/loki:2.9.6"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "loki" {

  datacenters = ["DG"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "loki" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {
      port "port_loki_http"  {
        static = 3100
      }
      port "port_loki_grpc"  {
        static = 9097
      }
    }

    volume "vol_loki" {
      type      = "host"
      read_only = false
      source    = "vol_loki"
    }

    task "loki" {
      driver = "docker"
      config {
        # NOTA BENE - do NOT go for latest or master - you WILL have a very bad day, as 
        #             Grafana Corp are admirably relentless with their breaking changes 
        #             between versions.
        image = "${var.image_loki}"

        ports = ["port_loki_http", "port_loki_grpc"]

        args = [

          # Note -- Most configuration items are done here, using CLI parameters passed 
          # in via [args]
          # Items that can ONLY go into a configuration file are provided in the 
          # template{} below.
          #
          # Refer:      https://grafana.com/docs/loki/latest/configuration/
          # and also:   NOTES-loki-parameters.{...}.txt   file in this repo, which
          #             dumps output of ```/usr/bin/loki -help```.
          #
          # Note also that those two sources often conflict or simply lie.  Hilarity ensues.



          # Configuration file to load, can be a comma separated list of paths, first 
          # existing file will be used (default "config.yaml,config/config.yaml")
          "-config.file=local/loki/config.yaml",

          # Enables authentication through the X-Scope-OrgID header, which must be present
          # if true. If false, the OrgID will always be set to "fake".
          # Set to false to disable auth. (default true)
          "-auth.enabled=false",

          # Only log messages with the given severity or above.
          # Valid levels: [debug, info, warn, error] (default info)
          "-log.level=warn",

          # HTTP server listen port. (default 80)
          "-server.http-listen-port=${NOMAD_PORT_port_loki_http}",

          # gRPC server listen port. (default 9095) ### default conflicts with cortex & mimir
          "-server.grpc-listen-port=${NOMAD_PORT_port_loki_grpc}",

          # Jedd 2022-10-17 - experimental reset of default path away from lengthy URL path:
          #                   http://loki.int.jeddi.org:3100/loki/api/v1/push
          #
          # Base path to serve all API routes from (e.g., /v1/).
          # [http_path_prefix: <string> | default = ""]
          # CLI flag: -server.path-prefix
          # "-server.path-prefix=/",

          "-server.grpc-listen-port=${NOMAD_PORT_port_loki_grpc}",


          # Jedd 2022-03 - sometimes we see 'permission denied on mkdir /wal' errors,
          # especially with the 2.4.x releases.  This seems to force resolve that issue.
          # Enable writing of ingested data into WAL. (default true)
          "-ingester.wal-enabled=true",
          # Directory to store the WAL and/or recover from WAL. (default "wal")
          "-ingester.wal-dir=/loki/wal",

          # IP address to advertise in the ring.
          "-ingester.lifecycler.addr=${NOMAD_IP_port_loki_http}",

          # Duration to sleep for before exiting, to ensure metrics are scraped. (default 30s)
          "-ingester.final-sleep=10s",



          # Maximum chunk age before flushing. (default 2h0m0s)
          "-ingester.max-chunk-age=1h",

          # Any chunk not receiving new logs in this time will be flushed (default 30m0s)
          "-ingester.chunks-idle-period=1h",


          # The targeted _uncompressed_ size in bytes of a chunk block
          # When this threshold is exceeded the head block will be cut and 
          # compressed inside the chunk.
          # Default is  262144
          "-ingester.chunks-block-size=2621440",


          # A target _compressed_ size in bytes for chunks.
          # This is a desired size not an exact size, chunks may be slightly bigger
          # or significantly smaller if they get flushed for other reasons 
          # (e.g. chunk_idle_period)
          # A value of 0 creates chunks with a fixed 10 blocks,
          # A non zero value will create chunks with a variable number of 
          # blocks to meet the target size.
          # Loki will attempt to build chunks up to 1.5MB, flushing if chunk_idle_period 
          # or max_chunk_age is reached first.  (default 1572864)
          # We use this *instead* of chunk_idle_period (default 30m).
          "-ingester.chunk-target-size=10485760",




          # How long chunks should be retained in-memory after they've been flushed.
          # CLI flag: -ingester.chunks-retain-period
          # [chunk_retain_period: <duration> | default = 0s]
          "-ingester.chunks-retain-period=30s",

          # Number of times to try and transfer chunks before falling back to flushing.
          # If set to 0 or negative value, transfers are disabled.
          "-ingester.max-transfer-retries=0",


          # Directory where ingesters would write index files which would then be 
          # uploaded by shipper to configured storage.
          "-boltdb.shipper.active-index-directory=/loki/boltdb-shipper-active",

          # Cache location for restoring index files from storage for queries
          "-boltdb.shipper.cache-location=/loki/boltdb-shipper-cache",


          # TTL for index files restored in cache for queries (default 24h0m0s)
          # Can be increased for faster performance over longer query periods, uses more disk space.
          "-boltdb.shipper.cache-ttl=24h",

          # Shared store for keeping index files. Supported types: gcs, s3, azure, filesystem
          "-boltdb.shipper.shared-store=filesystem",

          # Location of BoltDB index files.
          "-boltdb.dir=/loki/index",

          # Directory to store chunks in.
          "-local.chunk-directory=/loki/chunks",

          # Backend storage to use for the ring. Supported values are:
          #   consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-boltdb.shipper.compactor.ring.store=inmemory",

          # Directory where files can be downloaded for compaction.
          "-boltdb.shipper.compactor.working-directory=/tmp/loki/boltdb-shipper-compactor",

          # Shared store for keeping index files. Supported types: gcs, s3, azure, filesystem
          "-boltdb.shipper.shared-store=filesystem",


          # If true, enables retention deletes of DB tables.
          "-table-manager.retention-deletes-enabled=true",

          # Tables older than this retention period are deleted. Must be either 0 (disabled)
          # or a multiple of 24h. When enabled, be aware this setting is destructive to data!
          "-table-manager.retention-period=168h",

          # This replaces query_range // split_queries_by_day
          # Split queries by an interval and execute in parallel, 0 disables it.
          # This also determines how cache keys are chosen when result caching is enabled (default 30m)
          "-querier.split-queries-by-interval=30m",


          # If enabled requests to Alertmanager will utilize the V2 API.
          # "-ruler.alertmanager-use-v2=true",

          # Method to use for backend rule storage (configdb, azure, gcs, s3, swift, local) (default "configdb")
          # "-ruler.storage.type=local",
  
          # Directory to scan for rules
          # "-ruler.storage.local.directory=/loki/rules",

          # File path to store temporary rule files for the prometheus rule managers (default "/rules")
          # "-ruler.rule-path=/loki/scratch",
  
          #  Backend storage to use for the ring. Supported values are:
          #     consul, etcd, inmemory, memberlist, multi. (default "consul")
          # "-ruler.ring.store=inmemory",


          # Comma-separated list of URL(s) of the Alertmanager(s) to send notifications to. 
          # Each Alertmanager URL is treated as a separate group in the configuration. 
          # Multiple Alertmanagers in HA per group can be supported by using DNS resolution 
          # via -ruler.alertmanager-discovery.
          # "-ruler.alertmanager-url=http://dg-pan-01.int.jeddi.org:19009",


          # Enable the ruler api
          # "-experimental.ruler.enable-api=true",

          # Enable anonymous usage reporting. (default true)
          # For args this is reporting.enabled or reporting_enabled in config, but failing as arg on 2.6.1
          # "-analytics.reporting.enabled=true",
          # "-reporting.enabled=true",

          # Backend storage to use for the ring. 
          # Supported values are: consul, etcd, inmemory, memberlist, multi.
          # CLI flag: -<prefix>.store
          # [store: <string> | default = "memberlist"]
          # "-ruler.ring.kvstore.store=inmemory",
          # Backend storage to use for the ring. Supported values are: 
          #      consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-ring.store=inmemory",


          # Backend storage to use for the ring. Supported values are:
          #    consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-query-scheduler.ring.store=inmemory",


          # The number of ingesters to write to and read from. (default 3)
          "-distributor.replication-factor=1",

          # Backend storage to use for the ring. Supported values are:
          #   consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-distributor.ring.store=inmemory",

          # Mode in which the index gateway client will be running (default "simple")
          # It supports two modes:
          # 'simple': an index gateway server instance is responsible for handling,
          #     storing and returning requests for all indices for all tenants.
          # 'ring': an index gateway server instance is responsible for a subset 
          #     of tenants instead of all tenants.
          # This is in the -help, but simply causes an error at run time.
          # "-index-gateway.mode=simple"

        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      template {
        data = <<EOH

# These items MUST live in the configuration file.

# Refer:  https://grafana.com/docs/loki/latest/configuration/#schema_config

# 2024-08-31 - Claude recommends:
server:
  http_server_read_timeout: 1m
  http_server_write_timeout: 1m
  grpc_server_max_recv_msg_size: 1048576
  grpc_server_max_send_msg_size: 1048576

schema_config:
  configs:
  - from: 2021-07-01
    store: boltdb-shipper
    object_store: filesystem
    schema: v11
    index:
      prefix: index_
      period: 24h


# Refer:  https://grafana.com/docs/loki/latest/configuration/#limits_config

limits_config:
  ingestion_rate_mb: 4
  ingestion_burst_size_mb: 6
  reject_old_samples: false
  reject_old_samples_max_age: 7d
  retention_period: 7d
  split_queries_by_interval: 24h
  # This is default (true) since 2.4.2
  unordered_writes: true

  # 2024-08-31 - Claude recommends:
  max_streams_per_user: 5000
  max_global_streams_per_user: 5000

# Refer:  https://grafana.com/docs/loki/latest/configuration/#chunk_store_config

chunk_store_config:
  max_look_back_period: 0s


# Refer:  https://grafana.com/docs/loki/latest/configuration/#frontend
# 2022-03 jedd - to reduce the incidence of 'too many outstanding requests' errors on
#                Grafana panels - default value is 100, which is WAY too small.

frontend:
  max_outstanding_per_tenant: 4096


# 2024-08-31 Claude recommends:
tenant_id: "default"


# Refer:  https://grafana.com/docs/loki/latest/configuration/#analytics

#analytics:
#  reporting_enabled: true



EOH
        destination = "local/loki/config.yaml"
      }

      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }

      resources {
        cpu = 512
        memory = 512
        memory_max = 1512
      }

      service {
        name = "loki"
        port = "port_loki_http"
        check {
          name     = "Loki healthcheck"
          port     = "port_loki_http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.loki.rule=Host(`loki.int.jeddi.org`)",
#          "traefik.http.routers.loki.tls=false",
#          "traefik.http.routers.loki.entrypoints=http",
#        ]

      }

    }
  }
}
