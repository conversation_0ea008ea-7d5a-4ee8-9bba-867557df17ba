
// mimir-rwb (read, write, backend) for jedd-lab (dg)

// Refer:  https://grafana.com/docs/mimir/latest/operators-guide/architecture/deployment-modes/#read-write-mode

// Similar to the other modes, each Grafana Mimir process is invoked with its -target parameter set to the 
// specific service: -target=read, -target=write, or -target=backend.

// Pay careful attention to the variables section this needs to align with the nomad cluster

variables {
  image_mimir = "grafana/mimir:2.6.0"

  loki_url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"

  mimir_read_endpoint = "mimir-rwb-read.obs.int.jeddi.org"
  mimir_write_endpoint = "mimir-rwb-write.obs.int.jeddi.org"
  mimir_redis_endpoint = "mimir-rwb-redis.obs.int.jeddi.org"
  mimir_backend_endpoint = "mimir-rwb-backend.obs.int.jeddi.org"

  jaeger_endpoint = "http://tempo.obs.int.jeddi.org/api/traces"
  # var.jaeger_endpoint = "https://otel-jaeger-thrift.obs.nsw.education/api/traces"


}

job "mimir-rwb" {

  datacenters = ["DG"]

  update {
    max_parallel     = 1
    min_healthy_time = "10s"
    healthy_deadline = "2m"
    canary = 1
    auto_promote = true
    auto_revert = true
  }
  
  # READ group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-read-group" {
    count = 3

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[456]"
    }    
    
    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-read"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "mimir-rwb-read"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-rwb-read.entrypoints=http",
         "traefik.http.routers.mimir-rwb-read.rule=Host(`${var.mimir_read_endpoint}`)",
       ]      

      check {
        name            = "mimir rwb read"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "1s"
      }
    }

    # TASK mimir-rwb-read = = = = = = = = = = = = = = = = = = = = = = =
    task "mimir-rwb-read" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        TZ = "Australia/Sydney"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
 
      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc"
        ]

        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},service_name=mimir-read"
          }
        }

        args = [
          "-target=read",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }
      
      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 1000
        memory = 8000
        memory_max = 12000
      }
    }
  }   # end-READ group


  # WRITE group  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-write-group" {
    count = 3

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[456]"
    }

    ephemeral_disk {
      migrate = true
      sticky  = true
      size = 500
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-write"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "write"
        service = "mimir-write"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-write.entrypoints=http",
        "traefik.http.routers.mimir-rwb-write.rule=Host(`${var.mimir_write_endpoint}`)",
      ]   

      check {
        name            = "mimir rwb write"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    # TASK mimir-rwb-write = = = = = = = = = = = = = = = = = = = = = = =
    task "mimir-rwb-write" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        TZ = "Australia/Sydney"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},service_name=mimir-write"
          }
        }

        volumes = [
          "/opt/sharednfs/mimir-rwb:/mimir"
        ]

        args = [
          "-target=write",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 1000
        memory = 3000
        memory_max = 5000
      }
    }
  }   # end-WRITE group


  # BACKEND group = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-backend-group" {
    count = 3

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[456]"
    }

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }
    service {
      name = "mimir-rwb-backend"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "backend"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-backend.entrypoints=http",
        "traefik.http.routers.mimir-rwb-backend.rule=Host(`${var.mimir_backend_endpoint}`)",
    ]      

      check {
        name            = "mimir rwb backend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    # TASK mimir-rwb-backend = = = = = = = = = = = = = = = = = = = = = =
    task "mimir-rwb-backend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        TZ = "Australia/Sydney"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},service_name=mimir-backend"
          }
        }

        volumes = [
          "/opt/sharednfs/mimir-rwb:/mimir"
        ]

        args = [
          "-target=backend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 1000
        memory = 4096
        memory_max = 8000
      }
    }
  }   # end-BACKEND group

}
