
// mimir (single instance version) for j<PERSON>'s nomad lab (DG)

variables {
  consul_hostname = "jarre.int.jeddi.org:8500"
}

job "mimir" {
  datacenters = ["JA"]
  type = "service"

  group "mimir" {
    network {
      port "port_http" {
        # static = 9009
        static = 9009
      }
      port "port_grpc" {
        # static = 9095
        static = 9095
      }
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "jarre"
    }

    task "mimir" {
      driver = "podman"

      volume_mount {
        volume = "vol_mimir"
        destination = "/mnt/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir"
        dns_servers = ["**************"]
        ports = ["port_http", "port_grpc"]
        volumes = []
        args = [
          "-config.file=/local/mimir.yml",
        ]
      }

      resources {
        cpu = 500
        memory = 4096
      }

      service {
        name = "mimir-ruler"
        port = "port_http"
      }

      service {
        name = "openmetrics"
        port = "port_http"
      }

      service {
        name = "mimir-querier"
        port = "port_http"
      }

      service {
        name = "mimir-store-gateway"
        port = "port_http"
      }

      service {
        name = "mimir"
        port = "port_http"




      }

      service {
        name = "mimir-query-frontend"
        port = "port_http"
        tags = ["traefik.enable=true"]

#        check {
#          type = "http"
#          port = "port_http"
#          path = "/services"
#          interval = "30s"
#          timeout = "5s"
#        }
      }

      template {
        data = <<EOH

# Do not use this configuration in production.
# It is for demonstration purposes only.
multitenancy_enabled: false

blocks_storage:
  backend: filesystem
  bucket_store:
    sync_dir: /tmp/mimir/tsdb-sync
  filesystem:
    dir: /tmp/mimir/data/tsdb
  tsdb:
    dir: /tmp/mimir/tsdb

compactor:
  data_dir: /tmp/mimir/compactor
  sharding_ring:
    kvstore:
      store: memberlist

distributor:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist

ingester:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist
    replication_factor: 1

ruler_storage:
  backend: local
  local:
    directory: /tmp/mimir/rules

server:
  http_listen_port: 9009
  log_level: error

store_gateway:
  sharding_ring:
    replication_factor: 1

EOH
        destination = "local/mimir.yml"
      }
    }
  }
}
