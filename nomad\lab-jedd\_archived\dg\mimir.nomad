
// mimir (single instance version) for jedd's nomad lab (DG) -- dg-pan-01 server - SNAPSHOT
//
// (original in repo jgit:nomad from 2024-06-24 onwards)

variables {
  consul_hostname = "dg-pan-01.int.jeddi.org:8500"
}

job "mimir" {
  datacenters = ["DG"]
  type = "service"

  group "mimir" {
    network {
      port "port_http" {
        # static = 19009
        static = 19009
      }
      port "port_grpc" {
        # static = 19095
        static = 19095
      }
      port "port_alertmanager" {
        # to = 8181
        static = 8181
      }
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    task "mimir" {
      driver = "docker"

      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }

      config {
        # 2022-09-23 jedd - explicitly specify penultimate version, as 2.3.x (2022-09 release) fails with our current nomad config
        # image = "grafana/mimir"
        image = "grafana/mimir:2.6.0"
        dns_servers = ["**************"]
        ports = ["port_http", "port_grpc", "port_alertmanager"]
        volumes = []

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        args = [
          
          # "-target=all",     ## Note that this does NOT include 'alertmanager'.
          # Full list of modules we can pull in with -target= as follows:
          # all,       alertmanager,        compactor,  distributor,  flusher,   
          # ingester,  overrides-exporter,  purger,     querier,      query-frontend,   
          # query-scheduler,                ruler,      store-gateway,   
          # Note that we do NOT want:  flusher , overrides-exporter , query-scheduler (needs 2 hosts)
          # "-target=alertmanager,compactor,distributor,ingester,purger,querier,query-frontend,ruler,store-gateway",
          # 2023-02-08 jedd v2.6.0 disallows 'purger' to be specified, as it's an internal module now.
          "-target=alertmanager,compactor,distributor,ingester,querier,query-frontend,ruler,store-gateway",

          #"-server.grpc-listen-address=${NOMAD_IP_port_grpc}",
          #"-server.http-listen-address=${NOMAD_IP_port_http}",

          "-server.grpc-listen-port=${NOMAD_PORT_port_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_port_http}",

          #  Valid levels: [debug, info, warn, error] (default info)
          "-log.level=warn",

          # Log queries that are slower than the specified duration. Set to 0 to disable.
          # Set to < 0 to enable on all queries.
          # CLI flag: -query-frontend.log-queries-longer-than
          # [log_queries_longer_than: <duration> | default = 0s]
          "-query-frontend.log-queries-longer-than=-1s",




          # When set to true, incoming HTTP requests must specify tenant ID in HTTP X-Scope-OrgId header. 
          # When set to false, tenant ID from -auth.no-auth-tenant is used instead. (default true)
          "-auth.multitenancy-enabled=false",

          # Tenant ID to use when multitenancy is disabled. (default "anonymous")
          "-auth.no-auth-tenant=anonymous",


          # Local filesystem storage directory. (default "alertmanager")
          "-alertmanager-storage.filesystem.dir=/mimir/alertmanager",
        
          # Path at which alertmanager configurations are stored.
          "-alertmanager-storage.local.path=/mimir/alertmanager-configuration",

          # Directory to store Alertmanager state and temporarily configuration files. The
          # content of this directory is not required to be persisted between restarts
          # unless Alertmanager replication has been disabled.
          # The storage.path does not need to persist except when replication is disabled,
          # which is basically our default position with single-instance.
          "-alertmanager.storage.path=/mimir/alertmanager-state",

          # The configs.fallback points to a basic alertmanager configuration for any
          # tenant that lacks a configuration - without this, adding the datasource will
          # give you a 'healthcheck fail' error.
          "-alertmanager.configs.fallback=/mimir/fallback/alertmanager-fallback-config.yml",

          "-alertmanager.sharding-ring.replication-factor=1",
          # "-alertmanager.web.external-url=http://${NOMAD_IP_port_http}:${NOMAD_PORT_port_http}/alertmanager",

          "-alertmanager.enable-api=true",

          "-auth.multitenancy-enabled=false",

          "-blocks-storage.backend=filesystem",
          "-blocks-storage.bucket-store.sync-dir=/mimir/tsdb-sync",
          "-blocks-storage.filesystem.dir=/mimir/blocks",
          "-blocks-storage.tsdb.dir=/mimir/tsdb",

          #"-compactor.data_dir=/mimir/compactor",
          #"-compactor.sharding_ring.store=memberlist",
          "-compactor.ring.store=memberlist",
          "-compactor.ring.instance-addr=${NOMAD_IP_port_http}",
          "-compactor.ring.instance-port=${NOMAD_PORT_port_grpc}",
          "-compactor.ring.instance-id=${node.unique.name}",

          "-distributor.ring.instance-addr=${NOMAD_IP_port_http}",
          "-distributor.ring.instance-port=${NOMAD_PORT_port_grpc}",
          "-distributor.ring.instance-id=${node.unique.name}",
          "-distributor.ring.store=memberlist",

          "-ingester.ring.store=memberlist",
          "-ingester.ring.replication-factor=1",
          "-ingester.ring.instance-addr=${NOMAD_IP_port_http}",
          "-ingester.ring.instance-port=${NOMAD_PORT_port_grpc}",
          "-ingester.ring.instance-id=${node.unique.name}",
          "-ingester.max-global-series-per-user=300000",
          "-ingester.max-global-series-per-metric=50000",

          "-querier.iterators=true",
          "-query-frontend.instance-addr=${NOMAD_IP_port_http}",
          "-query-frontend.instance-port=${NOMAD_PORT_port_grpc}",
          "-querier.frontend-address=${NOMAD_IP_port_http}:${NOMAD_PORT_port_grpc}",
          "-querier.id=${node.unique.name}",

          # "-ruler-storage.backend=local",
          "-ruler-storage.backend=filesystem",
          "-ruler-storage.filesystem.dir=/mimir/ruler",

          # scan this directory for rules
          "-ruler-storage.local.directory=/mimir/rules",

          # Comma-separated list of URL(s) of the Alertmanager(s) to send notifications to.
          # Each URL is treated as a separate group. Multiple Alertmanagers in HA per group
          # can be supported by using DNS service discovery format. 
          # Basic auth is supported as part of the URL.
          "-ruler.alertmanager-url=http://${NOMAD_IP_port_http}:${NOMAD_PORT_port_http}/alertmanager",
  
          # The replication factor to use when sharding blocks. This option needs
          # be set both on the store-gateway, querier and ruler when running in 
          # microservices mode. (default 3)
          "-store-gateway.sharding-ring.instance-addr=${NOMAD_IP_port_http}",
          "-store-gateway.sharding-ring.instance-port=${NOMAD_PORT_port_grpc}",
          "-store-gateway.sharding-ring.instance-id=${node.unique.name}",
          "-store-gateway.sharding-ring.replication-factor=1",

        ]
      }


      resources {
        cpu = 500
        memory = 3000
      }

      service {
        name = "mimir-ruler"
        port = "port_http"
      }

      service {
        name = "alertmanager"
        port = "port_alertmanager"
      }

      service {
        name = "openmetrics"
        port = "port_http"
      }

      service {
        name = "mimir-querier"
        port = "port_http"
      }

      service {
        name = "mimir-store-gateway"
        port = "port_http"
      }

      service {
        name = "mimir"
        port = "port_http"

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.mimir.rule=Host(`mimir.int.jeddi.org`)",
#          "traefik.http.routers.mimir.tls=false",
#        ]
      }

      service {
        name = "mimir-query-frontend"
        port = "port_http"
#         tags = ["traefik.enable=true"]

        check {
          type = "http"
          port = "port_http"
          path = "/services"
          interval = "10s"
          timeout = "5s"
        }

      }

    }
  }
}
