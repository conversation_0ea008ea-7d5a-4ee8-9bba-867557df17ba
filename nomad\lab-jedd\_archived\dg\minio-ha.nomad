
# minio-ha - cluster mode - 3 nodes (should really be 4, but meh) for DG, running on dg-hac-0[123]
#
# PoC - obviously NOT actual ha, as it all runs on the same vmware host, sharing the same platters.


variables {
  image_minio = "minio/minio:latest"
  # image_minio = " minio/minio:RELEASE.2024-02-14T21-36-02Z"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "minio-ha" {

  datacenters = ["DG"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
    # value = "dg-pan-01"
   }

# Group minio-ha  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "minio-ha" {

    count = 3
		constraint {
			operator  = "distinct_hosts"
			value     = "true"
		}

    network {
      # We don't use Traefik - instead we use static ports, and static task-per-host.
      port "port_api" {
        static = 9000
      }
      port "port_console" {
        static = 9001
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "minio-ha-task" {
      driver = "docker"

      env = {
        "MINIO_ROOT_USER"                    = "admin",
        "MINIO_ROOT_PASSWORD"                = "bigsecret",

        "MINIO_PROMETHEUS_AUTH_TYPE"         = "public",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true",


				"MINIO_VOLUMES" = "http://dg-hac-0{1...3}.obs.int.jeddi.org:${NOMAD_PORT_port_api}/minio",
        "MINIO_OPTS" = "--console-address :${NOMAD_PORT_port_console}",
				"MINIO_SERVER_URL" = "http://minioha.obs.int.jeddi.org:${NOMAD_PORT_port_console}",

        # This is needed to avoid the error:  
        #      Unable to configure server grid RPC services: grid: local host not found
        "hostname" = "${NOMAD_IP_port_console}",
				# can try: ${attr.unique.hostname}


#        "MINIO_PROMETHEUS_URL"               = "http://dg-pan-01.int.jeddi.org:9090",
#        "MINIO_PROMETHEUS_JOB_ID"            = "minio_metrics"
      }

      config {
        image = var.image_minio

        ports = ["port_api", "port_console"]

        args = [ 
          "server", 
          "--address",           "0.0.0.0:${NOMAD_HOST_PORT_port_api}",
          "--console-address",   "0.0.0.0:${NOMAD_HOST_PORT_port_console}",
					# "/data",
					"http://dg-hac-0{1...3}.obs.int.jeddi.org:${NOMAD_PORT_port_api}/data",

        ]

        volumes = [
          "local/default-minio:/etc/default/minio"
        ]

        logging {
          type = "loki"
          config {
            # loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-url = "http://lokiwrite.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      resources {
        cpu    = 300
        memory = 800
        memory_max = 2000
      }

      template {
        data = <<EOH
# Set the hosts and volumes MinIO uses at startup
# The command uses MinIO expansion notation {x...y} to denote a
# sequential series.
#
# The following example covers four MinIO hosts
# with 4 drives each at the specified hostname and drive locations.
# The command includes the port that each MinIO server listens on
# (default 9000)

MINIO_VOLUMES="http://dg-hac-0{1...3}.obs.int.jeddi.org:{{ env "NOMAD_PORT_port_api" }}/data"

# Set all MinIO server options
#
# The following explicitly sets the MinIO Console listen address to
# port 9001 on all network interfaces. The default behavior is dynamic
# port selection.

MINIO_OPTS="--console-address :{{ env "NOMAD_PORT_port_console" }}"

# Set the root username. This user has unrestricted permissions to
# perform S3 and administrative API operations on any resource in the
# deployment.
#
# Defer to your organizations requirements for superadmin user name.

MINIO_ROOT_USER=admin

# Set the root password
#
# Use a long, random, unique string that meets your organizations
# requirements for passwords.

MINIO_ROOT_PASSWORD=bigsecret

# Set to the URL of the load balancer for the MinIO deployment
# This value *must* match across all MinIO servers. If you do
# not have a load balancer, set this value to to any *one* of the
# MinIO hosts in the deployment as a temporary measure.
MINIO_SERVER_URL="http://minioha.obs.int.jeddi.org:{{ env "NOMAD_PORT_port_console" }}"


EOH
        destination = "local/default-minio"
        perms = "755"
      }


      service {
        name = "minio"
        port = "port_console"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.minioha.rule=Host(`minioha.obs.int.jeddi.org`)",
          "traefik.http.routers.minioha.entrypoints=http",
          "traefik.http.routers.minioha.tls=false",
        ]

#        check {
#          name     = "minio_alive"
#          type     = "http"
#          port     = "port_console"
#          path     = "/minio/health/live"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
