
//  mock redfish server for experimenting with the prometheus redfish-exporter - for jedd's lab (dg)
//
//  redfish is the API used by Dell iDRAC OOB systems, amongst others
//
//  Similar to the ersatz Oracle container - used for testing the redfish-exporter without needing an
//  actual live hardware endpoint.
//
//  Refer:   
//       https://github.com/DMTF/Redfish-Mockup-Server
//       https://hub.docker.com/r/dmtf/redfish-mockup-server

job "mock-redfish-server" {
  type = "service"
  datacenters = ["DG"]

  group "mock-redfish-server" {
    network {
      port "port_mock_redfish" {
        static = 9611
      }
    }

    task "mock-redfish" {
      driver = "docker"

      config {
        ports = [ 
          "port_mock_redfish" 
          ]
        image = "dmtf/redfish-mockup-server"
        dns_servers = [
          "**************"
          ]
#        command = "redfishMockupServer.py"
#        command = "/usr/src/redfishMockupServer.py"
#        command = "/usr/local/bin/python"
#        args = [
#          "/usr/src/redfishMockupServer.py",
#          " --host",
#          " 0.0.0.0",
#          " --port",
#          " 9611"
#          ]

      }

      service {
        name = "mock-redfish-server"
        port = "port_mock_redfish"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.mockredfish.rule=Host(`mockredfish.int.jeddi.org`)",
          "traefik.http.routers.mockredfish.tls=false",
        ]

#        check {
#          type = "http"
#          port = "port_mock_redfish"
#          path = "/"
#          interval = "20s"
#          timeout = "10s"
#        }

      }

      resources {
        cpu    = 100
        memory = 256
      }

    }

  }
}
