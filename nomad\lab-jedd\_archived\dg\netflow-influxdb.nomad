
// telegraf for netflow - feeding to influxdb

variables {
  consul_hostname = "dg-pan-01.int.jeddi.org:8500"
}

job "netflow-influxdb" {
  datacenters = ["DG"]
  type = "service"

  group "netflow-influxdb" {
    network {

      port "port_netflow" {
        static = 2055
      }

      port "port_prom" {
        static = 9274
      }

    }

    #volume "vol_netflow"  {
    #  type = "host"
    #  source = "vol_netflow"
    #  read_only = false
    #}

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }


    task "netflow-influxdb" {
      driver = "docker"

      # user = "101:100"
      # user = "nginx:nginx"
      user = "root:root"

      env = {
        "DB_HOST" = "dg-pan-01.int.jeddi.org:3306",
        "DB_DATABASE" = "bookstack",
        "DB_USERNAME" = "bookstack",
        "DB_PASSWORD" = "bigsecret"
      }

      #volume_mount {
      #  volume = "vol_netflow"
      #  destination = "/mnt/netflow"
      #  read_only = false
      #}

      config {
        
        image = "telegraf:latest"

        dns_servers = ["**************"]

        ports = ["port_netflow", "port_prom"]

        volumes = [
          "local/telegraf.conf:/etc/telegraf/telegraf.conf"
        ]

        volumes = [
          "/opt/influxdb/config:/etc/influxdb2",
          "/opt/influxdb/data:/var/lib/influxdb2"
        ]

        # cap_add = ["net_raw", "net_admin", "setfcap", "setuid"]
        cap_add = ["all"]

        args = [
          "--bolt-path=/var/lib/influxdb2/influxd.bolt",
          "--engine-path=/var/lib/influxdb2/engine",
          "--log-level=info",
          "--flux-log-enabled=true"
#          "-config.file=/local/mimir.yml",
#          "-config.expand-env",
##          "-consul.hostname=dg-pan-01.int.jeddi.org:8500",
#           "-log.level=warn",
#          "server.http_listen-address=**************",
#          "server.grpc_listen-address=**************"
        ]
      }

      resources {
        cpu = 500
        memory = 1024
      }

      service {
        name = "http"
        port = "port_prom"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.netflow.rule=Host(`netflow.int.jeddi.org`)",
          "traefik.http.routers.netflow.tls=false",
        ]

#        check {
#          type = "http"
#          port = "port_http"
#          path = "/"
#          interval = "30s"
#          timeout = "5s"
#        }
      }

      template {
        data = <<EOH

# Netflow v5, Netflow v9 and IPFIX collector
[[inputs.netflow]]
  ## Address to listen for netflow/ipfix packets.
  ##   example: service_address = "udp://:2055"
  ##            service_address = "udp4://:2055"
  ##            service_address = "udp6://:2055"
  service_address = "udp://:2055"

  ## Set the size of the operating system's receive buffer.
  ##   example: read_buffer_size = "64KiB"
  ## Uses the system's default if not set.
  # read_buffer_size = ""

  ## Protocol version to use for decoding.
  ## Available options are
  ##   "netflow v5" -- Netflow v5 protocol
  ##   "netflow v9" -- Netflow v9 protocol (also works for IPFIX)
  ##   "ipfix"      -- IPFIX / Netflow v10 protocol (also works for Netflow v9)
  # protocol = "ipfix"
  protocol = "netflow v9"

  ## Dump incoming packets to the log
  ## This can be helpful to debug parsing issues. Only active if
  ## Telegraf is in debug mode.
  # dump_packets = false

#[[outputs.prometheus_client]]
#  listen = ":9274"
#  collectors_exclude = ["gocollector"]

[[outputs.influxdb_v2]]
  urls = [ "http://dg-pan-01.int.jeddi.org:8086"]
  token = "9o7lE9iH_OtCmvg-iF33Ekf3-MT9jJ1OeT9FFv4YRXG0NeKZqsBICsAS0UAwkdIon2Gnllus8-769VkBkRitiA=="
  bucket = "telegraf"
  organization = "jeddi"



EOH
        destination = "/local/telegraf.conf"
      }
    }
  }
}
