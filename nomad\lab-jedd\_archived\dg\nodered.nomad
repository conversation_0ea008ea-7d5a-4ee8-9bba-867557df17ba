
// SNAPSHOT 2025-01 - production release relocated to jedd's git repo


# NodeRED for <PERSON><PERSON>'s lab (DG) to provide telegraf phone-home configurations
#
# Stolen then heavily modified from https://github.com/leowmjw/nomad-box


job "nodered" {

  datacenters = ["DG"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "nodered" {

    network {
      port "port_nodered" {
        # Without traefik:
        # static = 1880

        # With traefik:
        to = 1880
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    ephemeral_disk {
      size = 300
    }

    task "nodered" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
#        "NODE_RED_ENABLE_PROJECTS" = "true"
      }

      config {
        image = "nodered/node-red:latest"

        ports = ["port_nodered"]

        volumes = [ 
          "/opt/sharednfs/nodered:/data"
			  ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "nodered"
        port = "port_nodered"

        tags = [
           "traefik.enable=true",
           "traefik.http.routers.nodered.rule=Host(`nodered.obs.int.jeddi.org`)",
           "traefik.http.routers.nodered.tls=false",
           "traefik.http.routers.nodered.entrypoints=http",
        ]

        check {
          name     = "alive"
          type     = "tcp"
          interval = "30s"
          timeout  = "5s"
        }

      }

    }

  }
}
