
# oncall - jedd's lab Grafana OnCall discovery

# 2023-08-31 reprise - multi-task breakdown of docker-compose.yml


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_oncall     = "grafana/oncall"
  image_redis      = "redis:7.0.5"
  image_grafana    = "grafana/grafana:latest"

  DATABASE_TYPE = "sqlite3"
  BROKER_TYPE = "redis"

  # From .env file - we can't dynamically drop the 'with_grafana', the rest are nomad-ised.
  #
  # Remove 'with_grafana' below if you want to use existing grafana
  # Add 'with_prometheus' below to optionally enable a local prometheus for oncall metrics
  # e.g. COMPOSE_PROFILES=with_grafana,with_prometheus
  # COMPOSE_PROFILES=with_grafana
  # to setup an auth token for prometheus exporter metrics:
  # PROMETHEUS_EXPORTER_SECRET=my_random_prometheus_secret
  # also, make sure to enable the /metrics endpoint:
  # FEATURE_PROMETHEUS_EXPORTER_ENABLED=True
  PROMETHEUS_COLLECT_DEFAULT_METRICS = "true"


  BASE_URL = "http://oncall-engine.obs.int.jeddi.org"

  SECRET_KEY = "0123456789012345678901234567890123456789"

  # FEATURE_PROMETHEUS_EXPORTER_ENABLED = ${FEATURE_PROMETHEUS_EXPORTER_ENABLED:-false}
  FEATURE_PROMETHEUS_EXPORTER_ENABLED = "true"

  # PROMETHEUS_EXPORTER_SECRET = "randomstring"

  # REDIS_URI = "redis://redis:6379/0"
  # REDIS_URI = "redis://oncall-redis.int.jeddi.org:6379/0"

  # This will work at DG:
  REDIS_URI = "redis://oncall-redis.obs.int.jeddi.org/0"
  # This will work at PY:
  # REDIS_URI = "redis://192.168.1.46:6379/0"

  DJANGO_SETTINGS_MODULE = "settings.hobby"
  CELERY_WORKER_QUEUE = "default,critical,long,slack,telegram,webhook,retry,celery"
  CELERY_WORKER_CONCURRENCY = "1"
  CELERY_WORKER_MAX_TASKS_PER_CHILD = "100"
  CELERY_WORKER_SHUTDOWN_INTERVAL = "65m"
  CELERY_WORKER_BEAT_ENABLED = "True"

  RABBITMQ_PASSWORD = "rabbitmq_secret_pw"

  # Even if not using mysql, this must be set. For reasons.
  MYSQL_PASSWORD = "mysql_secret_pw"

  # Dedicated grafana for oncall:
  GRAFANA_API_URL = "http://oncall-grafana.obs.int.jeddi.org"
  # Shared grafana for oncall:
  # GRAFANA_API_URL = "http://dg-pan-01.int.jeddi.org:3000"

  GRAFANA_USER = "admin"
  GRAFANA_PASSWORD = "admin" 
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "oncall" {

  datacenters = ["DG"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "oncall" {

    network {
      port "port_oncall_engine" {
        to = 8080
      }

      port "port_oncall_redis" {
        to = 6379
        static = 6379
      }

      port "port_oncall_grafana" {
        to = 3000
      }

      port "port_oncall_celery" {
      }

    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[12]"
      # value = "dg-hac-0[3]"
    }

    ephemeral_disk {
      size = 300
    }


    # TASK engine = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "oncall-engine" {
      driver = "docker"

      env = {
        # Not all of these are needed in each container, but it's not at all clear which
        # are / aren't, so we just dump the same set in for every container.
        "DATABASE_TYPE" = "${var.DATABASE_TYPE}",
        "BROKER_TYPE" = "${var.BROKER_TYPE}",
        #
        "BASE_URL" = "${var.BASE_URL}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        "FEATURE_PROMETHEUS_EXPORTER_ENABLED" = "${var.FEATURE_PROMETHEUS_EXPORTER_ENABLED}",
        "REDIS_URI" = "${var.REDIS_URI}",
        "DJANGO_SETTINGS_MODULE" = "${var.DJANGO_SETTINGS_MODULE}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        #
        "CELERY_WORKER_QUEUE" = "${var.CELERY_WORKER_QUEUE}",
        "CELERY_WORKER_CONCURRENCY" = "${var.CELERY_WORKER_CONCURRENCY}",
        "CELERY_WORKER_MAX_TASKS_PER_CHILD" = "${var.CELERY_WORKER_MAX_TASKS_PER_CHILD}",
        "CELERY_WORKER_SHUTDOWN_INTERVAL" = "${var.CELERY_WORKER_SHUTDOWN_INTERVAL}",
        "CELERY_WORKER_BEAT_ENABLED" = "${var.CELERY_WORKER_BEAT_ENABLED}",
        #
        # Not used - should be coming in via GRAFAPA_API_URL
        # "DOMAIN" = "http://localhost:8090",
        "GRAFANA_API_URL" = "${var.GRAFANA_API_URL}",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "${var.PROMETHEUS_COLLECT_DEFAULT_METRICS}",
        #
        "RABBITMQ_PASSWORD" = "${var.RABBITMQ_PASSWORD}",
        "MYSQL_PASSWORD" = "${var.MYSQL_PASSWORD}",
        #
        "GRAFANA_USER" = "${var.GRAFANA_USER}",
        "GRAFANA_PASSWORD" = "${var.GRAFANA_PASSWORD}",
      }

      config {
        image = "${var.image_oncall}"

        ports = ["port_oncall_engine"]

        privileged = "true"

        # command =  "sh -c 'uwsgi --ini uwsgi.ini'"

        args = [ 
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "/opt/sharednfs/oncall/varlib:/var/lib/oncall"
        ]


      }

      resources {
        cpu = 200
        memory = 100
        memory_max = 1024
      }

      service {
        name = "oncall-engine"
        port = "port_oncall_engine"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oncall-engine.rule=Host(`oncall-engine.obs.int.jeddi.org`)",
          "traefik.http.routers.oncall-engine.tls=false",
          "traefik.http.routers.oncall-engine.entrypoints=http",
        ]

        #check {
        #  name     = "oncall"
        #  type     = "http"
        #  port     = "port_oncall_engine"
        #  path     = "/oncall/health/live"
        #  interval = "30s"
        #  timeout  = "5s"
        #}

      }

    }  // end-task oncall engine


    # TASK celery = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "oncall-celery" {
      driver = "docker"

      env = {
        # Not all of these are needed in each container, but it's not at all clear which
        # are / aren't, so we just dump the same set in for every container.
        "DATABASE_TYPE" = "${var.DATABASE_TYPE}",
        "BROKER_TYPE" = "${var.BROKER_TYPE}",
        #
        "BASE_URL" = "${var.BASE_URL}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        "FEATURE_PROMETHEUS_EXPORTER_ENABLED" = "${var.FEATURE_PROMETHEUS_EXPORTER_ENABLED}",
        "REDIS_URI" = "${var.REDIS_URI}",
        "DJANGO_SETTINGS_MODULE" = "${var.DJANGO_SETTINGS_MODULE}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        #
        "CELERY_WORKER_QUEUE" = "${var.CELERY_WORKER_QUEUE}",
        "CELERY_WORKER_CONCURRENCY" = "${var.CELERY_WORKER_CONCURRENCY}",
        "CELERY_WORKER_MAX_TASKS_PER_CHILD" = "${var.CELERY_WORKER_MAX_TASKS_PER_CHILD}",
        "CELERY_WORKER_SHUTDOWN_INTERVAL" = "${var.CELERY_WORKER_SHUTDOWN_INTERVAL}",
        "CELERY_WORKER_BEAT_ENABLED" = "${var.CELERY_WORKER_BEAT_ENABLED}",
        #
        # Not used - should be coming in via GRAFAPA_API_URL
        # "DOMAIN" = "http://localhost:8090",
        "GRAFANA_API_URL" = "${var.GRAFANA_API_URL}",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "${var.PROMETHEUS_COLLECT_DEFAULT_METRICS}",
        #
        "RABBITMQ_PASSWORD" = "${var.RABBITMQ_PASSWORD}",
        "MYSQL_PASSWORD" = "${var.MYSQL_PASSWORD}",
        #
        "GRAFANA_USER" = "${var.GRAFANA_USER}",
        "GRAFANA_PASSWORD" = "${var.GRAFANA_PASSWORD}",
      }

      config {
        image = "${var.image_oncall}"

        ports = ["port_oncall_celery"]

        # command =  "sh -c './celery_with_exporter.sh'"

        privileged = "true"

        args = [ 
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "/opt/sharednfs/oncall/varlib:/var/lib/oncall"
        ]

      }

      resources {
        cpu = 100
        memory = 150
        memory_max = 700
      }

      service {
        name = "oncall-celery"
        # port = "port_oncall_celery"
      }

    }  // end-task oncall celery


    # TASK db migration = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "oncall-db-migration" {
      driver = "docker"

      env = {
        # Not all of these are needed in each container, but it's not at all clear which
        # are / aren't, so we just dump the same set in for every container.
        "DATABASE_TYPE" = "${var.DATABASE_TYPE}",
        "BROKER_TYPE" = "${var.BROKER_TYPE}",
        #
        "BASE_URL" = "${var.BASE_URL}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        "FEATURE_PROMETHEUS_EXPORTER_ENABLED" = "${var.FEATURE_PROMETHEUS_EXPORTER_ENABLED}",
        "REDIS_URI" = "${var.REDIS_URI}",
        "DJANGO_SETTINGS_MODULE" = "${var.DJANGO_SETTINGS_MODULE}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        #
        "CELERY_WORKER_QUEUE" = "${var.CELERY_WORKER_QUEUE}",
        "CELERY_WORKER_CONCURRENCY" = "${var.CELERY_WORKER_CONCURRENCY}",
        "CELERY_WORKER_MAX_TASKS_PER_CHILD" = "${var.CELERY_WORKER_MAX_TASKS_PER_CHILD}",
        "CELERY_WORKER_SHUTDOWN_INTERVAL" = "${var.CELERY_WORKER_SHUTDOWN_INTERVAL}",
        "CELERY_WORKER_BEAT_ENABLED" = "${var.CELERY_WORKER_BEAT_ENABLED}",
        #
        # Not used - should be coming in via GRAFAPA_API_URL
        # "DOMAIN" = "http://localhost:8090",
        "GRAFANA_API_URL" = "${var.GRAFANA_API_URL}",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "${var.PROMETHEUS_COLLECT_DEFAULT_METRICS}",
        #
        "RABBITMQ_PASSWORD" = "${var.RABBITMQ_PASSWORD}",
        "MYSQL_PASSWORD" = "${var.MYSQL_PASSWORD}",
        #
        "GRAFANA_USER" = "${var.GRAFANA_USER}",
        "GRAFANA_PASSWORD" = "${var.GRAFANA_PASSWORD}",
      }

      config {
        image = "${var.image_oncall}"

        # @TODO This doesn't run sanely at boot as shown - might need to have a cd /etc/app first - but
        #       DOES need to be run once to populate the sqlite DB (persistent, hence can be run manually).
        #       Failing to do this gives lots of errors about user_management_organization table not found.
        # command = "python manage.py migrate --noinput"

        privileged = "true"

        args = [ 
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "/opt/sharednfs/oncall/varlib:/var/lib/oncall"
        ]

      }

      resources {
        cpu = 100
        memory = 100
        memory_max = 1024
      }

      service {
        name = "oncall-db-migration"
        # port = "port_oncall_db"
      }
    }  // end-task oncall-db-migration


    # TASK redis = == = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "oncall-redis" {
      driver = "docker"

      env = {
        # Not all of these are needed in each container, but it's not at all clear which
        # are / aren't, so we just dump the same set in for every container.
        "DATABASE_TYPE" = "${var.DATABASE_TYPE}",
        "BROKER_TYPE" = "${var.BROKER_TYPE}",
        #
        "BASE_URL" = "${var.BASE_URL}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        "FEATURE_PROMETHEUS_EXPORTER_ENABLED" = "${var.FEATURE_PROMETHEUS_EXPORTER_ENABLED}",
        "REDIS_URI" = "${var.REDIS_URI}",
        "DJANGO_SETTINGS_MODULE" = "${var.DJANGO_SETTINGS_MODULE}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        #
        "CELERY_WORKER_QUEUE" = "${var.CELERY_WORKER_QUEUE}",
        "CELERY_WORKER_CONCURRENCY" = "${var.CELERY_WORKER_CONCURRENCY}",
        "CELERY_WORKER_MAX_TASKS_PER_CHILD" = "${var.CELERY_WORKER_MAX_TASKS_PER_CHILD}",
        "CELERY_WORKER_SHUTDOWN_INTERVAL" = "${var.CELERY_WORKER_SHUTDOWN_INTERVAL}",
        "CELERY_WORKER_BEAT_ENABLED" = "${var.CELERY_WORKER_BEAT_ENABLED}",
        #
        # Not used - should be coming in via GRAFAPA_API_URL
        # "DOMAIN" = "http://localhost:8090",
        "GRAFANA_API_URL" = "${var.GRAFANA_API_URL}",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "${var.PROMETHEUS_COLLECT_DEFAULT_METRICS}",
        #
        "RABBITMQ_PASSWORD" = "${var.RABBITMQ_PASSWORD}",
        "MYSQL_PASSWORD" = "${var.MYSQL_PASSWORD}",
        #
        "GRAFANA_USER" = "${var.GRAFANA_USER}",
        "GRAFANA_PASSWORD" = "${var.GRAFANA_PASSWORD}",
      }

      config {
        image = "${var.image_redis}"

        privileged = "true"

        ports = ["port_oncall_redis"]

        args = [ 
          "/etc/redis.conf"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "local/redis.conf:/etc/redis.conf",
          "/opt/sharednfs/oncall/redis:/data"
        ]

      }

      resources {
        cpu = 100
        memory = 100
        memory_max = 1024
      }

      template {
        data = <<EOF
port 6379

EOF
        destination = "local/redis.conf"

      }

      service {
        name = "oncall-redis"
        port = "port_oncall_redis"
       
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oncall-redis.rule=Host(`oncall-redis.obs.int.jeddi.org`)",
          "traefik.http.routers.oncall-redis.tls=false",
          "traefik.http.routers.oncall-redis.entrypoints=http",
        ]
      }

    }  // end-task oncall-redis



    # TASK grafana = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "oncall-grafana" {
      driver = "docker"

      env = {
        # Not all of these are needed in each container, but it's not at all clear which
        # are / aren't, so we just dump the same set in for every container.
        "DATABASE_TYPE" = "${var.DATABASE_TYPE}",
        "BROKER_TYPE" = "${var.BROKER_TYPE}",
        #
        "BASE_URL" = "${var.BASE_URL}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        "FEATURE_PROMETHEUS_EXPORTER_ENABLED" = "${var.FEATURE_PROMETHEUS_EXPORTER_ENABLED}",
        "REDIS_URI" = "${var.REDIS_URI}",
        "DJANGO_SETTINGS_MODULE" = "${var.DJANGO_SETTINGS_MODULE}",
        "SECRET_KEY" = "${var.SECRET_KEY}",
        #
        "CELERY_WORKER_QUEUE" = "${var.CELERY_WORKER_QUEUE}",
        "CELERY_WORKER_CONCURRENCY" = "${var.CELERY_WORKER_CONCURRENCY}",
        "CELERY_WORKER_MAX_TASKS_PER_CHILD" = "${var.CELERY_WORKER_MAX_TASKS_PER_CHILD}",
        "CELERY_WORKER_SHUTDOWN_INTERVAL" = "${var.CELERY_WORKER_SHUTDOWN_INTERVAL}",
        "CELERY_WORKER_BEAT_ENABLED" = "${var.CELERY_WORKER_BEAT_ENABLED}",
        #
        # Not used - should be coming in via GRAFAPA_API_URL
        # "DOMAIN" = "http://localhost:8090",
        "GRAFANA_API_URL" = "${var.GRAFANA_API_URL}",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "${var.PROMETHEUS_COLLECT_DEFAULT_METRICS}",
        #
        "RABBITMQ_PASSWORD" = "${var.RABBITMQ_PASSWORD}",
        "MYSQL_PASSWORD" = "${var.MYSQL_PASSWORD}",
        #
        "GRAFANA_USER" = "${var.GRAFANA_USER}",
        "GRAFANA_PASSWORD" = "${var.GRAFANA_PASSWORD}",

        # GRAFANA task specific entries:

        "GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS" = "grafana-oncall-app",
        "GF_INSTALL_PLUGINS" = "grafana-oncall-app",
      }

      config {
        image = "${var.image_grafana}"

        ports = ["port_oncall_grafana"]

        privileged = "true"

        args = [ 
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
         "/opt/sharednfs/oncall/grafana:/var/lib/grafana"
        ]

      }

      resources {
        cpu = 100
        memory = 100
        memory_max = 1024
      }

      service {
        name = "oncall-grafana"
        port = "port_oncall_grafana"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oncall-grafana.rule=Host(`oncall-grafana.obs.int.jeddi.org`)",
          "traefik.http.routers.oncall-grafana.tls=false",
          "traefik.http.routers.oncall-grafana.entrypoints=http",
        ]

      }

    }  // end-task oncall grafana

  } // end-group  oncall

} // end-job  grafana-oncall

