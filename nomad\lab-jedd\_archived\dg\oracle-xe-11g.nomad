// jedd lab - oracle in a nutshell - a mini Oracle instance for testing oracle db exporter metrics

// docker commandline is:
//   docker run -d -p 1521:1521 -p 8888:8080 -e ORACLE_ALLOW_REMOTE=true -e ORACLE_DISABLE_ASYNCH_IO=true  oracleinanutshell/oracle-xe-11g

// Docker hub page:
//   https://hub.docker.com/r/oracleinanutshell/oracle-xe-11g

// Should expose on 1521 - login with:
//     username:   system
//     password:   oracle

job "oracle-xe-11g" {
  datacenters = ["DG"]

  type        = "service"

  #update {
  #  max_parallel      = 1
  #  health_check      = "checks"
  #  min_healthy_time  = "10s"
  #  healthy_deadline  = "3m"
  #  progress_deadline = "5m"
  #}

  group "oracle" {

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "dg-hac-0[123]"
      value = "dg-hac-02"
    }

    count = 1

    network {
      port "port_oracle"  {
        static = 1521
        to     = 1521
      }
      port "port_apex"  {
        static = 8888
        to     = 8080
      }
    }

    task "oracle" {
      driver = "docker"
      env = {
        "ORACLE_ALLOW_REMOTE" = "true",
        "ORACLE_DISABLE_ASYNCH_IO" = "true"
      }

      config {
        image = "oracleinanutshell/oracle-xe-11g:1.0.0"

        # ports = ["port_oracle"]
        ports = ["port_oracle", "port_apex"]

        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      resources {
        cpu    = 512
        memory = 1000
        memory_max = 8000
      }

      service {
        name = "oracle"
        port = "port_oracle"

        #check {
        #  name     = "Oracle healthcheck"
        #  port     = "port_oracle"
        #  type     = "tcp"
        #  interval = "60s"
        #  timeout  = "5s"
        #  check_restart {
        #    limit           = 3
        #    grace           = "60s"
        #    ignore_warnings = false
        #  }
        #}

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oracle.rule=Host(`oracle.obs.int.jeddi.org`)",
          "traefik.http.routers.oracle.tls=false",
          "traefik.http.routers.oracle.entrypoints=http",
        ]

      }
    }
  }
}
