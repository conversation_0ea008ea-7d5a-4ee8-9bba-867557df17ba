
// Grafana for jedd-lab - with bundled postgresql

// Part of the 2023-06 migration of Grafana from Classic to ObsCol 



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_postgresql =  "postgres:12"

}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "pgbackup" {
  datacenters = ["DG"]

  # Constraint all tasks in this job to HACluster
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    # value = "dg-hac-0[123]"
    value = "dg-hac-0[2]"
  }

  type = "service"

  group "pgbackup" {

    volume "vol_grafana_obs" {
      type = "host"
      source = "vol_grafana_obs"
      read_only = false
    }


    # task pgbackup  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "pgbackup" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      user = "postgres:postgres"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = "${var.image_postgresql}"

        command = "/backup-looper.sh"
        # command = "/bin/sleep"
        # args = [" infinity"]

        volumes = [
          "local/backup-looper.sh:/backup-looper.sh",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }


      }

      env = {
        "POSTGRES_USER"     = "nouser",
        "POSTGRES_PASSWORD" = "notpresent",
        "POSTGRES_DB"       = "notpresent",
        # "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
        "PGPORT"            = "5432",
        # "PGDATA"            = "/persistent/postgresql/data/pgdata",

        "TZ" = "Australia/Sydney",
      }

      resources {
        cpu    = 512
        memory = 1024
      }

      service {
        name = "pgbackup"
      }

      #  FILE:   backup-looper.sh
      #  this is our entry point - we're just here for pg_dump but use the same postgresql
      #  *server* image as we've already got it in cache on this host AND we get guaranteed
      #  client / server version alignment.
      template {
        data = <<EOH
#! /usr/bin/env bash

# Heavily opinionated backup script for small PostgreSQL database
#
# Sleep regularly, wake up to detect if we're in one of the right windows for the
# day (typically once a day, but can be adjusted below).  If so, perform a db dump
# then return to sleep.


TARGETDIR=/persistent/BACKUPS

if [ !  -d ${TARGETDIR} ]
then
  mkdir -p ${TARGETDIR}
fi

while [ 1 ]
do
  # Sleep first, as the database is typically not ready on instantiation
  # sleep 1h
  sleep 10s

  HOUR=`date "+%H"`
  TARGETFILE=grafana-obs-postgresql-dump-`date "+%a-%H"`H.sql

  # Multi-value option:   if [ ${HOUR} -eq 23 ] || [ ${HOUR} -eq 08 ] || [ ${HOUR} -eq 16 ] 

  # Daily option:
  #if [ ${HOUR} -eq 23 ]
  #then
    echo Backing up grafana-obs postgresql database at `date` UTC to ${TARGETFILE}
    echo variable substitution for password is {{ key "grafana-obs/POSTGRES_PASSWORD" }}
    touch ${TARGETDIR}/${TARGETFILE}
   
  #fi


done

EOH
        destination = "local/backup-looper.sh"
        perms = "755"
      }
    }    #  end-task  postgresql

  }
}
