// jedd lab - vanilla postgresql image


job "postgresql" {
  datacenters = ["DG"]
  type        = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    # value = "dg-hac-0[123]"
    value = "dg-hac-0[3]"
  }

  group "postgresql" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {

      port "port_postgresql"  {
        # without traefik
        to = 5432
        # with traefik
        # to = 6543
      }

    }

#    volume "vol_postgresql" {
#      type            = "host"
#      source          = "vol_postgresql"
#      read_only       = false
#    }


# TASK PostgreSQL
    # You WILL get these errors:
    #  chown: changing ownership of '/var/lib/postgresql/data/pgdata': Operation not permitted
    #  chmod: changing permissions of '/var/lib/postgresql/data/pgdata': Operation not permitted
    # -- and the same for /var/run/postgres
    # ... with the native docker instance.  It's documented at:
    #  https://github.com/docker-library/docs/blob/master/postgres/README.md#arbitrary---user-notes
    # The /var/run/postgres issue is resolved as below with the config/volumes[] section.

    task "postgresql" {
      driver = "docker"

      user = "postgres:postgres"

      config {
        image = "postgres:12"
#        args = [
#          "--name",
#          "timescalepostgresql"
#        ]
        ports = ["port_postgresql"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

#        volumes = [
#          "local/var-run:/var/run",
          # "/etc/passwd:/etc/passwd",
         # "local/var-lib-pgdata:/var/lib/postgresql/data/pgdata"
#        ]

      }

      env = {
        "POSTGRES_USER"     = "postgres",
        "POSTGRES_PASSWORD" = "password",
        "PGDATA" = "/var/lib/postgresql/data/pgdata"
      }

#      volume_mount {
#        volume      = "vol_postgresql"
#        destination = "/var/lib/postgresql"
#        read_only   = false
#      }

      resources {
        cpu    = 512
        memory = 1024
      }

      service {
        name = "postgresql"

        port = "port_postgresql"

##        check {
##          name     = "PostgreSQL healthcheck"
##          port     = "port_postgresql"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.dg-hac-postgresql.rule=Host(`dg-hac-postgresql.obs.int.jeddi.org`)",
          "traefik.http.routers.dg-hac-postgresql.tls=false",
          "traefik.http.routers.dg-hac-postgresql.entrypoints=http",
        ]
      }
    }    #  END-TASK PostgreSQL




  }
}
