
// jedd lab - prometheus-experimental - throwaway (not to mimir)

job "prometheus-exp" {
  type = "service"
  datacenters = ["DG"]

  group "prometheus-exp" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "dg-hac-0[123]"
    }

    network {
      port "port_prometheus" { }
  	}

    task "prometheus-exp" {
      driver = "docker"

      config {
        image = "https://docker.io/prom/prometheus:v2.45.0"

        args = [
          "--storage.tsdb.retention.time=7d",
          "--storage.tsdb.min-block-duration=1h",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.external-url=http://prometheus-exp.obs.int.jeddi.org",
          "--web.page-title=Prometheus Experimental (throwaway)",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 140
        memory = 400
      }

      service {
        name = "prometheus-http"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-exp.entrypoints=http",
          "traefik.http.routers.prometheus-exp.rule=Host(`prometheus-exp.obs.int.jeddi.org`)",
          "traefik.http.routers.prometheus-exp.tls=false",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
  scrape_interval: 1m
  # This creates a log of *all* queries, and will show up in /metrics as prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log

scrape_configs:
  - job_name: 'prometheus-self'
    static_configs:
      - targets: ['prometheus-exp.obs.int.jeddi.org']
    metrics_path: /metrics






  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
#  - job_name: 'webcheck_oliver'
#    metrics_path: /probe
#    scrape_interval: 3m
#    params:
#      module: [webcheck_oliver]
#    file_sd_configs:
#    # Periodicity to re-read the source file(s) below.
#    - refresh_interval: 3m
#      files: 
#      - "/local/prometheus-configuration/dg-pan-01/blackbox/blackbox_webcheck_oliver.yml"
#    relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: dg-pan-01.int.jeddi.org:9115


# We are unlikely to need or want this, ever
#remote_write:
  #- name: mimir
  #  url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }

}
