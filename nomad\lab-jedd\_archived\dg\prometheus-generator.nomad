
// jedd lab - prometheus-generator - for throwing some data towards a novel mimir

job "prometheus-generator" {

  type = "service"

  datacenters = ["DG"]

  # run this on the HAC so we get traefik
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[123]"
  }
    
  group "prometheus-generator" {

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }


    network {
      port "port_prometheus" {
        to = 9090
      }
  	}

    task "prometheus" {
      driver = "docker"

      config {
        # 2023-01-16 bump to new 2.41.0
        # image = "https://docker.io/prom/prometheus:v2.39.0"
        image = "https://docker.io/prom/prometheus:v2.41.0"

        args = [
          # "--storage.tsdb.retention.time=45d" ,
          # "--storage.tsdb.min-block-duration=1h",
          "--config.file=/etc/prometheus/prometheus.yml",
          
          "--enable-feature=agent",

          "--web.page-title=Prometheus for Generating Cruft",
          # "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.listen-address=0.0.0.0:9090",

          # This lets us his the /-/reload endpoint to get prometheus to reload config
          # (which we mostly mean alert rules if they're pulled in asynchronously to a
          # common directory).  This may be a security concern (DoS, primarily, I guess).
          # Primarily this is needed if we enable 'query_log_file' in the configuration,
          # as prometheus will close/reopen() that file on a reload() (SIGHUP), which in
          # turn needs *this* parameter set.
          "--web.enable-lifecycle",

          # max web connections defaults to 512 
          # No change to VSS on restart
          "--web.max-connections=12",

        ]

        # Memory consumption args to experiment with include:
        # --web.max-connections=512  Maximum number of simultaneous connections.
        # --storage.agent.retention.max-time=STORAGE.AGENT.RETENTION.MAX-TIME
        # --storage.remote.read-max-bytes-in-frame=1048576
        # --query.lookback-delta=5m  The maximum lookback duration for retrieving
        # --query.max-concurrency=20
        # --query.max-samples=50000000


        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 140
        memory = 400
      }

      service {
        name = "prometheus-http"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-generator=http",
          "traefik.http.routers.prometheus-generator.rule=Host(`prometheus-generator.obs.int.jeddi.org`)",
        ]      

        #check {
        #  type = "http"
        #  port = "port_prometheus"
        #  path = "/-/healthy"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    # Do not add this label it will cause duplicate series every time a prometheus scraper restarts
    # nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}

  scrape_interval: 1m
  # This creates a log of *all* queries, and will show up in /metrics as prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus-generator'
    static_configs:
      - targets: ['prometheus-generator.obs.int.jeddi.org']
    metrics_path: /metrics

  - job_name: 'prometheus'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9090']
    metrics_path: /metrics

  - job_name: 'telegraf-static'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9273', 'jarre.int.jeddi.org:9273',
                 'dg-hassio-01.int.jeddi.org:9273', 'freya.int.jeddi.org:9273']

  - job_name: 'esp_iot_weather_station-shed'
    static_configs:
      - targets: ['esp-shed:80']
        labels:
          name: esp-shed
          location: shed
    metrics_path: /metrics

  - job_name: 'esp_iot_weather_station-cabin'
    static_configs:
      - targets: ['esp-cabin:80']
        labels:
          name: esp-cabin
          location: cabin
    metrics_path: /metrics




# We use this prometheus-generator job to generate redundant data to throw at a LTS db, so
# the following will change regularly.

remote_write:

  - name: mimir
    url: "http://mimir-rwb-write.obs.int.jeddi.org/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
        insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }

}
