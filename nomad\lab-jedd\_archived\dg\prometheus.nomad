
// jedd lab dg - prometheus - snapshot
//
// (original in repo jgit:nomad from 2024-05-31 onwards)

job "prometheus" {
  type = "service"
  datacenters = ["DG"]

  group "prometheus" {
    
    # For long-term-storage (LTS) of time-series TSDB
    volume "promvol"  {
      type = "host"
      source = "promvol"
      read_only = false
      }

    # For external configuration (prometheus-configuation, including alert-manager rules)
    volume "promconfvol"  {
      type = "host"
      source = "promconfvol"
      read_only = false
      }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {
      port "prometheus" {
        static = 9090
      }
  	}

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "promvol"
        destination = "/prometheus"
        read_only = false
      }

      volume_mount {
        volume = "promconfvol"
        destination = "/prometheus-configuration"
        read_only = false
        }

      config {
        # 2023-01-16 bump to new 2.41.0
        # image = "https://docker.io/prom/prometheus:v2.39.0"
        image = "https://docker.io/prom/prometheus:v2.41.0"

        args = [
          "--storage.tsdb.retention.time=45d" ,
          "--config.file=/etc/prometheus/prometheus.yml",
          
          "--storage.tsdb.min-block-duration=1h",

          # This lets us his the /-/reload endpoint to get prometheus to reload config
          # (which we mostly mean alert rules if they're pulled in asynchronously to a
          # common directory).  This may be a security concern (DoS, primarily, I guess).
          # Primarily this is needed if we enable 'query_log_file' in the configuration,
          # as prometheus will close/reopen() that file on a reload() (SIGHUP), which in
          # turn needs *this* parameter set.
          "--web.enable-lifecycle",

          # max web connections defaults to 512 
          # No change to VSS on restart
          "--web.max-connections=12",

          # query max concurrency defaults 20 - in theory should match or be less than cores
          # VSS from 10.1 GB to 10.0 on restart
          # Res dropped from 248 MB to 193 on restart but soon (5-10m) returned to the same volume
          "--query.max-concurrency=5",

        ]

        # Memory consumption args to experiment with include:
        # --web.max-connections=512  Maximum number of simultaneous connections.
        # --storage.agent.retention.max-time=STORAGE.AGENT.RETENTION.MAX-TIME
        # --storage.remote.read-max-bytes-in-frame=1048576
        # --query.lookback-delta=5m  The maximum lookback duration for retrieving
        # --query.max-concurrency=20
        # --query.max-samples=50000000

        dns_servers = ["**************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

#        mounts = [
#          {
#            type = "volume"
#            target = "/prometheus-configuration"
#            source = "promconfvol"
#          }
#        ]

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
#          "local/prometheus-configuration/dg-pan-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 140
        memory = 400
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

       check {
         type = "http"
         port = "prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }


#      artifact {
#      source = "git::ssh://<EMAIL>/prometheus-configuration"
#      destination = "local/prometheus-configuration"
#      options {
#        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
#
#        }
#      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    # Do not add this label it will cause duplicate series every time a prometheus scraper restarts
    # nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}
  scrape_interval: 1m
  # This creates a log of *all* queries, and will show up in /metrics as prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9090']
    metrics_path: /metrics

  - job_name: 'alertmanager'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9093']
    metrics_path: /metrics

  - job_name: 'telegraf-static'
    static_configs:
      - targets: [
          'dg-pan-01.int.jeddi.org:9273',
          'dg-hac-01.int.jeddi.org:9273',
          'dg-hac-02.int.jeddi.org:9273',
          'dg-hac-03.int.jeddi.org:9273',
          'dg-beast-01.int.jeddi.org:9273',
          'dg-hac-04.int.jeddi.org:9273',
          'dg-hac-05.int.jeddi.org:9273',
          'dg-hac-06.int.jeddi.org:9273',
          'jarre.int.jeddi.org:9273',
          'tosca.int.jeddi.org:9273',
          'dg-hassio-01.int.jeddi.org:9273',
          'freya.int.jeddi.org:9273'
          ]

  - job_name: 'telegraf-topk'
    static_configs:
      - targets: [ 'dg-pan-01.int.jeddi.org:9274' , 'jarre.int.jeddi.org:9274' ]

  # 2023-01-16 jedd - duplicate using grafana-agent, with custom ports (default = 9090 http / 9091 grpc)
  - job_name: 'grafana-agent'
    metrics_path: /metrics
    static_configs:
      - targets: [ 'dg-pan-01.int.jeddi.org:19090' ,
                   'jarre.int.jeddi.org:19090' ]

  - job_name: 'node-exporter-static'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9100']
        labels:
          agent: node-exporter

  - job_name: 'esp_iot_weather_station-shed'
    static_configs:
      - targets: ['esp-shed:80']
        labels:
          name: esp-shed
          location: shed
    metrics_path: /metrics

  - job_name: 'esp_iot_weather_station-cabin'
    static_configs:
      - targets: ['esp-cabin:80']
        labels:
          name: esp-cabin
          location: cabin
    metrics_path: /metrics

#  - job_name: 'windows-exporter-static'
#    static_configs:
#      - targets: ['shpongle.int.jeddi.org:9182']


  - job_name: 'minio_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9888']
    metrics_path: /minio/v2/metrics/cluster
    params:
      format: ['prometheus']


  - job_name: 'nomad_metrics'
    static_configs:
      # - targets: ['dg-pan-01.int.jeddi.org:4646', 'jarre.int.jeddi.org:4646']
      - targets: ['dg-pan-01.int.jeddi.org:4646',
                  'dg-hac-01.int.jeddi.org:4646',
                  'dg-hac-02.int.jeddi.org:4646',
                  'dg-hac-03.int.jeddi.org:4646',
                  'dg-hac-04.int.jeddi.org:4646',
                  'dg-hac-05.int.jeddi.org:4646',
                  'dg-hac-06.int.jeddi.org:4646',
      ]
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']


  - job_name: 'consul_metrics'
    static_configs:
      # - targets: ['dg-pan-01.int.jeddi.org:8500', 'jarre.int.jeddi.org:8500']
      - targets: ['dg-pan-01.int.jeddi.org:8500']
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']

  - job_name: 'promtail_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:9080',
                  'jarre.int.jeddi.org:9080',
                  'dg-hassio-01.int.jeddi.org:9080'
                 ]
    metrics_path: /metrics

  - job_name: 'loki_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:3100']
    metrics_path: /metrics

  - job_name: 'loki_s3_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:3101']
    metrics_path: /metrics

  - job_name: 'traefik_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:8081']
#                  'jarre.int.jeddi.org:8081']
    metrics_path: /metrics

#  - job_name: 'oracle_metrics'
#    static_configs:
#      - targets: ['dg-pan-01.int.jeddi.org:9161']
#    metrics_path: /metrics

  - job_name: 'grafana_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:3000']
    metrics_path: /metrics

  - job_name: 'nodered_metrics'
    static_configs:
      - targets: ['nodered.int.jeddi.org:8080']
    metrics_path: /metrics

#  - job_name: 'starlink_metrics'
#    static_configs:
#      - targets: ['dg-pan-01.int.jeddi.org:9817']
#    metrics_path: /metrics

  - job_name: 'solar_sungrow_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:8000']
    metrics_path: /
    scrape_interval: 1m

  - job_name: 'postgresql_metrics'
    metrics_path: "/probe"

    static_configs:
      - targets: [
          'dg-pan-01',
          'jarre'
        ]

    # Need: 'exporter-postgresql.obs.int.jeddi.org/probe?target=dg-pan-01&auth_module=dg-pan-01'

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: target

      - source_labels: [__address__]
        target_label: __param_auth_module
      - source_labels: [__param_auth_module]
        target_label: auth_module

      - target_label: __address__
        replacement: exporter-postgresql.obs.int.jeddi.org






#  - job_name: 'cortex_metrics'
#    static_configs:
#      - targets: ['dg-pan-01.int.jeddi.org:9009']
#    metrics_path: /metrics

  - job_name: 'mimir_metrics'
    static_configs:
      - targets: ['dg-pan-01.int.jeddi.org:19009']
        labels:
          # REFER:  https://grafana.com/docs/mimir/latest/operators-guide/visualizing-metrics/requirements/
          # in monolith mode, stick with 'mimir' - in microservices mode we name per-component
          job: mimir
          # cluster and namespace MUST exist for stock dashboards to work - simple choice in a lab env
          cluster: dg
          namespace: dg
    metrics_path: /metrics


  - job_name: 'cadvisor_metrics'
    static_configs:
      # - targets: ['dg-pan-01.int.jeddi.org:12345' , 'jarre.int.jeddi.org:12345']
      - targets: ['dg-pan-01.int.jeddi.org:12345' ]
    metrics_path: /metrics

  - job_name: 'docker_metrics'
    static_configs:
      # - targets: ['dg-pan-01.int.jeddi.org:9323' , 'jarre.int.jeddi.org:9323']
      - targets: ['dg-pan-01.int.jeddi.org:9323' ]
    metrics_path: /metrics





#  - job_name: 'ping_member_servers_static'
#    # @TODO in production we're happy with very low poll rate
#    # scrape_interval: 10m
#    scrape_interval: 1m
#    static_configs:
#      # use cameras, which have no metrics capability
#      - targets: ['**************', '**************', '**************']
#    metrics_path: /probe
#    params:
#      module: [icmp]
#    relabel_configs:
#      - source_labels: [__address__]
#        target_label: __param_target
#      - source_labels: [__param_target]
#        target_label: instance
#      - target_label: __address__
#        # blackbox binds to public ethernet, not loopback
#        replacement: **************:9115


#   - job_name: 'ping_member_servers_sd'
# #   scrape_interval: 1m
#     metrics_path: /probe
#     params:
#       module: ["icmp"]
#     consul_sd_configs:
#       - server: 'dg-pan-01.int.jeddi.org:8500'
#         datacenter: 'dg'
#         services: ['pingtarget']
#     relabel_configs:
#       # - source_labels: [__address__]
#       - source_labels: [__meta_consul_address]
#         target_label: __param_target
#       - source_labels: [__param_target]
#         # target_label: target
#         target_label: instance
# 
#       - target_label: __address__
#         replacement: **************:9115
# 
#         # http://blackbox.obs.nsw.education:9115/probe?target=__address__&type=icmp
# 
#       # - source_labels: [__meta_consul_service_metadata_metrics_path]
#        # target_label: __metrics_path__
#         # blackbox binds to public ethernet, not loopback



  - job_name: 'telegraf'
    consul_sd_configs:
      - server: 'dg-pan-01.int.jeddi.org:8500'
        datacenter: 'dg'
        services: ['telegraf', 'windows']

  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
#  - job_name: 'webcheck_oliver'
#    metrics_path: /probe
#    scrape_interval: 3m
#    params:
#      module: [webcheck_oliver]
#    file_sd_configs:
#    # Periodicity to re-read the source file(s) below.
#    - refresh_interval: 3m
#      files: 
#      - "/local/prometheus-configuration/dg-pan-01/blackbox/blackbox_webcheck_oliver.yml"
#    relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: dg-pan-01.int.jeddi.org:9115





#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

#    2022-08-01 jedd - commenting out to avoid unmarshal errors as 'field params' already set,
#                      which generated an error at start after adding the stanza above for
#                      oliver web checks.
#    metrics_path: /v1/metrics
#    params:
#      format: ['prometheus']


  # using snmp-exporter container
  - job_name: 'snmp-unifi'
    static_configs:
      - targets:
        - ************
        - ************
        - ************
        - ************00
        - **************
        - ************29
    metrics_path: /snmp
    params:
      module: [ubiquiti_unifi]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: **************:9116

  # using snmp-exporter container with custom OID-nominated module
  - job_name: 'snmp-unifi-custom-jedd'
    static_configs:
      - targets:
        - ************
        - ************
        - ************
        labels:
          name: snmp-unifi-custom-jedd
    metrics_path: /snmp
    params:
      module: [ubiquiti_jedd]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: **************:9116
      - source_labels: [ __name__ ]
        regex: 'scrape_duration_seconds|scrape_samples_post_metric_relabeling|scrape_samples_scraped|scrape_series_added|snmp_scrape_duration_seconds|snmp_scrape_pdus_returned|snmp_scrape_walk_duration_seconds'
        action: drop


#  Confluent Cloud ccloud native metrics to DoE DEV cluster
#  - job_name: Confluent Cloud By Kafka ID
#    scrape_interval: 10m
#    scrape_timeout: 1m
#    honor_timestamps: true
#    static_configs:
#      - targets:
#        - api.telemetry.confluent.cloud
#    scheme: https
#    basic_auth:
#      username: "{{ key "ccloud/api-key" }}"
#      password: "{{ key "ccloud/api-secret" }}"
#    metrics_path: /v2/metrics/cloud/export
#    params:
#      "resource.kafka.id":
#        # DEV xfi_cluster_0
#        # Kafka: lkc-1k503
#        # Schema Registry: lsrc-qqz9d
#        # Connector: lcc-2zy52
#        - lkc-1k503
#      #"resource.schema_registry.id":
#      #  - lsrc-qqz9d
#      #"resource.connector.id":
#      #  - lcc-2zy52

#  - job_name: Confluent Cloud By Schema Registry ID
#    scrape_interval: 10m
#    scrape_timeout: 1m
#    honor_timestamps: true
#    static_configs:
#      - targets:
#        - api.telemetry.confluent.cloud
#    scheme: https
#    basic_auth:
#      username: "{{ key "ccloud/api-key" }}"
#      password: "{{ key "ccloud/api-secret" }}"
#    metrics_path: /v2/metrics/cloud/export
#    params:
#      #"resource.kafka.id":
#        # DEV xfi_cluster_0
#        # Kafka: lkc-1k503
#        # Schema Registry: lsrc-qqz9d
#        # Connector: lcc-2zy52
#      #  - lkc-1k503
#      "resource.schema_registry.id":
#        - lsrc-qqz9d
#      #"resource.connector.id":
#      #  - lcc-2zy52

#  - job_name: Confluent Cloud By Connectos ID
#    scrape_interval: 10m
#    scrape_timeout: 1m
#    honor_timestamps: true
#    static_configs:
#      - targets:
#        - api.telemetry.confluent.cloud
#    scheme: https
#    basic_auth:
#      username: "{{ key "ccloud/api-key" }}"
#      password: "{{ key "ccloud/api-secret" }}"
#    metrics_path: /v2/metrics/cloud/export
#    params:
#      #"resource.kafka.id":
#        # DEV xfi_cluster_0
#        # Kafka: lkc-1k503
#        # Schema Registry: lsrc-qqz9d
#        # Connector: lcc-2zy52
#      #  - lkc-1k503
#      #"resource.schema_registry.id":
#      #  - lsrc-qqz9d
#      "resource.connector.id":
#        - lcc-2zy52


rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - static_configs:
      - targets:
        - dg-pan-01.int.jeddi.org:9093






# 2021-08-16 jedd - remote_write to cortex (on localhost)
remote_write:
  #- name: cortex
  #  url: "http://dg-pan-01.int.jeddi.org:9009/api/v1/push"

  # 2022-04-01 jedd - also send to mimir (on localhost)
  - name: mimir
    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"

  # 2024-05-31 jedd - start sending to mimir-rwb (on hac cluster)
  - name: mimir-rwb
    url: "http://mimir-rwb-write.obs.int.jeddi.org/api/v1/push"

  # 2022-10-09 jedd - also send to mimir (on jarre) to test minio s3 backend
  #- name: mimir-jarre
  #  url: "http://jarre.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }

}
