# promtail example from 
#  https://gist.github.com/asrivascrealytee/00856a03518e665754ed87b8224cc8ea

job "promtail-system" {
  datacenters = ["DG"]
  # type = system == forces to run on all nomad nodes
  type        = "system"

  group "promtail-system" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port  "promtail_port" {
        static = 3200
      }
      port  "promtail_9080" {
        static = 9080
      }
    }

    task "promtail" {
      driver = "docker"

      config {
        image = "grafana/promtail:master"

        volumes = [
          "/var/log:/alloc/var/log",
          "/var/lib/docker/containers:/var/lib/docker/containers",
          "local/config.yaml:/etc/promtail/config.yml",
          "/etc/promtail/promtail-targets.yaml:/etc/promtail/promtail-targets.yaml"
        ]

      #  args = [
      #    "-config.file", "local/config.yaml",
      #  ]
        network_mode = "host"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      template {
        data = <<EOH
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

client:
  url: http://**************:3100/api/prom/push

scrape_configs:

# OS (host) logs - the full /var/log collection
- job_name: varlog
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlog
      __path__: /alloc/var/log/*.log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}

- job_name: varlogsubdirs
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlogsubs
      __path__: /alloc/var/log/*/*.log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}

# DOCKER logs - with naive TAGGING (requires external job to provide file),
# specifically this needs to be done and present, and mapped (volume above)
# docker ps --format '- targets: ["{{.ID}}"]\n  labels:\n    container_name: "{{.Names}}"' > /etc/promtail/promtail-targets.yaml
- job_name: dockertagged
  file_sd_configs:
  - files:
    - /etc/promtail/promtail-targets.yaml
  relabel_configs:
  - source_labels: [__address__]
    target_label: container_id
  - source_labels: [container_id]
    target_label: __path__
    replacement: /var/lib/docker/containers/$1*/*.log


EOH

        destination = "local/config.yaml"
      }

      resources {
        cpu    = 50
        memory = 512
      }

      service {
        name = "promtail"
        port = "promtail_port"

        check {
          type     = "http"
          port     = "promtail_9080"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }

      }
    }
  }
}



###  
###  log file from nomad job for promtail-system
###  
###  
###  level=info ts=2021-10-13T08:24:19.333113058Z caller=server.go:239 http=[::]:9080 grpc=[::]:40899 msg="server listening on addresses"
###  level=info ts=2021-10-13T08:24:19.333272585Z caller=main.go:113 msg="Starting Promtail" version="(version=, branch=, revision=)"
###  level=info ts=2021-10-13T08:24:24.333049767Z caller=filetargetmanager.go:255 msg="Adding target" key="{dc=\"DG\", host=\"dg-pan-01\", job=\"varlog\", node_id=\"84a8bfd3-dc3a-6be7-92e6-ff9763dcb5a1\", nodename=\"dg-pan-01\"}"
###  level=info ts=2021-10-13T08:24:24.333725201Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"60f5b64c5bf0\", container_name=\"loki-1f12b41c-8e37-dcf4-9ed6-2bc6e7e4a24e\"}"
###  level=info ts=2021-10-13T08:24:24.33402021Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"bab66594de72\", container_name=\"promtail-c31c02c2-2683-05ae-595e-a0601543389c\"}"
###  level=info ts=2021-10-13T08:24:24.334184214Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"ecd5e4682a43\", container_name=\"prometheus-0c35a7f7-9df8-cfd9-89bf-72e3964fa7a3\"}"
###  level=info ts=2021-10-13T08:24:24.334404834Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"1930a75bb5ef\", container_name=\"cortex-d50ba1ec-7560-a898-b84a-ba91fad0f410\"}"
###  level=info ts=2021-10-13T08:24:24.334630429Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"e83e9b6a0372\", container_name=\"traefik-942bd375-4000-fe1a-8169-158def42bc63\"}"
###  level=info ts=2021-10-13T08:24:24.334840297Z caller=filetargetmanager.go:255 msg="Adding target" key="{container_id=\"64d4eb800c24\", container_name=\"tempo-372a2493-7a5a-b0a3-bc06-e3f28024472e\"}"
###  level=info ts=2021-10-13T08:24:24.335068137Z caller=filetargetmanager.go:271 msg="Removing target" key="{container_id=\"bab66594de72\", container_name=\"promtail-c31c02c2-2683-05ae-595e-a0601543389c\"}"
###  ts=2021-10-13T08:24:24.33525717Z caller=log.go:124 level=info msg="Seeked /var/lib/docker/containers/1930a75bb5ef9c6e097830610efc7e316ae6b3bdfecfc446d7211b208f2c3da1/1930a75bb5ef9c6e097830610efc7e316ae6b3bdfecfc446d7211b208f2c3da1-json.log - &{Offset:0 Whence:0}"
###  ts=2021-10-13T08:24:24.335309628Z caller=log.go:124 level=info msg="Seeked /var/lib/docker/containers/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67-json.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.335352958Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/var/lib/docker/containers/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67-json.log
###  ts=2021-10-13T08:24:24.335762321Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/dpkg.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.335926778Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/var/lib/docker/containers/e83e9b6a0372137c40e3f5edd0f4cd6a1ec193dea972658141f2c5c3d41ccb47/e83e9b6a0372137c40e3f5edd0f4cd6a1ec193dea972658141f2c5c3d41ccb47-json.log
###  level=info ts=2021-10-13T08:24:24.336037103Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/var/lib/docker/containers/1930a75bb5ef9c6e097830610efc7e316ae6b3bdfecfc446d7211b208f2c3da1/1930a75bb5ef9c6e097830610efc7e316ae6b3bdfecfc446d7211b208f2c3da1-json.log
###  level=info ts=2021-10-13T08:24:24.336143208Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/dpkg.log
###  ts=2021-10-13T08:24:24.336222022Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/daemon.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.336456531Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/daemon.log
###  ts=2021-10-13T08:24:24.336526984Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/auth.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.336698491Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/auth.log
###  ts=2021-10-13T08:24:24.336791913Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/user.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.336851862Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/user.log
###  ts=2021-10-13T08:24:24.336922702Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/alternatives.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.337067807Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/alternatives.log
###  ts=2021-10-13T08:24:24.337181128Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/kern.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.337360014Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/kern.log
###  ts=2021-10-13T08:24:24.337429303Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/fontconfig.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.337569818Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/fontconfig.log
###  ts=2021-10-13T08:24:24.337642373Z caller=log.go:124 level=info msg="Seeked /alloc/var/log/mail.log - &{Offset:0 Whence:0}"
###  level=info ts=2021-10-13T08:24:24.337689919Z caller=tailer.go:126 component=tailer msg="tail routine: started" path=/alloc/var/log/mail.log
###  
###  
###  
### level=error ts=2021-10-13T08:25:14.48746323Z caller=client.go:355 component=client host=**************:3100 msg="final error sending batch" status=400 error="server returned HTTP status 400 Bad Request (400): entry with timestamp 2021-10-13 08:25:13.09839659 +0000 UTC ignored, reason: 'entry out of order' for stream: {container_id=\"60f5b64c5bf0\", container_name=\"loki-1f12b41c-8e37-dcf4-9ed6-2bc6e7e4a24e\", filename=\"/var/lib/docker/containers/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67-json.log\"},"

### level=warn ts=2021-10-13T08:25:15.28336397Z caller=client.go:344 component=client host=**************:3100 msg="error sending batch, will retry" status=429 error="server returned HTTP status 429 Too Many Requests (429): entry with timestamp 2021-10-13 08:25:15.221793332 +0000 UTC ignored, reason: 'Per stream rate limit exceeded (limit: 3MB/sec) while attempting to ingest for stream '{dc=\"DG\", filename=\"/alloc/var/log/daemon.log\", host=\"dg-pan-01\", job=\"varlog\", node_id=\"84a8bfd3-dc3a-6be7-92e6-ff9763dcb5a1\", nodename=\"dg-pan-01\"}' totaling 167B, consider splitting a stream via additional labels or contact your Loki administrator to see if the limt can be increased' for stream: {dc=\"DG\", filename=\"/alloc/var/log/daemon.log\", host=\"dg-pan-01\", job=\"varlog\", node_id=\"84a8bfd3-dc3a-6be7-92e6-ff9763dcb5a1\", nodename=\"dg-pan-01\"},"

### level=error ts=2021-10-13T08:25:16.177943745Z caller=client.go:355 component=client host=**************:3100 msg="final error sending batch" status=400 error="server returned HTTP status 400 Bad Request (400): entry with timestamp 2021-10-13 08:25:15.108751768 +0000 UTC ignored, reason: 'entry out of order' for stream: {container_id=\"60f5b64c5bf0\", container_name=\"loki-1f12b41c-8e37-dcf4-9ed6-2bc6e7e4a24e\", filename=\"/var/lib/docker/containers/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67/60f5b64c5bf08ceae61262de161ecabc127c969b10cd9fa87f156874e4cb0a67-json.log\"},"

