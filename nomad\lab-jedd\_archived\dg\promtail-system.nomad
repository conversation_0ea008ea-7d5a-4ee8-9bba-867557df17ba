# promtail example from 
#  https://gist.github.com/asrivascrealytee/00856a03518e665754ed87b8224cc8ea

job "promtail-system" {
  datacenters = ["DG"]
  # type = system == forces to run on all nomad nodes
  type        = "system"

  group "promtail-system" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port  "promtail_port" {
        static = 3200
      }
      port  "promtail_9080" {
        static = 9080
      }
    }

    volume "vol_var_log" {
      type      = "host"
      read_only = true
      source    = "vol_var_log"
    }

    task "promtail" {
      driver = "docker"

      volume_mount {
        volume = "vol_var_log"
        destination = "/var/log"
        read_only = true
      }

      config {
        image = "grafana/promtail:master"

        volumes = [
          # "/var/log:/alloc/var/log",
          # "/var/lib/docker/containers:/var/lib/docker/containers",
          "local/config.yaml:/etc/promtail/config.yml"
        ]

      #  args = [
      #    "-config.file", "local/config.yaml",
      #  ]
        network_mode = "host"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      template {
        data = <<EOH
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

client:
  url: http://**************:3100/api/prom/push

scrape_configs:

# OS (host) logs - the full /var/log collection
- job_name: system
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlogs
      __path__: /alloc/logs/*
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}

# DOCKER logs - no tagging applied - just /var/lib/docker/containers/*/*log
- job_name: docker
  static_configs:
  - targets:
      - localhost
    labels:
      job: dockerlogs
      __path__: /var/lib/docker/containers/*/*log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}
      # we really want NOMAD_TASK_NAME to be picked up *per job* but that won't work

EOH

        destination = "local/config.yaml"
      }

      resources {
        cpu    = 50
        memory = 512
      }

      service {
        name = "promtail"
        port = "promtail_port"

        check {
          type     = "http"
          port     = "promtail_9080"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }

      }
    }
  }
}
