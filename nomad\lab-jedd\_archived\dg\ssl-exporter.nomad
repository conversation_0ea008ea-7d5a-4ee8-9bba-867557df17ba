// jedd lab - ssl-exporter - proxies queries from prometheus jobs (like snmp-exporter)

job "ssl-exporter" {
  type = "service"
  datacenters = ["DG"]

  group "ssl-exporter" {
    network {
      port "ssl-exporter" {
        // Use the default port
        static = 9219
      }
    }

    task "ssl-exporter" {
      driver = "docker"

      config {
        image = "https://docker.io/ribbybibby/ssl-exporter:latest"

        ports = [
          "ssl-exporter"
        ]

        dns_servers = ["192.168.27.123"]

        volumes = [
          "local/ssl_exporter.yaml:/etc/ssl_exporter.yaml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      service {
        name = "ssl-exporter"
        port = "ssl-exporter"

//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }

      }

      template {
        data = <<EOH
modules:
  prober: https
  timeout: 10s

EOH
        destination = "local/ssl_exporter.yaml"
      }
    }
  }
}
