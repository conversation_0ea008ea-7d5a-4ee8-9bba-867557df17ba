
// Jedd lab - SSL Self-Service - a WIP 

variables {
  consul_hostname = "dg-pan-01.int.jeddi.org:8500"
}

job "sslselfservice"  {
  datacenters = ["DG"]
  type = "service"

  group "sslselfservice" {
    network {
      port "port_http" {
        static = 8088
      }
    }

#    volume "vol_sslselfservice"  {
#      type = "host"
#      source = "vol_sslselfservice"
#      read_only = false
#    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    task "sslselfservice" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_sslselfservice"
#        destination = "/mnt/sslselfservice"
#        read_only = false
#      }

      config {
        image = "python:3.9-bullseye"
        dns_servers = ["**************"]
        ports = ["port_http"]
        # command = "/local/prestart.sh"
        command = "/local/sslselfservice/launcher.sh"
        # args  = [ "3000" ]
        volumes = [ 
#          "local/prestart.sh:/prestart.sh",
#          "local/main.py:/main.py",
#          "local/main.py:/main.py",
        ]
        network_mode = "host"
      }

      resources {
        cpu = 500
        memory = 512
      }

      service {
        name = "sslselfservice"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.sslss.rule=Host(`sslss.int.jeddi.org`)",
          "traefik.http.routers.sslss.tls=false",
        ]

      }

      service {
        name = "sslss-web"
        port = "port_http"
        tags = ["traefik.enable=true"]

#        check {
#          type = "http"
#          port = "port_http"
#          path = "/"
#          interval = "15s"
#          timeout = "2s"
#        }
      }

      #  FILE:   prestart.sh
      #  this is our entry point - prepares the environment and launches the python script
      #  MOVED to repository, as our pip pulls are more tightly coupled there.
      template {
        data = <<EOH
#! /usr/bin/env bash
pip install flask beautifulsoup4 requests
python3 /local/sslselfservice/main.py
EOH
        destination = "local/prestart.sh"
        perms = "755"
      }

      #  FILE:   main.py 
      #  actual target - launched by prestart.sh (above)
      artifact {
      source = "git::ssh://<EMAIL>/sslselfservice"
      destination = "local/sslselfservice"
      options {
        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }


    }
  }
}
