// jedd lab - full jaeger app, plus tempo, plus grafana in one job
//


job "tempo_everything" {
  datacenters = ["DG"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "tempo" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port "port_tempo"  {
        static = 3333
      }
      port "port_yaeger_ingest" {
        static = 14268
      }
    }

#    volume "vol_tempo" {
#      type      = "host"
#      read_only = false
#      source    = "vol_tempo"
#    }

    task "tempo" {
      driver = "docker"
      config {
        image = "grafana/tempo"
        args = [
          "-config.file",
          "local/tempo.yaml",
        ]
        ports = ["port_tempo"]
        volumes = [
          "local/load-generator.json:/etc/load-generator.json"
        ]
      }
#      volume_mount {
#        volume      = "loki"
#        destination = "/loki"
#        read_only   = false
#      }
      template {
        data = <<EOH

server:
  http_listen_port: 3333

distributor:
  receivers:                           # this configuration will listen on all ports and protocols that tempo is capable of.
    jaeger:                            # the receives all come from the OpenTelemetry collector.  more configuration information can
      protocols:                       # be found there: https://github.com/open-telemetry/opentelemetry-collector/tree/main/receiver
        thrift_http:                   #
        grpc:                          # for a production deployment you should only enable the receivers you need!
        thrift_binary:
        thrift_compact:
    zipkin:
    otlp:
      protocols:
        http:
        grpc:
    opencensus:

ingester:
  trace_idle_period: 10s               # the length of time after a trace has not received spans to consider it complete and flush it
  max_block_bytes: 1_000_000           # cut the head block when it hits this size or ...
  max_block_duration: 5m               #   this much time passes

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together
    max_block_bytes: 100_000_000       # maximum size of compacted blocks
    block_retention: 1h
    compacted_block_retention: 10m

storage:
  trace:
    backend: local                     # backend configuration to use
    block:
      bloom_filter_false_positive: .05 # bloom filter false positive rate.  lower values create larger filters but fewer false positives
      index_downsample_bytes: 1000     # number of bytes per index record
      encoding: zstd                   # block encoding/compression.  options: none, gzip, lz4-64k, lz4-256k, lz4-1M, lz4, snappy, zstd
    wal:
      path: /tmp/tempo/wal             # where to store the the wal locally
      encoding: none                   # wal encoding/compression.  options: none, gzip, lz4-64k, lz4-256k, lz4-1M, lz4, snappy, zstd
    local:
      path: /tmp/tempo/blocks
    pool:
      max_workers: 100                 # worker pool determines the number of parallel requests to the object store backend
      queue_depth: 10000


EOH
        destination = "local/tempo.yaml"
      }
      resources {
        cpu    = 512
        memory = 512
      }
      service {
        name = "tempo"
        port = "port_tempo"
        check {
          name     = "Tempo healthcheck"
          port     = "port_tempo"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }
    }



    task "tempo-load-generator" {
      driver = "docker"
      env = {
        "TOPOLOGY_FILE" = "/etc/load-generator.json",
        "JAEGER_COLLECTOR_URL" = "http://localhost:14268"
      }
      config {
        image = "omnition/synthetic-load-generator:1.0.25"
        volumes = [
          "local/load-generator.json:/etc/load-generator.json"
        ]
      }

      template {
        source = "./shared/load-generator.json"
        destination = "/etc/load-generator.json"
      }
#      volume_mount {
#        volume      = "loki"
#        destination = "/loki"
#        read_only   = false
#      }

      resources {
        cpu    = 512
        memory = 512
      }
    }



  }
}
