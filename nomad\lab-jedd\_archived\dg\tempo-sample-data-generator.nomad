// jedd lab - sample data generator only, for tempo
//


job "tempo_sample_data_generator" {
  datacenters = ["DG"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "tempo" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }




    task "tempo-load-generator" {
      driver = "docker"
      env = {
        "TOPOLOGY_FILE" = "/etc/load-generator.json",
        "JAEGER_COLLECTOR_URL" = "http://tempo.int.jeddi.org:14268"
      }
      config {
        image = "omnition/synthetic-load-generator:1.0.25"
        volumes = [
          "/tmp/load-generator.json:/etc/load-generator.json"
        ]
      }

      resources {
        cpu    = 512
        memory = 512
      }
    }

  }
}
