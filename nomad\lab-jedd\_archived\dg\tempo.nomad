// jedd lab - tempo nomad job - standalone tempo instance
//


job "tempo" {
  datacenters = ["DG"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "tempo" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_tempo" {
      type = "host"
      source = "vol_tempo"
      read_only = false
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "dg-pan-01"
    }

    network {

      port "port_tempo"  {
        static = 3333
      }

      port "port_jaeger_ingest"  {
        static = 14268
      }

    }

    task "tempo" {
      driver = "docker"

      volume_mount {
        volume      = "vol_tempo"
        destination = "/mnt/tempo"
        read_only   = false
      }

      config {
        image = "grafana/tempo:2.2.0"

        dns_servers = ["**************"]

        ports = ["port_tempo", "port_jaeger_ingest"]

        args = [
          "-config.file",
          "local/tempo.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }


      template {
        data = <<EOH

server:
  http_listen_port: 3333

distributor:
  receivers:                           # this configuration will listen on all ports and protocols that tempo is capable of.
    jaeger:                            # the receives all come from the OpenTelemetry collector.  more configuration information can
      protocols:                       # be found there: https://github.com/open-telemetry/opentelemetry-collector/tree/main/receiver
        thrift_http:                   #
        grpc:                          # for a production deployment you should only enable the receivers you need!
        thrift_binary:
        thrift_compact:
    zipkin:
    otlp:
      protocols:
        http:
        grpc:
    opencensus:

ingester:
  trace_idle_period: 10s               # the length of time after a trace has not received spans to consider it complete and flush it
  max_block_bytes: 1_000_000           # cut the head block when it hits this size or ...
  max_block_duration: 5m               #   this much time passes

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together
    max_block_bytes: 100_000_000       # maximum size of compacted blocks
    block_retention: 1h
    compacted_block_retention: 10m

storage:
  trace:
    backend: local                     # backend configuration to use
    block:
      bloom_filter_false_positive: .05 # bloom filter false positive rate.  lower values create larger filters but fewer false positives
      # index_downsample_bytes: 1000     # number of bytes per index record
      # encoding: zstd                   # block encoding/compression.  options: none, gzip, lz4-64k, lz4-256k, lz4-1M, lz4, snappy, zstd
    wal:
      path: /mnt/tempo/wal             # where to store the the wal locally
      # encoding: none                   # wal encoding/compression.  options: none, gzip, lz4-64k, lz4-256k, lz4-1M, lz4, snappy, zstd
    local:
      path: /mnt/tempo/blocks
    pool:
      max_workers: 100                 # worker pool determines the number of parallel requests to the object store backend
      queue_depth: 10000


EOH
        destination = "local/tempo.yaml"
      }

      resources {
        cpu    = 512
        memory = 512
      }

      service {
        name = "tempo"
        port = "port_tempo"

        check {
          name     = "Tempo healthcheck"
          port     = "port_tempo"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo.rule=Host(`tempo.int.jeddi.org`)",
          "traefik.http.routers.tempo.tls=false",
        ]

      }

    }
  }
}
