// jedd lab - traefik load balancer - based on learn.hashicorp example code





// THIS ONE uses a static port defined in the injected configuration TOML
// file that points vault.obs.int.jeddi.org to 'localhost' on tcp/8000





# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_traefik = "traefik:v2.10.5"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "traefik" {
  datacenters = ["DG"]
  type        = "system"

  group "traefik" {
    count = 1

    network {
      port "port_http" {
        static = 80
      }

      port "port_https" {
        static = 443
      }

      port "port_api" {
        static = 8081
      }

      # This is my hard-coded vault port on dg-hac-0[123]
      port "port_vault" {
        static = 8000
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "port_http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "dg-hac-0[123]"
      value = "dg-hac-0[123456]"
    }

    task "traefik" {
      driver = "docker"

      config {
        image = "${var.image_traefik}"

        network_mode = "host"

        args = [
          "--ping=true",
          "--accesslog=true",
          "--log.format=json",
          "--api=true",
          "--metrics=true",
          "--metrics.prometheus=true",
          "--metrics.prometheus.entryPoint=metrics",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
          "local/traefik-provider-vault.yaml:/etc/traefik/dynamic/traefik-provider-vault.yaml",
        ]
      }


      #template {
      #  source = file("assets/traefik-provider-empty.toml")
      #  destination = "local/traefik-provider-empty.toml"
      #  perms = "755"
      #}

      template {
        data = file("assets/traefik-provider-vault.yaml")
        destination = "local/traefik-provider-vault.yaml"
        perms = "644"
      }

      template {
        data = file("assets/traefik.toml")
        destination = "local/traefik.toml"
        perms = "755"
      }



      resources {
        cpu    = 100
        memory = 128
        memory_max = 256
      }
    }
  }
}

