
//  exporter-starlink for prometheus - for jedd's lab (dg)

job "exporter-starlink" {
  type = "service"
  datacenters = ["DG"]

  group "exporter-starlink" {
    network {
      port "port-starlink" {
        static = 9817 
      }
    }

    task "exporter-starlink" {
      driver = "docker"

      config {
        ports = [ "port-starlink" ]
        # dns_servers = [ "**************" ]

        image = "danopstech/starlink_exporter"
        args = [
          "-address",
          "*************:9200",
          "-port",
          "9817",
          # "${NOMAD_PORT_port-starlink}"
        ]


      }

      service {
        name = "exporter-starlink"
        port = "port-starlink"

//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }
      }
    }



  }
}
