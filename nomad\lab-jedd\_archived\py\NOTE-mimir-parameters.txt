# ./mimir  -help

Usage of ./mimir:

  -activity-tracker.filepath string
        File where ongoing activities are stored. If empty, activity tracking is disabled. (default "./metrics-activity.log")

  -alertmanager-storage.azure.account-key string
        Azure storage account key

  -alertmanager-storage.azure.account-name string
        Azure storage account name

  -alertmanager-storage.azure.container-name string
        Azure storage container name

  -alertmanager-storage.azure.endpoint-suffix string
        Azure storage endpoint suffix without schema. The account name will be prefixed to this value to create the FQDN. If set to empty string, default endpoint suffix is used.

  -alertmanager-storage.backend string
        Backend storage to use. Supported backends are: s3, gcs, azure, swift, filesystem, local. (default "filesystem")

  -alertmanager-storage.filesystem.dir string
        Local filesystem storage directory. (default "alertmanager")

  -alertmanager-storage.gcs.bucket-name string
        GCS bucket name

  -alertmanager-storage.gcs.service-account string
        JSON either from a Google Developers Console client_credentials.json file, or a Google Developers service account key. Needs to be valid JSON, not a filesystem path. If empty, fallback to Google default logic: 
        1. A JSON file whose path is specified by the GOOGLE_APPLICATION_CREDENTIALS environment variable. For workload identity federation, refer to https://cloud.google.com/iam/docs/how-to#using-workload-identity-federation on how to generate the JSON configuration file for on-prem/non-Google cloud platforms.
        2. A JSON file in a location known to the gcloud command-line tool: $HOME/.config/gcloud/application_default_credentials.json.
        3. On Google Compute Engine it fetches credentials from the metadata server.

  -alertmanager-storage.local.path string
        Path at which alertmanager configurations are stored.

  -alertmanager-storage.s3.access-key-id string
        S3 access key ID

  -alertmanager-storage.s3.bucket-name string
        S3 bucket name

  -alertmanager-storage.s3.endpoint string
        The S3 bucket endpoint. It could be an AWS S3 endpoint listed at https://docs.aws.amazon.com/general/latest/gr/s3.html or the address of an S3-compatible service in hostname:port format.

  -alertmanager-storage.s3.region string
        S3 region. If unset, the client will issue a S3 GetBucketLocation API call to autodetect it.

  -alertmanager-storage.s3.secret-access-key string
        S3 secret access key

  -alertmanager-storage.s3.sse.kms-encryption-context string
        KMS Encryption Context used for object encryption. It expects JSON formatted string.

  -alertmanager-storage.s3.sse.kms-key-id string
        KMS Key ID used to encrypt objects in S3

  -alertmanager-storage.s3.sse.type string
        Enable AWS Server Side Encryption. Supported values: SSE-KMS, SSE-S3.

  -alertmanager-storage.swift.auth-url string
        OpenStack Swift authentication URL

  -alertmanager-storage.swift.auth-version int
        OpenStack Swift authentication API version. 0 to autodetect.

  -alertmanager-storage.swift.container-name string
        Name of the OpenStack Swift container to put chunks in.

  -alertmanager-storage.swift.domain-id string
        OpenStack Swift user's domain ID.

  -alertmanager-storage.swift.domain-name string
        OpenStack Swift user's domain name.

  -alertmanager-storage.swift.password string
        OpenStack Swift API key.

  -alertmanager-storage.swift.project-domain-id string
        ID of the OpenStack Swift project's domain (v3 auth only), only needed if it differs the from user domain.

  -alertmanager-storage.swift.project-domain-name string
        Name of the OpenStack Swift project's domain (v3 auth only), only needed if it differs from the user domain.

  -alertmanager-storage.swift.project-id string
        OpenStack Swift project ID (v2,v3 auth only).

  -alertmanager-storage.swift.project-name string
        OpenStack Swift project name (v2,v3 auth only).

  -alertmanager-storage.swift.region-name string
        OpenStack Swift Region to use (v2,v3 auth only).

  -alertmanager-storage.swift.user-domain-id string
        OpenStack Swift user's domain ID.

  -alertmanager-storage.swift.user-domain-name string
        OpenStack Swift user's domain name.

  -alertmanager-storage.swift.user-id string
        OpenStack Swift user ID.

  -alertmanager-storage.swift.username string
        OpenStack Swift username.

  -alertmanager.configs.fallback string
        Filename of fallback config to use if none specified for instance.

  -alertmanager.max-alerts-count int
        Maximum number of alerts that a single tenant can have. Inserting more alerts will fail with a log message and metric increment. 0 = no limit.

  -alertmanager.max-alerts-size-bytes int
        Maximum total size of alerts that a single tenant can have, alert size is the sum of the bytes of its labels, annotations and generatorURL. Inserting more alerts will fail with a log message and metric increment. 0 = no limit.

  -alertmanager.max-config-size-bytes int
        Maximum size of configuration file for Alertmanager that tenant can upload via Alertmanager API. 0 = no limit.

  -alertmanager.max-dispatcher-aggregation-groups int
        Maximum number of aggregation groups in Alertmanager's dispatcher that a tenant can have. Each active aggregation group uses single goroutine. When the limit is reached, dispatcher will not dispatch alerts that belong to additional aggregation groups, but existing groups will keep working properly. 0 = no limit.

  -alertmanager.max-template-size-bytes int
        Maximum size of single template in tenant's Alertmanager configuration uploaded via Alertmanager API. 0 = no limit.

  -alertmanager.max-templates-count int
        Maximum number of templates in tenant's Alertmanager configuration uploaded via Alertmanager API. 0 = no limit.

  -alertmanager.notification-rate-limit float
        Per-tenant rate limit for sending notifications from Alertmanager in notifications/sec. 0 = rate limit disabled. Negative value = no notifications are allowed.

  -alertmanager.notification-rate-limit-per-integration value
        Per-integration notification rate limits. Value is a map, where each key is integration name and value is a rate-limit (float). On command line, this map is given in JSON format. Rate limit has the same meaning as -alertmanager.notification-rate-limit, but only applies for specific integration. Allowed integration names: webhook, email, pagerduty, opsgenie, wechat, slack, victorops, pushover, sns. (default {})

  -alertmanager.receivers-firewall-block-cidr-networks value
        Comma-separated list of network CIDRs to block in Alertmanager receiver integrations.

  -alertmanager.receivers-firewall-block-private-addresses
        True to block private and local addresses in Alertmanager receiver integrations. It blocks private addresses defined by  RFC 1918 (IPv4 addresses) and RFC 4193 (IPv6 addresses), as well as loopback, local unicast and local multicast addresses.

  -alertmanager.sharding-ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -alertmanager.sharding-ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -alertmanager.sharding-ring.etcd.password string
        Etcd password.

  -alertmanager.sharding-ring.etcd.username string
        Etcd username.

  -alertmanager.sharding-ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -alertmanager.storage.path string
        Directory to store Alertmanager state and temporarily configuration files. The content of this directory is not required to be persisted between restarts unless Alertmanager replication has been disabled. (default "./data-alertmanager/")

  -alertmanager.web.external-url value
        The URL under which Alertmanager is externally reachable (eg. could be different than -http.alertmanager-http-prefix in case Alertmanager is served via a reverse proxy). This setting is used both to configure the internal requests router and to generate links in alert templates. If the external URL has a path portion, it will be used to prefix all HTTP endpoints served by Alertmanager, both the UI and API. (default http://localhost:8080/alertmanager)

  -auth.multitenancy-enabled
        When set to true, incoming HTTP requests must specify tenant ID in HTTP X-Scope-OrgId header. When set to false, tenant ID from -auth.no-auth-tenant is used instead. (default true)

  -blocks-storage.azure.account-key string
        Azure storage account key

  -blocks-storage.azure.account-name string
        Azure storage account name

  -blocks-storage.azure.container-name string
        Azure storage container name

  -blocks-storage.azure.endpoint-suffix string
        Azure storage endpoint suffix without schema. The account name will be prefixed to this value to create the FQDN. If set to empty string, default endpoint suffix is used.

  -blocks-storage.backend string
        Backend storage to use. Supported backends are: s3, gcs, azure, swift, filesystem. (default "filesystem")

  -blocks-storage.bucket-store.bucket-index.enabled
        If enabled, queriers and store-gateways discover blocks by reading a bucket index (created and updated by the compactor) instead of periodically scanning the bucket. (default true)

  -blocks-storage.bucket-store.chunks-cache.backend string
        Backend for chunks cache, if not empty. Supported values: memcached.

  -blocks-storage.bucket-store.chunks-cache.memcached.addresses string
        Comma separated list of memcached addresses. Supported prefixes are: dns+ (looked up as an A/AAAA query), dnssrv+ (looked up as a SRV query, dnssrvnoa+ (looked up as a SRV query, with no A/AAAA lookup made after that).

  -blocks-storage.bucket-store.chunks-cache.memcached.timeout duration
        The socket read/write timeout. (default 200ms)

  -blocks-storage.bucket-store.index-cache.backend string
        The index cache backend type. Supported values: inmemory, memcached. (default "inmemory")

  -blocks-storage.bucket-store.index-cache.inmemory.max-size-bytes uint
        Maximum size in bytes of in-memory index cache used to speed up blocks index lookups (shared between all tenants). (default 1073741824)

  -blocks-storage.bucket-store.index-cache.memcached.addresses string
        Comma separated list of memcached addresses. Supported prefixes are: dns+ (looked up as an A/AAAA query), dnssrv+ (looked up as a SRV query, dnssrvnoa+ (looked up as a SRV query, with no A/AAAA lookup made after that).

  -blocks-storage.bucket-store.index-cache.memcached.timeout duration
        The socket read/write timeout. (default 200ms)

  -blocks-storage.bucket-store.metadata-cache.backend string
        Backend for metadata cache, if not empty. Supported values: memcached.

  -blocks-storage.bucket-store.metadata-cache.memcached.addresses string
        Comma separated list of memcached addresses. Supported prefixes are: dns+ (looked up as an A/AAAA query), dnssrv+ (looked up as a SRV query, dnssrvnoa+ (looked up as a SRV query, with no A/AAAA lookup made after that).

  -blocks-storage.bucket-store.metadata-cache.memcached.timeout duration
        The socket read/write timeout. (default 200ms)

  -blocks-storage.bucket-store.sync-dir string
        Directory to store synchronized TSDB index headers. This directory is not required to be persisted between restarts, but it's highly recommended in order to improve the store-gateway startup time. (default "./tsdb-sync/")

  -blocks-storage.filesystem.dir string
        Local filesystem storage directory. (default "blocks")

  -blocks-storage.gcs.bucket-name string
        GCS bucket name

  -blocks-storage.gcs.service-account string
        JSON either from a Google Developers Console client_credentials.json file, or a Google Developers service account key. Needs to be valid JSON, not a filesystem path. If empty, fallback to Google default logic: 
        1. A JSON file whose path is specified by the GOOGLE_APPLICATION_CREDENTIALS environment variable. For workload identity federation, refer to https://cloud.google.com/iam/docs/how-to#using-workload-identity-federation on how to generate the JSON configuration file for on-prem/non-Google cloud platforms.
        2. A JSON file in a location known to the gcloud command-line tool: $HOME/.config/gcloud/application_default_credentials.json.
        3. On Google Compute Engine it fetches credentials from the metadata server.

  -blocks-storage.s3.access-key-id string
        S3 access key ID

  -blocks-storage.s3.bucket-name string
        S3 bucket name

  -blocks-storage.s3.endpoint string
        The S3 bucket endpoint. It could be an AWS S3 endpoint listed at https://docs.aws.amazon.com/general/latest/gr/s3.html or the address of an S3-compatible service in hostname:port format.

  -blocks-storage.s3.region string
        S3 region. If unset, the client will issue a S3 GetBucketLocation API call to autodetect it.

  -blocks-storage.s3.secret-access-key string
        S3 secret access key

  -blocks-storage.s3.sse.kms-encryption-context string
        KMS Encryption Context used for object encryption. It expects JSON formatted string.

  -blocks-storage.s3.sse.kms-key-id string
        KMS Key ID used to encrypt objects in S3

  -blocks-storage.s3.sse.type string
        Enable AWS Server Side Encryption. Supported values: SSE-KMS, SSE-S3.

  -blocks-storage.swift.auth-url string
        OpenStack Swift authentication URL

  -blocks-storage.swift.auth-version int
        OpenStack Swift authentication API version. 0 to autodetect.

  -blocks-storage.swift.container-name string
        Name of the OpenStack Swift container to put chunks in.

  -blocks-storage.swift.domain-id string
        OpenStack Swift user's domain ID.

  -blocks-storage.swift.domain-name string
        OpenStack Swift user's domain name.

  -blocks-storage.swift.password string
        OpenStack Swift API key.

  -blocks-storage.swift.project-domain-id string
        ID of the OpenStack Swift project's domain (v3 auth only), only needed if it differs the from user domain.

  -blocks-storage.swift.project-domain-name string
        Name of the OpenStack Swift project's domain (v3 auth only), only needed if it differs from the user domain.

  -blocks-storage.swift.project-id string
        OpenStack Swift project ID (v2,v3 auth only).

  -blocks-storage.swift.project-name string
        OpenStack Swift project name (v2,v3 auth only).

  -blocks-storage.swift.region-name string
        OpenStack Swift Region to use (v2,v3 auth only).

  -blocks-storage.swift.user-domain-id string
        OpenStack Swift user's domain ID.

  -blocks-storage.swift.user-domain-name string
        OpenStack Swift user's domain name.

  -blocks-storage.swift.user-id string
        OpenStack Swift user ID.

  -blocks-storage.swift.username string
        OpenStack Swift username.

  -blocks-storage.tsdb.dir string
        Directory to store TSDBs (including WAL) in the ingesters. This directory is required to be persisted between restarts. (default "./tsdb/")

  -blocks-storage.tsdb.retention-period duration
        TSDB blocks retention in the ingester before a block is removed, relative to the newest block written for the tenant. This should be larger than the -blocks-storage.tsdb.block-ranges-period, -querier.query-store-after and large enough to give store-gateways and queriers enough time to discover newly uploaded blocks. (default 24h0m0s)

  -compactor.block-upload-enabled
        Enable block upload API for the tenant.

  -compactor.blocks-retention-period value
        Delete blocks containing samples older than the specified retention period. 0 to disable.

  -compactor.compactor-tenant-shard-size int
        Max number of compactors that can compact blocks for single tenant. 0 to disable the limit and use all compactors.

  -compactor.data-dir string
        Directory to temporarily store blocks during compaction. This directory is not required to be persisted between restarts. (default "./data-compactor/")

  -compactor.ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -compactor.ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -compactor.ring.etcd.password string
        Etcd password.

  -compactor.ring.etcd.username string
        Etcd username.

  -compactor.ring.instance-interface-names value
        List of network interface names to look up when finding the instance IP address. (default [<private network interfaces>])

  -compactor.ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -compactor.split-and-merge-shards int
        The number of shards to use when splitting blocks. 0 to disable splitting.

  -compactor.split-groups int
        Number of groups that blocks for splitting should be grouped into. Each group of blocks is then split separately. Number of output split shards is controlled by -compactor.split-and-merge-shards. (default 1)

  -config.expand-env
        Expands ${var} or $var in config according to the values of the environment variables.

  -config.file value
        Configuration file to load.

  -distributor.ha-tracker.cluster string
        Prometheus label to look for in samples to identify a Prometheus HA cluster. (default "cluster")

  -distributor.ha-tracker.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -distributor.ha-tracker.enable
        Enable the distributors HA tracker so that it can accept samples from Prometheus HA replicas gracefully (requires labels).

  -distributor.ha-tracker.enable-for-all-users
        Flag to enable, for all tenants, handling of samples with external labels identifying replicas in an HA Prometheus setup.

  -distributor.ha-tracker.etcd.endpoints value
        The etcd endpoints to connect to.

  -distributor.ha-tracker.etcd.password string
        Etcd password.

  -distributor.ha-tracker.etcd.username string
        Etcd username.

  -distributor.ha-tracker.max-clusters int
        Maximum number of clusters that HA tracker will keep track of for a single tenant. 0 to disable the limit.

  -distributor.ha-tracker.replica string
        Prometheus label to look for in samples to identify a Prometheus HA replica. (default "__replica__")

  -distributor.ha-tracker.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "consul")

  -distributor.ingestion-burst-size int
        Per-tenant allowed ingestion burst size (in number of samples). (default 200000)

  -distributor.ingestion-rate-limit float
        Per-tenant ingestion rate limit in samples per second. (default 10000)

  -distributor.ingestion-tenant-shard-size int
        The tenant's shard size used by shuffle-sharding. Must be set both on ingesters and distributors. 0 disables shuffle sharding.

  -distributor.ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -distributor.ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -distributor.ring.etcd.password string
        Etcd password.

  -distributor.ring.etcd.username string
        Etcd username.

  -distributor.ring.instance-interface-names value
        List of network interface names to look up when finding the instance IP address. (default [<private network interfaces>])

  -distributor.ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -h
        Print basic help.

  -help
        Print basic help.

  -help-all
        Print help, also including advanced and experimental parameters.

  -ingester.max-global-metadata-per-metric int
        The maximum number of metadata per metric, across the cluster. 0 to disable.

  -ingester.max-global-metadata-per-user int
        The maximum number of active metrics with metadata per tenant, across the cluster. 0 to disable.

  -ingester.max-global-series-per-metric int
        The maximum number of active series per metric name, across the cluster before replication. 0 to disable. (default 20000)

  -ingester.max-global-series-per-user int
        The maximum number of active series per tenant, across the cluster before replication. 0 to disable. (default 150000)

  -ingester.ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -ingester.ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -ingester.ring.etcd.password string
        Etcd password.

  -ingester.ring.etcd.username string
        Etcd username.

  -ingester.ring.replication-factor int
        Number of ingesters that each time series is replicated to. This option needs be set on ingesters, distributors, queriers and rulers when running in microservices mode. (default 3)

  -ingester.ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -ingester.ring.tokens-file-path string
        File path where tokens are stored. If empty, tokens are not stored at shutdown and restored at startup.

  -ingester.ring.zone-awareness-enabled
        True to enable the zone-awareness and replicate ingested samples across different availability zones. This option needs be set on ingesters, distributors, queriers and rulers when running in microservices mode.

  -log.format value
        Output log messages in the given format. Valid formats: [logfmt, json] (default logfmt)

  -log.level value
        Only log messages with the given severity or above. Valid levels: [debug, info, warn, error] (default info)

  -memberlist.abort-if-join-fails
        If this node fails to join memberlist cluster, abort.

  -memberlist.advertise-addr string
        Gossip address to advertise to other members in the cluster. Used for NAT traversal.

  -memberlist.advertise-port int
        Gossip port to advertise to other members in the cluster. Used for NAT traversal. (default 7946)

  -memberlist.bind-addr value
        IP address to listen on for gossip messages. Multiple addresses may be specified. Defaults to 0.0.0.0

  -memberlist.bind-port int
        Port to listen on for gossip messages. (default 7946)

  -memberlist.join value
        Other cluster members to join. Can be specified multiple times. It can be an IP, hostname or an entry specified in the DNS Service Discovery format.

  -modules
        List available values that can be used as target.

  -print.config
        Print the config and exit.

  -querier.cardinality-analysis-enabled
        Enables endpoints used for cardinality analysis.

  -querier.frontend-address string
        Address of the query-frontend component, in host:port format. Only one of -querier.frontend-address or -querier.scheduler-address can be set. If neither is set, queries are only received via HTTP endpoint.

  -querier.label-names-and-values-results-max-size-bytes int
        Maximum size in bytes of distinct label names and values. When querier receives response from ingester, it merges the response with responses from other ingesters. This maximum size limit is applied to the merged(distinct) results. If the limit is reached, an error is returned. (default 419430400)

  -querier.label-values-max-cardinality-label-names-per-request int
        Maximum number of label names allowed to be queried in a single /api/v1/cardinality/label_values API call. (default 100)

  -querier.max-concurrent int
        The maximum number of concurrent queries. This config option should be set on query-frontend too when query sharding is enabled. (default 20)

  -querier.max-fetched-chunk-bytes-per-query int
        The maximum size of all chunks in bytes that a query can fetch from each ingester and storage. This limit is enforced in the querier and ruler. 0 to disable.

  -querier.max-fetched-chunks-per-query int
        Maximum number of chunks that can be fetched in a single query from ingesters and long-term storage. This limit is enforced in the querier, ruler and store-gateway. 0 to disable. (default 2000000)

  -querier.max-fetched-series-per-query int
        The maximum number of unique series for which a query can fetch samples from each ingesters and storage. This limit is enforced in the querier and ruler. 0 to disable

  -querier.max-query-lookback value
        Limit how long back data (series and metadata) can be queried, up until <lookback> duration ago. This limit is enforced in the query-frontend, querier and ruler. If the requested time range is outside the allowed range, the request will not fail but will be manipulated to only query data within the allowed time range. 0 to disable.

  -querier.max-query-parallelism int
        Maximum number of split (by time) or partial (by shard) queries that will be scheduled in parallel by the query-frontend for a single input query. This limit is introduced to have a fairer query scheduling and avoid a single query over a large time range saturating all available queriers. (default 14)

  -querier.max-samples int
        Maximum number of samples a single query can load into memory. This config option should be set on query-frontend too when query sharding is enabled. (default 50000000)

  -querier.scheduler-address string
        Address of the query-scheduler component, in host:port format. Only one of -querier.frontend-address or -querier.scheduler-address can be set. If neither is set, queries are only received via HTTP endpoint.

  -querier.timeout duration
        The timeout for a query. This config option should be set on query-frontend too when query sharding is enabled. This also applies to queries evaluated by the ruler (internally or remotely). (default 2m0s)

  -query-frontend.align-querier-with-step
        Mutate incoming queries to align their start and end with their step.

  -query-frontend.cache-results
        Cache query results.

  -query-frontend.log-queries-longer-than duration
        Log queries that are slower than the specified duration. Set to 0 to disable. Set to < 0 to enable on all queries.

  -query-frontend.max-queriers-per-tenant int
        Maximum number of queriers that can handle requests for a single tenant. If set to 0 or value higher than number of available queriers, *all* queriers will handle requests for the tenant. Each frontend (or query-scheduler, if used) will select the same set of queriers for the same tenant (given that all queriers are connected to all frontends / query-schedulers). This option only works with queriers connecting to the query-frontend / query-scheduler, not when using downstream URL.

  -query-frontend.parallelize-shardable-queries
        True to enable query sharding.

  -query-frontend.query-sharding-max-sharded-queries int
        The max number of sharded queries that can be run for a given received query. 0 to disable limit. (default 128)

  -query-frontend.query-sharding-total-shards int
        The amount of shards to use when doing parallelisation via query sharding by tenant. 0 to disable query sharding for tenant. Query sharding implementation will adjust the number of query shards based on compactor shards. This allows querier to not search the blocks which cannot possibly have the series for given query shard. (default 16)

  -query-frontend.results-cache.backend string
        Backend for query-frontend results cache, if not empty. Supported values: [memcached].

  -query-frontend.results-cache.compression string
        Enable cache compression, if not empty. Supported values are: snappy.

  -query-frontend.results-cache.memcached.addresses string
        Comma separated list of memcached addresses. Supported prefixes are: dns+ (looked up as an A/AAAA query), dnssrv+ (looked up as a SRV query, dnssrvnoa+ (looked up as a SRV query, with no A/AAAA lookup made after that).

  -query-frontend.results-cache.memcached.timeout duration
        The socket read/write timeout. (default 200ms)

  -query-frontend.scheduler-address string
        DNS hostname used for finding query-schedulers.

  -query-scheduler.max-outstanding-requests-per-tenant int
        Maximum number of outstanding requests per tenant per query-scheduler. In-flight requests above this limit will fail with HTTP response status code 429. (default 100)

  -ruler-storage.azure.account-key string
        Azure storage account key

  -ruler-storage.azure.account-name string
        Azure storage account name

  -ruler-storage.azure.container-name string
        Azure storage container name

  -ruler-storage.azure.endpoint-suffix string
        Azure storage endpoint suffix without schema. The account name will be prefixed to this value to create the FQDN. If set to empty string, default endpoint suffix is used.

  -ruler-storage.backend string
        Backend storage to use. Supported backends are: s3, gcs, azure, swift, filesystem, local. (default "filesystem")

  -ruler-storage.filesystem.dir string
        Local filesystem storage directory. (default "ruler")

  -ruler-storage.gcs.bucket-name string
        GCS bucket name

  -ruler-storage.gcs.service-account string
        JSON either from a Google Developers Console client_credentials.json file, or a Google Developers service account key. Needs to be valid JSON, not a filesystem path. If empty, fallback to Google default logic: 
        1. A JSON file whose path is specified by the GOOGLE_APPLICATION_CREDENTIALS environment variable. For workload identity federation, refer to https://cloud.google.com/iam/docs/how-to#using-workload-identity-federation on how to generate the JSON configuration file for on-prem/non-Google cloud platforms.
        2. A JSON file in a location known to the gcloud command-line tool: $HOME/.config/gcloud/application_default_credentials.json.
        3. On Google Compute Engine it fetches credentials from the metadata server.

  -ruler-storage.local.directory string
        Directory to scan for rules

  -ruler-storage.s3.access-key-id string
        S3 access key ID

  -ruler-storage.s3.bucket-name string
        S3 bucket name

  -ruler-storage.s3.endpoint string
        The S3 bucket endpoint. It could be an AWS S3 endpoint listed at https://docs.aws.amazon.com/general/latest/gr/s3.html or the address of an S3-compatible service in hostname:port format.

  -ruler-storage.s3.region string
        S3 region. If unset, the client will issue a S3 GetBucketLocation API call to autodetect it.

  -ruler-storage.s3.secret-access-key string
        S3 secret access key

  -ruler-storage.s3.sse.kms-encryption-context string
        KMS Encryption Context used for object encryption. It expects JSON formatted string.

  -ruler-storage.s3.sse.kms-key-id string
        KMS Key ID used to encrypt objects in S3

  -ruler-storage.s3.sse.type string
        Enable AWS Server Side Encryption. Supported values: SSE-KMS, SSE-S3.

  -ruler-storage.swift.auth-url string
        OpenStack Swift authentication URL

  -ruler-storage.swift.auth-version int
        OpenStack Swift authentication API version. 0 to autodetect.

  -ruler-storage.swift.container-name string
        Name of the OpenStack Swift container to put chunks in.

  -ruler-storage.swift.domain-id string
        OpenStack Swift user's domain ID.

  -ruler-storage.swift.domain-name string
        OpenStack Swift user's domain name.

  -ruler-storage.swift.password string
        OpenStack Swift API key.

  -ruler-storage.swift.project-domain-id string
        ID of the OpenStack Swift project's domain (v3 auth only), only needed if it differs the from user domain.

  -ruler-storage.swift.project-domain-name string
        Name of the OpenStack Swift project's domain (v3 auth only), only needed if it differs from the user domain.

  -ruler-storage.swift.project-id string
        OpenStack Swift project ID (v2,v3 auth only).

  -ruler-storage.swift.project-name string
        OpenStack Swift project name (v2,v3 auth only).

  -ruler-storage.swift.region-name string
        OpenStack Swift Region to use (v2,v3 auth only).

  -ruler-storage.swift.user-domain-id string
        OpenStack Swift user's domain ID.

  -ruler-storage.swift.user-domain-name string
        OpenStack Swift user's domain name.

  -ruler-storage.swift.user-id string
        OpenStack Swift user ID.

  -ruler-storage.swift.username string
        OpenStack Swift username.

  -ruler.alertmanager-client.basic-auth-password string
        HTTP Basic authentication password. It overrides the password set in the URL (if any).

  -ruler.alertmanager-client.basic-auth-username string
        HTTP Basic authentication username. It overrides the username set in the URL (if any).

  -ruler.alertmanager-url string
        Comma-separated list of URL(s) of the Alertmanager(s) to send notifications to. Each URL is treated as a separate group. Multiple Alertmanagers in HA per group can be supported by using DNS service discovery format. Basic auth is supported as part of the URL.

  -ruler.enable-api
        Enable the ruler config API. (default true)

  -ruler.evaluation-delay-duration value
        Duration to delay the evaluation of rules to ensure the underlying metrics have been pushed.

  -ruler.external.url value
        URL of alerts return path.

  -ruler.max-rule-groups-per-tenant int
        Maximum number of rule groups per-tenant. 0 to disable. (default 70)

  -ruler.max-rules-per-rule-group int
        Maximum number of rules per rule group per-tenant. 0 to disable. (default 20)

  -ruler.query-frontend.address string
        GRPC listen address of the query-frontend(s). Must be a DNS address (prefixed with dns:///) to enable client side load balancing.

  -ruler.ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -ruler.ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -ruler.ring.etcd.password string
        Etcd password.

  -ruler.ring.etcd.username string
        Etcd username.

  -ruler.ring.instance-interface-names value
        List of network interface names to look up when finding the instance IP address. (default [<private network interfaces>])

  -ruler.ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -ruler.rule-path string
        Directory to store temporary rule files loaded by the Prometheus rule managers. This directory is not required to be persisted between restarts. (default "./data-ruler/")

  -ruler.tenant-federation.enabled
        Enable running rule groups against multiple tenants. The tenant IDs involved need to be in the rule group's 'source_tenants' field. If this flag is set to 'false' when there are already created federated rule groups, then these rules groups will be skipped during evaluations.

  -ruler.tenant-shard-size int
        The tenant's shard size when sharding is used by ruler. Value of 0 disables shuffle sharding for the tenant, and tenant rules will be sharded across all ruler replicas.

  -runtime-config.file string
        File with the configuration that can be updated in runtime.

  -server.grpc-listen-address string
        gRPC server listen address.

  -server.grpc-listen-port int
        gRPC server listen port. (default 9095)

  -server.http-listen-address string
        HTTP server listen address.

  -server.http-listen-port int
        HTTP server listen port. (default 8080)

  -store-gateway.sharding-ring.consul.hostname string
        Hostname and port of Consul. (default "localhost:8500")

  -store-gateway.sharding-ring.etcd.endpoints value
        The etcd endpoints to connect to.

  -store-gateway.sharding-ring.etcd.password string
        Etcd password.

  -store-gateway.sharding-ring.etcd.username string
        Etcd username.

  -store-gateway.sharding-ring.instance-availability-zone string
        The availability zone where this instance is running. Required if zone-awareness is enabled.

  -store-gateway.sharding-ring.instance-interface-names value
        List of network interface names to look up when finding the instance IP address. (default [<private network interfaces>])

  -store-gateway.sharding-ring.store string
        Backend storage to use for the ring. Supported values are: consul, etcd, inmemory, memberlist, multi. (default "memberlist")

  -store-gateway.sharding-ring.tokens-file-path string
        File path where tokens are stored. If empty, tokens are not stored at shutdown and restored at startup.

  -store-gateway.sharding-ring.unregister-on-shutdown
        Unregister from the ring upon clean shutdown. (default true)

  -store-gateway.sharding-ring.zone-awareness-enabled
        True to enable zone-awareness and replicate blocks across different availability zones. This option needs be set both on the store-gateway, querier and ruler when running in microservices mode.

  -store-gateway.tenant-shard-size int
        The tenant's shard size, used when store-gateway sharding is enabled. Value of 0 disables shuffle sharding for the tenant, that is all tenant blocks are sharded across all store-gateway replicas.

  -store.max-labels-query-length value
        Limit the time range (end - start time) of series, label names and values queries. This limit is enforced in the querier. If the requested time range is outside the allowed range, the request will not fail but will be manipulated to only query data within the allowed time range. 0 to disable.

  -store.max-query-length value
        Limit the query time range (end - start time). This limit is enforced in the query-frontend (on the received query), in the querier (on the query possibly split by the query-frontend) and ruler. 0 to disable.

  -target value
        Comma-separated list of components to include in the instantiated process. The default value 'all' includes all components that are required to form a functional Grafana Mimir instance in single-binary mode. Use the '-modules' command line flag to get a list of available components, and to see which components are included with 'all'. (default all)

  -tenant-federation.enabled
        If enabled on all services, queries can be federated across multiple tenants. The tenant IDs involved need to be specified separated by a '|' character in the 'X-Scope-OrgID' header.

  -validation.max-label-names-per-series int
        Maximum number of label names per series. (default 30)

  -validation.max-length-label-name int
        Maximum length accepted for label names (default 1024)

  -validation.max-length-label-value int
        Maximum length accepted for label value. This setting also applies to the metric name (default 2048)

  -validation.max-metadata-length int
        Maximum length accepted for metric metadata. Metadata refers to Metric Name, HELP and UNIT. (default 1024)

  -version
        Print application version and exit.

