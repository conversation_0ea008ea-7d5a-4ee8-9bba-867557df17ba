jedd@royksopp:~/src/oncall$ docker ps
CONTAINER ID   IMAGE                         COMMAND                  CREATED                  STATUS                       PORTS                                                 NAMES
883bfdfe5fed   grafana/oncall                "sh -c ./celery_with…"   About an hour ago        Up 7 minutes                                                                       oncall_celery_1
f3134a1c7815   grafana/oncall                "sh -c 'uwsgi --ini …"   About an hour ago        Up About an hour             0.0.0.0:8080->8080/tcp, :::8080->8080/tcp             oncall_engine_1
fd9db6000cda   grafana/grafana:9.0.0-beta3   "/run.sh"                About an hour ago        Up About an hour             0.0.0.0:3000->3000/tcp, :::3000->3000/tcp             oncall_grafana_1
a11a98a531f7   rabbitmq:3.7.15-management    "docker-entrypoint.s…"   About an hour ago        Up About an hour             4369/tcp, 5671-5672/tcp, 15671-15672/tcp, 25672/tcp   oncall_rabbitmq_1
10f5e5caf2c3   redis                         "docker-entrypoint.s…"   About an hour ago        Up About an hour             6379/tcp                                              oncall_redis_1
f955642b6c76   mysql:5.7                     "docker-entrypoint.s…"   About an hour ago        Up About an hour (healthy)   3306/tcp, 33060/tcp                                   oncall_mysql_1

