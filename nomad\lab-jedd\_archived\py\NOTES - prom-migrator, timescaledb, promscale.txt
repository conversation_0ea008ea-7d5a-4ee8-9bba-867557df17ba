References:
  TimescaleDB's Prom-migrator overview page:  https://www.timescale.com/blog/introducing-prom-migrator-a-universal-open-source-prometheus-data-migration-tool/
  The README on the github project is a subset of the above:  https://github.com/timescale/promscale/tree/master/cmd/prom-migrator



Use cases:

  We have a couple of obvious, and probably some unknown, use cases.

  1.  Replicating a data store 

      Though a full replica would be easy for cortex->cortex or cortex->mimir or mimir->mimir,
      and we're unlikely to need to do partial restorations / insertions of extant data from
      these back-end systems, it's possible we may want to inject data from native Prometheus
      or Zabbix or some other systems.  In general, though, we'd eschew such a proposition.


  2.  HA / BCP

      This use case is much clearer, and realistically we are better off agreeing that a) full
      restores are all we will consider (these are unlikely to be excessively time-consuming,
      and the simplicity of a full block storage restore probably outweighs the complexity and
      risks of trying a piecemeal), and b) setting RPO & RTO expectations up front, and then
      ensuring we can meet them with policy in play.

      Specifically with RPO, even if we expect to surrender short-term (prometheus, or non-
      compacted TSDB) data, that still gives us an RPO of > 2-3 hours.   RTO predictions need
      some clarity around what live backups we can maintain of our S3 buckets, the types of
      major failures we're contending with, and, of course, the final rolling size of our TSDB.


  3.  Migrations

      Potentially a subset of (1) above - we are likely to do a cortex -> mimir migration 'at
      some point' (writing in 2022-04).  Mimir was recently published to the world, but has been
      developed and used by Grafana corp for some time.   This means a) it has some stability
      despite its age, b) they've developed a migration plan that is reasonably robust, c) we
      have commercial support with Grafana, which may be constrained to some products, but we
      can likely get some assistance if needed, and d) it would be a one-way, full-TSDB path,
      and we can (relatively easily) perform a full S3 bucket copy (at time of writing this
      is around 200GB of data - quite tiny) to play with.


Caveats & limitations around prom-migrator:

  Backfilling data in a TSDB is HARD - that is, inserting data that's older than the latest
  existing data, in the TSDB (Cortex, Mimir, etc).

  This tool can backfill on Promscale (sitting on TimescaleDB, in turn sitting on PostgreSQL),
  VictoriaMetrics, and InfluxDB - none of which are useful to us (though we *may* re-visit
  using TimescaleDB in the future, as the query & aggregation options there far exceed those
  we can do with PromQL).

  Cortex (and presumably Mimir, since it's a fork of Cortex, and uses a Thanos backend) can
  NOT be backfilled, whether we're referring to their chunk or block storage).  There's a
  caveat that it *may* be possible, but that's a warning sign, not a comforting assurance.


Playing along at home
  
  Notes on installing prom-migrator etc.

  First, grab the timescaledb.nomad job (obs-collector/nomad/lab-jedd-*/timescaledb.nomad)
  and adjust to taste.  This includes a postgres + timescale component, but does not set up
  its own DB or hypertable, so you'll need to create a database and a hypertable inside.  This
  is why we've used persistent storage on the nomad file system.





