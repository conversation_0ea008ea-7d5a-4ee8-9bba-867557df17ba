/ # ./tempo --help
Usage of ./tempo:
  -auth.enabled
        Set to true to enable auth (deprecated: use multitenancy.enabled)
  -compactor.compaction.block-retention duration
        Duration to keep blocks/traces. (default 336h0m0s)
  -compactor.compaction.compaction-window duration
        Maximum time window across which to compact blocks. (default 1h0m0s)
  -compactor.compaction.max-block-bytes uint
        Maximum size of a compacted block. (default 107374182400)
  -compactor.compaction.max-objects-per-block int
        Maximum number of traces in a compacted block. (default 6000000)
  -config.expand-env value
        Whether to expand environment variables in config file
  -config.file value
        Configuration file to load
  -distributor.log-received-traces
        Enable to log every received trace id to help debug ingestion.
  -http-api-prefix string
        String prefix for all http api endpoints.
  -ingester.complete-block-timeout duration
        Duration to keep blocks in the ingester after they have been flushed. (default 15m0s)
  -ingester.lifecycler.ID string
        ID to register in the ring. (default "848663915cc0")
  -ingester.max-block-bytes uint
        Maximum size of the head block before cutting it. (default 1073741824)
  -ingester.max-block-duration duration
        Maximum duration which the head block can be appended to before cutting it. (default 1h0m0s)
  -ingester.trace-idle-period duration
        Duration after which to consider a trace complete if no spans have been received (default 10s)
  -log.level value
        Only log messages with the given severity or above. Valid levels: [debug, info, warn, error] (default info)
  -mem-ballast-size-mbs int
        Size of memory ballast to allocate in MBs.
  -memberlist.bind-port int
        Port for memberlist to communicate on (default 7946)
  -memberlist.host-port value
        Host port to connect to memberlist cluster.
  -multitenancy.enabled
        Set to true to enable multitenancy.
  -mutex-profile-fraction int
        Enable mutex profiling.
  -querier.frontend-address string
        Address of query frontend service, in host:port format.
  -search.enabled
        Set to true to enable search (unstable).
  -server.grpc-listen-port int
        gRPC server listen port. (default 9095)
  -server.http-listen-port int
        HTTP server listen port. (default 80)
  -storage.trace.azure.container-name string
        Azure container name to store blocks in.
  -storage.trace.azure.endpoint string
        Azure endpoint to push blocks to. (default "blob.core.windows.net")
  -storage.trace.azure.max-buffers int
        Number of simultaneous uploads. (default 4)
  -storage.trace.azure.storage-account-key string
        Azure storage access key.
  -storage.trace.azure.storage-account-name string
        Azure storage account name.
  -storage.trace.backend string
        Trace backend (s3, azure, gcs, local)
  -storage.trace.block.bloom-filter-false-positive float
        Bloom Filter False Positive. (default 0.01)
  -storage.trace.block.bloom-filter-shard-size-bytes int
        Bloom Filter Shard Size in bytes. (default 102400)
  -storage.trace.block.index-downsample-bytes int
        Number of bytes (before compression) per index record. (default 1048576)
  -storage.trace.block.index-page-size-bytes int
        Number of bytes per index page. (default 256000)
  -storage.trace.blocklist_poll duration
        Period at which to run the maintenance cycle. (default 5m0s)
  -storage.trace.gcs.bucket string
        gcs bucket to store traces in.
  -storage.trace.local.path string
        path to store traces at.
  -storage.trace.pool.max-workers int
        Workers in the worker pool. (default 50)
  -storage.trace.pool.queue-depth int
        Work item queue depth. (default 200)
  -storage.trace.s3.access_key string
        s3 access key.
  -storage.trace.s3.bucket string
        s3 bucket to store blocks in.
  -storage.trace.s3.endpoint string
        s3 endpoint to push blocks to.
  -storage.trace.s3.secret_key string
        s3 secret key.
  -storage.trace.wal.path string
        Path at which store WAL blocks. (default "/var/tempo/wal")
  -target string
        target module (default "all")
  -use-otel-tracer
        Set to true to replace the OpenTracing tracer with the OpenTelemetry tracer
  -version
        Print this builds version information
