{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 14, "panels": [], "title": "Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(runtime_cpython_cpu_time{type=~\"system\"}[$__interval])*100", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(runtime_cpython_cpu_time{type=~\"user\"}[$__interval])*100", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Recommendation Service (CPU%)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decmbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(runtime_cpython_memory{type=~\"rss|vms\"}[$__interval])/1024/1024", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Recommendation Service (Memory)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(app_recommendations_counter{recommendation_type=\"catalog\"}[$__interval])", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Recommendations Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(calls_total{status_code=\"STATUS_CODE_ERROR\"}[$__interval])", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Error Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dtdurationms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.50, sum(rate(latency_bucket{service_name=\"${service}\"}[$__rate_interval])) by (le))", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": false, "expr": "histogram_quantile(0.95, sum(rate(latency_bucket{service_name=\"${service}\"}[$__rate_interval])) by (le))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": false, "expr": "histogram_quantile(0.99, sum(rate(latency_bucket{service_name=\"${service}\"}[$__rate_interval])) by (le))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": false, "expr": "histogram_quantile(0.999, sum(rate(latency_bucket{service_name=\"${service}\"}[$__rate_interval])) by (le))", "hide": false, "legendFormat": "__auto", "range": true, "refId": "D"}], "title": "Service Latency (from SpanMetrics)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "expr": "rate(latency_count{service_name=\"${service}\"}[$__rate_interval])", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Endpoint Rate by Service", "type": "timeseries"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "", "current": {"selected": false, "text": "featureflagservice", "value": "featureflagservice"}, "datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "definition": "latency_bucket", "hide": 0, "includeAll": false, "multi": false, "name": "service", "options": [], "query": {"query": "latency_bucket", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/.*service_name=\\\"([^\\\"]+)\\\".*/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Demo Dashboard", "uid": "W2gX2zHVk", "version": 1, "weekStart": ""}