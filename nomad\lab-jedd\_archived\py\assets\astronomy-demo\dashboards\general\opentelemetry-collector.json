{"__inputs": [{"name": "webstore", "label": "Prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.0.3"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Provides information about the status of the OpenTelemetry Collector", "editable": true, "gnetId": 12553, "graphTooltip": 0, "id": 22, "iteration": 1593502896956, "links": [], "panels": [{"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 23, "panels": [], "title": "Receivers", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "Rate of spans successfully accepted vs refused per second", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 28, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_receiver_accepted_spans{receiver=~\"$receiver\"}[1m])) by (receiver)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{receiver}} spans accepted / sec", "refId": "A"}, {"expr": "sum(rate(otelcol_receiver_refused_spans{receiver=~\"$receiver\"}[1m])) by (receiver)", "interval": "", "legendFormat": "{{ receiver }} spans refused /sec", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Spans Accepted vs Refused /Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "Rate of metrics successfully accepted vs refused per second", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 19, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_receiver_accepted_metric_points{receiver=~\"$receiver\"}[1m])) by (receiver)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{receiver}} accepted /sec", "refId": "A"}, {"expr": "sum(rate(otelcol_receiver_refused_metric_points{receiver=~\"$receiver\"}[1m])) by (receiver)", "interval": "", "legendFormat": "{{ receiver }} refused / sec", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Metrics Accepted vs Refused Received/Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 25, "panels": [], "title": "Exporters", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "Rate of spans successfully exported vs Failed per second", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 11}, "hiddenSeries": false, "id": 30, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_exporter_sent_spans{exporter=~\"$exporter\"}[1m])) by (exporter)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{exporter}} sent / sec", "refId": "A"}, {"expr": "sum(rate(otelcol_exporter_send_failed_spans{exporter=~\"$exporter\"}[1m])) by (exporter)", "interval": "", "legendFormat": "{{ exporter }} failed /sec", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Spans Exported vs Failed / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "Rate of timeseries successfully exported per second", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 11}, "hiddenSeries": false, "id": 31, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_exporter_sent_metric_points{exporter=~\"$exporter\"}[1m])) by (exporter)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{exporter}} sent /sec", "refId": "A"}, {"expr": "sum(rate(otelcol_exporter_send_failed_metric_points{exporter=~\"$exporter\"}[1m])) by (exporter)", "interval": "", "legendFormat": "{{ exporter }} failed / sec", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Metrics Exported vs failed /Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 21, "panels": [], "repeat": null, "title": "Processors", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 21}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(otelcol_processor_queued_retry_queue_length)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Queue Length", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queued Retry Max <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 21}, "hiddenSeries": false, "id": 11, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(otelcol_processor_queued_retry_queue_latency_sum/ otelcol_processor_queued_retry_queue_latency_count)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ms", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queued Retry Processor In-queue Latency (ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(otelcol_processor_queued_retry_send_latency_sum/ otelcol_processor_queued_retry_send_latency_count)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "ms", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queued Retry Processor Send Latency (ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 30}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_processor_spans_received{processor=~\"$processor\"}[1m])) by (processor)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{processor}} | received", "refId": "A"}, {"expr": "sum(rate(otelcol_processor_spans_dropped{processor=~\"$processor\"}[1m])) by (processor)", "interval": "", "legendFormat": "{{processor}} | dropped", "refId": "B"}, {"expr": "sum(rate(otelcol_processor_accepted_spans{processor=~\"$processor\"}[1m])) by (processor)", "interval": "", "legendFormat": "{{processor}} | accepted", "refId": "C"}, {"expr": "sum(rate(otelcol_processor_refused_spans{processor=~\"$processor\"}[1m])) by (processor)", "interval": "", "legendFormat": "{{processor}} | refused", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processor Spans Received, Dropped, Accepted, Refused/Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 30}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_processor_queued_retry_success_send{processor=~\"$processor\"}[1m])) by (processor)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{processor}}", "refId": "A"}, {"expr": "sum(rate(otelcol_processor_queued_retry_fail_send{processor=~\"$processor\"}[1m])) by (processor)", "interval": "", "legendFormat": "{{processor}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QueuedRetry Successful vs Failed Sent/Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 2, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_processor_spans_received{processor=~\"$processor\"}[1m]) / rate(otelcol_processor_batches_received{processor=~\"$processor\"}[1m])) by (processor)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{processor}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Spans per Batch (avg from rates)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 39}, "hiddenSeries": false, "id": 12, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(otelcol_processor_batches_received{processor=~\"$processor\"}[1m])) by (processor)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{processor}} | received", "refId": "A"}, {"expr": "sum(rate(otelcol_processor_trace_batches_dropped{processor=~\"$processor\"}[1m])) by (processor)", "interval": "", "legendFormat": "{{processor}} | trace dropped", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Batches Received vs Dropped /Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 48}, "hiddenSeries": false, "id": 6, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(otelcol_processor_queued_retry_success_send{processor=~\"$processor\"}) by (processor)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{processor}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Successful Batches Sent Cumulative", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 48}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(otelcol_processor_spans_received{exporter=~\"$exporter\"}) by (exporter)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{exporter}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Enqueued Spans Cumulative", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 25, "style": "dark", "tags": ["opentelemetry"], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(otelsvc_receiver)", "hide": 0, "includeAll": true, "label": "receiver", "multi": true, "name": "receiver", "options": [], "query": "label_values(otelsvc_receiver)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(otelsvc_exporter)", "hide": 0, "includeAll": true, "label": "exporter", "multi": true, "name": "exporter", "options": [], "query": "label_values(otelsvc_exporter)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(processor)", "hide": 0, "includeAll": true, "label": "processor", "multi": true, "name": "processor", "options": [], "query": "label_values(processor)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "OpenTelemetry Collector", "uid": "BKf2sowmj", "version": 7}