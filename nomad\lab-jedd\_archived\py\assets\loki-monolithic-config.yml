
# This is a complete configuration to deploy Loki backed by the filesystem.
# The index will be shipped to the storage via tsdb-shipper.
#
# Stripped config to absolute basics with a 'monolithic' architecture.
#
# Designed for Loki v3.4 (2025-03)


auth_enabled: false

server:
  # Nomad has a 'to = 3100' mapping - so this works (no need for NOMAD_ vars).
  http_listen_port: 3100

common:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory
  replication_factor: 1
  path_prefix: /tmp/loki

schema_config:
  configs:
  - from: 2025-01-01
    store: tsdb
    object_store: filesystem
    schema: v13
    index:
      prefix: index_
      period: 24h

storage_config:
  filesystem:
    directory: /tmp/loki/chunks

  tsdb_shipper:
    active_index_directory: /alloc/tsdb-index
    cache_location: /alloc/tsdb-cache

  aws:
    s3: https://garage-s3.obs.int.jeddi.org
    region: garage
    bucketnames: loki-monolithic
    access_key_id: GK280758b912cbb475ea3d8f9c
    secret_access_key: 67b48dae7272ad8ec3f524bee8e24036b72d55334429782542f7f137d932d1c2
    s3forcepathstyle: true

