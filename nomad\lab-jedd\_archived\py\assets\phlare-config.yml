
# Used by phlare.nomad
#
# Full list of configuration options at:
#   https://grafana.com/docs/phlare/latest/operators-guide/configure/reference-configuration-parameters/

storage:
  backend: filesystem

  filesystem:
    dir: "/data/"

phlaredb:
  # default retention is only 3h
  max_block_duration: 24h

scrape_configs:
  - job_name: "phlareself"
    scrape_interval: "10s"
    static_configs:
      - targets: ["py-mon-01:4100"]

    # = = = = = = = = = = = = = = = = = = = = = = = =
    # These are maintained in alphabetical order

#  - job_name: "phlare-exporter-blackbox"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:9115"]
#
#  - job_name: "phlare-exporter-snmp"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:9116"]

  - job_name: "phlare-loki"
    scrape_interval: "30s"
    static_configs:
      - targets: ["py-mon-01:3100"]

#  - job_name: "phlare-mimir"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:19009"]
#
#  - job_name: "phlare-prom"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:9090"]
#
#  - job_name: "phlare-prom-blackbox"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:9091"]
#
#  - job_name: "phlare-tempo"
#    scrape_interval: "10s"
#    static_configs:
#      - targets: ["py-mon-01:3333"]
#
