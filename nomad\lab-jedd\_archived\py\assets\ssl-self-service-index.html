<!doctype html>
<html>
  <head>
    <title>URL Sanitizer</title>
  </head>
  <body>
    <h1>URL Sanitizer</h1>
    <form method="POST" action="/sanitize">
      <label for="urls">Enter one or more URLs:</label><br>
      <textarea id="urls" name="urls" rows="5" cols="50"></textarea><br>
      <input type="submit" value="Sanitize URLs">
    </form>

    {% if registered_urls %}
      <h2>Registered URLs:</h2>
      <ul>
        {% for url in registered_urls %}
          <li>{{ url }} <button type="button" onclick="deregisterUrl('{{ url }}')">Deregister</button></li>
        {% endfor %}
      </ul>
    {% endif %}

    <script>
      function deregisterUrl(url) {
        if (confirm(`Are you sure you want to deregister ${url}?`)) {
          fetch(`/deregister?url=${encodeURIComponent(url)}`)
            .then(response => {
              if (response.ok) {
                alert(`URL ${url} has been deregistered from Consul`);
                location.reload();
              } else {
                alert(`Error deregistering URL ${url}: ${response.statusText}`);
              }
            });
        }
      }
    </script>
  </body>
</html>



