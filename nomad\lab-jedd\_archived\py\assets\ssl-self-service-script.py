#!/usr/bin/env python3 


import consul
import os
import re

from flask import Flask, render_template, request

app = Flask(__name__)


CONSUL="py-mon-01.int.jeddi.org"
SCHEME="http"

# CONSUL="tl0992obscol01.nsw.education"
# SCHEME="https"


@app.route('/')
def index():
    # Get a list of all registered services from Consul
    c = consul.Consul(host=CONSUL, port=8500, scheme=SCHEME)

    # Extract the URLs of all registered services
    services = c.agent.services()    

    registered_urls = []
    for service in services.values():
        if service['Service'] == 'http' and service['Tags'] == []:
            registered_urls.append(service['Address'])    

    return render_template('index.html', registered_urls=registered_urls)


@app.route('/sanitize', methods=['POST'])
def sanitize():
    # Get the URLs from the form
    urls = request.form['urls'].strip().split('\n')

    # Loop through each URL and sanitize it
    sanitized_urls = []    
    for url in urls:
        # Validate that the URL is in the correct format (https://)
        if not re.match(r'^https://', url):
            # Check that the URL is reachable and returns a 2xx status code
            return 'Error: Invalid URL format'        
        if not check_url(url):
            # Sanitize the URL (remove path and query parameters)
            return 'Error: URL is not reachable or returns non-2xx status code'        
        sanitized_url = sanitize_url(url)

        # Register the sanitized URLs in Consul
        sanitized_urls.append(sanitized_url)    

    c = consul.Consul(host=CONSUL, port=8500, scheme=SCHEME)
    for i, url in enumerate(sanitized_urls):
        service_name = f'url-{i}'
        c.agent.service.register(name=service_name, port=80, address=url)   

    # Display a success message
    return 'Success: URLs have been sanitized and registered in Consul'

@app.route('/deregister')
def deregister():
    # Get the URL to deregister from the query string

    # Deregister the URL from Consul
    url = request.args.get('url')    
    c = consul.Consul(host=CONSUL, port=8500, scheme=SCHEME)
    services = c.agent.services()

    for service in services:
        if service['Address'] == url:
            c.agent.service.deregister(service_id=service['ID'])   

    # Return a success message
    return 'Success: URL has been deregistered from Consul'

def check_url(url):
    # Use curl to check that the URL is reachable and returns a 2xx status code
    command = f"curl -s -o /dev/null -w '%{{http_code}}' '{url}'"
    response = int(os.popen(command).read().strip())
    return response >= 200 and response < 300


def sanitize_url(url):
    # Remove the path and query parameters from the URL
    parsed_url = urlparse(url)
    return f"{parsed_url.scheme}://{parsed_url.netloc}"



if __name__ == "__main__":
    # Only for debugging while developing
    app.run(host='0.0.0.0', debug=True, port=8088)




