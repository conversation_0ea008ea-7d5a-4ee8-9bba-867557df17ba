// cadvisor (single instance version) for j<PERSON>'s nomad lab (PY / royksopp)
// this is for monitoring docker containers

// It requires some additional volumes to work: 
//    https://github.com/google/cadvisor#quick-start-running-cadvisor-in-a-docker-container

job "cadvisor" {
  datacenters = ["PYR"]

  type = "service"

  group "cadvisor" {
    network {
        port "cadvisor"{
            to = 8080
            static = 12345
        }
    }

    volume "vol_var_run" {
        type = "host"
        source = "vol_var_run"
        read_only = true
    }

    volume "vol_sys" {
        type = "host"
        source = "vol_sys"
        read_only = true
    }

#    volume "vol_var_lib_docker" {
#        type = "host"
#        source = "vol_var_lib_docker"
#        read_only = true
#    }

    volume "vol_dev_disk" {
        type = "host"
        source = "vol_dev_disk"
        read_only = true
    }


    task "cadvisor" {
      driver = "docker"
      config {
        # image = "google/cadvisor:v0.31.0"
        image = "gcr.io/cadvisor/cadvisor"
        dns_servers = ["************"]
        ports = ["cadvisor"]
        privileged = true


##        mount {
##          # type = "bind"
##          target = "/var/lib/docker"
##          source = "vol_var_lib_docker"
##          readonly = true
##          bind_options = {
##            propagation = "rprivate"
##          }
##        }

      }

      volume_mount {
        volume = "vol_var_run"
        destination = "/var/run"
        read_only = true
      }       

      volume_mount {
        volume = "vol_sys"
        destination = "/sys"
        read_only = true
      }       

#      volume_mount {
#        volume = "vol_var_lib_docker"
#        destination = "/var/lib/docker"
#        read_only = true
#      }       

      volume_mount {
        volume = "vol_dev_disk"
        destination = "/dev/disk"
        read_only = true
      }       

      service {
        port = "cadvisor"
        check {
          type = "http"
          path = "/"
          interval = "10s"
          timeout = "2s"
        }
      }
      resources {
        cpu = 100
        memory = 128
      }
    }
  }
}

