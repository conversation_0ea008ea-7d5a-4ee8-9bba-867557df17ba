
# collector-dfs - jedd lab instance

# Combined standalone prometheus + blackbox exporter for Digital Field Services
# refer:  https://jira.education.nsw.gov.au/browse/OBS-573


job "collector-dfs" {

  type = "service"

  datacenters = ["PY"]

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-dfs" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
      port "port_blackbox" { 
        to = 9115
      }
  	}


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=https://collector-dfs-prometheus.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Digital Field Services Collector",
        ]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        logging {
          type = "loki"
          config {
            loki-url = "http://py-mon-01.int.jeddi.org:3100/loki/api/v1/push"
          }
        }

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/ultimo.yaml:/etc/prometheus/ultimo.yaml",
          # "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]
      }

      resources {
        cpu    = 256
        memory = 256
      }

      service {
        name = "collector-dfs-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-prometheus.rule=Host(`collector-dfs-prometheus.obs.test.nsw.education`)",
          "traefik.http.routers.collector-dfs-prometheus.tls=false",
          "traefik.http.routers.collector-dfs-prometheus.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      artifact {
        source = "git::ssh://<EMAIL>/prometheus-configuration"
        destination = "local/prometheus-configuration"
        options {
          sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-dfs"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-dfs'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:{{ env "NOMAD_PORT_port_prometheus" }}']

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-dfs'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:{{ env "NOMAD_PORT_port_blackbox" }}']

  - job_name: 'dfs-pings-sd'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 1m
      files: 
      - "/etc/prometheus/ultimo.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: py-mon-01.int.jeddi.org:{{env "NOMAD_PORT_port_blackbox" }}

  - job_name: 'dfs-pings-static'
    metrics_path: /probe
    params:
      module: ["icmp"]
    static_configs:

      - targets: 
        - py-mon-01.int.jeddi.org
        - py-hub-01.int.jeddi.org
        - py-core-01.int.jeddi.org
        labels:
          site: upper kumbukta west

      - targets: 
        - ***********
        - ***********
        - ***********
        labels:
          site: middle regional networking center

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: py-mon-01.int.jeddi.org:{{env "NOMAD_PORT_port_blackbox" }}

#remote_write:
#  - name: mimir
#    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: test
#    tls_config:
#      insecure_skip_verify: true


EOH

        destination = "local/prometheus.yaml"
      }


      template {
        data = <<EOH
# Ultimo payload

- targets:
  - ***********
  - ***********
  labels:
    variant: "routing"

- targets:
  - ***********
  - ***********
  - ***********
  labels:
    variant: "ubiquiti routing gear"


EOH
        destination = "local/ultimo.yaml"
      }

    }


    # TASK blackbox for dfs   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--log.level",      "debug"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://py-mon-01.int.jeddi.org:3100/loki/api/v1/push"
          }
        }          

      }

      #env {
      #  HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      #  HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
      #  NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      #}

      resources {
        cpu    = 200
        memory = 256
      }


      service {
        name = "collector-dfs-blackbox"
        port = "port_blackbox"

        #tags = [
        #  "traefik.enable=true",
        #  "traefik.http.routers.collector-dfs-blackbox.rule=Host(`collector-dfs-blackbox.obs.test.nsw.education`)",
        #  "traefik.http.routers.collector-dfs-blackbox.tls=false",
        #  "traefik.http.routers.collector-dfs-blackbox.entrypoints=http",
        #]

        meta {
          cir_app_id = "obs"
          env = "test"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

      }


      template {
        data = <<EOH
modules:
  http_with_proxy:
    prober: http
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      fail_if_body_not_matches_regexp:
        - "1 results loaded."
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # = = = = = = = = = = = = = = = = = = = = = = = =
  # Web checks - bespoke, for Oliver School Library System (SLS) 
  # >>> requires a 200 and a string 'results found' on the target page.

  webcheck_generic:
    prober: http
    timeout: 20s
    http:
      # proxy_url: "http://proxy.det.nsw.edu.au:80"
      valid_status_codes: [200]
      # fail_if_not_ssl: true
      # fail_if_body_not_matches_regexp:
      #   - results found
      preferred_ip_protocol: "ip4" 

EOH
        destination = "local/config.yml"
      }


    }

  }

}


