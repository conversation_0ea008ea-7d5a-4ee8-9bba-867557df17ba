# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "otel/opentelemetry-collector-contrib:latest"
}

job "collector-opentelemetry" {
  datacenters = ["PY"]
  type        = "service"

  group "otel-collector" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "jaeger-grpc" {
        to = 14250
      }
      port "jaeger-thrift-http" {
        to = 14268
      }
      port "metrics" {
        to = 8888
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "zipkin" {
        to = 9411
      }
      port "zpages" {
        to = 55679
      }
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http,https",
        "traefik.http.routers.otel.rule=Host(`otel.int.jeddi.org`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

    service {
      name     = "jaeger"
      port     = "jaeger-grpc"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger.rule=Host(`otel-jaeger.int.jeddi.org`)",
        "traefik.http.routers.otel-jaeger.tls=false"
      ]
    }

    service {
      name     = "jaeger-thrift"
      port     = "jaeger-thrift-http"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger-thrift.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger-thrift.rule=Host(`otel-jaeger-thrift.int.jeddi.org`)",
        "traefik.http.routers.otel-jaeger-thrift.tls=false"
      ]
    }

      service {
        tags = [
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.int.jeddi.org`)",
          "traefik.http.routers.otel-collector-http.entrypoints=http",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
        port = "otlphttp"
      }

    task "otel-collector" {
      driver = "docker"
      env = { }

      config {
        image = var.otel_image

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
          type = "loki"
          config {
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }
        ports = [
        "otlphttp",
        "zipkin",
        "zpages",
        "healthcheck",
        "jaeger-grpc",
        "jaeger-thrift-http",
        "metrics",
        "otlp"
        ]
      }

      resources {
        cpu    = 200
        memory = 398
        memory_max = 1024
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      grpc:
      http:
  jaeger:
    protocols:
      thrift_compact:
      thrift_binary:
      thrift_http:
  zipkin:
  opencensus:
  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 60s
        static_configs:
        - targets: ['0.0.0.0:8888']
  filelog:
    include: [/var/log/*.log]

processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
  pprof:
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# NewRelic 
  otlp/newrelic:
    headers:
     "api-key": 52f01b42d596b903330e1e9f21845466FFFFNRAL
    endpoint: https://otlp.nr-data.net:4317

# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "http://py-mon-01.int.jeddi.org:19009/otlp"
#    headers:
#      X-Scope-ORGID: test

# loki onprem
  loki/onpremloki:
    endpoint: "http://loki.int.jeddi.org:3100/loki/api/v1/push"

# tempo on-prem for jaeger traces
  jaeger_thrift/onpremtempo:
    endpoint: http://tempo-jaeger.int.jeddi.org/api/traces
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    traces:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [attributes/env,memory_limiter,batch]
      exporters: [jaeger_thrift/onpremtempo,otlphttp/grafanacloud,otlp/newrelic]

    metrics:
      receivers: [otlp,prometheus]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud,otlp/newrelic]

    logs:
      receivers: [filelog,otlp]
      processors: [attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki,otlphttp/grafanacloud,otlp/newrelic]

  telemetry:
    logs:
      level: debug
      initial_fields:
        service: obscol-test
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
