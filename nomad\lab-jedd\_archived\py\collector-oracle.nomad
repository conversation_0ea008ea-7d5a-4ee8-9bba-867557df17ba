 
// Collector-Oracle - prometheus + exporter pair


#  skopeo  copy 
#          docker://registry.hub.docker.com/prom/prometheus:v2.41.0
#          docker://registry.obs.int.jeddi.org/prometheus:v2.41.0
#
#  skopeo  copy 
#          docker://registry.hub.docker.com/iamseth/oracledb_exporter:0.2.9 
#          docker://registry.obs.int.jeddi.org/oracledb_exporter:0.2.9


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_prometheus = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:5000/prometheus:v2.41.0" : "registry.obs.int.jeddi.org/prometheus:v2.41.0"
  image_oracledb_exporter = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:5000/oracledb_exporter:0.2.9" : "registry.obs.int.jeddi.org/oracledb_exporter:0.2.9"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123]" : "py-hac-0[123]"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-oracle" {
  datacenters = [ var.nomad_dc ]

  type = "service"

  group "collector-oracle" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    network {

      port "port_prometheus" {
      }

      port "port_oracle_exporter" {
        to = 9161
      }

  	}




    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      config {
        image = local.image_prometheus

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Starlink Collector",
          # "--enable-feature=agent",
          "--config.file=/etc/prometheus/prometheus.yml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          # "local/prometheus-configuration/dg-pan-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-oracle-prometheus"
        port = "port_prometheus"
        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oracle-prometheus.entrypoints=https,http",
          "traefik.http.routers.collector-oracle-prometheus.rule=Host(`collector-oracle-prometheus.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-oracle-prometheus.tls=false"
        ]      
      }

      template {

        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}

  scrape_interval: 1m

  # This creates a log of *all* queries, and will show up in /metrics as 
  #     prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus_oracle'
    metrics_path: /metrics
    static_configs:
      - targets: [ "{{ env "NOMAD_IP_port_prometheus" }}:{{ env "NOMAD_PORT_port_prometheus" }}" ]

  - job_name: 'oracle_exporter'
    metrics_path: /metrics
    static_configs:
      # - targets: [ "{{ env "NOMAD_IP_port_oracle_exporter" }}:{{ env "NOMAD_PORT_port_oracle_exporter" }}" ]
      - targets: [ "collector-oracle-exporter.obs.int.jeddi.org" ]

#remote_write:
#  - name: mimir
#    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }


    # TASK exporter oracle   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "exporter-oracle" {
      driver = "docker"

      env = {
        # "DATA_SOURCE_NAME" = "system/oracle@//**************:1521"
        "DATA_SOURCE_NAME" = "system/oracle@//oracle12.obs.int.jeddi.org:1521"
      }

      config {
        ports = [ "port_oracle_exporter" ]

        image = local.image_oracledb_exporter

        args = [
          "--log.level=warn",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_oracle_exporter}",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        


      }

      resources {
        cpu    = 100
        memory = 150
      }

      service {
        name = "collector-oracle-exporter"
        port = "port_oracle_exporter"

        check {
          type = "http"
          port = "port_oracle_exporter"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oracle-exporter.entrypoints=https,http",
          "traefik.http.routers.collector-oracle-exporter.rule=Host(`collector-oracle-exporter.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-oracle-exporter.tls=false"
        ]      
      }

    }

  }

}


