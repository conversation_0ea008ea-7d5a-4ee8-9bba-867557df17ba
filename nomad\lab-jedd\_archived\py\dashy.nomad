
// dashy - quick-access dashboard - prod obscol

// Refer:  https://dashy.to/docs/quick-start

// Prod obscol is primary - test-obscol uses *THE SAME* config (assets/dashy-config.yml)

job "dashy"  {
  datacenters = ["PY"]

  type = "service"

  group "dashy" {
    network {
      port "port_http" {
        to = 80
      }
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    task "dashy" {
      driver = "docker"

      config {
        image = "lissy93/dashy:latest"

        hostname = "dashy"

        dns_servers = ["************"]

        ports = ["port_http"]

        args  = [ ]

        volumes = [
           "local/dashy-config.yml:/app/public/conf.yml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }

      }

      template {
        # NOTE we use the same configuration for prod and test - test is 
        #      basically a fallback option, and may be removed.
        data = file("../prod-obscol/assets/dashy-config.yml")
        destination = "local/dashy-config.yml"
      }

      resources {
        cpu = 80
        memory = 150
        memory_max = 256
      }

      service {
        name = "dashy"
        port = "port_http"
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.dash.entrypoints=https",
#          "traefik.http.routers.dash.rule=Host(`dash.obs.nsw.education`)"
#        ]
        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }
      }

    }
  }
}
