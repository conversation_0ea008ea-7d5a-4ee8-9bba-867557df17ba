
// Jedd lab - dashy - home lab dashboard

// Refer:  https://dashy.to/docs/quick-start

variables {
  consul_hostname = "py-mon-01.int.jeddi.org:8500"
}

job "dashy"  {
  datacenters = ["PY"]
  type = "service"

  group "dashy" {
    network {
      port "port_http" {
        to     = 80
      }
    }

    volume "vol_dashy"  {
      type = "host"
      source = "vol_dashy"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    task "dashy" {
      driver = "docker"

      volume_mount {
        volume = "vol_dashy"
        destination = "/dashy"
        read_only = false
      }

      config {
        image = "lissy93/dashy:latest"

        hostname = "dashy"

        dns_servers = ["***********01,***********"]

        ports = ["port_http"]

        args  = [ 
          ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        } 
        
        volumes = [ 
#          "/dashy/conf.yml:/app/public/conf.yml",
           "local/dashy-config.yml:/app/public/conf.yml",
#          "local/dashy/public:/app/public",
#          "local/main.py:/main.py",
        ]

      }

      template {
        data = file("assets/dashy-config.yml")
        # destination = "dashy/conf.yml"
        destination = "local/dashy-config.yml"
      }

      resources {
        cpu = 500
        # Dashy won't run with 512MB
        memory = 512
      }

      service {
        name = "dashy"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.dashy.rule=Host(`dashy.int.jeddi.org`)",
          "traefik.http.routers.dashy.tls=false",
        ]

      }

      service {
        name = "dashy-web"
        port = "port_http"
        tags = ["traefik.enable=true"]

        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }
      }


    }
  }
}
