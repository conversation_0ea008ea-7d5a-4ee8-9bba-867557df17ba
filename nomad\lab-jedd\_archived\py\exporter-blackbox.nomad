
# Prometheus black-box exporter - for ping and web scenario style checks
# for Jedd-lab (py)

# Blackbox exporter can be bounced through like this:
#   curl -XGET "http://py-mon-01.int.jeddi.org:9115/probe?target=***********&module=icmp"
#
# This uses the icmp module, and hits that target IP address, asking blackbox (exposed
# on port 9115) to peform the ping test.


job "exporter-blackbox" {

  datacenters = ["PY"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "exporter-blackbox" {

    network {
      port "port-exporter-blackbox" {
        static = 9115
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "exporter-blackbox" {
      driver = "docker"

      config {
        # privileged = "true"
        # cap_add = ["net_raw"]
        # trying amongst:   net_admin, net_bind_service, net_broadcast, sys_admin, sys_boot, 
        cap_add = ["net_raw", "net_admin"]

        sysctl = {
          "net.ipv4.ping_group_range" = "0 2147483647"
        }

        image = "prom/blackbox-exporter"

        args = [
          "--config.file",
          "local/config.yml",
          "--log.level",
          "debug"
        ]

        ports = ["port-exporter-blackbox"]

      }

      template {
        data = <<EOH

modules:
  http_2xx:
    prober: http
  http_post_2xx:
    prober: http
    http:
      method: POST
  tcp_connect:
    prober: tcp
  pop3s_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^+OK"
      tls: true
      tls_config:
        insecure_skip_verify: false
  ssh_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^SSH-2.0-"
      - send: "SSH-2.0-blackbox-ssh-check"
  irc_banner:
    prober: tcp
    tcp:
      query_response:
      - send: "NICK prober"
      - send: "USER prober prober prober :prober"
      - expect: "PING :([^ ]+)"
        send: "PONG ${1}"
      - expect: "^:[^ ]+ 001"
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # Oliver School Library System (SLS) requires a 200 and a string 'results found' on the target page.
  webcheck_oliver:
    prober: http
    http:
      valid_status_codes: [200]
      fail_if_not_ssl: true
      fail_if_body_not_matches_regexp:
        - results found

EOH
        destination = "local/config.yml"
      }

      resources {
        cpu    = 256
        memory = 128
      }

      service {
        name = "service-exporter-blackbox"
        port = "port-exporter-blackbox"
        check {
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }

    }

  }
}
