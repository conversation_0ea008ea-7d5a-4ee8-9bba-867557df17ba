
//  exporter-EXP(erimental) for specific targets for prometheus - for jedd's lab (py)

//  Looking at ways to constrain specific OIDs.  For example, the PDUs return many hundreds of 
//  data points, but we really only care about ingesting 3 or 4 per PDU.
//  One option is to drop these as relabel rules at prometheus end, but this feels inelegant,
//  and means we'll hit performance / capacity limits on our snmp exporter(s) faster.

//  Most of this will be done in the prometheus (probably dedicated instance) configuration,
//  as the exporter is pretty dumb, but there are some OID / MIB considerations in here, and
//  they introduce some fragility - as we've seen previously with dumping mibs galore into
//  the Zabbix SNMP proxy container.  In any case we can isolate our testing.

//  @TODO doing a --web.listen-address to force snmpexporter to a non-9116 default port
//  seems to break the auto-discovery of the config file (at /etc/snmp_expoter/snmp.yml) so
//  that needs to be explicit now.   Should revisit what's going on there.

//  <PERSON>'s made some comments on https://groups.google.com/g/prometheus-users/c/T4nTkUs7iGY
//  and at https://github.com/prometheus/snmp_exporter/pull/283 -- but examples are a bit thin
//  on the ground.  Also refer:  https://github.com/prometheus/snmp_exporter/issues/432 and
//  continue working through the *bsd info at https://yetiops.net/posts/openbsd-snmp-exporter/


job "exporter-exp" {
  type = "service"
  datacenters = ["PY"]

  group "exporter-exp" {

    network {
      port "port-exporter-exp" {
        # to = 9116 
        static = 9117 
      }
    }

    task "exporter-exp" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = [
          "port-exporter-exp"
        ]

#      --config.file="snmp.yml"  Path to configuration file.
#      --web.config.file=""      [EXPERIMENTAL] Path to configuration file that can enable TLS or authentication.
#      --web.listen-address=":9116"  
#                                Address to listen on for web interface and telemetry.
#      --dry-run                 Only verify configuration is valid and exit.
#      --log.level=info          Only log messages with the given severity or above. One of: [debug, info, warn, error]
#      --log.format=logfmt       Output format of log messages. One of: [logfmt, json]

#      /etc/snmp_exporter/snmp.yml


        args = [
#          "--web.listen-address=9117"
          "--web.listen-address=:9117",
          "--config.file=/etc/snmp_exporter/snmp.yml"
        ]

        dns_servers = [
          "***********01", "***********"
        ]

        volumes = [
          "local/snmp_exporter.yaml:/etc/snmp_exporter.yaml"
        ]

      }

      service {
        name = "exporter-exp"
        port = "port-exporter-exp"
//
//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      template {
        data = <<EOH
modules:
  prober: https
  timeout: 10s

EOH
        destination = "local/snmp_exporter.yaml"
      }

      resources {
        cpu    = 256
        memory = 128
      }

    }

  }
}
