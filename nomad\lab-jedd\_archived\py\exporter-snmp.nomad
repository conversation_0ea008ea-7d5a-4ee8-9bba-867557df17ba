
//  exporter-snmp for prometheus - for jedd's lab (py)

job "exporter-snmp" {
  type = "service"
  datacenters = ["PY"]

  group "exporter-snmp" {

    network {
      port "port-exporter-snmp" {
        static = 9116 
      }
    }

    task "exporter-snmp" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = [
          "port-exporter-snmp"
          ]
        dns_servers = [
          "192.168.1.101", "192.168.1.1"]
        volumes = [
          "local/snmp_exporter.yaml:/etc/snmp_exporter.yaml"
        ]
      }

      service {
        name = "exporter-snmp"
        port = "port-exporter-snmp"
//
//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      template {
        data = <<EOH
modules:
  prober: https
  timeout: 10s

EOH
        destination = "local/snmp_exporter.yaml"
      }

      resources {
        cpu    = 256
        memory = 128
      }

    }

  }
}
