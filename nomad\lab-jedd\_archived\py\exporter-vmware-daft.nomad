
// jedd lab PY - vmware exporter (direct to esx - not vsphere) 

// mostly abandoned software, no way to parameterise, so just two hard-coded instances for daft & punk

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {

  # for vmware / esxi console
  image_exporter_vmware = "ideamans/prometheus-vmware-exporter:latest"
  # original upstream but abandoned: devinotelecom/prometheus-vmware-exporter"

  # for vsphere
  # image_exporter_vmware = "pryorda/vmware_exporter"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "exporter-vmware-daft" {
  datacenters = ["PY"]
  type        = "system"

  group "exporter-vmware-daft" {

    network {
      port "port-exporter-vmware" {
        static =  9512
        to = 9512
      }
    }

    task "exporter-vmware-daft" {
      driver = "docker"

      config {
        image = "${var.image_exporter_vmware}"

        ports = [
          "port-exporter-vmware"
          ]
        logging {

          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      env = {
        "ESX_HOST"     = "py-daft-01.int.jeddi.org",
        "ESX_USERNAME" = "prom",
        "ESX_PASSWORD" = "PromTest27%",
        "ESX_LOG"      = "info"
      }

      resources {
        cpu    = 20
        memory = 20
        memory_max = 150
      }

    }

    service {
      name = "exporter-vmware-daft"
      port = "port-exporter-vmware"

      #tags = [
      #  "traefik.enable=true",
      #  "traefik.http.routers.exporter-vmware.rule=Host(`exporter-vmware.obs.int.jeddi.org`)",
      #  "traefik.http.routers.exporter-vmware.tls=false",
      #  "traefik.http.routers.exporter-vmware.entrypoints=http",
      #]

#      check {
#        name     = "alive"
#        type     = "tcp"
#        port     = "http"
#        interval = "10s"
#        timeout  = "2s"
#      }

    }

  }
}

