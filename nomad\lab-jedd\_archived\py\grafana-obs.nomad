
// Grafana for jedd-lab - with bundled postgresql

// Part of the 2023-06 migration of Grafana from Classic to ObsCol 



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_grafana    =  "grafana/grafana:9.5.3"
  image_postgresql =  "postgres:12"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-obs" {
  datacenters = ["PY"]


  type = "service"

  group "grafana-obs" {

    network {
      port "port_grafana" {
        to = 3000
      }

      port "port_postgresql"  {
        to = 5432
        # static = 5432
      }
    }

    volume "vol_grafana_obs" {
      type = "host"
      source = "vol_grafana_obs"
      read_only = false
    }


    # task postgresql  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      # user = "postgres:postgres"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = "${var.image_postgresql}"
#        args = [
#          "--name",
#          "timescalepostgresql"
#        ]

        ports = ["port_postgresql"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          # "local/var-run:/var/run",
          # "/persistent/postgresql:/var/lib/postgresql",
          # "/etc/passwd:/etc/passwd",
        ]

      }

      env = {
        "POSTGRES_DB"       = "grafana",
        "POSTGRES_USER"     = "grafana",
        "POSTGRES_PASSWORD" = "password",
        # "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
        "PGPORT"            = "5432",
        "PGDATA"            = "/persistent/postgresql/data/pgdata",
        # This SHOULD do something functionally equivalent to:
        #   echo "host all all all $POSTGRES_HOST_AUTH_METHOD" >> pg_hba.conf
        # Which SHOULD resolve our 'no pg_hba.conf entry for ************' error - but does not
        "POSTGRES_HOST_AUTH_METHOD" = "md5"
        # We should be able to brute force this with 'trust' but that doesn't work either
        # "POSTGRES_HOST_AUTH_METHOD" = "trust"
      }

      resources {
        cpu    = 200
        memory = 800
        memory_max = 1800
      }

      service {
        name = "postgresql"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-postgresql.entrypoints=http",
          "traefik.http.routers.grafana-postgresql.rule=Host(`grafana-postgresql.obs.int.jeddi.org`)",
        ]      

        port = "port_postgresql"

##        check {
##          name     = "PostgreSQL for Grafana healthcheck"
##          port     = "port_postgresql"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }

      }
    }    #  end-task  postgresql


    # task grafana  = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "grafana" {
      driver = "docker"

      # This sidecar approach should force Grafana to 'post start' after PostgreSQL - though
      # in practice Grafana should start fine without PostgreSQL answering immediately, and
      # just retry until the DB is ready.
      #lifecycle {
      #  hook = "poststart"
      #  sidecar = true
      #}

      user = "root:root"

      volume_mount {
        volume = "vol_grafana_obs"
        destination = "/persistent"
        read_only = false
      }

      config {
        image = "${var.image_grafana}"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        dns_servers = ["192.168.1.101", "192.168.1.1"]

        ports = ["port_grafana"]

        volumes = [
          # "/persistent/grafana/etc/grafana:/etc/grafana",
          # "/persistent/grafana/plugins:/var/lib/grafana/plugins/",
        ]

      }

      env {
        # Expose Grafana on this port - as with other ObsObs jobs we're sticking with standard ports for each job.
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_port_grafana}"

        # This points to /grafana is our persistent mount point - this directory defaults to /var/lib/grafana, 
        # and would contain several sub-directories: alerting/, csv/, plugins/, and png/
        # as well as the default location for the sqlite database file:   grafana.db
        # Note - plugins/ does NOT get created here at instantiation - but is at /var/lib/grafana/plugins internally - needs more experimentation
        GF_PATHS_DATA         = "/persistent/grafana"

        # This defaults to /var/log/grafana - we may have better ways of extracting logs via nomad (docker/loki) but
        # for troubleshooting it should be convenient. Frustratingly Grafana employees like to pluralise everythings.
        GF_PATHS_LOGS         = "/grafana/log"

        # GF_LOG_LEVEL “debug”, “info”, “warn”, “error”, and “critical”. Default is info
        GF_LOG_LEVEL = "warn"

        # We can send logs to console (captured by loki above), or file (dumped to GF_PATHS_LOGS above), or both with "console file"
        GF_LOG_MODE           = "console"


        # We probably want this set so we can call in dashboards from grafana.com
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTP_PROXY  = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY    = "10.0.0.0/8,172.0.0.0/8,192.168/16.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.consul"

        # Pass the plugins you want installed to Docker with the GF_INSTALL_PLUGINS environment variable as a 
        # comma-separated list. This sends each plugin name to grafana-cli plugins install ${plugin} and installs 
        # them when Grafana starts.
        GF_INSTALL_PLUGINS = ""
        # We can load this up via the template (below) to autogenerate two datasources, though we prefer to keep
        # those in the SQLite database in GF_PATHS_DATA (/opt/grafana in host) so we can modify them, and more
        # conveniently add new items.  Commented out (also the template below) and create from within the UI.
        # GF_PATHS_PROVISIONING = "/local/grafana/provisioning"

        # I can't even find a reference to this on the googles in 2022-11
        # GF_LIVE_ALLOWED_ORIGINS = "http://*"

        # This is another poorly documented feature, and I'm doubtful that we need it.
        # GF_FEATURE_TOGGLES_ENABLE = "ngalert"



        # Database configuration - can be moved to a grafana.ini (asset) at some point, though this method
        # allows pulling in from Nomad key-value KV
        GF_DATABASE_TYPE = "postgres"
        # GF_DATABASE_HOST = "grafana-postgresql.obs.int.jeddi.org:${NOMAD_HOST_PORT_port_postgresql}"
        # GF_DATABASE_HOST = "${NOMAD_IP_grafana_int_jeddi_postgresql}:${NOMAD_HOST_PORT_port_postgresql}"
        #GF_DATABASE_HOST = "${NOMAD_ADDR_port_postgresql}"
        #GF_DATABASE_NAME = "grafana"
        #GF_DATABASE_USER = "grafana"
        #GF_DATABASE_PASSWORD = "password"

        # GF_DATABASE_HOST = "grafana-postgresql.obs.int.jeddi.org"
        # GF_DATABASE_HOST = "*************:25097"
        GF_DATABASE_HOST = "${NOMAD_ADDR_port_postgresql}"
        GF_DATABASE_NAME = "grafana"
        GF_DATABASE_USER = "grafana"
        GF_DATABASE_PASSWORD = "password"

        # With Postgresql can be: "require" (default), "verify-full", "verify-ca", and "disable"
        GF_DATABASE_SSL_MODE = "disable"

        # GF_DATABASE_LOG_QUERIES = "true"

      }

      service {
        name = "grafana"
        port = "port_grafana"

#        check {
#          type = "http"
#          port = "port_grafana"
#          path = "/api/health"
#          interval = "5s"
#          timeout = "2s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.entrypoints=http",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.int.jeddi.org`)",
        ]      
      }

      resources {
        cpu    = 200
        memory = 600
        memory_max = 1000
      }

#### Disabling this entirely as we will create the data sources and persist them in the /grafana
#### storage mapped above onto host's /opt/grafana structure.  

#      template {
#        data        = <<EOH
#
#apiVersion: 1
#datasources:
#
#  - name: ObsObsPrometheus
#    type: prometheus
#    access: proxy
#    url: http://tl0992obsobs01.obs.test.nsw.education:9090
#    #jsonData:
#    #    httpHeaderName1: 'X-Scope-OrgID'
#    #secureJsonData:
#    #    httpHeaderValue1: 'testobsobs'
#
#  - name: ObsObsLoki
#    type: loki
#    access: proxy
#    url: http://tl0992obsobs01.obs.test.nsw.education:3100
#
#
#EOH
#        destination = "/local/grafana/provisioning/datasources/ds.yaml"
#      }

    } // end-task grafana



  }

}
