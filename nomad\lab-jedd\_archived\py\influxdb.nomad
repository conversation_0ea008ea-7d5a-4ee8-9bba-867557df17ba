
// jedd lab py - influxdb nomad job

job "influxdb" {
  datacenters = ["PY"]

  type        = "service"

  group "influxdb" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port "port_influxdb"  {
        static = 8086
      }
    }

    task "influxdb" {

      driver = "docker"

      # user = "root:root"

      config {
        image = "influxdb:latest"

        ports = ["port_influxdb"]

        # Needed, lest we get permission denied errors for /var/lib/influxdb2/engine
        privileged = true

        volumes = [
          "/opt/influxdb:/var/lib/influxdb2"
        ]

        logging  {
            type = "loki"
            config {
              loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
              loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
            }
          }


        args = [
          "--log-level", "debug"
        ]

      }

      env = {
        "DOCKER_INFLUXDB_INIT_MODE"     = "setup",
        "DOCKER_INFLUXDB_INIT_USERNAME" = "admin",
        "DOCKER_INFLUXDB_INIT_PASSWORD" = "bigsecret",
        "DOCKER_INFLUXDB_INIT_ORG"      = "jeddi",
        "DOCKER_INFLUXDB_INIT_BUCKET"   = "bucket"
      }

      template {
        data = file("assets/influxdb-config.yml")
        destination = "local/influxdb.yaml"
      }

      resources {
        cpu    = 512
        memory = 512
        memory_max = 1024
      }

      service {
        name = "influxdb"
        port = "port_influxdb"

        check {
          name     = "Influxdb healthcheck"
          port     = "port_influxdb"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.influxdb.entrypoints=http,https",
          "traefik.http.routers.influxdb.rule=Host(`influxdb.obs.int.jeddi.org`)",
          "traefik.http.routers.influxdb.tls=false",
        ]

      }

    }
  }
}


