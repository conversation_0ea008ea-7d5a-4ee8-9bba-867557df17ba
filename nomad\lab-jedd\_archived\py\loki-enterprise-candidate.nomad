# Obtained via <PERSON> (Austria) on Grafna slack (#mimir channel) 2022-04-27
# https://gist.githubusercontent.com/langerma/c67866f87bf8beb5bc2d2fdad9998b9a/raw/68a50164989aa1fc7eee5300a5a22d06ab74afbf/loki.nomad.hcl



job "loki" {
  datacenters = ["hetzner"]
  type        = "service"

  group "loki-distributor" {
    count = 2

    network {
      dns {
        servers = ["***********"]
      }
      port "http" {
        #static = 3100
        host_network = "default"
      }
      port "grpc" {
        host_network = "default"
      }
    }

    restart {
      attempts = 3
      delay    = "30s"
      mode     = "delay"
    }

    task "loki-distributor" {
      template {
        change_mode = "noop"
        destination = "local/loki.yml"
        data        = "{{ key \"config/loki\" }}"
      }
      driver = "docker"
      env {
        JAEGER_AGENT_HOST    = "tempo.service.consul"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        image        = "grafana/loki"
        network_mode = "host"
        ports        = ["http", "grpc"]
        args = [
          "-config.file=local/loki.yml",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-target=distributor"
        ]
      }

      resources {
        cpu    = 200
        memory = 128
      }

      service {
        name = "loki-distributor"
        port = "http"
        tags = [
          "monitoring",
          "prometheus",
          "log",
          "traefik.enable=true",
          "traefik.http.services.loki.loadbalancer.server.port=${NOMAD_PORT_http}",
          "traefik.http.middlewares.lokiHeaders.headers.customRequestHeaders.X-Scope-OrgID=someid",
          "traefik.http.routers.loki.middlewares=lokiHeaders",
          "traefik.http.services.loki.loadbalancer.healthcheck.path=/ready",
          "traefik.http.routers.loki.rule=Host(`loki.example.com`) || Host(`loki-distributor.service.consul`)",
          "traefik.frontend.entryPoints=http"
        ]

        check {
          name     = "Loki HTTP"
          type     = "http"
          path     = "/ready"
          interval = "10s"
          timeout  = "5s"

          check_restart {
            limit           = 2
            grace           = "90s"
            ignore_warnings = false
          }
        }
      }
    }
  }
  group "loki-querier" {
    count = 8

    network {
      dns {
        servers = ["***********"]
      }
      port "http" {
        #static = 3100
        host_network = "default"
      }
      port "grpc" {
        host_network = "default"
      }
    }

    volume "loki" {
      type      = "host"
      source    = "loki-data"
      read_only = false
    }

    restart {
      attempts = 3
      delay    = "30s"
      mode     = "delay"
    }

    task "loki-querier" {
      template {
        change_mode = "noop"
        destination = "local/loki.yml"
        data        = "{{ key \"config/loki\" }}"
      }
      driver = "docker"
      volume_mount {
        volume      = "loki"
        destination = "/loki"
      }
      env {
        JAEGER_AGENT_HOST    = "tempo.service.consul"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        image        = "grafana/loki"
        network_mode = "host"
        ports        = ["http", "grpc"]
        args = [
          "-config.file=local/loki.yml",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-consul.hostname=consul.service.consul:8500",
          "-target=querier"
        ]
      }

      resources {
        cpu    = 500
        memory = 2048
      }

      service {
        name = "loki-querier"
        port = "http"
        tags = [
          "monitoring",
          "prometheus",
          "log",
          "traefik.enable=true",
          "traefik.frontend.entryPoints=http"
        ]

        check {
          name     = "Loki HTTP"
          type     = "http"
          path     = "/ready"
          interval = "10s"
          timeout  = "5s"

          check_restart {
            limit           = 2
            grace           = "90s"
            ignore_warnings = false
          }
        }
      }
    }
  }

  group "loki-query-frontend" {
    count = 2

    network {
      dns {
        servers = ["***********"]
      }
      port "http" {
        #static = 3100
        host_network = "default"
      }
      port "grpc" {
        host_network = "default"
      }
    }

    restart {
      attempts = 3
      delay    = "30s"
      mode     = "delay"
    }
    task "loki-query-frontend" {
      template {
        change_mode = "noop"
        destination = "local/loki.yml"
        data        = "{{ key \"config/loki\" }}"
      }
      driver = "docker"
      env {
        JAEGER_AGENT_HOST    = "tempo.service.consul"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        image        = "grafana/loki"
        network_mode = "host"
        ports        = ["http", "grpc"]
        args = [
          "-config.file=local/loki.yml",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-consul.hostname=consul.service.consul:8500",
          "-target=query-frontend,ruler"
        ]
      }

      resources {
        cpu    = 400
        memory = 512
      }

      service {
        name = "loki-query-frontend"
        port = "http"
        tags = [
          "monitoring",
          "prometheus",
          "log",
          "traefik.enable=true",
          "traefik.http.services.loki-query-frontend.loadbalancer.server.port=${NOMAD_PORT_http}",
          "traefik.http.middlewares.lokiHeaders.headers.customRequestHeaders.X-Scope-OrgID=someid",
          "traefik.http.routers.loki-query-frontend.middlewares=lokiHeaders",
          "traefik.http.services.loki-query-frontend.loadbalancer.healthcheck.path=/ready",
          "traefik.http.routers.loki-query-frontend.rule=Host(`querier.loki.example.com`) || Host(`loki-query-frontend.service.consul`)",
          "traefik.frontend.entryPoints=http"
        ]

        check {
          name     = "Loki HTTP"
          type     = "http"
          path     = "/ready"
          interval = "10s"
          timeout  = "5s"

          check_restart {
            limit           = 2
            grace           = "90s"
            ignore_warnings = false
          }
        }
      }
    }
  }

  group "loki-ingester" {
    count = 8
    constraint {
      operator = "distinct_hosts"
      value    = "true"
    }
    network {
      dns {
        servers = ["***********"]
      }
      port "http" {
        #static = 3100
        host_network = "default"
      }
      port "grpc" {
        host_network = "default"
      }
    }

    volume "loki" {
      type      = "host"
      source    = "loki-data"
      read_only = false
    }

    restart {
      attempts = 3
      delay    = "30s"
      mode     = "delay"
    }

    task "loki-ingester" {
      template {
        change_mode = "noop"
        destination = "local/loki.yml"
        data        = "{{ key \"config/loki\" }}"
      }
      driver = "docker"
      volume_mount {
        volume      = "loki"
        destination = "/loki"
      }
      env {
        JAEGER_AGENT_HOST    = "tempo.service.consul"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        image        = "grafana/loki"
        network_mode = "host"
        ports        = ["http", "grpc"]
        args = [
          "-config.file=local/loki.yml",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-target=ingester"
        ]
      }

      resources {
        cpu    = 400
        memory = 1024
      }

      service {
        name = "loki-ingester"
        port = "http"
        tags = [
          "monitoring",
          "prometheus",
          "log",
          "traefik.enable=true",
          "traefik.frontend.entryPoints=http"
        ]

        check {
          name     = "Loki HTTP"
          type     = "http"
          path     = "/ready"
          interval = "10s"
          timeout  = "5s"

          check_restart {
            limit           = 2
            grace           = "90s"
            ignore_warnings = false
          }
        }
      }
    }
  }
  group "loki-compactor" {
    count = 1

    network {
      dns {
        servers = ["***********"]
      }
      port "http" {
        #static = 3100
        host_network = "default"
      }
      port "grpc" {
        host_network = "default"
      }
    }

    restart {
      attempts = 3
      delay    = "30s"
      mode     = "delay"
    }

    task "loki-compactor" {
      template {
        change_mode = "noop"
        destination = "local/loki.yml"
        data        = "{{ key \"config/loki\" }}"
      }
      driver = "docker"
      env {
        JAEGER_AGENT_HOST    = "tempo.service.consul"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        image        = "grafana/loki"
        network_mode = "host"
        ports        = ["http", "grpc"]
        args = [
          "-config.file=local/loki.yml",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-target=compactor"
        ]
      }

      resources {
        cpu    = 100
        memory = 512
      }

      service {
        name = "loki-compactor"
        port = "http"
        tags = [
          "monitoring",
          "prometheus",
          "log",
          "traefik.enable=true",
          "traefik.frontend.entryPoints=http"
        ]

        check {
          name     = "Loki HTTP"
          type     = "http"
          path     = "/ready"
          interval = "10s"
          timeout  = "5s"

          check_restart {
            limit           = 2
            grace           = "90s"
            ignore_warnings = false
          }
        }
      }
    }
  }
}

