
# Loki-monolithic - a version 3.4 revisit from scratch in <PERSON><PERSON>'s lab

# The r/w/b variant was just too much mucking about to get working sanely.

# This uses a garage.dev S3 endpoint for long term storage.

# skopeo copy                                               \
#     docker://registry.hub.docker.com/grafana/loki:3.4.2   \
#     docker://registry.obs.int.jeddi.org/loki:3.4.2


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  # This pulls from shell environment - should be 'DG' or 'PY'.
  # Note we don't define it here, that's not how Nomad variables work 
  # unfortunately, but later we can use it as `var.nomad_dc`.
  default = ""
  description = "DC to constrain and run this job - set in shell environment."
}

locals {
  image_loki = var.nomad_dc == "DG" ?  "dg-pan-01.int.jeddi.org:5000/loki:3.4.2" : "registry.obs.int.jeddi.org/loki:3.4.2"
  consul_master = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:8500"          : "py-mon-01.int.jeddi.org:8500"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123456]"                    : "py-hac-0[123]"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "loki-monolithic" {
  datacenters = [ var.nomad_dc ]

  update {
    max_parallel = 1
    health_check = "checks"
    min_healthy_time = "10s"
    healthy_deadline = "3m"
    progress_deadline = "5m"
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
   }

  # Group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-monolithic" {
    count = 1

    ephemeral_disk {
      # Size of disk in MB - used in job placement heuristics, but not enforced by Nomad.
      size = 1000

      # Make a best-effort to migrate data if we're moving between clients, will obviously
      # fail (gracefully) if original client disappears (patching etc), but works if it
      # a client is drained.
      migrate = true

      # Make a best-effort to deploy updated allocations to the same machine - will
      # relocate /local and /alloc/data if / when it needs to move (implies 'migrate').
      sticky = true
    }

    network {
      port "port_http" {
        to = 3100 
        }
    }

    # Task  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "loki-monolithic" {
      driver = "docker"
      user = "nobody"

      config {
        image = local.image_loki

        logging {
          type = "loki"

          config {
            loki-url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},service_name=loki-read"
          }
        }

        ports = [
          "port_http",
        ]

        volumes = [
          # In jedd-lab we don't need persistence, and in DOE we would prefer to
          # eschew NFS due to problems with it in 2025-02/03.
          # "/opt/sharednfs/loki-rw:/loki"
        ]

        args = [
          # Monolithic target.
          "-target=all",

          "-config.file=/local/config.yml",

          # Probably unused in this instance, but harmless.
          "-config.expand-env=true",

          # This dumps configuration, in reverse order at startup.
          # (This then reads sensibly in Grafana -> Loki GUI.)
          "-log-config-reverse-order",
        ]
      }

      template {
        data = file("assets/loki-monolithic-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
      }
      
      service {
        name = "loki"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki.entrypoints=https",
          "traefik.http.routers.loki.rule=Host(`loki.obs.int.jeddi.org`)",
        ]

        check {
          name = "Loki read"
          port = "port_http"
          type = "http"
          path = "/ready"
          interval = "20s"
          timeout = "1s"

          # This lets the CLI return quickly, by assuming success. Checks continue.
          initial_status = "passing"
        }
      }

      resources {
        cpu = 512
        memory = 1024
      }

    }  // end-task

  }  // end-group

}
