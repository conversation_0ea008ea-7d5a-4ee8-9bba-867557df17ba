// metricbeat hcl2 for jedd lab

job "metricbeat" {
  type = "service"
  datacenters = ["PY"]

  group "metricbeat" {

    network {
      port "prometheus_remote_write_port" {
        static = 9201
      }
    }

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.1"
        dns_servers = ["************1"]
        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
        network_mode = "host"
      }

      service {
        name = "prometheus-remote-write"
        port = "prometheus_remote_write_port"
      }

      env {
        ELASTICSEARCH_HOSTS = "py-mon-01.int.jeddi.org"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '${ELASTICSEARCH_HOSTS}'

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"

        }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  # This fails - with a cannot bind to port on this interface
  # host: "************"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}
