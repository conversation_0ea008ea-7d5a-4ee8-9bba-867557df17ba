
// mimir (single instance version) for j<PERSON>'s nomad lab (PY / royksopp)

variables {
  consul_hostname = "royksopp.int.jeddi.org:8500"
}

job "mimir" {
  datacenters = ["PYR"]
  type = "service"

  group "mimir" {
    network {
      port "port_http" {
        # static = 19009
        static = 19009
      }
      port "port_grpc" {
        # static = 19095
        static = 19095
      }
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "royksopp"
    }

    task "mimir" {
      driver = "docker"

      volume_mount {
        volume = "vol_mimir"
        destination = "/mnt/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir"
        dns_servers = ["*************"]
        ports = ["port_http", "port_grpc"]
        volumes = []
        args = [
          "-config.file=/local/mimir.yml",
        ]
      }

      resources {
        cpu = 500
        memory = 4096
      }

      service {
        name = "mimir-ruler"
        port = "port_http"
      }

      service {
        name = "openmetrics"
        port = "port_http"
      }

      service {
        name = "mimir-querier"
        port = "port_http"
      }

      service {
        name = "mimir-store-gateway"
        port = "port_http"
      }

      service {
        name = "mimir"
        port = "port_http"




      }

      service {
        name = "mimir-query-frontend"
        port = "port_http"
        tags = ["traefik.enable=true"]

#        check {
#          type = "http"
#          port = "port_http"
#          path = "/services"
#          interval = "30s"
#          timeout = "5s"
#        }
      }

      template {
        data = <<EOH

# Do not use this configuration in production.
# It is for demonstration purposes only.
multitenancy_enabled: false

blocks_storage:
  backend: filesystem
  bucket_store:
    sync_dir: /mnt/mimir/tsdb-sync
  filesystem:
    dir: /mnt/mimir/data/tsdb
  tsdb:
    dir: /mnt/mimir/tsdb

compactor:
  data_dir: /mnt/mimir/compactor
  sharding_ring:
    kvstore:
      store: memberlist

distributor:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist

ingester:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist
    replication_factor: 1

ruler_storage:
  backend: local
  local:
    directory: /mnt/mimir/rules

server:
  http_listen_port: 19009
  log_level: error

store_gateway:
  sharding_ring:
    replication_factor: 1

EOH
        destination = "local/mimir.yml"
      }
    }
  }
}
