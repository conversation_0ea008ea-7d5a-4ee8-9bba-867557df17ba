
// mimir (single instance version) for jedd's nomad lab (PY / py-mon-01)

variables {
  consul_hostname = "py-mon-01.int.jeddi.org:8500"
}

job "mimir" {
  datacenters = ["PY"]
  type = "service"

  group "mimir" {
    network {
      port "port_http" {
        # static = 19009
        static = 19009
      }
      port "port_grpc" {
        # static = 19095
        static = 19095
      }
      port "port_alertmanager" {
        # to = 8181
        static = 8181
      }
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    task "mimir" {
      driver = "docker"

      env = {
        JAEGER_ENDPOINT = "http://py-mon-01.int.jeddi.org:14268/api/traces?format=jaeger.thrift"

        JAEGER_SERVICE_NAME = "mimir"

        JAEGER_TAGS = "hostname=py-mon-01"

        JAEGER_SAMPLER_TYPE = "probabilistic"
        JAEGER_SAMPLER_PARAM = 0.1

        # JAEGER_AGENT_HOST = "py-mon-01.int.jeddi.org"
        # JAEGER_AGENT_HOST = "************"
        # JAEGER_AGENT_HOST = "14268"

        # JAEGER_COLLECTOR_URL = "http://py-mon-01.int.jeddi.org:14268"
      }

      volume_mount {
        volume = "vol_mimir"
        destination = "/mnt/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:2.6.0"

        dns_servers = ["***********01","***********"]

        ports = ["port_http", "port_grpc", "port_alertmanager"]

        volumes = []

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        


        args = [
          "-config.file=/local/mimir.yml",

          "-config.expand-env=true",


          # Full list of modules we can pull in with -target= as follows:
          # all,       alertmanager,        compactor,  distributor,  flusher,   
          # ingester,  overrides-exporter,  purger,     querier,      query-frontend,   
          # query-scheduler,                ruler,      store-gateway,   
          #
          # Not that we do NOT want:flusher  , overrides-exporter , query-scheduler (needs 2 hosts)
          #
          # "-target=all",     ## Note that this does NOT include 'alertmanager'.
          # "-target=alertmanager,compactor,distributor,ingester,purger,querier,query-frontend,ruler,store-gateway",
          # 2022-12-08 jedd - purger generates an error (internal module) with 2.5.0-rc.0
          "-target=alertmanager,compactor,distributor,ingester,querier,query-frontend,ruler,store-gateway",

          "-log.level=info",

          "-alertmanager.sharding-ring.replication-factor=1",
          # "-alertmanager.web.external-url=http://py-mon-01.int.jeddi.org:8181/alertmanager",
          "-alertmanager-storage.filesystem.dir=alertmanageralertmanager",
          "-alertmanager-storage.local.path=/mnt/mimir/alertmanager",
          # The storage.path does not need to persist except when replication is disabled,
          # which is basically our default position with single-instance.
          "-alertmanager.storage.path=/mnt/mimir/alertmanager",
          # The configs.fallback points to a basic alertmanager configuration for any
          # tenant that lacks a configuration - without this, adding the datasource will
          # give you a 'healthcheck fail' error.
          # "-alertmanager.configs.fallback=/mnt/mimir/alertmanager/alertmanager-fallback-config.yml",

          "-alertmanager.enable-api=true",
          "-auth.multitenancy-enabled=false",

          # Enables endpoints used for cardinality analysis.
          "-querier.cardinality-analysis-enabled=true",

          # "-ruler-storage.backend=local",
          "-ruler-storage.backend=filesystem",
          "-ruler-storage.filesystem.dir=/mnt/mimir/ruler-storage",
          "-ruler-storage.local.directory=/mnt/mimir/rules",


          # 2022-12-08 jedd - this asserts persistence is not required, but experiments
          # suggest it might be, as the rule disappear (from GUI) after a restart of mimir.
          # Directory to store temporary rule files loaded by the Prometheus rule managers.
          # This directory is not required to be persisted between restarts. (default "./data-ruler/")
          "-ruler.rule-path=/mnt/mimir/data-ruler",


          "-ruler.alertmanager-url=http://py-mon-01.int.jeddi.org:8181/alertmanager",

        ]

      }

      resources {
        cpu = 100
        memory = 350
        memory_max = 1400
      }

      service {
        name = "mimir-ruler"
        port = "port_http"
      }

      service {
        name = "alertmanager"
        port = "port_alertmanager"
      }

      service {
        name = "openmetrics"
        port = "port_http"
      }

      service {
        name = "mimir-querier"
        port = "port_http"
      }

      service {
        name = "mimir-store-gateway"
        port = "port_http"
      }

      service {
        name = "mimir"
        port = "port_http"
      }

      service {
        name = "mimir-query-frontend"
        port = "port_http"
        tags = ["traefik.enable=true"]

        check {
          type = "http"
          port = "port_http"
          path = "/services"
          interval = "30s"
          timeout = "5s"
        }
      }

      template {
        data = <<EOH

# Do not use this configuration in production.
# It is for demonstration purposes only.
multitenancy_enabled: false

blocks_storage:
  backend: filesystem
  bucket_store:
    sync_dir: /mnt/mimir/tsdb-sync
  filesystem:
    dir: /mnt/mimir/data/tsdb
  tsdb:
    dir: /mnt/mimir/tsdb

compactor:
  data_dir: /mnt/mimir/compactor
  sharding_ring:
    kvstore:
      store: memberlist

distributor:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist

ingester:
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: memberlist
    replication_factor: 1

ruler_storage:
  backend: filesystem
  local:
    directory: /mimir/rules

server:
  http_listen_port: 19009
  log_level: error

store_gateway:
  sharding_ring:
    replication_factor: 1

EOH
        destination = "local/mimir.yml"
      }
    }
  }
}
