
# minio - single node instance for <PERSON><PERSON>'s lab (PY) to provide block storage layer
#         potential for mimir


#  @TODO resolve the second port_api in services (it simply breaks and indicates the network port hasn't been defined

#  @TODO resolve the port binding - I've changed the /etc/nomad/client.hcl to include specific host-network stanzas
#        for 'default' and 'public'(now unused) to interface ens192.  Otherwise it was binding to ************ (the docker
#        virtual network) only, despite the 0.0.0.0 as an [args] passed in.

job "minio" {

  datacenters = ["PY"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "minio" {

    network {
      # mode = "bridge"
      # mode = "host"

      port "port_minio" {
        static = 9001
        to = 9001
        # host_network = "public"
      }
      port "port_api " {
        static = 9000
        to = 9000
        # host_network = "public"
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    volume "vol_minio" {
      type = "host"
      source = "vol_minio"
      read_only = false
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    ephemeral_disk {
      size = 300
    }

    task "minio" {
      driver = "docker"

      volume_mount {
        volume      = "vol_minio"
        destination = "/data"
        read_only   = false
      }

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true",
        "MINIO_ROOT_USER" = "admin",
        "MINIO_ROOT_PASSWORD" = "bigsecret"
#        "NODE_RED_ENABLE_PROJECTS" = "true"
      }

      config {
        # image = "minio/minio:latest"
        image = "minio/minio:RELEASE.2023-01-31T02-24-19Z"

        dns_servers = ["*************","***********"]
        # ports = ["port_minio", "port_api"]
        ports = ["port_minio"]

        args = [ 
          "server", "/data",
#           "--console-address",  "************:9001"
           "--console-address",  "0.0.0.0:9001"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

        }

      resources {
        cpu    = 128
        memory = 256
        memory_max = 512
      }

#      service {
#        name = "api"
#        port = "port_api"
#      }

      service {
        name = "http"
        port = "port_minio"

        tags = [
           "traefik.enable=true",
           "traefik.http.routers.minio.rule=Host(`minio.int.jeddi.org`)",
           "traefik.http.routers.minio.tls=false",
        ]

#        check {
#          name     = "minio_alive"
#          type     = "http"
#          port     = "port_minio"
#          path     = "/minio/health/live"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
