
# NodeRED for <PERSON><PERSON>'s lab (PY) to provide telegraf phone-home configurations
#
# <PERSON>olen then heavily modified from https://github.com/leowmjw/nomad-box


job "nodered" {

  datacenters = ["PY"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "nodered" {

    network {
      port "port_nodered" {
        # Without traefik:
        # static = 1880

        # With traefik:
        to = 1880
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    volume "vol_nodered" {
      type = "host"
      source = "vol_nodered"
      read_only = false
    }

    ephemeral_disk {
      size = 300
    }

    task "nodered" {
      driver = "docker"

      volume_mount {
        volume      = "vol_nodered"
        destination = "/data"
        read_only   = false
      }

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
#        "NODE_RED_ENABLE_PROJECTS" = "true"
      }

      config {
        image = "nodered/node-red:latest"
        ports = ["port_nodered"]
#        2022-05-17 jedd - note that highest level logging does not give us notifications of endpoints being hit
#        args = [ 
#          "-D" , "logging.console.audit=true",
#          "-D" , "logging.console.level=trace",
#        ]
        }

      resources {
        cpu    = 500 # 500 MHz
        memory = 256 # 256MB
      }

      service {
        name = "nodered"

        tags = [
           "traefik.enable=true",
           "traefik.http.routers.nodered.rule=Host(`nodered.int.jeddi.org`)",
           "traefik.http.routers.nodered.tls=false",
           "traefik.http.routers.nodered.entrypoints=http,nodered",
        ]

        port = "port_nodered"
        check {
          name     = "alive"
          type     = "tcp"

          # name     = "http"
          # type     = "http"
          # path     = "/admin"

          interval = "30s"
          timeout  = "5s"
        }
      }

    }

  }
}
