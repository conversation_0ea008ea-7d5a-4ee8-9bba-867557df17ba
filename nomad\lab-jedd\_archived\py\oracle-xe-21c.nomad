// jedd lab - gvenzl/oracle-xe

// docker commandline is:
//    Run a new db container - basic:
//     docker run -d -p 1521:1521 -e ORACLE_PASSWORD=<your password> gvenzl/oracle-xe

// Database will reside under /u01/app/oracle/oradata/XE

// Docker hub page:
//    https://hub.docker.com/r/gvenzl/oracle-xe

// Connect to /bin/bash in docker container, then:
// sqlplus
// login with:
//    user:  SYSTEM
//    pass: oracle
// confirm life with:
//    SELECT TABLESPACE_NAME FROM USER_TABLESPACES;

// Documentation says you will have interface exposed on 1521 - login with:
//     sqlplus system/oracle@//localhost:1521/xe.oracle.docker
//     username:   SYS or SYSTEM
//     password:   oracle
//     service name:  XEPDB1
//     database app user:  my_user
//     database app password:  my_password_which_I_really_should_change

// Environment variables:
//    ORACLE_PASSWORD - for SYS and SYSTEM users
//    ORACLE_RANDOM_PASSWORD - if non-empty, eg 'yes', will create and display at startup
//    ORACLE_DATABASE - optional, create new pluggable db with this name
//    APP_USER - optional, create new db schema user, defaulting to XEPDB1 db
//    APP_USER_PASSWORD - optional, if non-empty set password for APP_USER above

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_oracle = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:5000/oracle-xe:21.3.0-slim" : "registry.obs.int.jeddi.org/oracle-xe:21.3.0-slim"

  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123]" : "py-hac-0[123]"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "oracle-xe-21c" {
  datacenters = [ var.nomad_dc ]

  type        = "service"

  group "oracle" {

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    count = 1

    network {
      port "port_oracle"  {
        static = 1521
#        to     = 1521
      }
      port "port_apex"  {
        # static = 8080
        to     = 8080
      }
    }

    task "oracle" {
      driver = "docker"
      env = {
        "ORACLE_PASSWORD" = "oracle",
        "ORACLE_ALLOW_REMOTE" = "true",
        "ORACLE_DISABLE_ASYNCH_IO" = "true"
      }

      config {
        image = "gvenzl/oracle-xe"
        image_pull_timeout = "5m"

        ports = ["port_oracle", "port_apex"]

        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      resources {
        cpu    = 512
        memory = 1000
        memory_max = 8000
      }

      service {
        name = "oracle"
        port = "port_oracle"

#        check {
#          name     = "Oracle healthcheck"
#          port     = "port_oracle"
#          type     = "tcp"
#          interval = "60s"
#          timeout  = "5s"
#          check_restart {
#            limit           = 3
#            grace           = "60s"
#            ignore_warnings = false
#          }
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oracle12.rule=Host(`oracle12.obs.int.jeddi.org`)",
          "traefik.http.routers.oracle12.tls=false",
          "traefik.http.routers.oracle12.entrypoints=http,https",
        ]

      }
    }
  }
}
