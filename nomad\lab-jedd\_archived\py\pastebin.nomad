job "pastebin-server" {
  type        = "service"

  datacenters = ["PY"]


  group "pastebin-server" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    ephemeral_disk {
      migrate = true
      size    = 300
      sticky  = true
    }

    network {
      port "port_pastebin" {
        to = 80
        }
      }

    task "pastebin-server" {
      driver = "docker"



      config {
        image = "mkodockx/docker-pastebin"

#        args = [
#          "--port",
#          "7777"
#        ]

        ports = ["port_pastebin"]



      }


      resources {
        cpu    = 256
        memory = 128
      }

      service {
        name = "pastebin-server"
        port = "port_pastebin"

        check {
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}

