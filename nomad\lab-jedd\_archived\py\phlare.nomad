
// jedd lab py - phlare nomad job

job "phlare" {
  datacenters = ["PY"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "phlare" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_phlare" {
      type = "host"
      source = "vol_phlare"
      read_only = false
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    network {
      port "port_phlare"  {
        static = 4100
      }
    }

    task "phlare" {

      driver = "docker"

      volume_mount {
        volume      = "vol_phlare"
        destination = "/data"
        read_only   = false
      }

      config {
        image = "grafana/phlare:0.1.0"

        dns_servers = ["*************"]

        ports = ["port_phlare"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

        args = [
          "--config.file",
          "local/phlare.yaml",
        ]

      }

      template {
        data = file("assets/phlare-config.yml")
        destination = "local/phlare.yaml"
      }

      resources {
        cpu    = 512
        memory = 512
      }

      service {
        name = "phlare"
        port = "port_phlare"

        check {
          name     = "Phlare healthcheck"
          port     = "port_phlare"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.phlare.rule=Host(`phlare.int.jeddi.org`)",
          "traefik.http.routers.phlare.tls=false",
        ]

      }

    }
  }
}

