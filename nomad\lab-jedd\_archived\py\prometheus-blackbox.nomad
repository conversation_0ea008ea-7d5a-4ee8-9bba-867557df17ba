// jedd lab (PY) - prometheus-blackbox  - exclusively for blackbox checks

job "prometheus-blackbox" {
  type = "service"
  datacenters = ["PY"]

  group "prometheus-blackbox" {
    
    # For long-term-storage (LTS) of time-series TSDB
    volume "vol_prometheus_blackbox"  {
      type = "host"
      source = "vol_prometheus_blackbox"
      read_only = false
      }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    network {
      port "port_prometheus" {
        static = 9091
      }
  	}

    task "prometheus-blackbox" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus_blackbox"
        destination = "/prometheus"
        read_only = false
      }


      config {
        # image = "https://docker.io/prom/prometheus:v2.28.1"
        image = "https://docker.io/prom/prometheus:v2.32.0"
        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--storage.tsdb.retention.time=1y" ,
          "--config.file=/etc/prometheus/prometheus.yml"
        ]
        dns_servers = ["*************"]

#        logging {
#          type = "loki"
#          config {
#            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
#          }
#        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      service {
        name = "prometheus-http"
        port = "port_prometheus"

       check {
         type = "http"
         port = "port_prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }

      artifact {
      source = "git::ssh://<EMAIL>/prometheus-configuration"
      destination = "local/prometheus-configuration"
      options {
        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}
  scrape_interval: 1m

scrape_configs:
  - job_name: 'prometheus-blackbox'
    static_configs:
      - targets: [ py-mon-01.int.jeddi.org:{{ env "NOMAD_PORT_port_prometheus" }} ]

  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
  - job_name: 'webcheck_oliver'
    metrics_path: /probe
    scrape_interval: 3m
    params:
      module: [webcheck_oliver]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below.
    - refresh_interval: 3m
      files: 
      - "/local/prometheus-configuration/py-mon-01/blackbox/blackbox_webcheck_oliver.yml"
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: py-mon-01.int.jeddi.org:9115


rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - static_configs:
      - targets:
        - py-mon-01.int.jeddi.org:9093


remote_write:
  - name: mimir-mon
    url: "http://py-mon-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }

    }
  }
}
