// jedd lab (PY) - prometheus hcl  - merged long-term-storage (LTS) into here

job "prometheus-exp" {
  type = "service"
  datacenters = ["PY"]

  group "prometheus-exp" {
    
    # For long-term-storage (LTS) of time-series TSDB
    volume "vol_prom_exp"  {
      type = "host"
      source = "vol_prom_exp"
      read_only = false
      }


    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    network {
      port "port_prometheus" {
        static = 9091
      }
  	}

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "vol_prom_exp"
        destination = "/prometheus"
        read_only = false
      }


      config {
        image = "https://docker.io/prom/prometheus:v2.32.0"
        args = [
          "--storage.tsdb.retention.time=1y" ,
          "--config.file=/etc/prometheus/prometheus.yml"
        ]
        dns_servers = ["*************", "***********"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      service {
        name = "prometheus-http"
        port = "port_prometheus"

       check {
         type = "http"
         port = "port_prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }


      artifact {
      source = "git::ssh://<EMAIL>/prometheus-configuration"
      destination = "local/prometheus-configuration"
      options {
        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        }
      }


      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}
  scrape_interval: 1m

scrape_configs:
  # @TODO prometheus-exp needs a dedicated port and we can self-monitor
  - job_name: 'prometheus-exp'
    static_configs:
      - targets: ['localhost:9091']



  # using snmp-EXPERIMENTAL exporter container
  - job_name: 'snmp-unifi'
    static_configs:
      - targets:
        - ***********
        - ***********
        - ***********
        - ***********
        labels:
          # Just so we can track these specifically while experimenting with ignore_oids
          job: snmp-experimental
          source: snmp
    metrics_path: /snmp
    params:
      module: [ubiquiti_unifi]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        # EXPERIMENTAL SNMP is on 9117
        replacement: ************:9117



rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - static_configs:
      - targets:
        - py-mon-01.int.jeddi.org:9093


# remote_write:
#   - name: mimir-mon
#     url: "http://py-mon-01.int.jeddi.org:19009/api/v1/push"

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }
}
