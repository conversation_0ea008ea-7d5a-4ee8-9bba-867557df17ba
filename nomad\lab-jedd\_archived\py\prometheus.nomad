// jedd lab (PY) - prometheus hcl  - merged long-term-storage (LTS) into here


// 2025-03-18 - moved from here to jedd's repo - left here as snapshot only


job "prometheus" {
  type = "service"
  datacenters = ["PY"]

  group "prometheus" {
    
    # For long-term-storage (LTS) of time-series TSDB
    volume "promvol"  {
      type = "host"
      source = "promvol"
      read_only = false
      }


    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    # We really only want this running on the main server
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    network {
      port "prometheus" {
        static = 9090
      }
  	}

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "promvol"
        destination = "/prometheus"
        read_only = false
      }


      config {

        image = "https://docker.io/prom/prometheus:v2.42.0"

        args = [
          "--storage.tsdb.retention.time=1y" ,
          "--config.file=/etc/prometheus/prometheus.yml",

          "--enable-feature=examplar-storage"
        ]

        dns_servers = ["***********01"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/py-mon-01/prometheus/rules:/etc/prometheus/rules.d"
        ]

        network_mode = "host"
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

       check {
         type = "http"
         port = "prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }


#      artifact {
#      source = "git::ssh://<EMAIL>/prometheus-configuration"
#      destination = "local/prometheus-configuration"
#      options {
#        sshkey = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
#
#        }
#      }


      template {
        data = <<EOH

# Storage configuration.
#
# Very early - around 1.6 vintage - you used ```storage.local.memory-chunks``` instead, but that was deprecated in 1.8.
#
# Around the 1.8 version, the ```storage.local.target-heap-size``` flag was used to configure how
# much memory would be allocated to cache the most recently used chunks that were kept in memory.
#
# The default was 2GB, and with the recommendation this be at least 50% of actual allocated memory, this
# sat well with the commonly deployed 3-4GB configured instance.
#
# In 2.1+ there's references to ```chunks_head``` (memory-mapped chunks, with its own directory in the persistent file 
# hierarchy) though the massive 'configuration' page for prometheus (latest) has no reference to 'chunk', or 'cach', 
# and the only 'map' hits are for keymap, port mapping, and keyword case mapping.
#
# 2.3's prometheus --help ... shows no matches for these keywords either, and retention for tsdb seems to be the
# only knob we can turn, as "2.x has no memory limiting because it doesn't have a separate cache memory like 1.x had"
#
# Refer thread for 'prometheus-users' list from 2018-06 at:  https://groups.google.com/g/prometheus-users/c/K-63dBkJwuY
#
# Ostensibly block caching is now off-loaded entirely to kernel page cache, so the guide (rather than strict limit)
# previously set by the target heap size setting is obsoleted since ~2017.
#
# There's a few blogs since Prometheus 2 was released that look at memory consumption and ways to mitigate, but
# typically it comes down to reducing sample rate (but not less than 2m intervals) and relabelling to drop metrics
# and/or labels with high cardinality, to reduce overall memory consumption.
#
# One such deep dive is at:  https://source.coveo.com/2021/03/03/prometheus-memory/
#
# Ultimately, labels matter more than metrics (probably), and our biggest bang per buck is probably going to
# be switching to agent mode - which prioritises remote_write, sacrifices any alertmanager (which we won't use
# via prometheus native anyway) and querying (which we'd rather not use via prometheus native either, as it
# introduces too many variances in memory demands), and focuses on scraping and shipping data out, in our case
# to Mimir, while also maintaining a customised TSDB / WAL that contains only data that hasn't yet been
# shipped off to the LTS (remote_write) target.
#
# Possibly the over-subscribe / memory over-commit feature can assist in parallel - it's described here:
#   https://developer.hashicorp.com/nomad/tutorials/advanced-scheduling/memory-oversubscription
# This may be another *effective* remedy for this problem, but tries to solve the problem from a different angle.

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}
  scrape_interval: 1m

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'prometheus-agent'
    static_configs:
      - targets: ['localhost:7070']

  - job_name: 'telegraf-static'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:9273',   'royksopp.int.jeddi.org:9273',
                  'py-core-01.int.jeddi.org:9273',  'py-hub-01.int.jeddi.org:9273',
                  'py-jedd-01.int.jeddi.org:9273',  'py-jan-01.int.jeddi.org:9273',
                  'py-irc-01.int.jeddi.org:9273',   'py-georgia-01.int.jeddi.org:9273',
                  'py-bt-01.int.jeddi.org:9273',    'py-bt-02.int.jeddi.org:9273',
                  'delerium.int.jeddi.org:9273',    'py-beast-01.int.jeddi.org:9273',
                  'py-hac-01.int.jeddi.org:9273',   'py-hac-02.int.jeddi.org:9273',
                  'py-hac-03.int.jeddi.org:9273',   "py-jellyfin-01.int.jeddi.org:9273"
                  ]

  - job_name: 'alertmanager'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:9093']
    metrics_path: /metrics


  - job_name: 'windows-exporter-static'
    static_configs:
      - targets: ['shpongle.int.jeddi.org:9182']


  - job_name: 'nomad_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:4646', 'royksopp.int.jeddi.org:4646']
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']


  # pushgateway running sometimes on royksopp - for process mapping
  - job_name: 'push'
    scrape_interval: 10s
    static_configs:
      - targets: ['royksopp.int.jeddi.org:9091']



  - job_name: 'consul_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:8500']
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']

#  - job_name: 'promtail_metrics'
#    static_configs:
#      - targets: ['dg-pan-01.int.jeddi.org:9080',
#                  'jarre.int.jeddi.org:9080',
#                  'dg-hassio-01.int.jeddi.org:9080'
#                 ]
#    metrics_path: /metrics

  - job_name: 'loki_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:3100']
    metrics_path: /metrics

  - job_name: 'traefik_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:8081',
                  'royksopp.int.jeddi.org:8081']
    metrics_path: /metrics

  - job_name: 'vmware_metrics'
    static_configs:
      - targets: [
          'py-mon-01.int.jeddi.org:9512',    # py-daft-01
          'py-mon-01.int.jeddi.org:9513'     # py-punk-01
          ]
    metrics_path: /metrics

  - job_name: 'mimir_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:19009']
        labels:
          # REFER:  https://grafana.com/docs/mimir/latest/operators-guide/visualizing-metrics/requirements/
          # in monolith mode, stick with 'mimir' - in microservices mode we name per-component
          job: mimir
          # cluster and namespace MUST exist for stock dashboards to work - simple choice in a lab env
          cluster: py
          namespace: py
    metrics_path: /metrics

  - job_name: 'grafana_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:3000']
    metrics_path: /metrics

  - job_name: 'cadvisor_metrics'
    static_configs:
      # - targets: ['py-mon-01.int.jeddi.org:12345' , 'royksopp.int.jeddi.org:12345']
      - targets: ['py-mon-01.int.jeddi.org:12345' ]
    metrics_path: /metrics

  - job_name: 'docker_metrics'
    static_configs:
      # - targets: ['py-mon-01.int.jeddi.org:9323' , 'royksopp.int.jeddi.org:9323']
      - targets: ['py-mon-01.int.jeddi.org:9323' ]
    metrics_path: /metrics

  - job_name: 'tempo_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:3200']
    metrics_path: /metrics

#  - job_name: 'ping_member_servers_static'
#    # @TODO in production we're happy with very low poll rate
#    # scrape_interval: 10m
#    scrape_interval: 1m
#    static_configs:
#      # use cameras, which have no metrics capability
#      - targets: ['**************', '**************', '**************']
#    metrics_path: /probe
#    params:
#      module: [icmp]
#    relabel_configs:
#      - source_labels: [__address__]
#        target_label: __param_target
#      - source_labels: [__param_target]
#        target_label: instance
#      - target_label: __address__
#        # blackbox binds to public ethernet, not loopback
#        replacement: **************:9115




  - job_name: 'telegraf'
    consul_sd_configs:
      - server: 'py-mon-01.int.jeddi.org:8500'
        datacenter: 'py'
        services: ['telegraf', 'windows']

#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

    metrics_path: /v1/metrics
    params:
      format: ['prometheus']

  # using snmp-exporter container
  - job_name: 'snmp-unifi'
    static_configs:
      - targets:
        - ***********
        - ***********
        - ***********
        - ***********
    metrics_path: /snmp
    params:
      module: [ubiquiti_unifi]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: ************:9116


rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - static_configs:
      - targets:
        - py-mon-01.int.jeddi.org:9093


remote_write:
  - name: mimir-mon
    url: "http://py-mon-01.int.jeddi.org:19009/api/v1/push"
    send_exemplars: true

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }
}
