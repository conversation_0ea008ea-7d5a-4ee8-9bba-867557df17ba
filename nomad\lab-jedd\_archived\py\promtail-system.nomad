# promtail example from 
#  https://gist.github.com/asrivascrealytee/00856a03518e665754ed87b8224cc8ea

job "promtail-system" {
  datacenters = ["PY"]
  # type = system == forces to run on all nomad nodes
  type        = "system"

  group "promtail-system" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port  "port_promtail" {
        static = 3300
      }
      port  "promtail_9080" {
        static = 9080
      }
    }

    task "promtail" {
      driver = "docker"

      config {
        image = "grafana/promtail:master"

        volumes = [
          "/var/log:/var/log",
          "/var/lib/docker/containers:/var/lib/docker/containers",
          "local/config.yaml:/etc/promtail/config.yml"
        ]

      #  args = [
      #    "-config.file", "local/config.yaml",
      #  ]
        network_mode = "host"
      }

      template {
        data = <<EOH
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

client:
  url: http://************:3100/api/prom/push

scrape_configs:

# OS (host) logs - the full /var/log collection
- job_name: system
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlogs
      __path__: /alloc/logs/*
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}

# DOCKER logs - no tagging applied - just /var/lib/docker/containers/*/*log
- job_name: docker
  static_configs:
  - targets:
      - localhost
    labels:
      job: dockerlogs
      __path__: /var/lib/docker/containers/*/*log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}
      # we really want NOMAD_TASK_NAME to be picked up *per job* but that won't work

EOH

        destination = "local/config.yaml"
      }

      resources {
        cpu    = 50
        memory = 512
      }

      service {
        name = "promtail"
        port = "port_promtail"

        check {
          type     = "http"
          port     = "promtail_9080"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }

      }
    }
  }
}
