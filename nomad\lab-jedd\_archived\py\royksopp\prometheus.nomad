// jedd lab (PYR) - prometheus for royksopp  - merged long-term-storage (LTS) into here

job "prometheus" {
  type = "service"
  datacenters = ["PYR"]

  group "prometheus" {
    
    # For long-term-storage (LTS) of time-series TSDB
    volume "promvol"  {
      type = "host"
      source = "promvol"
      read_only = false
      }

    # For external configuration (prometheus-configuation, including alert-manager rules)
#    volume "promconfvol"  {
#      type = "host"
#      source = "promconfvol"
#      read_only = false
#      }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "prometheus" {
        static = 9090
      }
  	}

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "promvol"
        destination = "/prometheus"
        read_only = false
      }

#      volume_mount {
#        volume = "promconfvol"
#        destination = "/prometheus-configuration"
#        read_only = false
#        }

      config {
        image = "https://docker.io/prom/prometheus:v2.28.1"
        args = [
          "--storage.tsdb.retention.time=1y" ,
          "--config.file=/etc/prometheus/prometheus.yml"
        ]
        dns_servers = ["*************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

#        mounts = [
#          {
#            type = "volume"
#            target = "/prometheus-configuration"
#            source = "promconfvol"
#          }
#        ]

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml"
        ]

        network_mode = "host"
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

       check {
         type = "http"
         port = "prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}
  scrape_interval: 1m



scrape_configs:
  - job_name: 'docker_metrics'
    static_configs:
      - targets: ['py-mon-01.int.jeddi.org:9323' , 'royksopp.int.jeddi.org:9323']
    metrics_path: /metrics








EOH
        destination = "local/prometheus.yaml"
      }
    }
  }
}
