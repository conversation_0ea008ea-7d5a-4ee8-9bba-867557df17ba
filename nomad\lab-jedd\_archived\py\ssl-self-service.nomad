
// Jedd lab - ssl certificate self service - https://jira.education.nsw.gov.au/browse/OBS-442

variables {
  consul_hostname = "py-mon-01.int.jeddi.org:8500"
}

job "ssl-self-service"  {
  datacenters = ["PY"]
  type = "service"

  group "ssl-self-service" {
    network {
      port "port_http" {
        static = 8088
      }
    }

#    volume "vol_python39"  {
#      type = "host"
#      source = "vol_python39"
#      read_only = false
#    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "py-mon-01"
    }

    task "ssl-self-service" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_python39"
#        destination = "/mnt/python39"
#        read_only = false
#      }

      config {
        image = "python:3.11.2-bullseye"

        dns_servers = ["*************"]

        ports = ["port_http"]

        command = "/local/prestart.sh"

        # args  = [ "3000" ]

        volumes = [ 
#          "local/prestart.sh:/prestart.sh",
#          "local/main.py:/main.py",
#          "local/main.py:/main.py",
        ]

        network_mode = "host"
      }

      resources {
        cpu = 500
        memory = 512
      }

      service {
        name = "ssl-self-service"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.ssl-self-service.rule=Host(`ssl-self-service.int.jeddi.org`)",
          "traefik.http.routers.ssl-self-service.tls=false",
        ]

      }

      service {
        name = "ssl-self-service-web"
        port = "port_http"
        tags = ["traefik.enable=true"]

        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }
      }

      #  FILE:   prestart.sh
      #  this is our entry point - prepares the environment and launches the python script
      template {
        data = <<EOH
#! /usr/bin/env bash

pip install flask
pip install python-consul
pip install Jinja2


python3 /local/main.py

EOH
        destination = "local/prestart.sh"
        perms = "755"
      }

      #  FILE:   index.html 
      template {
        data = file("assets/ssl-self-service-index.html")
        left_delimiter = "{{{{"
        destination = "local/templates/index.html"
      }

      #  Actual target - launched by prestart.sh (above)
      template {
        data = file("assets/ssl-self-service-script.py")
        left_delimiter = "{{{{"
        destination = "local/main.py"
      }




    }
  }
}
