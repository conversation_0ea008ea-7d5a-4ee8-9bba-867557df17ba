
// jedd lab (PY) - tempo-load-generator

// Note - we need /tmp/load-generator.json as jedd didn't bother to work out how
//        to do relative paths for config volume mounts - so it's in the ./shared/ 
//        dir here but needs to be copied to /tmp.  This also explains the constraint.

job "tempo-load-generator" {

  type        = "service"

  datacenters = ["PY"]

  group "tempo-load-generator" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    task "tempo-load-generator" {
      driver = "docker"

      env = {
        "TOPOLOGY_FILE" = "/etc/load-generator.json",
        "JAEGER_COLLECTOR_URL" = "http://py-mon-01.int.jeddi.org:14268"
        # "JAEGER_COLLECTOR_URL" = "http://py-mon-01.int.jeddi.org:3200"
      }

     
      config {
        # 2023-01-31 jedd - bumped from 1.0.25 to most recent (4yo) 1.0.29
        image = "omnition/synthetic-load-generator:1.0.29"

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

        volumes = [
          "local/load-generator.json:/etc/load-generator.json"
        ]

      }


      template {
        data = file("assets/tempo-load-generator.json")
        destination = "local/load-generator.json"
      }

      resources {
        cpu    = 512
        memory = 512
      }
    }

  }
}

