
// jedd lab (PY) - tempo nomad job - standalone tempo instance

job "tempo" {
  type = "service"

  datacenters = ["PY"]

  group "tempo" {
    count = 1


    network {
      port "port_grpc" {
        static = 9095	
      }
      port "port_jaeger" {
        static = 	14268
        # to = 14268
      }
      port "port_tempo" {
        static = 3200
      }
      port "port_zipkin" {
        static = 9411
        # to = 9411
      }
    }

    volume "vol_tempo"  {
      type = "host"
      source = "vol_tempo"
      read_only = false
    }

    service {
      name = "tempo"
      port = "port_tempo"

      check {
        type     = "tcp"
        interval = "17s"
        timeout  = "5s"
      }
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.tempo.rule=Host(`tempo.obs.test.nsw.education`)",
#          "traefik.http.routers.tempo.tls=false",
#          "traefik.http.routers.tempo.entrypoints=http,https",
#        ]      
    }

    service {
      name = "zipkin"
      port = "port_zipkin"
      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.zipkin.rule=Host(`zipkin.obs.test.nsw.education`)",
#          "traefik.http.routers.zipkin.tls=false",
#          "traefik.http.routers.zipkin.entrypoints=http,https",
#        ]      
    }

    service {
      name = "jaeger"
      port = "port_jaeger"
      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.jaeger.rule=Host(`jaeger.obs.test.nsw.education`)",
#          "traefik.http.routers.jaeger.tls=false",
#          "traefik.http.routers.jaeger.entrypoints=http,https",
#        ]      
    }

    service {
      name = "grpc"
      port = "port_grpc"
    }





    task "tempo" {

      driver = "docker"

      env =  {
        JAEGER_ENDPOINT   = "http://py-mon-01.int.jeddi.org:3200/api/traces?format=jaeger.thrift"

        JAEGER_AGENT_HOST = "py-mon-01.int.jeddi.org"
        JAEGER_AGENT_HOST = "14268"

        JAEGER_COLLECTOR_URL = "http://py-mon-01.int.jeddi.org:14268"
      }

      config {
        image = "grafana/tempo:2.0.0"
       
        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        } 
        
        args = [
          "--config.file=/etc/tempo/config/tempo.yml",
        ]

        ports = [
          "port_grpc",
          "port_jaeger",
          "port_tempo",
          "port_zipkin",
        ]

        volumes = [
          "local/config:/etc/tempo/config",
        ]
        
      }

      volume_mount {
        volume = "vol_tempo"
        destination = "/mnt/tempo"
        read_only = false
      }

      template {
        data = <<EOH

auth_enabled: false

server:
  http_listen_port: {{ env "NOMAD_PORT_port_tempo" }}

distributor:
  receivers:                           
    # This configuration will listen on all ports and protocols that tempo is capable of.
    # Refer: found there: https://github.com/open-telemetry/opentelemetry-collector/tree/master/receiver
    jaeger:                            
      protocols:                       
        thrift_http:
        grpc:                          
        thrift_binary:
        thrift_compact:
    zipkin:
    otlp:
      protocols:
        http:
        grpc:
    opencensus:

  # Troubleshooting guide says to use `log_received_traces` but config page says that's deprecated
  # in favour of `log_received_spans`:
  log_received_spans:
    enabled: true
    include_all_attributes: true
    filter_by_status_error: true

querier:
  frontend_worker:
    frontend_address:  py-mon-01.int.jeddi.org:9095


ingester:
  # length of time after a trace has not received spans to consider it complete and flush it
  trace_idle_period: 10s
  max_block_duration: 5m

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together
    max_compaction_objects: 1000000    # maximum size of compacted blocks
    block_retention: 1h
    compacted_block_retention: 10m

storage:
  trace:

    backend: local

    local:
      path: /mnt/tempo/blocks

    wal:
      # where to store the the wal locally
      path: /mnt/tempo/wal             

#      # wal encoding/compression.  options: none, gzip, lz4-64k, lz4-256k, lz4-1M, lz4, snappy, zstd
#      encoding: none                   


    pool:
      # worker pool determines the number of parallel requests to the object store backend
      max_workers: 100                 
      queue_depth: 10000

metrics_generator:
  processor:
    service_graphs:
      max_items: 50000

  registry:
    external_labels:
      source: tempo
      cluster: py

  storage:
    path: /mnt/tempo/generator/wal
    remote_write:
      - url: http://py-mon-01.int.jeddi.org:19009/api/v1/push
        headers: 
          X-Scope-OrgID: py
        tls_config:
          insecure_skip_verify: true

overrides:
  metrics_generator_processors: [service-graphs, span-metrics]
  max_search_bytes_per_trace: 50000
  max_traces_per_user: 50000

EOH

        change_mode   = "signal"
        change_signal = "SIGHUP"
        destination   = "local/config/tempo.yml"
      }

      resources {
        cpu    = 700
        memory = 1200
      }
    }
  }
}
