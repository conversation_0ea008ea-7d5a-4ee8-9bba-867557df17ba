// jedd lab - TimeScaleDB (official image)


job "timescaledb" {
  datacenters = ["PYR"]
  type        = "service"

  group "timescaledb" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {

      port "port_timescaledb"  {
        # without traefik
        static = 5444
        # with traefik
        # to = 6543
      }

    }

    volume "vol_timescaledb" {
      type      = "host"
      read_only = false
      source    = "vol_timescaledb"
    }


# TASK TimescaleDB
    task "timescaledb" {
      driver = "docker"

      user = "postgres:postgres"

      config {
        image = "timescale/timescaledb:latest-pg12"
#        args = [
#          "-config.file",
#          "local/timescaledb.yaml",
#          "-log.level",
#          "warn",
#        ]
        ports = ["port_timescaledb"]
      }

      env = {
        "POSTGRES_USER"     = "postgres",
        "POSTGRES_PASSWORD" = "password",
        # "PGDATA" = "/var/lib/postgresql/data/pgdata",
        "PGDATA" = "/timescaledb/postgresql/data/pgdata",
        "POSTGRES_HOST_AUTH_METHOD" = "trust",
      }

      volume_mount {
        volume      = "vol_timescaledb"
        destination = "/timescaledb"
        read_only   = false
      }

      resources {
        cpu    = 512
        memory = 4096
      }
      service {
        name = "timescaledb"

        port = "port_timescaledb"

#        check {
#          name     = "Timescaledb healthcheck"
#          port     = "port_timescaledb"
#          type     = "http"
#          path     = "/ready"
#          interval = "20s"
#          timeout  = "5s"
#          check_restart {
#            limit           = 3
#            grace           = "60s"
#            ignore_warnings = false
#          }
#        }

      }
    }    #  END-TASK TimescaleDB



  }
}
