// jedd lab (PY) - traefik load balancer - based on learn.hashicorp example code

job "traefik" {
  # region      = "global"
  datacenters = ["PY"]
  type        = "system"

  group "traefik" {
    count = 1

    network {
      port "http" {
        static = 8888
      }

      port "api" {
        static = 8081
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        # image        = "traefik:v2.2"
        image        = "traefik:v2.10.3"
        network_mode = "host"

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
        ]
      }

      template {
        data = <<EOF
[entryPoints]
    [entryPoints.http]
    address = ":8888"
    [entryPoints.traefik]
    address = ":8081"

[api]
    dashboard = true
    insecure  = true

# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = true

    [providers.consulCatalog.endpoint]
      address = "127.0.0.1:8500"
      scheme  = "http"

# 2021-08-23 jedd - enable metrics out of traefik
[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]
    addServicesLabels = true
    addEntryPointsLabels = true
    addRoutersLabels = true
    
EOF

        destination = "local/traefik.toml"
      }

      resources {
        cpu    = 100
        memory = 128
      }
    }
  }
}

