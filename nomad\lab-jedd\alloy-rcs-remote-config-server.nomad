
// alloy-remote-config-server  (by opsplane)
// alloy-rcs

// Self-hosted endpoint to provide <PERSON><PERSON> configs over gRPC

// skopeo copy 
//        docker://registry.hub.docker.com/opsplane/alloy-remote-config-server:v0.1.0
//        docker://registry.obs.int.jeddi.org/alloy-remote-config-server:v0.1.0

// Refer:  https://github.com/opsplane-services/alloy-remote-config-server


// Custom images - to
//   a) update to localAttributes (was attributes)
//   b) change bind for gRPC and HTTP from 127.0.0.1 to 0.0.0.0
//
// Build process - noting we need golang 1.21 or better for 'toolchain':
//     cd ~/src
//     <NAME_EMAIL>:opsplane-services/alloy-remote-config-server.git
//     cd alloy-remote-config-server
//     docker build -t alloy-remote-config-server .
//     skopeo  copy  docker-daemon:alloy-remote-config-server:latest  docker://registry.obs.int.jeddi.org/alloy-remote-config-server:v0.1.1
//     



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_alloy_rcs = "registry.obs.int.jeddi.org/alloy-remote-config-server:v0.1.1"

  # host_constraint = var.nomad_dc == "DG" ? "dg-hac-01" : "py-hac-01"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-*" : "py-hac-*"

  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "alloy_rcs" {
  type = "service"
  datacenters = ["DG"]

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "alloy_rcs" {

    network {
      port "port_alloy_rcs" {
        to = 8888
        # static = 8888
      }
      port "port_alloy_rcs_http" {
        to = 8080
        # static = 8080
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    task "alloy_rcs" {
      driver = "docker"

      env = {
        # It's just easier if we have local timezone inside the container.
        "TZ" = "Australia/Sydney"

        # Format of config folder still not clear - but is mapped to /opt/sharednfs/alloy_rcs
        "CONFIG_FOLDER" = "/configs",
      }

      config {
        image = local.image_alloy_rcs

        ports = ["port_alloy_rcs"]

        args = [ ]

        # privileged = "true"

        volumes = [
#          "/local/bashrc:/root/.bashrc"
          "local/.profile:/root/.profile",
          "/opt/sharednfs/alloy_rcs/:/configs"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]alloy-remote-config-server:\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.profile"
      }

      resources {
        cpu = 50
        memory = 100
      }

      service {
        name = "alloyrcs"
        port = "port_alloy_rcs"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alloyrcs.rule=Host(`alloyrcs.obs.int.jeddi.org`)",
          "traefik.http.routers.alloyrcs.tls=false",
          "traefik.http.routers.alloyrcs.entrypoints=https,http",
        ]
      }

      service {
        name = "alloyrcshttp"
        port = "port_alloy_rcs_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alloyrcshttp.rule=Host(`alloyrcshttp.obs.int.jeddi.org`)",
          "traefik.http.routers.alloyrcshttp.tls=false",
          "traefik.http.routers.alloyrcshttp.entrypoints=https,http",
        ]
      }


    }

  }
}
