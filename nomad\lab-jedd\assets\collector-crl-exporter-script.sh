#!/bin/bash

# Configuration
# CRL_BASE_URL="http://internalcrl.det.nsw.edu.au/CRL"
CRL_BASE_URL="py-mon-01.int.jeddi.org/crl"

# ENDPOINT_LABEL="internalcrl.det.nsw.edu.au"
ENDPOINT_LABEL="py-mon-01.int.jeddi.org"


METRICS_FILE="/tmp/crl_metrics.prom"
METRICS_PORT="${METRICS_PORT:-8080}"
UPDATE_INTERVAL="${UPDATE_INTERVAL:-21600}"  # 6 hours in seconds

# CRL files to monitor
CRL_FILES=(
    "DOE-INT-CA.crl"
    "DOE-ISS-CA1.crl"
    "DOE-ISS-CA1+.crl"
    "DOE-ISS-CA2.crl"
    "DOE-ISS-CA2+.crl"
    "DOE-ISS-CA3.crl"
    "DOE-ISS-CA3+.crl"
    "DOE-ROOT-CA.crl"
)

# Function to convert OpenSSL date to epoch timestamp
date_to_epoch() {
    local openssl_date="$1"
    # OpenSSL format: "Sep  5 00:54:43 2025 GMT"
    # Convert to format date can parse
    date -d "$openssl_date" +%s 2>/dev/null || echo "0"
}

# Function to extract CRL file name without extension for label
get_crl_label() {
    local filename="$1"
    echo "${filename%.crl}"
}

# Function to download and parse a single CRL file
process_crl() {
    local crl_file="$1"
    local crl_url="${CRL_BASE_URL}/${crl_file}"
    local crl_label=$(get_crl_label "$crl_file")
    local temp_crl="/tmp/${crl_file}"
    
    echo "Processing CRL: $crl_file"
    
    # Download CRL file
    if ! curl -s -f -o "$temp_crl" "$crl_url"; then
        echo "ERROR: Failed to download $crl_url"
        return 1
    fi
    
    # Parse CRL with OpenSSL (try DER format first, then PEM)
    local crl_output
    if ! crl_output=$(openssl crl -in "$temp_crl" -inform DER -text -noout 2>/dev/null); then
        # If DER fails, try PEM format
        if ! crl_output=$(openssl crl -in "$temp_crl" -inform PEM -text -noout 2>/dev/null); then
            echo "ERROR: Failed to parse CRL $crl_file in both DER and PEM formats"
            rm -f "$temp_crl"
            return 1
        fi
    fi
    
    # Extract timestamps
    local last_update=$(echo "$crl_output" | grep "Last Update:" | sed 's/.*Last Update: //')
    local next_update=$(echo "$crl_output" | grep "Next Update:" | sed 's/.*Next Update: //')
    
    # Convert to epoch
    local last_update_epoch=$(date_to_epoch "$last_update")
    local next_update_epoch=$(date_to_epoch "$next_update")
    
    # Count revoked certificates
    # broken:
    # local revoked_count=$(echo "$crl_output" | grep -c "Serial Number:" || echo "0")
    # attempt to fix:
    local revoked_count=$(echo "$crl_output" | grep -c "Serial Number:")

    
    # Generate metrics
    cat >> "$METRICS_FILE" << EOF
# HELP crl_last_update_timestamp_seconds Unix timestamp of CRL last update
# TYPE crl_last_update_timestamp_seconds gauge
crl_last_update_timestamp_seconds{endpoint="$ENDPOINT_LABEL",crl_file="$crl_label"} $last_update_epoch
# HELP crl_next_update_timestamp_seconds Unix timestamp of CRL next update
# TYPE crl_next_update_timestamp_seconds gauge
crl_next_update_timestamp_seconds{endpoint="$ENDPOINT_LABEL",crl_file="$crl_label"} $next_update_epoch
# HELP crl_revoked_certificates_total Number of certificates in the CRL
# TYPE crl_revoked_certificates_total gauge
crl_revoked_certificates_total{endpoint="$ENDPOINT_LABEL",crl_file="$crl_label"} $revoked_count
EOF
    
    echo "  Last Update: $last_update ($last_update_epoch)"
    echo "  Next Update: $next_update ($next_update_epoch)"
    echo "  Revoked Certificates: $revoked_count"
    
    # Cleanup
    rm -f "$temp_crl"
    return 0
}

# Function to update all CRL metrics
update_metrics() {
    echo "Starting CRL metrics update at $(date)"
    
    # Create temporary metrics file with headers
    cat > "$METRICS_FILE" << EOF

EOF
    
    # Process each CRL file
    local success_count=0
    for crl_file in "${CRL_FILES[@]}"; do
        if process_crl "$crl_file"; then
            ((success_count++))
        fi
    done
    
    echo "CRL update completed: $success_count/${#CRL_FILES[@]} files processed successfully"
    echo "Metrics written to: $METRICS_FILE"
    echo ""
}

# Function to serve metrics via HTTP
serve_metrics() {
    echo "Starting metrics HTTP server on port $METRICS_PORT"
    while true; do
        {
            echo -e "HTTP/1.1 200 OK\r\nContent-Type: text/plain; version=0.0.4\r\n\r"
            cat "$METRICS_FILE" 2>/dev/null || echo "# Metrics not available"
        } | nc -l -p "$METRICS_PORT" -q 1
    done
}

# Function to run the update loop
update_loop() {
    while true; do
        update_metrics
        echo "Sleeping for $UPDATE_INTERVAL seconds..."
        sleep "$UPDATE_INTERVAL"
    done
}

# Main execution
main() {
    echo "CRL Monitor starting..."
    echo "Base URL: $CRL_BASE_URL"
    echo "Metrics Port: $METRICS_PORT"
    echo "Update Interval: $UPDATE_INTERVAL seconds"
    echo ""
    
    # Initial metrics update
    update_metrics
    
    # Start background processes
    serve_metrics &
    SERVER_PID=$!
    
    update_loop &
    UPDATE_PID=$!
    
    # Handle cleanup on exit
    trap 'echo "Shutting down..."; kill $SERVER_PID $UPDATE_PID 2>/dev/null; exit 0' INT TERM
    
    # Wait for processes
    wait
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi


