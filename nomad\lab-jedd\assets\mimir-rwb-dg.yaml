
multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "warn"

  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

alertmanager:
  sharding_ring:
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/alertmanagers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  data_dir: /mimir/alertmanager/

alertmanager_storage:
  backend: "filesystem"
  local:
    path: "/mimir/alertmanager/"

distributor:
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    replication_factor: 3 # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

# frontend_worker:
#   scheduler_address: "mimir-rwb-backend.service.dc-cir-un-prod.collectors.obs.nsw.education:8096"
#   id: {{ env "node.unique.name" }}
#   grpc_client_config:
#     grpc_compression: "snappy"

frontend:
  results_cache:
    backend: redis
    redis:
      endpoint: redis.obs.int.jeddi.org:6379
      db: 4
  cache_results: true
  scheduler_address: "mimir-rwb-backend.service.dg.collectors.obs.int.jeddi.org:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  port: {{ env "NOMAD_PORT_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"

query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  ring:
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/rulers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "https://mimir-rwb-read.obs.int.jeddi.org/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  alertmanager_url: "https://alertmanager.obs.int.jeddi.org"
  query_frontend:
    address: "https://mimir-rwb-read.obs.int.jeddi.org"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

querier:
  #query_ingesters_within: 13h0m0s
  #query_store_after: 8h0m0s
  #query_store_after: 6h # Setting to 6 hours to go to the store(compacted blocks) sooner
  #query_ingesters_within: 7h # 
  query_store_after: 12h0m0s

compactor: #backend component
  data_dir: /mimir/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway: #backend component
  sharding_ring:
    replication_factor: 3 # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      # We use mimir-rwb/ so that monolithic mimir can still be run, using /mimir in Consul
      prefix: "mimir-rwb/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage: #only used by ingestors, write-tasks
  backend: s3
  bucket_store:
    sync_dir: /mimir/tsdb-sync/{{ env "node.unique.name" }}/

  tsdb:
    # Required to be persistent between restarts
    dir: /mimir/tsdb/{{ env "node.unique.name" }}/
    retention_period: 8h0m0s
    flush_blocks_on_shutdown: false # If false then incomplete blocks will be used on startup
    memory_snapshot_on_shutdown: false #(experimental) True to enable snapshotting of in-memory TSDB data on disk when shutting down.

common:
  storage:
    backend: s3
    s3:
      # 2025-05-02 jedd - 'mimir-rwb' / 'mimir-rwb-key' on DG garage
      #access_key_id: GK72529a176fb40beefd2dee50
      access_key_id: {{ key "mimir-rwb/access_key_id" }}
      #secret_access_key: 1610404f7237ab2c6e184fb4403728ad87bf5deab13128c67d638142324a9f48
      secret_access_key: {{ key "mimir-rwb/secret_access_key" }}
      bucket_name: mimir-rwb
      # Endpoint must be JUST hostname + port (no URI type, trailing slash)
      endpoint: garage-s3.obs.int.jeddi.org
      region: garage

limits:
  accept_ha_samples: true
  max_global_series_per_user: 0
  max_label_names_per_series: 200
  out_of_order_time_window:  5m
  ingestion_rate: 200000
  ingestion_burst_size: 3000000
  # Value can be 1y, 2y, etc - 0 means never delete data. 
  compactor_blocks_retention_period: 0
  compactor_block_upload_enabled: true

