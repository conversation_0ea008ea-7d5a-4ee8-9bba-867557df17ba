extensions:
  health_check:
    # Explicitly set the health check endpoint
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"

receivers:
  otlp:
    protocols:
      http:
        endpoint: 0.0.0.0:8088

exporters:
  # Send logs to 'loki-monolithic' instance
  otlphttp/logs:
    # Loki has OTLP native ingest, so OpenTel's abandoning a Loki exporter.
    # endpoint: "https://loki.obs.int.jeddi.org/loki/api/v1/push"
    endpoint: https://loki.obs.int.jeddi.org/otlp

  # Send logs to 'loki-rwb' instance
  otlphttp/logsrwb:
    endpoint: https://loki-rwb.obs.int.jeddi.org/otlp

  # Send logs to ClickHouse
  clickhouse:
    endpoint: https://clickhouse-jedd.obs.int.jeddi.org:443
    database: chdb
    username: jedd
    password: bigsecret
    logs_table_name: otel_logs
    traces_table_name: otel_traces
    metrics_table_name: otel_metrics
    timeout: 30s
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s

processors:
  attributes:
    actions:
      - action: insert
        key: provenance
        value: "otel-logs"
      - action: insert
        key: service.name
        value: "otel-logs"

  # Optional: Add batch processor for better ClickHouse performance
  batch:
    timeout: 10s
    send_batch_size: 1024

service:
  extensions:
    - health_check

  pipelines:
    logs:
      receivers: [otlp]
      processors: [attributes, batch]
      exporters: [otlphttp/logs, otlphttp/logsrwb, clickhouse]

  telemetry:
    logs:
      level: debug
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
