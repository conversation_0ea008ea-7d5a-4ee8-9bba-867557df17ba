server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

distributor:
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

  receivers: # this configuration will listen on all ports and protocols that tempo is capable of.
    otlp:
      protocols:
        http:
        grpc:
          endpoint: 0.0.0.0:{{ env "NOMAD_PORT_otlp" }}

ingester:
  max_block_duration: 5m
  lifecycler:
    ring:
      kvstore:
        store: consul
        prefix: tempo/
        consul:
          host: {{ env "attr.unique.network.ip-address" }}:8500
      replication_factor: 3

compactor:
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

  compaction:
    block_retention: 336h  #Duration to keep blocks.  Default is 14 days (336h).

querier:
  #scheduler-address
  frontend_worker:
    # CONSUL - we're not using consul DNS
    # frontend_address: tempo-query-frontend-grpc.service.consul:9095

    # This fails - generating errors from the querier like:
    # level=error caller=frontend_processor.go:84
    #  msg="error processing requests" 
    #  address=***********5:443 
    #  err="rpc error: code = Unimplemented desc = unexpected HTTP status code received from
    #       server: 404 (Not Found); transport: received unexpected content-type \"text/plain; charset=utf-8\""
    # frontend_address: tempo-out-grpc.obs.int.jeddi.org:443

    # This generates these types of complaints - it doesn't like the URI:
    #  caller=dns_resolver.go:225 msg="failed DNS A record lookup" err="lookup https on *************:53: no such host"
    # frontend_address: https://tempo-out-grpc.obs.int.jeddi.org

    # This generates these types of complaints - it seems to think it wants to talk to an HTTP endpoint, not grpc:
    # level=error 
    # caller=frontend_processor.go:84
    # msg="error processing requests" 
    # address=***********4:443 
    # err="rpc error: code = Unimplemented desc = unexpected HTTP status code received from
    #      server: 404 (Not Found); transport: received unexpected content-type \"text/plain; charset=utf-8\""
    # frontend_address: tempo-query-frontend-grpc.obs.int.jeddi.org

    # But calling http endpoint makes it clear it does want grpc:
    # level=error 
    # caller=frontend_processor.go:84
    # msg="error processing requests" 
    # address=***********4:443 
    # err="rpc error: code = Unimplemented desc = unexpected HTTP status code received from 
    #      server: 404 (Not Found); transport: received unexpected content-type \"text/plain; charset=utf-8\""
    # frontend_address: tempo-out-http.obs.int.jeddi.org

    # Trying this instead -- IF we stick with 'static 9095' in job
    # frontend_address: tempo-out-grpc.obs.int.jeddi.org:{{ env "NOMAD_PORT_grpc" }}

    # Trying any random old shit, because in the worker.go file they say (and it was a
    # change made in 2022-01-26 - so NOT recently) :
    #     f.StringVar(&cfg.FrontendAddress, "querier.frontend-address", "", "Address of query frontend service, in host:port format. If -querier.scheduler-address is set as well, querier will use scheduler instead. Only one of -querier.frontend-address or -querier.scheduler-address can be set. If neither is set, queries are only received via HTTP endpoint.")
    # But trying to use a 'scheduler-address' just fails, and that's not documented *anywhere*.

    # frontend_address: tempo-query-frontend-grpc.obs.int.jeddi.org:9095

    # frontend_address: {{ env "NOMAD_ADDR_grpc" }}
    # The above generates an 'unimplemented desc = unknown service frontend.Frontend'
    # after successfully resolving to ***********4:23446

    # AHA!!!!
    # This generates a lot of errors we've seen already - but when query-frontend instantiates
    # to say py-hac-03 (************) - I see ONLY errors about contacting transport errors
    # to ***********[1245] - ie, not 53.  So it's being chatty, and getting to 53:9095, but
    # traefik is not redirecting these calls to other hosts - so need to revisit grpc forwarding,
    # but a short-term hack is to constrain query-frontend to a single host, and do the same here.
    # frontend_address: tempo-query-frontend-grpc.obs.int.jeddi.org:9095
    frontend_address: py-hac-05.int.jeddi.org:9095


metrics_generator:
  processor:
    service_graphs:
      max_items: 10000
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
  storage:
    path: {{ env "NOMAD_ALLOC_DIR" }}/tempo/wal
    remote_write:
      # Prometheus HTTP API docs suggest v1/write should be used with caution, it's
      # 'not considered efficient' and should be used with low-volume data only, with
      # a preference stated for scraping.
      #
      # Mimir does not seem to support api/v1/write.
      #
      # If setting up a prometheus to ingest this, it needs this parameter:
      # --web.enable-remote-write-receiver
      #
      # This is a LOW priority anyway until tempo ingest is sorted out.
      #
      # The metrics-generator component is used for deriving metrics from traces,
      # and is quite optional.
      - url: http://prometheus.service.consul/api/v1/write
        send_exemplars: true

storage:
  trace:
    backend: s3
    wal:
      path: {{ env "NOMAD_ALLOC_DIR" }}/tempo/wal
    local:
      path: {{ env "NOMAD_ALLOC_DIR" }}/tempo/blocks
    s3:
      bucket: tempo-distributed # how to store data in s3
      # endpoint: seaweedfs-s3.service.consul
      endpoint: garage-s3.obs.int.jeddi.org
      region: garage
      insecure: true
      #access_key: ${S3_ACCESS_KEY_ID}
      #secret_key: ${S3_SECRET_ACCESS_KEY}
      # 2025-07-14 bucket created on PY garage
      # Key name: tempo-distributed-key
      # Key ID: GK071ecef12816c0679ae2aaa7
      # Secret key: 250a5ee26e49490d21516df7934d987fdb0c0b2cbc18fa0527101814e20a47af
      # $ garage bucket allow --read --write --owner tempo-distributed --key tempo-distributed-key
      access_key: GK071ecef12816c0679ae2aaa7
      secret_key: 250a5ee26e49490d21516df7934d987fdb0c0b2cbc18fa0527101814e20a47af

overrides:
  defaults:
    metrics_generator:
      processors:
        - service-graphs
        - span-metrics
