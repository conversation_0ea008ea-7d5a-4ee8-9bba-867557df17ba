
// aws-cli in a container

// Replacement for 's3cmd' 

// skopeo copy
//        docker://registry.hub.docker.com/amazon/aws-cli:2.27.7 
//        docker://registry.obs.int.jeddi.org/aws-cli:2.27.7

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_awscli = "registry.obs.int.jeddi.org/aws-cli:2.27.7"
  # hac cluster
  #host_constraint = var.nomad_dc == "DG" ? "dg-hac-*" : "py-hac-*"
  # hac-02
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-02" : "py-hac-02"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "awscli" {
  type = "service"
  datacenters = ["DG"]

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "awscli" {

    network {
      port "port_awscli" { }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    task "awscli" {
      driver = "docker"

      env = {
      }

      config {
        image = "${local.image_awscli}"
        ports = ["port_awscli"]
        entrypoint = [ "/usr/bin/sleep" ]
        args = [" infinity"]
        privileged = "true"

        volumes = [
#          "/local/bashrc:/root/.bashrc"
          "local/:/root/"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]AWS CLI docker:\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 50
        memory = 100
      }

      service {
        name = "awscli"
        port = "port_awscli"

      }

    }

  }
}
