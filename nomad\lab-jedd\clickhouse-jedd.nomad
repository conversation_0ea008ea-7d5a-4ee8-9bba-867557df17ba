
# experimental clickhouse basic image - jedd-labs

#  Official ClickHouse image
#  skopeo copy 
#         docker://registry.hub.docker.com/library/clickhouse:25.4-jammy
#         docker://registry.obs.int.jeddi.org/clickhouse:25.4-jammy

#  Clickhouse GUI https://github.com/caioricciuti/ch-ui
#  image hosted at - ghcr.io/caioricciuti/ch-ui:latest
#  skopeo copy 
#         docker://ghcr.io/caioricciuti/ch-ui:latest  
#         docker://registry.obs.int.jeddi.org/clickhouse-ch-ui:2025-05-28


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

variables {
}

locals {
  # image_clickhouse = "clickhouse:25.4-jammy"
  image_clickhouse = "registry.obs.int.jeddi.org/clickhouse:25.4-jammy"
  image_ch_ui = "registry.obs.int.jeddi.org/clickhouse-ch-ui:2025-05-28"

  #host_constraint = ".*0475.*"
  host_constraint = ".*hac.*"

  loki_url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
  # loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "clickhouse-jedd" {

  datacenters = [ var.nomad_dc ]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "clickhouse-jedd" {

    network {
      port "port_clickhouse_http" {
         to = 8123
      }
      port "port_clickhouse_native" {
         to = 9000
      }

      port "port_ch_ui" {
         to = 5521
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    ephemeral_disk {
      size = 300
    }

    # = = = = = = = = = = = = = = = = = = = = = = = =
    task "clickhouse-jedd" {
      driver = "docker"

      #user = "1000:1000"
      user = "nobody"

      env = {
        # "CLICKHOUSE_SKIP_USER_SETUP" = "1"
        "CLICKHOUSE_USER" = "jedd",
        "CLICKHOUSE_PASSWORD" = "bigsecret",
        "CLICKHOUSE_DB" = "chdb",

      }

      config {
        image = "${local.image_clickhouse}"

        privileged = "true"

        # clickhouse wants "IPC_LOCK" - but docker / nomad supports:
        # [ "audit_write", "chown", "dac_override", "fowner", 
        #   "fsetid", "kill", "mknod", "net_bind_service", 
        #   "setfcap", "setgid", "setpcap", "setuid", "sys_chroot" ]
        # *OUR* nomad configuration as of 2025-05-22 is only:
        #   [ "net_raw" , "net_bind_service" ]
        # cap_add  = [ "all" ]

        ports = [
          "port_clickhouse_native",
          "port_clickhouse_http"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

#        command = "/bin/sleep"
#        args = [" infinity"]


        volumes = [
          "/opt/sharednfs/clickhouse-jedd:/clickhouse",

          "/opt/sharednfs/clickhouse-jedd/data:/var/lib/clickhouse/",
          "/opt/sharednfs/clickhouse-jedd/logs:/var/log/clickhouse-server/",

#          "/opt/sharednfs/clickhouse-jedd/etc-config.d:/etc/clickhouse-server/config.d",
#         "/opt/sharednfs/clickhouse-jedd/etc-users.d:/etc/clickhouse-server/users.d",


          "local/.bashrc:/root/.bashrc"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]clickhouse-jedd (docker):\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 500 
        memory = 1000
        memory_max = 1300
      }

      service {
        name = "clickhouse"
        port = "port_clickhouse_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.clickhouse-jedd.entrypoints=http,https",
          "traefik.http.routers.clickhouse-jedd.tls=false",
          "traefik.http.routers.clickhouse-jedd.rule=Host(`clickhouse-jedd.obs.int.jeddi.org`)",
        ]

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }
      }

      service {
        name = "clickhouse-native"
        port = "port_clickhouse_native"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.clickhouse-jedd-native.entrypoints=http,https",
          "traefik.http.routers.clickhouse-jedd-native.tls=false",
          "traefik.http.routers.clickhouse-jedd-native.rule=Host(`clickhouse-jedd-native.obs.int.jeddi.org`)",
        ]

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }
      }

    }  // end-task clickhouse-jedd


    # = = = = = = = = = = = = = = = = = = = = = = = =
    task "ch-ui-jedd" {
      driver = "docker"

      # user = "nobody"
      user = "0:0"
      # user = "1000:1000"

      env = {
        # "VITE_CLICKHOUSE_URL" = "https://clickhouse-jedd:443/",
        "VITE_CLICKHOUSE_URL" = "https://clickhouse-jedd.obs.int.jeddi.org"
        "VITE_CLICKHOUSE_USER" = "jedd",
        "VITE_CLICKHOUSE_PASS" = "bigsecret",

        "VITE_CLICKHOUSE_USE_ADVANCED" = "true",
      }

      config {
        image = "${local.image_ch_ui}"

        privileged = "true"


        ports = [
          "port_ch_ui",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          # "/opt/sharednfs/clickhouse-jedd:/clickhouse",
          "local/.bashrc:/root/.bashrc"
        ]
      }

#      template {
#        data        = <<EOH
#export PS1="\[\e[33;1m\]ch-ui-jedd (docker):\w# \[\e[0m\]"
#export TERM=linux
#EOH
#        destination = "local/.bashrc"
#      }

      resources {
        cpu = 500 
        memory = 500
        memory_max = 800
      }

      service {
        name = "ch-ui-jedd"
        port = "port_ch_ui"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.ch-ui-jedd.entrypoints=http,https",
          "traefik.http.routers.ch-ui-jedd.tls=false",
          "traefik.http.routers.ch-ui-jedd.rule=Host(`ch-ui-jedd.obs.int.jeddi.org`)",
        ]

      }

    }  // end-task ch-ui-jedd

  }
}
