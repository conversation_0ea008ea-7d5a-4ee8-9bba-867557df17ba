
# Collector - prometheus + blackbox + bespoke task
#
# CRL - certificate revocation lists


# Notes on CRL infra checks covered in here:
#
#  1. blackbox check custom module: [fail_if_body_not_matches_regexp]
#   This is a set of 8 filenames that should present as a 'directory contents'
#   view on the /crl URL. The regex checks are accumulative / ordered, so they
#   all need to succeed. This can be proven out by changing one letter in any of
#   those lines, and re-launching the job, and checking the output at:
#   https://collector-crl-blackbox.obs.nsw.education/probe?module=http_2xx_and_confirm_content&target=internalcrl.det.nsw.edu.au%2Fcrl%2F
#   The value for `probe_success` will change to `0`


// skopeo copy 
//        docker://registry.hub.docker.com/prom/blackbox-exporter:v0.27.0 
//        docker://registry.obs.int.jeddi.org/blackbox-exporter:v0.27.0



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
}

locals {
  #image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
  image_prometheus = "registry.obs.int.jeddi.org/prometheus:v2.45.6"

  image_blackbox = "registry.obs.int.jeddi.org/blackbox-exporter:v0.27.0"

  image_nettools = "registry.obs.int.jeddi.org/network-tools:v2025-04-28"

  #host_constraint = ".*0475.*"
  #host_constraint = ".*0992.*"
  host_constraint = ".*"

  # @TODO jedd replace with doe
  # loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-crl" {
  # @TODO jedd replace with doe
  # datacenters = ["dc-cir-un-prod"]
  datacenters = ["PY"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
  }

  # GROUP  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "prometheus" { 
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }


    network {
      port "port_prometheus" { }
      port "port_blackbox" { to = 9115 }
      port "port_exporter" { to = 8080 }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "prometheus" {
      driver = "docker"

      config {
        image = local.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",

          # "--web.external-url=http://collector-crl-prometheus.obs.nsw.education",
          "--web.external-url=http://collector-crl-prometheus.obs.int.jeddi.org",

          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Collector-CRL",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 400
        memory_max = 800
      }

      service {
        name = "prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          # "traefik.http.routers.collector-crl-prometheus.rule=Host(`collector-crl-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-crl-prometheus.rule=Host(`collector-crl-prometheus.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-crl-prometheus.tls=false",
          "traefik.http.routers.collector-crl-prometheus.entrypoints=https",
        ]

#        check {
#          type = "http"
#          port = "port_prometheus"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "10s"
#        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: collector-crl-prometheus
    env: prod

  scrape_interval: 60s

# scrape configs are almost exclusively static references to hosts, to experiment with
# relabelling, dropping, etc.

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'collector-crl-prometheus'
    scheme: 'https'
    static_configs:
      # - targets: ['collector-crl-prometheus.obs.nsw.education']
      - targets: ['collector-crl-prometheus.obs.int.jeddi.org']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop


  # docs for http prober at:
  # https://github.com/prometheus/blackbox_exporter/blob/master/CONFIGURATION.md#http_probe
  - job_name: 'collector-crl-vip' 
    static_configs:
      - targets: [ 
        #'PW0991CACR0104.detnsw.win',
        #'PW0992CACR0104.detnsw.win',
        # reminder that crl endpoints are HTTP not https
        # 'internalcrl.det.nsw.edu.au/crl/',
        # While building out in jedd's lab - a replica:
        'py-mon-01.int.jeddi.org/crl/',
        ]
    metrics_path: /probe
    params:
      #module: [http_2xx]
      module: [http_2xx_and_confirm_content]

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        # blackbox binds to public ethernet, not loopback
        # replacement: collector-crl-blackbox.obs.nsw.education
        replacement: collector-crl-blackbox.obs.int.jeddi.org

  - job_name: 'collector-crl-exporter' 
    scheme: 'https'
    static_configs:
      - targets: [ 
        # While building out in jedd's lab - a replica:
        'collector-crl-exporter.obs.int.jeddi.org',

        # DOE
        # 'collector-crl-exporter.obs.nsw.education',
        ]


# Keep this disabled until testing is completed:
remote_write:
  - name: mimir-rwb-write
    #url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    url:  "https://mimir-rwb-write.obs.int.jeddi.org/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true        


EOH
        destination = "local/prometheus.yaml"
      }
    }  // end-task "prometheus"



    # TASK blackbox-jedd-experimental = = = = = = = = = = = = = = = = = = = = = = = = =
    task "blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = local.image_blackbox

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu = 100
        memory = 50
        memory_max = 100
      }


      service {
        name = "blackbox-collector-crl"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          # "traefik.http.routers.collector-crl-blackbox.rule=Host(`collector-crl-blackbox.obs.nsw.education`)",
          "traefik.http.routers.collector-crl-blackbox.rule=Host(`collector-crl-blackbox.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-crl-blackbox.tls=false",
          "traefik.http.routers.collector-crl-blackbox.entrypoints=https,http",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

#        check {
#          type = "http"
#          port = "port_blackbox"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "10s"
#        }
      }

      template {
        data = <<EOH
modules:
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  http_2xx:
    prober: http

  http_2xx_and_confirm_content:
    prober: http
    http:
      fail_if_body_not_matches_regexp:
        - "DOE-INT-CA.crl"
        - "DOE-ISS-CA1+.crl"
        - "DOE-ISS-CA1.crl"
        - "DOE-ISS-CA2+.crl"
        - "DOE-ISS-CA2.crl"
        - "DOE-ISS-CA3+.crl"
        - "DOE-ISS-CA3.crl"
        - "DOE-ROOT-CA.crl"



  tcp_connect:
    prober: tcp

EOH
        destination = "local/config.yml"
      }

    }  // end-task "blackbox"






    # TASK exporter (parsing-script) = = = = = = = = = = = = = = = = = = = =
    task "exporter" {
      # Bespoke bash script that parses the CRL files and presents as a scrapeable
      # web page - prometheus-compatible.

      driver = "docker"

      config {
        ports = [ "port_exporter" ]

        image = local.image_nettools

        #command = "/bin/sleep"
        #args = [" infinity"]

        command = "/exporter-script.sh"

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

        volumes = [
          "local/exporter-script.sh:/exporter-script.sh"
        ]

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu = 100
        memory = 50
        memory_max = 100
      }


      service {
        name = "collector-crl-exporter"
        port = "port_exporter"

        tags = [
          "traefik.enable=true",
          # "traefik.http.routers.collector-crl-exporter.rule=Host(`collector-crl-exporter.obs.nsw.education`)",
          "traefik.http.routers.collector-crl-exporter.rule=Host(`collector-crl-exporter.obs.int.jeddi.org`)",
          "traefik.http.routers.collector-crl-exporter.tls=false",
          "traefik.http.routers.collector-crl-exporter.entrypoints=https,http",
        ]

        #meta {
        #  cir_app_id = "obs"
        #  env = "prod"
        #}

#        check {
#          type = "http"
#          port = "port_exporter"
#          path = "/-/healthy"
#          interval = "20s"
#          timeout = "10s"
#        }

      }

      template {
        data        = file("assets/collector-crl-exporter-script.sh")
        destination = "local/exporter-script.sh"
        perms = "0755"
      }


    }  // end-task "exporter"






  }
}

