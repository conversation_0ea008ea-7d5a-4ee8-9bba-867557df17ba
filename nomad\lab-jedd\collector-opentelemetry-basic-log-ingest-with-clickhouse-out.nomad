

# 2025-06-01 -- modified with CLICKHOUSE configuration file

# Borrowed from <PERSON>' splunkhec job 2025-03

# Relatively simple collector this, some of the gotchas I experienced
# avoid trying to user {{ env NOMAD_{service}_port }} in the collector 
# config.yaml - for some reason when ever this was done the endpoint would 
# not be avoidable, 

# network ports defined to go to there expected defaults inside the 
# container, the exposed port is still a high range dynamic one.

# /metrics service endpoint did not want to work unless the /healthcheck 
# service was defined and working, might be expected behaviour but I could not recall.

# 3 instances running on the 0992 hosts I think is safest.



#  skopeo copy 
#         docker://registry.hub.docker.com/otel/opentelemetry-collector-contrib:0.122.1
#         docker://registry.obs.int.jeddi.org/opentelemetry-collector-contrib:0.122.1

variables {
  image_otel = "registry.obs.int.jeddi.org/opentelemetry-collector-contrib:0.122.1"
}



job "otel-log-clickhouse" {
  datacenters = ["DG"]
  type        = "service"

#  update {
#    max_parallel      = 1
#    min_healthy_time  = "10s"
#    healthy_deadline  = "2m"
#    canary            = 1
#    auto_promote      = true
#    auto_revert       = true
#  }
  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "dg-hac-0[3]"
  }  

  group "otel-log" {
    count = 1

    network {
      port "metrics" {
        to = 8888
      }
      port "ingest" {
        to = 8088
      }
      port "healthcheck" {
        to = 13133
      }
      }

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"

#      check {
#        type = "http"
#        path = "/metrics"
#        interval = "10s"
#        timeout = "5s"
#      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-log-metrics.entrypoints=http,https",
        "traefik.http.routers.otel-log-metrics.rule=Host(`otel-log-metrics.obs.int.jeddi.org`) && Path(`/metrics`)",
        "traefik.http.routers.otel-log-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-healthcheck"
      port     = "healthcheck"
      provider = "consul"

#      check {
#        type = "http"
#        path = "/health/status"
#        interval = "10s"
#        timeout = "5s"
#      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-healthcheck.entrypoints=https,http",
        "traefik.http.routers.otel-healthcheck.rule=Host(`otel-healthcheck.obs.int.jeddi.org`) && Path(`/health/status`)",
        "traefik.http.routers.otel-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-ingest"
      port     = "ingest"
      provider = "consul"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.ingest.entrypoints=https,http",
        "traefik.http.routers.ingest.rule=Host(`ingest.obs.int.jeddi.org`)",
        "traefik.http.routers.ingest.tls=false"
      ]
    }

    task "otel-log-clickhouse" {
      driver = "docker"

      env = {
      }

      config {
        image = var.image_otel

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]

        ports = [
          "metrics",
          "ingest",
          "healthcheck"
        ]

      }

      resources {
        cpu    = 300
        memory = 500
      }

      template {
        # data = file("assets/collector-opentelemetry-basic-log-ingest.yaml")
        data = file("assets/otel-logs-with-clickhouse-out.yaml")
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
