
# collector-oracle-official - two tasks:  Prometheus + Oracle-exporter

# Nomad job version 2025-07

# Using the Oracle appdev official oracle fork of iamseth's exporter
# Refer:  https://github.com/oracle/oracle-db-appdev-monitoring
# Version 1.0 released 2023-09-14


# --------------------------------------------------------------------------
# Updates to package (container) are available at Oracle Container Registry
# Search for:   'observability-exporter'
#     https://container-registry.oracle.com/ords/f?p=113:10
# --------------------------------------------------------------------------


# skopeo copy 
#        docker://container-registry.oracle.com/database/observability-exporter:2.0.2 
#        docker://registry.obs.int.jeddi.org/oracle-observability-exporter:2.0.2


// docker commandline WAS (in the iamseth days)
//   docker run   -p 9161:9161 -e DATA_SOURCE_NAME="system/oracle@//**************:1521" iamseth/oracledb_exporter

// Multi-target support should be present, from github page above:
//
// "To use the multi-target functionality, send a http request 
//  to the endpoint /scrape?target=foo:1521 where target is set
//  to the DSN of the Oracle instance to scrape metrics from."



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_prometheus = "registry.obs.int.jeddi.org/prometheus:v2.45.6"
  image_exporter = "registry.obs.int.jeddi.org/oracle-observability-exporter:2.0.2"

  host_constraint = ".*-hac-01"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}




# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-oracle-jeddlab12" {
  datacenters = [ var.nomad_dc ]

  type = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-oracle" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }

      port "port_exporter" { 
        to = 9161
      }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "prometheus" {
      driver = "docker"

      config {
        image = local.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          # "--web.external-url=http://prometheus-app-oracle.obs.nsw.education",
          # "--web.external-url=http://prometheus-app-oracle.obs.int.jeddi.org",
          "--web.external-url=http://${NOMAD_JOB_ID}-prometheus.obs.int.jeddi.org",

          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Oracle - ${NOMAD_JOB_ID}",
          # "--log-level=info",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu = 50
        memory = 150
        memory_max = 250
      }

      service {
        name = "${NOMAD_JOB_ID}-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          # "traefik.http.routers.collector-oracle-prometheus.rule=Host(`collector-oracle-prometheus.obs.nsw.education`)",

          #"traefik.http.routers.collector-oracle-prometheus.rule=Host(`collector-oracle-prometheus.obs.int.jeddi.org`)",
          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.rule=Host(`${NOMAD_JOB_ID}-prometheus.obs.int.jeddi.org`)",

          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.tls=false",
          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.entrypoints=http,https",
        ]

        check {
          name = "Oracle exporter prometheus healthcheck"
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    # nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    # nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: {{ env "NOMAD_JOB_NAME" }}
    env: prod

  scrape_interval: 120s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-oracle'
  # - job_name: '{{ env "NOMAD_JOB_NAME" }}-prometheus'
    scheme: 'https'
    static_configs:
      - targets: [
         # 'collector-oracle-prometheus.obs.int.jeddi.org'
         {{ env "NOMAD_JOB_ID" }}-prometheus.obs.int.jeddi.org
       ]
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*'
        action: drop

  # Job to scrape metrics of the exporter itself
  - job_name: 'exporter-oracle'
  # - job_name: '{{ env "NOMAD_JOB_NAME" }}-exporter'
    scheme: 'https'
    static_configs:
      - targets: [
         # 'collector-oracle-exporter-blank.obs.int.jeddi.org'
         {{ env "NOMAD_JOB_ID" }}-exporter.obs.int.jeddi.org
      ]
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*'
        action: drop

remote_write:
  - name: mimir
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // end-task "task-oracle-prometheus"



    # TASK exporter = = = = = = = = = = = = = = = = = = = = = = = = =
    task "exporter" {
      driver = "docker"

      # Use the default oracle-in-a-box credentials, but we have proven out
      # the srvmtmzbx account (when created per standard process) works.
      env = {
        # DATA_SOURCE_NAME = "system/oracle@//pl0992obscol02.nsw.education:1521"
        # DATA_SOURCE_NAME = "system/oracle@//**************:1521"
        # DATA_SOURCE_NAME = "oracle://user:password@myhost:1521/service"
        # DATA_SOURCE_NAME = "system/oracle@//pl0475obscol06.nsw.education:1521"
        # DATA_SOURCE_NAME = "oracle://system:<EMAIL>:1521/service"
        # DATA_SOURCE_NAME = "system/<EMAIL>:1521/XEPDB1"

        # "DATA_SOURCE_NAME" = "system/oracle@//*************:1521/XEPDB1"
        #"DB_USERNAME" = "system"
        #"DB_PASSWORD" = "oracle"
        #"DB_CONNECT_STRING" = "*************:1521/XEPDB1"


        #"DB_USERNAME" = "srv_obs"
        #"DB_PASSWORD" = "bigsecret"
        #"DB_CONNECT_STRING" = "oracle12.obs.int.jeddi.org:1521/XE"

        # Uses the dodgy workaround for importing variables from Consul into a template first
        #"DB_USERNAME" = "${env.DB_USERNAME}"
        #"DB_PASSWORD" = "${env.DB_PASSWORD}"
        #"DB_CONNECT_STRING" = "${env.DB_CONNECT_STRING}"

      }

      config {
        image = local.image_exporter

        ports = ["port_exporter"]

        args = [ 
          # "--custom.metrics=/etc/custom-metrics.toml",
          # "--log.level=debug",
          "--log.level=warn",
          "--config.file=/etc/oracle-config.yaml",
        ]

        volumes = [
          "local/custom-metrics.toml:/etc/custom-metrics.toml",
          "local/oracle-config.yaml:/etc/oracle-config.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }      

      }

      resources {
        cpu = 50
        memory = 50
        memory_max = 200
      }

      service {
        # name = "collector-oracle-exporter"
        name = "${NOMAD_JOB_ID}-exporter"
        port = "port_exporter"

        tags = [
          "traefik.enable=true",
          #"traefik.http.routers.collector-oracle-exporter-blank.rule=Host(`collector-oracle-exporter-blank.obs.nsw.education`)",

          #"traefik.http.routers.collector-oracle-exporter-blank.rule=Host(`collector-oracle-exporter-blank.obs.int.jeddi.org`)",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.rule=Host(`${NOMAD_JOB_ID}-exporter.obs.int.jeddi.org`)",

          #"traefik.http.routers.collector-oracle-exporter-blank.tls=false",
          #"traefik.http.routers.collector-oracle-exporter-blank.entrypoints=http,https",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.tls=false",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.entrypoints=http,https",
        ]

        check {
          name = "Oracle exporter healthcheck"
          port = "port_exporter"
          type = "tcp"
          interval = "60s"
          timeout = "5s"
          check_restart {
            limit = 3
            grace = "60s"
            ignore_warnings = false
          }
        }
      }

#      # A template purely to get key-value data into the environment, the
#      # usual wonky dodgy workaround for nomad using consul variables.
#      template {
#        data = <<EOH
#DB_USERNAME = {{ key "oracle/jeddlab12/user" }}
#DB_PASSWORD = {{ key "oracle/jeddlab12/password" }}
#DB_CONNECT_STRING = {{ key "oracle/jeddlab12/connect_string" }}
#
#EOH
#        destination = "local/consul-key-value-import.env"
#        env         = true
#      }

      template {
        data = <<EOH
databases:
  default:
    username: "{{ key "oracle/jeddlab12/user" }}"
    password: "{{ key "oracle/jeddlab12/password" }}"
    url: "{{ key "oracle/jeddlab12/connect_string" }}"

EOH
        destination = "local/oracle-config.yaml"
      }




      # A custom-metrics (TOML format) can run queries and return them
      # in custom named key/value pairs, including customary # comments
      # for HELP and TYPE lines.
      template {
        data = <<EOH

[[metric]]
context = "test"

request = "SELECT 1 as value_1, 2 as value_2 FROM DUAL"

metricsdesc = { value_1 = "Custom key that always returns the value 1.", value_2 = "Custom key that always returns the value 2." }

metricstype = { value_1 = "counter" , value_2 = "counter" }


#[[metric]]
#context = "meta"
#
#request = "select to_char(startup_time,'YYYY-MM-DD HH24:MI:SS') "DB Startup Time" from sys.v_$instance;"
#
#metricsdesc = { startup_time = "Time that the database was started." }
#
#metricstype = { startup_time = "counter" }


EOH
        destination = "local/custom-metrics.toml"
      }

    }



  }  // end-group "collector-oracle" 

} // end-job "collector-oracle"

