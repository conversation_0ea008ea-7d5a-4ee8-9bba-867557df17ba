
# Notes on setting up the nomad job

note that garage == distributed object storage (s3 or minio alike)

3-node cluster using nomad job - separate directories on /opt/sharednfs/garage/ - based on hostname (dg-hac-0[123])


# Forming the 3 jobs into a cluster

A lot of the following comes from the quickstart guide:
https://garagehq.deuxfleurs.fr/documentation/quick-start/


The 3 instances come up without knowing about each other.

On each host you can use the built-in 'garage' in the docker container this way.  Note that there's no sh or bash in these things. : (

jedd@dg-hac-01:~$ docker ps | grep garage
dbdb344438f1   dxflrs/garage:v0.9.1                  "/garage server"         7 minutes ago   Up 7 minutes            *************:3900-3904->3900-3904/tcp, *************:3900-3904->3900-3904/udp                                                             garage-task-9c7350e3-a914-876c-00e3-0b6c6334eb62

use the container id to:

jedd@dg-hac-01:~$ alias garage="docker exec -ti dbdb3 /garage"


Then you can run 'garage' command natively.

On first host - dg-hac-01 - run:

jedd@dg-hac-01:~$ garage node id
0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

To instruct a node to connect to this node, run the following command on that node:
    garage [-c <config file path>] node connect 0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

(There's some 'tell them to connect to me' instructions, but it' easier to visit the other two nodes)

Connect to 02 and 03, and use the same:   docker ps // alias garage sequence and then the command as above:

    garage  node connect 0d77307affe8d4c9dadcbd6ec3538400597d2864062c3300c910ff15894fe98e@*************:3901

Run 'garage status' on any of the hosts, once the above's done (the hosts share consensus, so you only need to tell 2 and 3 about 1)

jedd@dg-hac-03:~$ garage status
==== HEALTHY NODES ====
ID                Hostname      Address                      Tags              Zone  Capacity  DataAvail
0d77307affe8d4c9  dbdb344438f1  *************:3901           NO ROLE ASSIGNED
f06e1b2a7f28af20  9356f61fbf59  [::ffff:*************]:3901  NO ROLE ASSIGNED
a164b1fc6f69d576  ae633f23ef0b  *************:3901           NO ROLE ASSIGNED

It seem to flip between vanilla ipv4 and that weird ipv6 style prefix

Data is stored in the data-dir - in dg job that means /opt/sharednfs/garage/{hostname}/data

From any host in the cluster, using the aliased garage command:


# Review health of the cluster

jedd@dg-hac-03:~$ garage status
==== HEALTHY NODES ====
ID                Hostname      Address                      Tags              Zone  Capacity  DataAvail
0d77307affe8d4c9  dbdb344438f1  *************:3901           NO ROLE ASSIGNED
f06e1b2a7f28af20  9356f61fbf59  [::ffff:*************]:3901  NO ROLE ASSIGNED
a164b1fc6f69d576  ae633f23ef0b  *************:3901           NO ROLE ASSIGNED

# Create a cluster layout - assign disk to garage

## DG cluster

jedd@dg-hac-03:~$ df -h /opt/sharednfs/
Filesystem                    Type  Size  Used Avail Use% Mounted on
**************:/opt/sharednfs nfs4  108G   30G   74G  29% /opt/sharednfs

jedd@dg-hac-03:~$ garage layout assign 0d77 -z dg -c 10G    
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout assign f06e -z dg -c 10G 
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout assign a164 -z dg -c 10G 
Role changes are staged but not yet commited.
Use `garage layout show` to view staged role changes,
and `garage layout apply` to enact staged changes.

jedd@dg-hac-03:~$ garage layout show
==== CURRENT CLUSTER LAYOUT ====
No nodes currently have a role in the cluster.
See `garage status` to view available nodes.

Current cluster layout version: 0

==== STAGED ROLE CHANGES ====
ID                Tags  Zone  Capacity
0d77307affe8d4c9        dg    10.0 GB
a164b1fc6f69d576        dg    10.0 GB
f06e1b2a7f28af20        dg    10.0 GB


==== NEW CLUSTER LAYOUT AFTER APPLYING CHANGES ====
ID                Tags  Zone  Capacity  Usable capacity
0d77307affe8d4c9        dg    10.0 GB   10.0 GB (100.0%)
a164b1fc6f69d576        dg    10.0 GB   10.0 GB (100.0%)
f06e1b2a7f28af20        dg    10.0 GB   10.0 GB (100.0%)

Zone redundancy: maximum

==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     39.1 MB
Usable capacity / total cluster capacity:   30.0 GB / 30.0 GB (100.0 %)
Effective capacity (replication factor 3):  10.0 GB

dg                  Tags  Partitions        Capacity  Usable capacity
  0d77307affe8d4c9        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  a164b1fc6f69d576        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  f06e1b2a7f28af20        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  TOTAL                   768 (256 unique)  30.0 GB   30.0 GB (100.0%)


To enact the staged role changes, type:

    garage layout apply --version 1

You can also revert all proposed changes with: garage layout revert --version 1

```
jedd@dg-hac-03:~$ garage layout apply --version 1
==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     39.1 MB
Usable capacity / total cluster capacity:   30.0 GB / 30.0 GB (100.0 %)
Effective capacity (replication factor 3):  10.0 GB

dg                  Tags  Partitions        Capacity  Usable capacity
  0d77307affe8d4c9        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  a164b1fc6f69d576        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  f06e1b2a7f28af20        256 (256 new)     10.0 GB   10.0 GB (100.0%)
  TOTAL                   768 (256 unique)  30.0 GB   30.0 GB (100.0%)


New cluster layout with updated role assignment has been applied in cluster.
Data will now be moved around between nodes accordingly.
```

## PY cluster


Using original garage-README for process, noting details here for reference

/opt/sharednfs at PY is on py-core-01, and 300GB at this time (2025-03)

We're sticking with replication factor 3, on 3 nodes.


```
py-hac-01:~# garage status
==== HEALTHY NODES ====
ID                Hostname      Address                     Tags              Zone  Capacity  DataAvail
73393452a36b26f6  00060ba96494  [::ffff:************]:3901  NO ROLE ASSIGNED
50d6356840ced5ce  dd6ae0d64c84  [::ffff:************]:3901  NO ROLE ASSIGNED
d7ee8e56a1def6ff  fdae51f5c026  ************:3901           NO ROLE ASSIGNED
```


```
py-hac-01:~# garage layout show
==== CURRENT CLUSTER LAYOUT ====
No nodes currently have a role in the cluster.
See `garage status` to view available nodes.

Current cluster layout version: 0

==== STAGED ROLE CHANGES ====
ID                Tags  Zone  Capacity
50d6356840ced5ce        py    120.0 GB
73393452a36b26f6        py    120.0 GB
d7ee8e56a1def6ff        py    120.0 GB


==== NEW CLUSTER LAYOUT AFTER APPLYING CHANGES ====
ID                Tags  Zone  Capacity  Usable capacity
50d6356840ced5ce        py    120.0 GB  120.0 GB (100.0%)
73393452a36b26f6        py    120.0 GB  120.0 GB (100.0%)
d7ee8e56a1def6ff        py    120.0 GB  120.0 GB (100.0%)

Zone redundancy: maximum

==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     468.8 MB
Usable capacity / total cluster capacity:   360.0 GB / 360.0 GB (100.0 %)
Effective capacity (replication factor 3):  120.0 GB

py                  Tags  Partitions        Capacity  Usable capacity
  50d6356840ced5ce        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  73393452a36b26f6        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  d7ee8e56a1def6ff        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  TOTAL                   768 (256 unique)  360.0 GB  360.0 GB (100.0%)


To enact the staged role changes, type:

    garage layout apply --version 1

You can also revert all proposed changes with: garage layout revert --version 1
```

```
py-hac-01:~# garage layout apply --version 1
==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====

Partitions are replicated 3 times on at least 1 distinct zones.

Optimal partition size:                     468.8 MB
Usable capacity / total cluster capacity:   360.0 GB / 360.0 GB (100.0 %)
Effective capacity (replication factor 3):  120.0 GB

py                  Tags  Partitions        Capacity  Usable capacity
  50d6356840ced5ce        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  73393452a36b26f6        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  d7ee8e56a1def6ff        256 (256 new)     120.0 GB  120.0 GB (100.0%)
  TOTAL                   768 (256 unique)  360.0 GB  360.0 GB (100.0%)


New cluster layout with updated role assignment has been applied in cluster.
Data will now be moved around between nodes accordingly.
```





# Create key and bucket on the cluster

Do this from any host - after running the 'alias garage ...' (docker) command above.

Create a bucket:
  garage bucket create loki-ha

Show all buckets:
  garage bucket list

Show details about this newly created bucket:
  garage bucket info loki-ha

Create an API key:
  garage key create loki-ha-key

This provides key-id (access key) and Secret Key

Confirm things look sensible:
  garage key list

Details about this key:
  garage key info loki-ha-key
Note Authorized buckets section will be empty

Assign key to bucket:
  garage bucket allow --read --write --owner loki-ha --key loki-ha-key

Running `garage bucket info loki-ha` now has the 'RWO' perms and authorized key of 'loki-ha-key' listed

# Using the 'aws' CLI tool - on debian it's just 'awscli' package, and provides /usr/bin/aws

dg-pan-01:~$ export AWS_ACCESS_KEY_ID=GKfa249bae7d21cc4a1a5a05b4
dg-pan-01:~$ export AWS_SECRET_ACCESS_KEY=dabe3d073411f4a1f25a04af0f21a1cef4e7ee647720b97f9752106b59c1d6d9
dg-pan-01:~$ export AWS_DEFAULT_REGION='garage'
dg-pan-01:~$ export AWS_ENDPOINT_URL='http://dg-hac-01.int.jeddi.org:3900'

Noting on dg-pan-01 I get version awscli version 2.9.19 - and AWS_ENDPOINT_URL isn't respected until 2.13
consequently commands need to have --endpoint-url=http://dg-hac-01.int.jeddi.org:3900

eg.

   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900
2024-02-18 22:31:39 loki-ha

Diving into a specific bucket with a similar command:
   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900 loki-ha/
   aws s3 ls --endpoint-url=http://dg-hac-01.int.jeddi.org:3900 loki-ha/index/

... and so on.




Debian trixie (test at time of writing) - has a better version of awscli - but pan is on bookworm.

These mostly work:


dg-pan-01:~$ cat .aws/credentials 
[default]
aws_access_key_id=GKfa249bae7d21cc4a1a5a05b4
aws_secret_access_key=dabe3d073411f4a1f25a04af0f21a1cef4e7ee647720b97f9752106b59c1d6d9





dg-pan-01:~$ cat .aws/config 
[default]
region=garage
endpoint_url=http://dg-hac-01.int.jeddi.org:3900



Then you can:

dg-pan-01:~$   aws s3 ls s3://loki-ha/

dg-pan-01:~$   aws s3 rm s3://loki-ha/fake/ --recursive



# Monitoring

Refer:  https://garagehq.deuxfleurs.fr/documentation/cookbook/monitoring/

Prometheus configurations for both sites include 3 x calls (static targets) to :3903 on each host.

That link contains a grafana dashboard (json) that can be loaded up to interpret those metrics.




# Additional access & secret keys

## 2024-11-10 - created 'loki-key' for accessing 'loki' bucket (moving away from 'loki-ha')
garage bucket create loki
garage key list:
   GKfa249bae7d21cc4a1a5a05b4  - used for garage / loki-ha

garage key create loki-key
    Key ID: GKee0df1faf9ee96ca3019bc36
    Key ID: GKee0df1faf9ee96ca3019bc36
    Secret key: b56643f3c3b61925c2047ecc6845cb9802180005bd776cab3617f970e39e15ac
    Secret key: b56643f3c3b61925c2047ecc6845cb9802180005bd776cab3617f970e39e15ac






## 2025-03-19 - create 'loki-rw-key' and 'loki-rw' bucket for Loki 3.4 multi-instance Nomad job

py-hac-01:~# garage key create loki-rw-key
Key name: loki-rw-key
Key ID: GKd95b4c19618a3078f747e9ba
Key ID: GKd95b4c19618a3078f747e9ba
Secret key: d1be634c7624241cc287ac731065302800dc6892f407b7cd59b63603cd10c738
Secret key: d1be634c7624241cc287ac731065302800dc6892f407b7cd59b63603cd10c738


py-hac-01:~# garage bucket allow --read --write --owner loki-rw --key loki-rw-key

py-hac-01:~# garage bucket info loki-rw
Bucket: 7773d41398d7f1a84d6a732835c3d2856104cc11c6763b4b74be1a158267909f

Size: 0 B (0 B)
Objects: 0
Unfinished uploads (multipart and non-multipart): 0
Unfinished multipart uploads: 0
Size of unfinished multipart uploads: 0 B (0 B)

Website access: false

Global aliases:
  loki-rw

Key-specific aliases:

Authorized keys:
  RWO  GKd95b4c19618a3078f747e9ba  loki-rw-key



## 2025-03-19 - PY - create 'loki-monolithic' bucket and key

Key name: loki-monolithic-key
Key ID: GK280758b912cbb475ea3d8f9c
Key ID: GK280758b912cbb475ea3d8f9c
Secret key: 67b48dae7272ad8ec3f524bee8e24036b72d55334429782542f7f137d932d1c2
Secret key: 67b48dae7272ad8ec3f524bee8e24036b72d55334429782542f7f137d932d1c2


py-hac-01:~# garage bucket info loki-monolithic
Bucket: da3f98a816b74b4d1323be2c90567383131a2cf41ad1e5663287954e52083f6e

Size: 0 B (0 B)
Objects: 0
Unfinished uploads (multipart and non-multipart): 0
Unfinished multipart uploads: 0
Size of unfinished multipart uploads: 0 B (0 B)

Website access: false

Global aliases:
  loki-monolithic

Key-specific aliases:

Authorized keys:
  RWO  GK280758b912cbb475ea3d8f9c  loki-monolithic-key


## 2025-04-18 - PY - create 'mimir-rwb' and 'mimir-rwb-key'
  Key name: mimir-rwb-key
  Key ID: GKdd24e891058edf27893f69fa
  Key ID: GKdd24e891058edf27893f69fa
  Secret key: 36a49dea1e7ad40c15722eb17f336240341d67579ea29e2e97b481b062f9c6ae
  Secret key: 36a49dea1e7ad40c15722eb17f336240341d67579ea29e2e97b481b062f9c6ae


## 2025-04-24 PY create loki-rwb bucket
garage bucket create loki-rwb

garage key create loki-rwb-key
    Key name: loki-rwb-key
    Key ID: GK45cc5280affa204ae72703b8
    Key ID: GK45cc5280affa204ae72703b8
    Secret key: 9b14d187c400ad445dc81177d91d7e85991f8f65d91107a21936454f2c99546e
    Secret key: 9b14d187c400ad445dc81177d91d7e85991f8f65d91107a21936454f2c99546e

garage bucket allow --read --write --owner loki-rwb --key loki-rwb-key


## 2025-05-02 - DG - mimir-rwb
garage bucket create mimir-rwb

garage key create mimir-rwb-key
    Key name: mimir-rwb-key
    Key ID: GK72529a176fb40beefd2dee50
    Secret key: 1610404f7237ab2c6e184fb4403728ad87bf5deab13128c67d638142324a9f48

garage bucket allow --read --write --owner mimir-rwb --key mimir-rwb-key



