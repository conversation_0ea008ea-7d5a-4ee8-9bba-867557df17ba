
// mimir-rwb - for jedd-lab

// Uses read, write, and backend components (3 x tasks)

// Refer:  https://grafana.com/docs/mimir/latest/operators-guide/architecture/deployment-modes/#read-write-mode

# skopeo copy 
#        docker://registry.hub.docker.com/grafana/mimir:2.15.2
#        docker://registry.obs.int.jeddi.org/mimir:2.15.2


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
// Refer traefik.nomad for discussion about nomad_dc and locals{}
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

variables {
  mimir_read_endpoint = "mimir-rwb-read.obs.int.jeddi.org"
  mimir_write_endpoint = "mimir-rwb-write.obs.int.jeddi.org"
  mimir_backend_endpoint = "mimir-rwb-backend.obs.int.jeddi.org"
}

locals {
  image_mimir = "registry.obs.int.jeddi.org/mimir:2.15.2"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123]"  :  "py-hac-0[123]"
  mimir_config_file = var.nomad_dc == "DG" ? "assets/mimir-rwb-dg.yaml"  :  "assets/mimir-rwb-py.yaml"

  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"

  tracing_url = "https://tempo-in-otlp.obs.int.jeddi.org"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "mimir-rwb" {
  
  datacenters = [ var.nomad_dc ]

#  update {
#    max_parallel     = 1
#    min_healthy_time = "10s"
#    healthy_deadline = "2m"
#    canary = 1
#    auto_promote = true
#    auto_revert = true
#  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
}
  
  # READ group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-read-group" {
    count = 3
    
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }
    
    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-read"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "mimir-rwb-read"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-rwb-read.entrypoints=https",
         "traefik.http.routers.mimir-rwb-read.rule=Host(`${var.mimir_read_endpoint}`)",
       ]      

      check {
        name            = "mimir rwb read"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "1s"
      }
    }


    task "mimir-rwb-read" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"
 
      env {
        # JAEGER_AGENT_HOST = local.tracing_url
        OTEL_EXPORTER_OTLP_ENDPOINT = local.tracing_url
      }

      config {
        image = local.image_mimir

        ports = [
          "http",
          "grpc"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=read",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      
      template {
        data = file(local.mimir_config_file)
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 300
        memory = 1000
        memory_max = 1500
      }
    }
  }   # end-READ group


  # WRITE  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-write-group" {
    count = 3

    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    ephemeral_disk {
      migrate = true
      sticky  = true
      size = 500
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-write"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "write"
        service = "mimir-write"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-write.entrypoints=http,https",
        "traefik.http.routers.mimir-rwb-write.rule=Host(`${var.mimir_write_endpoint}`)",
      ]   

      check {
        name            = "mimir rwb write"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    task "mimir-rwb-write" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        # JAEGER_AGENT_HOST = local.tracing_url
        OTEL_EXPORTER_OTLP_ENDPOINT = local.tracing_url
      }

      config {
        image = local.image_mimir

        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "/opt/sharednfs/mimir-rwb/:/mimir"
        ]

        args = [
          "-target=write",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file(local.mimir_config_file)
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 300
        memory = 1000
        memory_max = 1500
      }
    }
  }   # end-WRITE group


  # BACKEND  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-rwb-backend" {
    count = 3

    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }
    service {
      name = "mimir-rwb-backend"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "backend"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-backend.entrypoints=https",
        "traefik.http.routers.mimir-rwb-backend.rule=Host(`${var.mimir_backend_endpoint}`)",
    ]      

      check {
        name            = "mimir rwb backend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    task "mimir-rwb-backend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        # JAEGER_AGENT_HOST = local.tracing_url
        OTEL_EXPORTER_OTLP_ENDPOINT = local.tracing_url
      }

      config {
        image = local.image_mimir

        ports = [
          "http",
          "grpc",
        ]
        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "/opt/sharednfs/mimir-rwb/:/mimir"
        ]

        args = [
          "-target=backend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file(local.mimir_config_file)
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 300
        memory = 1000
        memory_max = 1500
      }
    }
  }   # end-BACKEND group
}
