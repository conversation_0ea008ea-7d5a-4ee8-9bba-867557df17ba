
// network tools container for testing container networking

// Needs the command / args to sleep indefinitely otherwise it bombs out immediately.

// skopeo copy 
//        docker://registry.hub.docker.com/jonlabelle/network-tools:latest
//        docker://registry.obs.int.jeddi.org/network-tools:v2025-04-28
// (One of those repos with *only* a :latest tag available.)


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_nettools = "registry.obs.int.jeddi.org/network-tools:v2025-04-28"
  # hac cluster
  #host_constraint = var.nomad_dc == "DG" ? "dg-hac-*" : "py-hac-*"
  # hac-02
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-02" : "py-hac-02"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}
 
# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "network-tools" {
  datacenters = [ var.nomad_dc ]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "network-tools" {

    network {
      port "port_nettools" {
        static = 2222
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    ephemeral_disk {
      size = 300
    }

    task "network-tools" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
      }

      config {
        image = "${local.image_nettools}"
        ports = ["port_nettools"]
        command = "/bin/sleep"
        args = [" infinity"]
        privileged = "true"

        volumes = [
          "/opt/sharednfs/network-tools:/network-tools",
          "local/.bashrc:/root/.bashrc"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]network-tools (docker):\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 100
        memory = 200
      }

      service {
        name = "network-tools"
        port = "port_nettools"

      }

    }

  }
}
