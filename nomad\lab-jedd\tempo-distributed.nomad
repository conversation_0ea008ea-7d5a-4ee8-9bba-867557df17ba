
// Tempo Distributed microservers - taken from Grafana Tempo src repository

// Jedd-lab

// skopeo copy 
//        docker://registry.hub.docker.com/grafana/tempo:2.7.1  
//        docker://registry.obs.int.jeddi.org/tempo:2.7.1
//

// PY S3 garage:
//    garage bucket create tempo-distributed
//    garage key create tempo-distributed-key
//    garage bucket allow --read --write --owner tempo-distributed --key tempo-distributed-key
//    Key ID: GK071ecef12816c0679ae2aaa7
//    Secret key: 250a5ee26e49490d21516df7934d987fdb0c0b2cbc18fa0527101814e20a47af


// @TODO
// Need traefik tags for some components - but probably only distributor (incoming)
// and query-frontend (used for Grafana datasource).
//
// Interesting - Grafana Corp's template version of this job file does NOT INCLUDE
// a query-frontend service for 'otlp' -- only 'http' and 'grpc'.
//
// Their distributor group DOES include all three services -- otlp, http, grpc.

// Grafana Corp use Consul DNS, which neither my lab or DOE uses - so at home I could
// update my bind9 to point to py-mon-01, py-hac-01, and py-hac-02 like this:

/***
zone "consul" {
    type forward;
    forward only;
    forwarders { 
        192.168.1.48 port 8600;
        192.168.1.51 port 8600;
        192.168.1.52 port 8600;
    };
};
***/

// OR - given the only domain name that's complaining is `tempo-query-frontend-grpc.service.consul:9095`
// it's probably better to just mung _that_ in the configuration file to use the traefik url.


// State at 2025-07-14 is that it runs, but no data is going into the PY instance yet.

/************************************************************************************
py-mon-01:~$ curl http://tempo-out-http.obs.int.jeddi.org/status/endpoints
GET /status/endpoints
+-------------------------------------+-------------------------------------------+
| NAME                                | REGEX                                     |
+-------------------------------------+-------------------------------------------+
| /api/echo                           | ^/api/echo$                               |
| /api/metrics/query                  | ^/api/metrics/query$                      |
| /api/metrics/query_range            | ^/api/metrics/query_range$                |
| /api/metrics/summary                | ^/api/metrics/summary$                    |
| /api/search                         | ^/api/search$                             |
| /api/search/tag/{tagName}/values    | ^/api/search/tag/(?P<v0>[^/]+)/values$    |
| /api/search/tags                    | ^/api/search/tags$                        |
| /api/status/buildinfo               | ^/api/status/buildinfo$                   |
| /api/traces/{traceID}               | ^/api/traces/(?P<v0>[^/]+)$               |
| /api/v2/search/tag/{tagName}/values | ^/api/v2/search/tag/(?P<v0>[^/]+)/values$ |
| /api/v2/search/tags                 | ^/api/v2/search/tags$                     |
| /api/v2/traces/{traceID}            | ^/api/v2/traces/(?P<v0>[^/]+)$            |
| /debug/pprof                        | ^/debug/pprof                             |
| /memberlist                         | ^/memberlist$                             |
| /metrics                            | ^/metrics$                                |
| /ready                              | ^/ready$                                  |
| /static/                            | ^/static/                                 |
| /status                             | ^/status$                                 |
| /status/overrides                   | ^/status/overrides$                       |
| /status/overrides/{tenant}          | ^/status/overrides/(?P<v0>[^/]+)$         |
| /status/usage-stats                 | ^/status/usage-stats$                     |
| /status/{endpoint}                  | ^/status/(?P<v0>[^/]+)$                   |
+-------------------------------------+-------------------------------------------+

py-mon-01:~$ curl http://tempo-out-http.obs.int.jeddi.org/status/services
GET /status/services
+-----------------+---------+--------------+
| SERVICE NAME    |  STATUS | FAILURE CASE |
+-----------------+---------+--------------+
| cache-provider  | Running |              |
| internal-server | Running |              |
| memberlist-kv   | Running |              |
| overrides       | Running |              |
| overrides-api   | Running |              |
| query-frontend  | Running |              |
| server          | Running |              |
| store           | Running |              |
| usage-report    | Running |              |
+-----------------+---------+--------------+

Other cool stuff:

   curl http://tempo-out-http.obs.int.jeddi.org/status/usage-stats | jq
   curl http://tempo-out-http.obs.int.jeddi.org/status/config

**************************************************************************************/

// = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

#variable "version" {
#  type        = string
#  description = "Tempo version"
#  default     = "2.7.1"
#}

variable "s3_access_key_id" {
  type        = string
  description = "S3 Access Key ID - garage at PY"
  default     = "GK071ecef12816c0679ae2aaa7"
}

variable "s3_secret_access_key" {
  type        = string
  description = "S3 Secret Access Key - garage at PY"
  default     = "250a5ee26e49490d21516df7934d987fdb0c0b2cbc18fa0527101814e20a47af"
}



// = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
locals {
  image_tempo = "registry.obs.int.jeddi.org/tempo:2.7.1"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"

  query_frontend_host_constraint = var.nomad_dc == "DG" ? "dg-hac-*" : "py-hac-05"
}



// = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "tempo" {
  datacenters = [ var.nomad_dc ]


  // GROUP metrics-generator = = = = = = = = = = = = = = = = = = =
  group "metrics-generator" {
    count = 1

    network {
      port "http" {}
      port "grpc" {}
      port "otlp" { to = 4317 }
    }

    service {
      name = "tempo-metrics-generator"
      port = "http"
      tags = []
      check {
        name     = "metrics-generator"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "20s"
        timeout  = "1s"
      }
    }

    service {
      name = "tempo-metrics-generator-grpc"
      port = "grpc"
      tags = []
      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "metrics-generator" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=metrics-generator",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 1024
      }
    } 
  }   // end-group "metrics-generator"



  // GROUP query-frontend = = = = = = = = = = = = = = = = = = =
  group "query-frontend" {
    count = 1

    network {
      port "http" {}
      port "grpc" { 
        static = 9095
        to = 9095
      }
      # port "grpc" { to = 9095}
      port "otlp" { to = 4317 }
    }

    # We lock query-frontend to a single host, as traefik doesn't want to redirect 9095 for us,
    # and doesn't seem to want to run it over http either.
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.query_frontend_host_constraint
    }

    service {
      name = "tempo-query-frontend"
      port = "http"

      # Validate with:
      #    curl http://tempo-out-http.obs.int.jeddi.org/api/echo
      #    curl http://tempo-out-http.obs.int.jeddi.org/status
      # Refer:  https://grafana.com/docs/tempo/latest/api_docs/
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-out-http.entrypoints=https,http",
        "traefik.http.routers.tempo-out-http.rule=Host(`tempo-out-http.obs.int.jeddi.org`)",
      ]      
    }

    service {
      name = "tempo-query-frontend-grpc"
      # name = "tempo-out-grpc"
      port = "grpc"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-query-frontend-grpc.entrypoints=http",
        "traefik.http.routers.tempo-query-frontend-grpc.rule=Host(`tempo-query-frontend-grpc.obs.int.jeddi.org`)",
      ]      

      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "query-frontend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
          "otlp"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=query-frontend",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 1024
      }
    }
  }  // end-group "query-frontend"



  // GROUP ingester  = = = = = = = = = = = = = = = = = = =
  group "ingester" {
    count = 3

    network {
      port "http" {}
      port "grpc" {}
      port "otlp" { to = 4317 }
    }

    service {
      name = "tempo-ingester"
      port = "http"

      # Ingester sometimes gets tied in a knot, so we need to be able to access its endpoint.
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-ingester.entrypoints=https,http",
        "traefik.http.routers.tempo-ingester.rule=Host(`tempo-ingester.obs.int.jeddi.org`)",
      ]      

      check {
        name     = "Tempo ingester"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "20s"
        timeout  = "1s"
      }
    }

    service {
      name = "tempo-ingester-grpc"
      port = "grpc"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-ingester-grpc.entrypoints=https,http",
        "traefik.http.routers.tempo-ingester-grpc.rule=Host(`tempo-ingester-grpc.obs.int.jeddi.org`)",
      ]      

      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "ingester" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=ingester",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        network_mode = "host"
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 300
        memory     = 128
        memory_max = 2048
      }
    }
  }  // end-group "ingester"



  // GROUP compactor  = = = = = = = = = = = = = = = = = = =
  group "compactor" {
    count = 1

    ephemeral_disk {
      size    = 1000
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
      port "otlp" { to = 4317 }
    }

    service {
      name = "tempo-compactor"
      port = "http"
      tags = []
      check {
        name     = "Tempo compactor"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "20s"
        timeout  = "1s"
      }
    }

    service {
      name = "tempo-compactor-grpc"
      port = "grpc"
      tags = []
      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "compactor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=compactor",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 3000
        memory     = 256
        memory_max = 1024
      }
    }
  }  // end-group "compactor"



  // GROUP distributor  = = = = = = = = = = = = = = = = = = =
  group "distributor" {
    count = 1

    network {
      port "http" {}
      port "grpc" {}
      port "otlp" { to = 4317 }
    }

    service {
      name = "tempo-distributor"
      port = "http"

      # Validate with:
      #    curl http://tempo-in-http.obs.int.jeddi.org/ingester/ring
      #    curl http://tempo-out-http.obs.int.jeddi.org/status
      # Refer:  https://grafana.com/docs/tempo/latest/api_docs/
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-in-http.entrypoints=https,http",
        "traefik.http.routers.tempo-in-http.rule=Host(`tempo-in-http.obs.int.jeddi.org`)",
      ]      

      check {
        name     = "Tempo distributor"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "20s"
        timeout  = "1s"
      }
    }

    service {
      name = "tempo-distributor-otlp"
      port = "otlp"
      
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-in-otlp.entrypoints=https,http",
        "traefik.http.routers.tempo-in-otlp.rule=Host(`tempo-in-otlp.obs.int.jeddi.org`)",
      ]      

    }

    service {
      name = "tempo-distributor-grpc"
      port = "grpc"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-in-grpc.entrypoints=https,http",
        "traefik.http.routers.tempo-in-grpc.rule=Host(`tempo-in-grpc.obs.int.jeddi.org`)",
      ]      

      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "distributor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
          "otlp",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=distributor",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 1024
      }
    }
  }   // end-group "distributor"




  // GROUP querier  = = = = = = = = = = = = = = = = = = =
  group "querier" {
    count = 1

    network {
      port "http" {}
      port "grpc" {}
      port "otlp" { to = 4317 }
    }

    service {
      name = "tempo-querier"
      port = "http"
      tags = []
      check {
        name     = "Tempo querier"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "50s"
        timeout  = "1s"
      }
    }

    service {
      name = "tempo-querier-grpc"
      port = "grpc"
      tags = []
      check {
        port     = "grpc"
        type     = "grpc"
        interval = "20s"
        timeout  = "1s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "querier" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      config {
        # image = "grafana/tempo:${var.version}"
        image = local.image_tempo
        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        args = [
          "-target=querier",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yaml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH

        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 2048
      }
    }
  }   // end-group "querier"



}  // end-job 'tempo'
