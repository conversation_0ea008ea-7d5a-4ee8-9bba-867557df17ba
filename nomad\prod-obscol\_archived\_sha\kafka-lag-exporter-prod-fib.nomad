job "kafka-lag-exporter-prod-fib" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "kafka-lag-exporter" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol02.nsw.education"
    }

    task "lag-exporter" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/kafka-lagexporter.git"
        destination = "config"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      template {
        data = <<EOH
      kafka-lag-exporter {
        reporters.prometheus.port = {{ env "PROMETHEUS_PORT" }}
        clusters = [
          {
            name = "{{ env "KAFKA_CLUSTER_NAME" }}"
            bootstrap-brokers = "{{ env "KAFKA_BROKERS" }}"
            labels = {
              location = "{{ env "LOCATION" }}"
            }
          }
        ]
      }
      EOH

        destination = "config/application.conf"
      }

      config {
        image = "https://artifacts.mtm.nsw.education/lightbend/kafka-lag-exporter:0.6.5"

        port_map {
          prometheus = 8001
        }

        volumes = [
          "config/:/opt/docker/conf/",
        ]

        command = "/bin/bash"
        args = ["-c", "/opt/docker/bin/kafka-lag-exporter -Dconfig.file=/opt/docker/conf/application.conf -Dlogback.configurationFile=/opt/docker/conf/logback.xml"]

      }

      service {
        name = "prometheus-reporters-prod-fib"
        port = "prometheus"

        check {
          type = "tcp"
          port = "prometheus"
          interval = "20s"
          timeout = "10s"
        }
      }


      env {
        "KAFKA_BROKERS" = "pl0991kfkab1001.nsw.education:9092,pl0991kfkab1002.nsw.education:9092,pl0991kfkab1003.nsw.education:9092,pl0991kfkab1004.nsw.education:9092,pl0991kfkab1005.nsw.education:9092,pl0991kfkab1006.nsw.education:9092,pl0991kfkab1007.nsw.education:9092,"
        "KAFKA_CLUSTER_NAME" = "prod-fib"
        "LOCATION" = "DOE"
        "PROMETHEUS_PORT" = "8001"
      }

      resources {
        cpu = 400
        memory = 700

        network {
          port "prometheus" {
            static = 8001
          }
        }
      }
    }
  }
}
