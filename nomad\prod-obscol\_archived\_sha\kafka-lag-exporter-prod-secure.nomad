job "kafka-lag-exporter-sec-prod-bd" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "kafka-lag-exporter" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol01.nsw.education"
    }

    task "lag-exporter" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/kafka-lagexporter.git"
        destination = "config"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      template {
        data = <<EOH
      kafka-lag-exporter {
        reporters.prometheus.port = {{ env "PROMETHEUS_PORT" }}
        clusters = [
          {
            name = "{{ env "KAFKA_CLUSTER_NAME" }}"
            bootstrap-brokers = "{{ env "KAFKA_BROKERS" }}"
            admin-client-properties = {
              security.protocol = "SASL_SSL"
              ssl.truststore.location = "/opt/docker/conf/truststore.jks"
              ssl.truststore.password = "{{ env "TRUSTSTORE_PASS" }}"
              sasl.mechanism = "PLAIN"
              security.protocol = "SASL_SSL"
              sasl.jaas.config = "org.apache.kafka.common.security.plain.PlainLoginModule required username="{{key "kafka/username"}}" password="{{key "kafka/password"}}";"
            }
            consumer-properties = {
              security.protocol = "SASL_SSL"
              ssl.truststore.location = "/opt/docker/conf/truststore.jks"
              ssl.truststore.password = "{{ env "TRUSTSTORE_PASS" }}"
              sasl.mechanism = "PLAIN"
              security.protocol = "SASL_SSL"
              sasl.jaas.config = "org.apache.kafka.common.security.plain.PlainLoginModule required username="{{key "kafka/username"}}" password="{{key "kafka/password"}}";"
            }
            labels = {
              location = "{{ env "LOCATION" }}"
            }
          }
        ]
      }
      EOH

        destination = "config/application.conf"
      }

      config {
        image = "https://artifacts.mtm.nsw.education/lightbend/kafka-lag-exporter:0.6.5"

        port_map {
          prometheus = 8001
        }

        volumes = [
          "config/:/opt/docker/conf/",
        ]

        command = "/bin/bash"
        args = ["-c", "/opt/docker/bin/kafka-lag-exporter -Dconfig.file=/opt/docker/conf/application.conf -Dlogback.configurationFile=/opt/docker/conf/logback.xml"]

      }

      service {
        name = "prometheus-reporters-secure-prod-bd"
        port = "prometheus"

        check {
          type = "tcp"
          port = "prometheus"
          interval = "20s"
          timeout = "10s"
        }
      }


      env {
        "KAFKA_BROKERS" = "pl0991kfkab0001.nsw.education:9092,pl0991kfkab0002.nsw.education:9092,pl0991kfkab0003.nsw.education:9092"
        "KAFKA_CLUSTER_NAME" = "secure-prod-bd"
        "LOCATION" = "DOE"
        "PROMETHEUS_PORT" = "8001"
        "TRUSTSTORE_PASS" = "123456"
      }

      resources {
        cpu = 400
        memory = 700

        network {
          port "prometheus" {
            static = 8001
          }
        }
      }
    }
  }
}
