// prod obscol - alertmanager - talking to prometheus-LTS

variables {
  image_alertmanager = "quay.education.nsw.gov.au/observability/alertmanager:prod-obscol"
}

job "alertmanager" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "alertmanager" {

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

    network {
      port "http" {
        static = 9093
      }
    }

    task "alertmanager" {
      driver = "docker"

      config {
        ports = ["http"]
        image = var.image_alertmanager
        dns_servers = ["192.168.31.1"]
        volumes = [
          "local/alertmanager.yaml:/etc/alertmanager/alertmanager.yml",
          "/opt/sharednfs/prometheus-configuration/prod/alertmanager/templates:/etc/alertmanager/template",
        ]
        # 2022-07-06 jedd - post-patching, alertmanager job not running, error 'alertmanager.yml' not found.
        #                   Forced the --config.file param in here.  Unclear on what's changed.
        args = [
          "--web.external-url=https://alertmanager.obs.nsw.education",
          "--config.file=/etc/alertmanager/alertmanager.yml"
        ]
      }

      service {
        name = "alertmanager"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alertmanager.rule=Host(`alertmanager.obs.nsw.education`)",
          "traefik.http.routers.alertmanager.tls=false",
        ]
      }


      template {
        data = <<EOH

global:
  # mutethischannel hook
  #slack_api_url: *****************************************************************************
  http_config:
    proxy_url: http://proxy.det.nsw.edu.au:80/

templates:
- '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['alertname', 'cir_app_id', 'env', 'severity']
  group_wait: 1m
  group_interval: 15m
  repeat_interval: 3h  # 3 hours is the time it takes for xMatters to expire an incident, by default
  receiver: default
  routes:
#  - receiver: xmatters-dev-np
#    continue: true
  - receiver: xmatters
#  - receiver: slack-alerts-kfka
#    matchers:
#    - cir_app_id=~"kfka|hadoop|nifi"

receivers:
- name: default
  slack_configs:
    - channel: "#mutethischannel"
      send_resolved: true
      title: '{{`{{ template "custom_title" . }}`}}'
      text: '{{`{{ template "custom_slack_message" . }}`}}'

#- name: xmatters-dev-np
#  webhook_configs:
#  - url: https://nswdepartmentofeducation-dev-np.xmatters.com/api/integration/1/functions/e7d3f7c2-0d00-4a4c-9fa4-e6b4449b7d71/triggers?apiKey=3b098a70-65a9-4c80-934d-596d7197d890

#- name: xmatters
#  webhook_configs:
#  - url: https://nswdepartmentofeducation.xmatters.com/api/integration/1/functions/47c3d333-7bd0-4649-9b5f-1519a8912243/triggers?apiKey=e0546638-d621-4cb0-9e1a-ae3a8866d3ca

#- name: slack-alerts-kfka
#  slack_configs:
#    - channel: "#alerts-kfka"
#      api_url: *******************************************************************************
#      send_resolved: true
#      title: '{{`{{ template "custom_title" . }}`}}'
#      text: '{{`{{ template "custom_slack_message" . }}`}}'

EOH
        destination = "local/alertmanager.yaml"
      }

    }
  }
}
