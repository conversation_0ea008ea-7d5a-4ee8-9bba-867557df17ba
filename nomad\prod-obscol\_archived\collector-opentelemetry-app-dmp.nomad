# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
}

job "collector-opentelemetry-app-dmp" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }
  
  group "otel-app-dmp" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "pprof" {
        to = 1777
      }
      }

    

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-dmp-metrics.entrypoints=https",
        "traefik.http.routers.otel-app-dmp-metrics.rule=Host(`otel-app-dmp.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-app-dmp-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-app-dmp-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-dmp-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-app-dmp-healthcheck.rule=Host(`otel-app-dmp.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otel-app-dmp-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-app-dmp-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-dmp-pprof.entrypoints=https",
        "traefik.http.routers.otel-app-dmp-pprof.rule=Host(`otel-app-dmp.obs.nsw.education`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-app-dmp-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-app-dmp-otlphttp"
      port     = "otlphttp"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-dmp.entrypoints=https",
        "traefik.http.routers.otel-app-dmp.rule=Host(`otel-app-dmp.obs.nsw.education`)",
        "traefik.http.routers.otel-app-dmp.tls=false"
      ]
    }

    task "otel-app-dmp-receiver" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=otel-app-dmp-receiver"                
                }
            }
        ports = [
            "otlp",
            "otlphttp",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 512
        memory = 512
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      grpc:
      http:

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-app-dmp'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-app-dmp.obs.nsw.education']
        
processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# NewRelic 
#  otlp/newrelic:
#    headers:
#     "api-key": 24d31b4928075f57b361ad6b56c1213eFFFFNRAL
#    endpoint: https://otlp.nr-data.net:4317

# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/otlp"
    headers:
      X-Scope-ORGID: prod

# loki onprem
  loki/onpremloki:
    endpoint: "https://loki.obs.nsw.education/loki/api/v1/push"

# tempo on-prem for otlp traces
  otlphttp/onpremtempo:
    endpoint: https://traces.obs.nsw.education
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [attributes/env,memory_limiter,batch]
      exporters: [otlphttp/onpremtempo,otlphttp/grafanacloud]

    metrics:
      receivers: [otlp,prometheus]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud]

    logs:
      receivers: [otlp]
      processors: [attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki,otlphttp/grafanacloud]

  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-app-dmp
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}