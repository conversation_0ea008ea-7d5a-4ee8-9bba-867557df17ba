# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
}

job "collector-opentelemetry-aws-firehose" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }
  
  group "otel-app-firehose" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "firehose" {
        to = 4433
      }
      port "pprof" {
        to = 1777
        }
    }

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-aws-firehose-metrics.entrypoints=https",
        "traefik.http.routers.otel-app-aws-firehose-metrics.rule=Host(`otel-app-aws-firehose.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-app-aws-firehose-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-app-aws-firehose-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-aws-firehose-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-app-aws-firehose-healthcheck.rule=Host(`otel-app-aws-firehose.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otel-app-aws-firehose-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-app-aws-firehose-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-aws-firehose-pprof.entrypoints=https",
        "traefik.http.routers.otel-app-aws-firehose-pprof.rule=Host(`otel-app-aws-firehose.obs.nsw.education`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-app-aws-firehose-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-app-aws-firehose"
      port     = "firehose"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-firehose.entrypoints=https",
        "traefik.http.routers.otel-app-firehose.rule=Host(`otel-app-aws-firehose.obs.nsw.education`)",
        "traefik.http.routers.otel-app-firehose.tls=false"
      ]
    }

    task "otel-app-aws-firehose" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }
        ports = [
            "firehose",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 512
        memory = 512
      }

      template {
        data = <<EOF
receivers:
  awsfirehose:
    endpoint: "0.0.0.0:{{env "NOMAD_PORT_firehose"}}"
    record_type: cwmetrics

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-app-aws-firehose'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-app-aws-firehose.obs.nsw.education']
        
processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# NewRelic Account Class Data Integration Dev
  otlp/newrelic:
    headers:
     "api-key": dc7f6dcffc79133ad6e557548456a890f34eNRAL
    endpoint: https://otlp.nr-data.net:4317

# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/otlp"
    headers:
      X-Scope-ORGID: prod

# loki onprem
  loki/onpremloki:
    endpoint: "https://loki.obs.nsw.education/loki/api/v1/push"

# tempo on-prem for otlp traces
  otlphttp/onpremtempo:
    endpoint: https://traces.obs.nsw.education
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    metrics:
      receivers: [awsfirehose,prometheus]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud,otlp/newrelic]
  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-app-aws-firehose
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}