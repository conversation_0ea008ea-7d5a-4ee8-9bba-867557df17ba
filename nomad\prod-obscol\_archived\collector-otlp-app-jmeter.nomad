# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
}

job "collector-otlp-app-jmeter" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }
  
  group "otlp-app-jmeter" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "influxdb" {
        to = 8086
      }
      port "pprof" {
        to = 1777
      }
      }

    

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-jmeter-metrics.entrypoints=https",
        "traefik.http.routers.otel-app-jmeter-metrics.rule=Host(`otel-app-jmeter.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-app-jmeter-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-app-jmeter-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-jmeter-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-app-jmeter-healthcheck.rule=Host(`otel-app-jmeter.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otel-app-jmeter-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-app-jmeter-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-jmeter-pprof.entrypoints=https",
        "traefik.http.routers.otel-app-jmeter-pprof.rule=Host(`otel-app-jmeter.obs.nsw.education`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-app-jmeter-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-app-jmeter-influxdb"
      port     = "influxdb"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-jmeter.entrypoints=https",
        "traefik.http.routers.otel-app-jmeter.rule=Host(`otel-app-jmeter.obs.nsw.education`)",
        "traefik.http.routers.otel-app-jmeter.tls=false"
      ]
    }

    task "otel-app-jmeter" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }
        ports = [
            "influxdb",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 512
        memory = 512
      }

      template {
        data = <<EOF
receivers:
  influxdb:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_influxdb" }}"

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-app-jmeter'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-app-jmeter.obs.nsw.education']
        
processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"

exporters:


# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/otlp"
    headers:
      X-Scope-ORGID: prod

service:
  extensions: [health_check,pprof]
  pipelines:
    metrics:
      receivers: [influxdb,prometheus]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir]
  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-app-jmeter
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
