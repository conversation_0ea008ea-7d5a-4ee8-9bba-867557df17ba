# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
}

job "collector-opentelemetry-app-telegraf" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }  

  group "otel-app-telegraf" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "influxdb" {
        to = 8086
      }
      port "pprof" {
        to = 1777
      }
      }

    

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-telegraf-metrics.entrypoints=https",
        "traefik.http.routers.otel-app-telegraf-metrics.rule=Host(`otel-app-telegraf.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-app-telegraf-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-app-telegraf-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-telegraf-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-app-telegraf-healthcheck.rule=Host(`otel-app-telegraf.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otel-app-telegraf-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-app-telegraf-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-telegraf-pprof.entrypoints=https",
        "traefik.http.routers.otel-app-telegraf-pprof.rule=Host(`otel-app-telegraf.obs.nsw.education`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-app-telegraf-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-app-telegraf-influxdb"
      port     = "influxdb"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-telegraf.entrypoints=https",
        "traefik.http.routers.otel-app-telegraf.rule=Host(`otel-app-telegraf.obs.nsw.education`)",
        "traefik.http.routers.otel-app-telegraf.tls=false"
      ]
    }

    task "otel-app-telegraf" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }
        ports = [
            "influxdb",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 512
        memory = 512
      }

      template {
        data = <<EOF
receivers:
  influxdb:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_influxdb" }}"

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-app-telegraf'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-app-telegraf.obs.nsw.education']
        
processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/app_id:
    actions:
      - key: cir_app_id
        action: upsert
        value: testing

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/otlp"
    headers:
      X-Scope-ORGID: telegraf

service:
  extensions: [health_check,pprof,memory_ballast]
  pipelines:
    metrics:
      receivers: [influxdb,prometheus]
      processors: [attributes/env,attributes/app_id,batch]
      exporters: [otlphttp/onpremmimir]
  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-app-telegraf
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}