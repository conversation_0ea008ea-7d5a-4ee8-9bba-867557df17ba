# Prometheus SNMP exporter for ObsCol TEST env

# We use the assets from /opt / sharednfs / prometheus-configuration / snmp / 


   # collector-idrac - Collector for a app_ids and system metrics - PROD ObsCol

# Standard collector architecture 1 job: prometheus

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
  #image_snmp = "quay.education.nsw.gov.au/observability/prom/snmp-exporter:prod-obscol"
  image_snmp = "docker.io/prom/snmp-exporter:v0.20.0"
}

job "collector-schools-idrac" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "collector-schools-idrac" {
    network {

      port "port_exporter_idrac" {
        to = 9116 //default
      }
      port "port_prometheus" {
      }
    }

    task "exporter-snmp-idrac" {
      driver = "docker"

      config {
        image = var.image_snmp

        ports = ["port_exporter_idrac"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          
        dns_servers = [ "************" ]
        volumes = [
          "local/snmp.yml:/etc/snmp_exporter/snmp.yml"
        ]
      }
      template {
        data = file("assets/snmp-dell-idrac.yml")
        destination = "local/snmp.yml"
      }

      service {
        name = "exporter-idrac"
        port = "port_exporter_idrac"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-idrac.rule=Host(`exporter-idrac.obs.nsw.education`)",
          "traefik.http.routers.exporter-idrac.tls=false",
          "traefik.http.routers.exporter-idrac.entrypoints=https",
        ]
       check {
         type = "http"
         port = "port_exporter_idrac"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }
    }

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-idrac-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-idrac.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for schools idrac",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 1000
        memory_max = 6000
      }

      service {
        name = "prometheus-idrac"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-idrac.rule=Host(`prometheus-idrac.obs.nsw.education`)",
          "traefik.http.routers.prometheus-idrac.tls=false",
          "traefik.http.routers.prometheus-idrac.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = file("assets/collector-schools-idrac.yml")
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-a-prometheus"
  }
}
