// prod-obscol - ssl-exporter wrapped into a collector pairing (prometheus + exporter task)
//
// Refactoring of the 'ssl-exporter' job to provide 
//    a) isolation from main prometheus, and
//    b) retrieval of targets via consul sourced from terraform
//
// WIP 2023-04 jedd

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}

job "collector-ssl-certificates" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "collector-ssl-certificates" {
    count = 1

    network {
      port "port_prometheus" { }
      port "port_ssl_exporter" { 
        to = 9219
      }
    }


    # TASK -- ssl exporter  = = = = = = = = = = = = = = = = = = = = = = =

    task "ssl-exporter" {
      driver = "docker"

      config {
        image = "https://docker.io/ribbybibby/ssl-exporter:latest"

        ports = ["port_ssl_exporter"]

        volumes = [
          "local/ssl_exporter.yaml:/etc/ssl_exporter.yaml"
        ]

        args = [
          # optional
          # "--web.listen-address=":${NOMAD_PORT_port_ssl_exporter}"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},env=prod,task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      service {
        name = "collector-ssl-certificates"
        port = "port_ssl_exporter"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-ssl-certificates-exporter.rule=Host(`collector-ssl-certificates-exporter.obs.nsw.education`)",
          "traefik.http.routers.collector-ssl-certificates-exporter.tls=false",
          "traefik.http.routers.collector-ssl-certificates-exporter.entrypoints=http",
        ]

        check {
          type = "tcp"
          port = "port_ssl_exporter"
          interval = "20s"
          timeout = "10s"
        }
      }

      resources {
        cpu = 400
        memory = 700
      }

      # This *can* move to ./assets/ later, but it's tiny, closely coupled, and refers some assets template.
      template {
        data = <<EOH
modules:
  prober: https
  timeout: 10s
  https:
    proxy_url: http://proxy.det.nsw.edu.au:80
  tls_config:
    ca_file: /local/globalsignca.pem
EOH
        destination = "local/ssl_exporter.yaml"
      }

      template {
        data = file("assets/collector-ssl-certificates---detnsw.pem")
        destination = "local/detnsw.pem"
      }

      template {
        data = file("assets/collector-ssl-certificates---globalsignca.pem")
        destination = "local/globalsignca.pem"
      }

      template {
        data = file("assets/collector-ssl-certificates---globalsigncachain.pem")
        destination = "local/globalsigncachain.pem"
      }


    } // end-task "ssl-exporter" 


    # TASK -- prometheus = = = = = = = = = = = = = = = = = = = = = = = =

    task "task-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Collector for SSL Certificates",

          "--storage.tsdb.retention.time=48h",
          # "--storage.tsdb.retention.size=256MB",

          # We *will* flip this to agent-mode once we are sending to Mimir.
          # "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},env=prod,task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        network_mode = "host"
      }

      resources {
       cpu    = 200
        memory = 800
      }

      service {
        name = "collector-ssl-certificates-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-ssl-certificates-prometheus.rule=Host(`collector-ssl-certificates-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-ssl-certificates-prometheus.tls=false",
          "traefik.http.routers.collector-ssl-certificates-prometheus.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    # provenance: obscol-prometheus-collector-ssl-certificates
    provenance: prometheus-collector-ssl-certificates

  scrape_interval: 2m

  # This creates a log of *all* queries, and will show up in /metrics as 
  #     prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:

  # Self - our own prometheus metrics
  - job_name: 'collector_ssl_certificate_prometheus'
    metrics_path: /metrics
    static_configs:
      # - targets: [ "collector-ssl-certificates-prometheus:{{ env "NOMAD_PORT_port_prometheus" }}" ]
      - targets: [ "collector-ssl-certificates-prometheus.obs.nsw.education" ]
    metric_relabel_configs:
    - source_labels: [__name__]
      regex: "^go_.*"
      action: drop

  - job_name: 'collector_ssl_certificate_exporter'
    metrics_path: /metrics
    static_configs:
      # - targets: [ "collector-ssl-certificates-exporter:{{ env "NOMAD_PORT_port_exporter" }}" ]
      - targets: [ "collector-ssl-certificates-exporter.obs.nsw.education" ]
    metric_relabel_configs:
    - source_labels: [__name__]
      regex: "^go_.*"
      action: drop

#  - job_name: 'ssl_certificates_static'
#    static_configs:
#      - targets: [ "grafana.mtm.apps.nsw.edu.au" ]
#    metrics_path: /probe
#    params:
#      module: ["https"]
#    relabel_configs:
#      - source_labels: [__address__]
#        target_label: __param_target
#      - source_labels: [__param_target]
#        target_label: instance
#      - target_label: __address__
#        replacement: collector-ssl-certificates-exporter.obs.nsw.education

  - job_name: 'ssl_certificates_sd'
    metrics_path: /probe
    params:
      module: ["https"]

    consul_sd_configs:
      - server: 'consul.obs.nsw.education:8500'
      # - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['ssl-certificates']

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-ssl-certificates-exporter.obs.nsw.education

#remote_write:
#- name: mimir
#  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#  headers:
#    X-Scope-OrgID: prod
#  tls_config:
#    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }
    } // end-task "task-prometheus" 

  }
}

