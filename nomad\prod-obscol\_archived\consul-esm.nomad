// Consul havent released ESM as an official image but there is a dockerfile source in their repo:
// https://github.com/hashicorp/consul-esm under build-support/docker
// docker build -t artifacts.mtm.nsw.education/consul-esm:0.5.0 --build-arg NAME=consul-esm --build-arg VERSION=0.5.0 -f ./Release.dockerfile .
job "consul-esm" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "consul-esm" {

    task "consul-esm" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/consul-esm:0.5.0"
        #dns_servers = ["************"]
        args = [
          "consul-esm",
          "-config-file=/etc/consul-esm/config.hcl",
        ]
        volumes = [
          "local/etc/consul-esm/config.hcl:/etc/consul-esm/config.hcl",
        ]
        # consul-esm wants to listen to ICMP on the host adaptor
        # otherwise you get: `Error listening for ICMP packets: socket: permission denied`
        # privileged = true
      }

      env {
        #CONSUL_HTTP_ADDR = "http://consul.service.dc-cir-un.collectors.obs.nsw.education:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      template {
        data = <<EOH
  datacenter = "dc-cir-un-prod"
  http_addr = "localhost:8500"

  // Expose prometheus metrics
  client_address = "0.0.0.0:8080"
EOH
        destination = "local/etc/consul-esm/config.hcl"
      }
    }
  }
}
