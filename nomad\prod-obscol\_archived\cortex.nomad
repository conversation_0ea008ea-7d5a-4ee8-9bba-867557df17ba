variables {
  consul_hostname = "consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500"
  replicas = 1
}

job "cortex" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "cortex" {
    count = var.replicas

    # Ensure replica spawns on unique node
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }   
    network {
      port "http" {}
      port "grpc" {}
    }

    volume "cortex"  {
      type = "host"
      source = "cortex"
      read_only = false
    }

    task "cortex" {
      driver = "docker"

      config {
        image = "https://quay.io/cortexproject/cortex:v1.11.0"
        dns_servers = ["************"]
        ports = ["http", "grpc"]
        volumes = []
        args = [
          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-config.file=/local/cortex.yml",
          "-config.expand-env",
          "-ring.store=inmemory",
          "-consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
          "-distributor.replication-factor=${var.replicas}"
        ]
      }

      volume_mount {
        volume = "cortex"
        destination = "/mnt/cortex"
        read_only = false
      }

//      artifact {
//        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
//        destination = "local/prometheus-configuration"
//
//        options {
//          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
//        }
//      }

      resources {
        cpu = 500
        memory = 2048
      }

      service {
        name = "cortex"
        port = "http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.cortex.rule=Host(`cortex.obs.nsw.education`)",
          "traefik.http.routers.cortex.tls=false",
          "traefik.http.routers.cortex.entrypoints=http,https,cortex",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }

        check {
          type = "http"
          port = "http"
          path = "/services"
          interval = "30s"
          timeout = "5s"
        }
      }

      template {
        data = <<EOH
auth_enabled: false

server:
  http_listen_port: 9090

  # Configure the server to allow messages up to 100MB.
  grpc_server_max_recv_msg_size: 104857600
  grpc_server_max_send_msg_size: 104857600
  grpc_server_max_concurrent_streams: 1000

distributor:
  shard_by_all_labels: true
  pool:
    health_check_ingesters: true

ingester_client:
  grpc_client_config:
    # Configure the client to allow messages up to 100MB.
    max_recv_msg_size: 104857600
    max_send_msg_size: 104857600
    grpc_compression: gzip

ingester:
  lifecycler:
    # The address to advertise for this ingester.  Will be autodiscovered by
    # looking up address on eth0 or en0; can be specified if this fails.
    # address: 127.0.0.1

    # We want to start immediately and flush on shutdown.
    join_after: 0
    min_ready_duration: 0s
    final_sleep: 0s
    num_tokens: 512

    # Use an in memory ring store, so we don't need to launch a Consul.
    ring:
      kvstore:
        store: inmemory
      replication_factor: 1

storage:
  engine: blocks

limits:
  ingestion_rate: 100000
  ingestion_burst_size: 400000

  # Because openshift-monitoring always uses a StatefulSet of 2 Prometheus'es we need to de-dupe the incoming samples
  # Using the labels below. `prometheus_replica` is Guaranteed for the OpenShift 4.7.x Prometheus Operator. clusterid is ours
  accept_ha_samples: true
  ha_cluster_label: "clusterid"
  ha_replica_label: "prometheus_replica"

  # The default limit is 50k and will drop extras.
  # If you set this unlimited be prepared for unlimited memory usage on distributor/ingester
  # One cluster currently has over 600k series
  max_series_per_metric: 0

blocks_storage:
  backend: filesystem
  filesystem:
    dir: /mnt/cortex/blocks
  tsdb:
    dir: /mnt/cortex/tsdb

compactor:
  data_dir: /local/compactor
  sharding_ring:
    kvstore:
      store: inmemory

frontend_worker:
  match_max_concurrent: true

ruler:
  # Experimental, but required for Grafana 8.1 Managed Alerts
  # Requires the use of storage OTHER THAN "local"
  enable_api: true

  enable_sharding: false
  external_url: "http://${NOMAD_ADDR_http}"  # If running without a proxy

  alertmanager_url: "http://alertmanager.service.dc-cir-un-prod.collectors.obs.nsw.education:9093/"
#  alertmanager_url: "_alertmanager._tcp.service.dc-cir-un-prod.collectors.obs.nsw.education"
#  enable_alertmanager_discovery: true

# Use this for testing git-based rules
#  storage:
#    type: local
#    local:
#      directory: /local/prometheus-configuration/cortex/rules


# For some reason, in single binary only the legacy configuration was respected -eb.
ruler_storage:
  backend: filesystem
  # Store rules as a local database, not configdb, not a set of text files
  filesystem:
    dir: /mnt/cortex/rules

EOH
        destination = "local/cortex.yml"
      }
    }
  }

}
