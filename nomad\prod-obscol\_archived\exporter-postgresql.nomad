
# postgresql pgsql exporter for prod obscol 
#
# 2023-08-20 - WIP WIP WIP  

# Start an example database:
#     docker run --net=host -it --rm -e POSTGRES_PASSWORD=password postgres
#
# Connect to it:
#     docker run \
#      --net=host \
#      -e DATA_SOURCE_NAME="postgresql://postgres:password@localhost:5432/postgres?sslmode=disable" \
#      quay.io/prometheuscommunity/postgres-exporter

# Account creation / configuration:
#    CREATE USER monprom WITH PASSWORD 'bigsecret' CONNECTION LIMIT 5;
#    GRANT pg_monitor TO monprom;
#    GRANT pg_read_all_stats TO monprom;

#        $ /bin/postgres_exporter  --help
#        Flags:
#          -h, --[no-]help                Show context-sensitive help (also try --help-long and --help-man).
#              --[no-]collector.database  Enable the database collector (default: enabled).
#              --[no-]collector.postmaster
#                                         Enable the postmaster collector (default: disabled).
#              --[no-]collector.process_idle
#                                         Enable the process_idle collector (default: disabled).
#              --[no-]collector.replication
#                                         Enable the replication collector (default: enabled).
#              --[no-]collector.replication_slot
#                                         Enable the replication_slot collector (default: enabled).
#              --[no-]collector.stat_bgwriter
#                                         Enable the stat_bgwriter collector (default: enabled).
#              --[no-]collector.stat_database
#                                         Enable the stat_database collector (default: enabled).
#              --[no-]collector.stat_statements
#                                         Enable the stat_statements collector (default: disabled).
#              --[no-]collector.stat_user_tables
#                                         Enable the stat_user_tables collector (default: enabled).
#              --[no-]collector.statio_user_tables
#                                         Enable the statio_user_tables collector (default: enabled).
#              --config.file="postgres_exporter.yml"
#                                         Postgres exporter configuration file.
#              --[no-]web.systemd-socket  Use systemd socket activation listeners instead of port listeners (Linux only).
#              --web.listen-address=:9187 ...
#                                         Addresses on which to expose metrics and web interface. Repeatable for multiple addresses.
#              --web.config.file=""       [EXPERIMENTAL] Path to configuration file that can enable TLS or authentication. See: https://github.com/prometheus/exporter-toolkit/blob/master/docs/web-configuration.md
#              --web.telemetry-path="/metrics"
#                                         Path under which to expose metrics. ($PG_EXPORTER_WEB_TELEMETRY_PATH)
#              --[no-]disable-default-metrics
#                                         Do not include default metrics. ($PG_EXPORTER_DISABLE_DEFAULT_METRICS)
#              --[no-]disable-settings-metrics
#                                         Do not include pg_settings metrics. ($PG_EXPORTER_DISABLE_SETTINGS_METRICS)
#              --[no-]auto-discover-databases
#                                         Whether to discover the databases on a server dynamically. (DEPRECATED) ($PG_EXPORTER_AUTO_DISCOVER_DATABASES)
#              --extend.query-path=""     Path to custom queries to run. (DEPRECATED) ($PG_EXPORTER_EXTEND_QUERY_PATH)
#              --[no-]dumpmaps            Do not run, simply dump the maps.
#              --constantLabels=""        A list of label=value separated by comma(,). (DEPRECATED) ($PG_EXPORTER_CONSTANT_LABELS)
#              --exclude-databases=""     A list of databases to remove when autoDiscoverDatabases is enabled (DEPRECATED) ($PG_EXPORTER_EXCLUDE_DATABASES)
#              --include-databases=""     A list of databases to include when autoDiscoverDatabases is enabled (DEPRECATED) ($PG_EXPORTER_INCLUDE_DATABASES)
#              --metric-prefix="pg"       A metric prefix can be used to have non-default (not "pg") prefixes for each of the metrics ($PG_EXPORTER_METRIC_PREFIX)
#              --[no-]version             Show application version.
#              --log.level=info           Only log messages with the given severity or above. One of: [debug, info, warn, error]
#              --log.format=logfmt        Output format of log messages. One of: [logfmt, json]


job "exporter-postgresql" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "exporter-postgresql" {

    network {
      port "port-exporter-postgresql" {
        to = 9187
      }
    }

    count = 1

#    constraint {
#      attribute = "${attr.unique.hostname}"
#      value = "pl0475obscol06"
#    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "exporter-postgresql" {
      driver = "docker"

      env = {
        # DATA_SOURCE_NAME="******************************************/postgres?sslmode=disable"
        # DATA_SOURCE_NAME="**************************************************/postgres?sslmode=disable"

        # DATA_SOURCE_NAME="***************************************************/postgres?sslmode=disable"

        # HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        # HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        # NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
        "PG_EXPORTER_METRIC_PREFIX" = "postgres"
      }

      config {
        # 2023-08 - latest version is v0.13.2 - 2023-07-24 - refer https://quay.io/repository/prometheuscommunity/postgres-exporter?tab=tags
        # image = "quay.io/prometheuscommunity/postgres-exporter:latest"
        image = "quay.io/prometheuscommunity/postgres-exporter:v0.13.2"

        ports = ["port-exporter-postgresql"]

        args = [
          "--config.file=/etc/postgres_exporter.yml",

          # log level: debug, info, warn, error
          "--log.level=warn",

          # metric prefix - defaults to pg which is annoying - will force on postgres
          # note that the README is out of date of the shipped binary (go into the container and run /bin/postgres_exporter --help
          "--metric-prefix=postgres",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          

        volumes = [
          "local/postgres_exporter.yaml:/etc/postgres_exporter.yml",
        ]

      }

      resources {
        cpu    = 300
        memory = 200
      }

      template {
        data = <<EOH
auth_modules:

  # Translate:    psql -h ************** -p 5432 -U postgres -W postgres   # password 'bigsecret'

  # This key can be anything useful
  pu0992ttdbs001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: bigsecret
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0991tedbs001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: bigsecret
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0992ttdbd001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: bigsecret
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0991tedbd001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: bigsecret
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

EOH
        destination = "local/postgres_exporter.yaml"
      }


      service {
        name = "exporter-postgresql"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-postgresql.rule=Host(`exporter-postgresql.obs.nsw.education`)",
          "traefik.http.routers.exporter-postgresql.tls=false",
          "traefik.http.routers.exporter-postgresql.entrypoints=http,https",
        ]

        port = "port-exporter-postgresql"

        check {
          port     = "port-exporter-postgresql"
          type     = "tcp"
          name     = "alive"
          interval = "10s"
          timeout  = "2s"
        }

      }

    } // end-task "exporter-postgresql"

  }
}
