// obscol-prod - Prometheus exporter for Oracle database(s)
//
// docker commandline is:
//   docker run   -p 9161:9161 -e DATA_SOURCE_NAME="system/oracle@//192.168.27.123:1521" iamseth/oracledb_exporter

// minimalistic docker hub page:
//   https://hub.docker.com/r/iamseth/oracledb_exporter

// github project page:
//   https://github.com/iamseth/oracledb_exporter

variables {
  image_oracledb_exporter = "quay.education.nsw.gov.au/observability/oracledb_exporter:latest"
}

job "exporter-oracle" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "prometheus-oracle" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    # We don't have DNS for this, and it's talking to oracle on obscol02
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol02.nsw.education"
    }

    network {
      port "port_exporter"  {
        static = 9161
        to     = 9161
      }
    }

    task "oracle" {
      driver = "docker"

      # Use the default oracle-in-a-box credentials, but we have proven out
      # the srvmtmzbx account (when created per standard process) works.
      env = {
        "DATA_SOURCE_NAME" = "system/oracle@//pl0992obscol02.nsw.education:1521"
      }

      config {
        image = var.image_oracledb_exporter
        ports = ["port_exporter"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      
      }

      service {
        name = "exporter-oracle"
        port = "port_exporter"
        check {
          name     = "Oracle exporter healthcheck"
          port     = "port_exporter"
          type     = "tcp"
          interval = "60s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }

    }

  }
}
