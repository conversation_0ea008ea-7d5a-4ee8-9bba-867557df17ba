
# Prometheus SNMP exporter for GovDC PDU's (ObsCol PROD)

# There is no useful ObsCol TEST analogue for this job, as only PROD has routable access to the PDU subnets.

# SNMP Exporter traditionally is a centralised service, but we're splitting these out per customer, with matching
# Prometheus instances, to provide some isolation, allowing for easier / less-risky management, and reducing the size
# of any single nomad job.

# We use the assets from /opt / sharednfs / prometheus-configuration / snmp / 

job "exporter-snmp-pdu" {

  type = "service"

  datacenters = ["dc-cir-un-prod"]

  group "exporter-snmp-pdu" {
    network {
      port "port_exporter_snmp_pdu" {
        to = 9117         // rely on traefik
      }
    }

    task "exporter-snmp-pdu" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = ["port_exporter_snmp_pdu"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }          

        dns_servers = [ "************" ]

        volumes = [
          "/opt/sharednfs/prometheus-configuration/snmp/snmp-enlogic-pdu.yml:/etc/snmp_exporter/snmp.yml"
        ]

        args = [
          "--web.listen-address=:${NOMAD_PORT_port_exporter_snmp_pdu}",

          "--config.file=/etc/snmp_exporter/snmp.yml"
        ]

      }

      service {
        name = "exporter-snmp-pdu"
        port = "port_exporter_snmp_pdu"

//        check {
//          type = "http"
//          port = "port_exporter_snmp_pdu"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-snmp-pdu.rule=Host(`exporter-snmp-pdu.obs.nsw.education`)",
          "traefik.http.routers.exporter-snmp-pdu.tls=false",
          "traefik.http.routers.exporter-snmp-pdu.entrypoints=http",
        ]

      }

    }
  }
}
