job "exporter-app-vcenter" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "exporter-app-vcenter" {
    network {
      port "vcenter-port" {
        to = 9118
      }
    }

    task "exporter-app-vcenter" {
      driver = "docker"
      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
        }
      config {
        ports = ["vcenter-port"]
        logging {   
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            }
            }         
        image = "prom/blackbox-exporter:v0.23.0"
        dns_servers = ["************"]
        volumes = ["local/blackbox.yml:/config/blackbox.yml","local/detnsw.pem:/tmp/detnsw.pem"]
              args = [
        "--config.file=/config/blackbox.yml",
        "--web.listen-address=:${NOMAD_PORT_vcenter_port}"
      ]
      }


      service {
        name = "exporter-app-vcenter"
        port = "vcenter-port"

        check {
          type = "http"
          port = "vcenter-port"
          path = "/ready"
          interval = "20s"
          timeout = "10s"
        }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-app-vcenter.rule=Host(`exporter-app-vcenter.obs.nsw.education`)",
          "traefik.http.routers.exporter-app-vcenter-pdu.tls=false",
          "traefik.http.routers.exporter-app-vcenter.entrypoints=http",
        ]
        
      }

      template {
        data = <<EOH
modules:
  vcenter:
    prober: http
    timeout: 15s
    http:
      method: GET
      follow_redirects: true
      preferred_ip_protocol: "ip4" # defaults to "ip6"
      ip_protocol_fallback: false  # no fallback to "ip6"
      tls_config:
        ca_file: "/tmp/detnsw.pem"
        insecure_skip_verify: true
EOH
        destination = "local/blackbox.yml"
      }
      template {
        data = <<EOH
-----BEGIN CERTIFICATE-----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=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7eSPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----


EOH
        destination = "local/detnsw.pem"
      }
      template {
        data = <<EOH
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOH
        destination = "local/globalsignca.pem"
      }
      template {
        data = <<EOH
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7e
SPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOH
        destination = "local/globalsigncachain.pem"
      }
    }
  }
}
