// Grafana - jedd experimentation

// 2024-03 - preparation for:
//     a)  expiration of 'grafana.mtm' - migration of TEST grafana-obs instance
//     b)  expiration of on-prem enterprise licence (2024-04)

// Uses /opt/sharednfs/grafana-jedd for persistent storage.  

# @TODO sort out https certs problems - might need copy of certs locally

# @TODO sso - almost definitely never achievable

# @TODO postgresql task - the grafana task should wait for this to be running, 
# otherwise we have at least one failed grafana allocation at each job-start as
# it gets ahead of postgresql readiness

# @TODO sanitise residual configuration (environment) variables below - put as 
# many into the assets/grafana-jedd.ini file as possible

# @TODO work out whether WARN about /usr/share/grafana/plugins-bundled should 
# be / can be suppressed or resolved - it's related to one of the PATHs we can 
# define, but probably should NOT be redefined from default (internal to container)
# as the variable defines bin/grafana and other key paths - but the docker container 
# does NOT ship with an empty plugins-bundled directory for some reason. Perhaps a 
# pre-start mkdir would suppress safely


# Requirements = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# CONSUL variables must exist:
#     grafana-jedd/POSTGRES_DB
#     grafana-jedd/POSTGRES_USER
#     grafana-jedd/POSTGRES_PASSWORD


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:test-obscol"
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:10.1.5"
  # 2023-08-24 OBS-676 jedd - moving from 12.15-1.pgdg120+1 (latest at previous instantiation) to 12.17 specifically

  # We are matching prod grafana during initial build as we will try to replicate
  # postgresql and other configuration data - maybe bringing across existing SSO
  # accounts - and can THEN upgrade in place to a more modern version of grafana.
  # Grafana prod mtm 2024-03-15 is ******* - but that's not available on docker hub
  image_grafana = "grafana/grafana:9.3.2"
  # image_grafana = "grafana/grafana:10.1.10"
  image_postgresql =  "postgres:12.17"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-jedd-postgresql" {
  datacenters = ["dc-cir-un-prod"]

  # While testing, speed-up iterations by constraining to single host
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0475obscol0[6]"
  }

  type = "service"

  group "grafana-jedd" {

    network {
      port "port_grafana" {
        to = 3000
      }

      port "port_postgresql"  {
        # to = 5432
        static = 5432
      }
    }

    # Volumes are handled by explicit path to obt/sharednfs/ (refer below)

    # task postgresql  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      kill_timeout = "90s"
      # user = "postgres:postgres"

      # Volumes are handled by explicit path to obt/sharednfs/ (refer below)

      config {
        image = "${var.image_postgresql}"

        # userns does not work - but privileged does - to solve the 'cannot change permission on files' error
        # userns_mode = "host"
        privileged = true

        ports = ["port_postgresql"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "/opt/sharednfs/grafana-jedd:/persistent",
          "/opt/sharednfs/grafana-jedd/postgresql/data:/var/lib/postgresql/data",
          "/opt/sharednfs/grafana-jedd/postgresql/data:/var/lib/pgsql/12/data"
        ]

      }

      env = {
        "POSTGRES_DB"       = "grafana",
        "POSTGRES_USER"     = "grafana",
        "POSTGRES_PASSWORD" = "S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU",
        # "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
        "PGPORT"            = "5432",
        "PGDATA"            = "/persistent/postgresql/data",

        # This SHOULD do something functionally equivalent to:
        #   echo "host all all all $POSTGRES_HOST_AUTH_METHOD" >> pg_hba.conf
        # Which SHOULD resolve our 'no pg_hba.conf entry for ************' error - but does not
        "POSTGRES_HOST_AUTH_METHOD" = "md5"

        # We should be able to brute force this with 'trust' but that doesn't work either
        # "POSTGRES_HOST_AUTH_METHOD" = "trust"
      }

      resources {
        cpu    = 500
        memory = 2500
      }

      service {
        name = "postgresql"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-postgresql.entrypoints=http",
          "traefik.http.routers.grafana-postgresql.rule=Host(`grafana-postgresql.obs.nsw.education`)",
        ]      

        port = "port_postgresql"

##        check {
##          name     = "PostgreSQL for Grafana healthcheck"
##          port     = "port_postgresql"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }

      }
    }    #  end-task  postgresql



  }
}
