

// 2021-08 - built out by <PERSON><PERSON>.
// 2025-05 - shuffled into archive by Jedd.

variables {
    image_grafana = "quay.education.nsw.gov.au/observability/grafana:prod-obscol"
  }

job "grafana" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "grafana" {
    count = 1

    network {
      port "http" {}
    }

    volume "grafana" {
      type = "host"
      source = "grafana"
      read_only = false
    }

    task "grafana" {
      driver = "docker"

      config {
        image = var.image_grafana
        dns_servers = ["************"]
        ports = ["http"]
        volumes = []
      }

      env {
        GF_LOG_LEVEL          = "DEBUG"
        GF_LOG_MODE           = "console"
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_http}"

        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"

        GF_INSTALL_PLUGINS = ""
        GF_PATHS_PROVISIONING = "/local/grafana/provisioning"
        # GF_PATHS_DATA = "/data/data"
        GF_LIVE_ALLOWED_ORIGINS = "http://*"
        GF_FEATURE_TOGGLES_ENABLE = "ngalert"
      }

      service {
        name = "grafana"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/api/health"
          interval = "5s"
          timeout = "2s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.nsw.education`)",
          "traefik.http.routers.grafana.tls=false",
        ]
      }

      volume_mount {
        volume = "grafana"
        destination = "/data"
        read_only = false
      }

      resources {
        cpu    = 100
        memory = 100
      }

      template {
        data        = <<EOTC
apiVersion: 1
datasources:
  - name: Cortex
    type: prometheus
    access: proxy
    # It is necessary to use /api/prom legacy endpoint for Cortex Managed Alerts in Grafana 8.1
    url: http://cortex.service.dc-cir-un-prod.collectors.obs.nsw.education:9090/api/prom
    jsonData:
      exemplarTraceIdDestinations:
      - name: traceID
        datasourceUid: tempo
  - name: Tempo
    type: tempo
    access: proxy
    url: http://tempo.service.dc-cir-un-prod.collectors.obs.nsw.education:3400
    uid: tempo
  - name: Loki
    type: loki
    access: proxy
    url: http://loki.service.dc-cir-un-prod.collectors.obs.nsw.education:3100
    jsonData:
      derivedFields:
        - datasourceUid: tempo
          matcherRegex: (?:traceID|trace_id)=(\w+)
          name: TraceID
          url: $$${__value.raw}
EOTC
        destination = "/local/grafana/provisioning/datasources/ds.yaml"
      }
    }
  }
}
