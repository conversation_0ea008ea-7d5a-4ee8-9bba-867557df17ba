
# generic debian container for prod-obscol

# Needs the command / args to sleep indefinitely otherwise it bombs out immediately.
# Stolen then heavily modified from https://github.com/leowmjw/nomad-box


job "jedd-debian" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "control" {

    network {
      port "port_debian" {
        to = 2222
      }
    }

    count = 1

#    restart {
#      attempts = 10
#      interval = "5m"
#      delay = "25s"
#      mode = "delay"
#    }


    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl0475obscol0[6]"
    }

    ephemeral_disk {
      size = 300
    }

    task "jedd-debian" {
      driver = "docker"


#      env = {
#        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
#      }

      config {
        # image = "debian:stable"
        # image = "python:3.9-bullseye"
        # image = "ubuntu"
        #
        # image = "wbitt/network-multitool"
        # command = "/bin/bash"

        image = "someguy123/net-tools"
        # command = "/bin/bash"

        ports = ["port_debian"]
        command = "/bin/sleep"
        args = [" infinity"]
        }

      resources {
        cpu    = 500
        memory = 2048
      }


      service {
        name = "debian-app"
        port = "port_debian"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jedd-debian.rule=Host(`jedd-debian.obs.nsw.education`)",
          "traefik.http.routers.jedd-debian.tls=false",
          "traefik.http.routers.jedd-.entrypoints=http,https",
        ]


#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}
