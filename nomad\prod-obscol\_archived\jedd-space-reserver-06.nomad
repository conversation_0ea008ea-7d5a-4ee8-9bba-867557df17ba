
# space-reserver for 06, 07, 08 hosts until we resolve the docker / VEN / illumio issue

# just consumes the space space (up to 7gb) on those hosts so new provisions will go to 01, 02, 030

job "jedd-space-reserver-06" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  group "control" {

    network {
      port "port_debian" {
        to = 2222
      }
    }

    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl0475obscol0[6]"
    }

    ephemeral_disk {
      size = 300
    }

    task "jedd-space-reserver-06" {
      driver = "docker"

      config {
        image = "someguy123/net-tools"

        ports = ["port_debian"]
        command = "/bin/sleep"
        args = [" infinity"]
        }

      resources {
        cpu    = 150
        memory = 4400
      }


      service {
        name = "debian-app"
        port = "port_debian"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jedd-space-reserver-06.rule=Host(`jedd-space-reserver-06.obs.nsw.education`)",
          "traefik.http.routers.jedd-space-reserver-06.tls=false",
        ]


      }

    }

  }
}
