
# Nicolaka / netshoot image for troubleshooting networking delights in dockerland

# Refer guide at:
#    https://hub.docker.com/r/nicolaka/netshoot

variables {
  image_netshoot = "nicolaka/netshoot"
}


job "jedd-netshoot" {
  type = "service"

  datacenters = ["dc-cir-un-prod"]

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    # value = "pl0475obscol0[6]"
    value = "pl0992obscol0[2]"
  }


  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "jedd-netshoot" {
    
    network {
      port "port_netshoot" { }
  	}
    

    # TASK netshoot = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-jedd-netshoot" {
      driver = "docker"

      config {
        image = var.image_netshoot

        dns_servers = ["************"]

        args = [
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

        ports = [ "port_netshoot" ]

        volumes = [
        ]
      }

      resources {
        cpu    = 500
        memory = 1200
        memory_max = 2000
      }

      service {
        name = "jedd-netshoot"
        port = "port_netshoot"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jedd-netshoot.rule=Host(`jedd-netshoot.obs.nsw.education`)",
          "traefik.http.routers.jedd-netshoot.tls=false",
          "traefik.http.routers.jedd-netshoot.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_netshoot"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

    }  // END-task  "task-jedd-netshoot"


  }

}

