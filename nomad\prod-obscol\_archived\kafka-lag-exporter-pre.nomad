job "kafka-lag-exporter-pre" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "kafka-lag-exporter" {
    count = 1

    network {
      port "prometheus_exporter" { }
    }

    task "lag-exporter" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/kafka-lagexporter.git"
        destination = "local/conf"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      config {
        image = "https://docker.io/lightbend/kafka-lag-exporter:0.6.7"
        dns_servers = ["************"]
        ports = ["prometheus_exporter"]

        volumes = [
          "local/conf:/opt/docker/conf/",
        ]
        args = ["/opt/docker/bin/kafka-lag-exporter", "-Dconfig.file=/opt/docker/conf/application.conf"]  # -Dlogback.configurationFile=/opt/docker/conf/logback.xml
      }

      service {
        name = "openmetrics"
        port = "prometheus_exporter"

        check {
          type = "tcp"
          port = "prometheus_exporter"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
kafka-lag-exporter {
  reporters.prometheus.port = {{ env "PROMETHEUS_PORT" }}
  clusters = [
    {
      name = "{{ env "KAFKA_CLUSTER_NAME" }}"
      bootstrap-brokers = "{{ env "KAFKA_BROKERS" }}"
      labels = {
        cir_app_id = "{{ env "KAFKA_CIR_APP_ID" }}"
      }
      admin-client-properties = {
        security.protocol = "SASL_SSL"
        ssl.truststore.location = "/opt/docker/conf/truststore.jks"
        ssl.truststore.password = "{{ env "TRUSTSTORE_PASS" }}"
        sasl.mechanism = "PLAIN"
        security.protocol = "SASL_SSL"
        sasl.jaas.config = "org.apache.kafka.common.security.plain.PlainLoginModule required username="{{key "kafka/username"}}" password="{{key "kafka/password"}}";"
      }
      consumer-properties = {
        security.protocol = "SASL_SSL"
        ssl.truststore.location = "/opt/docker/conf/truststore.jks"
        ssl.truststore.password = "{{ env "TRUSTSTORE_PASS" }}"
        sasl.mechanism = "PLAIN"
        security.protocol = "SASL_SSL"
        sasl.jaas.config = "org.apache.kafka.common.security.plain.PlainLoginModule required username="{{key "kafka/username"}}" password="{{key "kafka/password"}}";"
      }
    }
  ]
}
EOH
        destination = "local/conf/application.conf"
      }

      env {
        # Available defaults
        # https://github.com/lightbend/kafka-lag-exporter/blob/master/src/main/resources/reference.conf
        #
        # KAFKA_LAG_EXPORTER_PORT = 8000
        # KAFKA_LAG_EXPORTER_POLL_INTERVAL_SECONDS = 30
        # KAFKA_LAG_EXPORTER_LOOKUP_TABLE_SIZE = 60
        # KAFKA_LAG_EXPORTER_CLIENT_GROUP_ID = "kafkalagexporter"
        # KAFKA_LAG_EXPORTER_KAFKA_CLIENT_TIMEOUT_SECONDS = "10 seconds"
        # KAFKA_LAG_EXPORTER_CLUSTERS = []
        # KAFKA_LAG_EXPORTER_STRIMZI = "false"

        KAFKA_BROKERS = "ql0992kfkab001.nsw.education:9092,ql0992kfkab002.nsw.education:9092,ql0992kfkab003.nsw.education:9092,"
        KAFKA_CLUSTER_NAME = "pre"
        KAFKA_CIR_APP_ID = "kfka"
        # KAFKA_LAG_EXPORTER_KAFKA_LOG_LEVEL = "debug"
        PROMETHEUS_PORT = "${NOMAD_PORT_prometheus_exporter}"
        TRUSTSTORE_PASS = "123456"
      }

      resources {
        cpu = 400
        memory = 700
      }
    }
  }
}
