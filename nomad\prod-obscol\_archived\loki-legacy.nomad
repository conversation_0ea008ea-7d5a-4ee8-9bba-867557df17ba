
// Loki in PROD ObsCol cluster - proposed (2022-08-23)

// 2021-07-21 jedd - constrained to 01 - we can later fix up dyn service
// 2021-10-11 jedd - this should map to loki.obs.nsw.education (CNAME)
// 2021-10-14 jedd - update with working jedd-lab changes
// 2022-08-23 jedd - locked loki version to 2.6.1, most config moved to [args]

// 2022-12-01 jedd - relegating this job to the 'legacy' role, retaining old data for two weeks or so,
//                   with intent to then retire and remove these data, relying on new 'loki-s3' job
//                   for future / long term storage.

job "loki-legacy" {

  datacenters = ["dc-cir-un-prod"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "loki-legacy" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {

      dns {
        servers = ["************"]
      }

      port "port_loki_http"  {
        to = 3100
      }
      port "port_loki_grpc"  {
        static = 9098
      }
    }

    volume "vol_loki" {
      type      = "host"
      source    = "vol_loki"
      read_only = false
    }

    task "loki-legacy" {
      driver = "docker"

      # 2022-08-25 - jedd - promoted from TEST
      env {
        JAEGER_AGENT_HOST    = "tempo.service.dc-cir-un-prod.collectors.obs.nsw.education"
        JAEGER_TAGS          = "cluster=nomad"
        JAEGER_SAMPLER_TYPE  = "probabilistic"
        JAEGER_SAMPLER_PARAM = "1"
      }

      config {
        # NOTA BENE - do NOT go for latest or master - you WILL have a very bad day, as 
        #             Grafana Corp are admirably relentless with their breaking changes 
        #             between versions.
        image = "grafana/loki:2.4.2"

        ports = ["port_loki_http", "port_loki_grpc"]

        # This logs to the primary long-term storage (s3) loki instance, not ourselves.
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                }
            }         

        args = [

          ### PROPOSED PROD args

          # Note -- Most configuration items are done here, using CLI parameters passed 
          # in via [args]
          # Items that can ONLY go into a configuration file are provided in the 
          # template{} below.

          # Refer:      https://grafana.com/docs/loki/latest/configuration/
          # and also:   NOTES-loki-parameters.{...}.txt   file in this repo, which
          #             dumps output of ```/usr/bin/loki -help```.

          # Note also that those two sources often conflict or simply lie.  Hilarity ensues.

          # Observant readers will see these are grouped into related blocks, and in
          # alphabetical order.


          # Enables authentication through the X-Scope-OrgID header, which must be present
          # if true. If false, the OrgID will always be set to "fake".
          # Set to false to disable auth. (default true)
          "-auth.enabled=false",


          # Configuration file to load, can be a comma separated list of paths, first 
          # existing file will be used (default "config.yaml,config/config.yaml")
          "-config.file=local/loki/config.yaml",


          # TTL for index files restored in cache for queries (default 24h0m0s)
          # Can be increased for faster performance over longer query periods, uses more disk space.
          "-boltdb.shipper.cache-ttl=24h",

          # Shared store for keeping index files. Supported types: gcs, s3, azure, filesystem
          "-boltdb.shipper.shared-store=filesystem",

          # Location of BoltDB index files.
          "-boltdb.dir=/loki/index",

          # Backend storage to use for the ring. Supported values are:
          #   consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-boltdb.shipper.compactor.ring.store=inmemory",

          # Directory where files can be downloaded for compaction.
          "-boltdb.shipper.compactor.working-directory=/tmp/loki/boltdb-shipper-compactor",

          # Directory where ingesters would write index files which would then be 
          # uploaded by shipper to configured storage.
          "-boltdb.shipper.active-index-directory=/loki/boltdb-shipper-active",

          # Cache location for restoring index files from storage for queries
          "-boltdb.shipper.cache-location=/loki/boltdb-shipper-cache",


          # The number of ingesters to write to and read from. (default 3)
          "-distributor.replication-factor=1",

          # Backend storage to use for the ring. Supported values are:
          #   consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-distributor.ring.store=inmemory",


          # Enable the ruler api
          "-experimental.ruler.enable-api=true",


          # Mode in which the index gateway client will be running (default "simple")
          # It supports two modes:
          # 'simple': an index gateway server instance is responsible for handling,
          #     storing and returning requests for all indices for all tenants.
          # 'ring': an index gateway server instance is responsible for a subset 
          #     of tenants instead of all tenants.
          # This is in the -help, but simply causes an error at run time.
          # "-index-gateway.mode=simple"


          # 2022-03 jedd - sometimes we see 'permission denied on mkdir /wal' errors,
          # especially with the 2.4.x releases.  This seems to force resolve that issue.
          # Enable writing of ingested data into WAL. (default true)
          "-ingester.wal-enabled=true",
          # Directory to store the WAL and/or recover from WAL. (default "wal")
          "-ingester.wal-dir=/loki/wal",

          # IP address to advertise in the ring.
          "-ingester.lifecycler.addr=${NOMAD_IP_port_loki_http}",

          # Duration to sleep for before exiting, to ensure metrics are scraped. (default 30s)
          "-ingester.final-sleep=10s",

          # Maximum chunk age before flushing. (default 2h0m0s)
          "-ingester.max-chunk-age=1h",

          # Any chunk not receiving new logs in this time will be flushed (default 30m0s)
          "-ingester.chunks-idle-period=1h",

          # A target _compressed_ size in bytes for chunks.
          # This is a desired size not an exact size, chunks may be slightly bigger
          # or significantly smaller if they get flushed for other reasons 
          # (e.g. chunk_idle_period)
          # A value of 0 creates chunks with a fixed 10 blocks,
          # A non zero value will create chunks with a variable number of 
          # blocks to meet the target size.
          # Loki will attempt to build chunks up to 1.5MB, flushing if chunk_idle_period 
          # or max_chunk_age is reached first.  (default 1572864)
          # We use this *instead* of chunk_idle_period (default 30m).
          # This is 10x the default size - at default size we have 600,000 tiny files
          # on prod-obscol loki/chunks/ with very little traffic coming into it.
          "-ingester.chunk-target-size=10485760",

          # The targeted _uncompressed_ size in bytes of a chunk block
          # When this threshold is exceeded the head block will be cut and 
          # compressed inside the chunk.
          # Default is  262144
          # Similar to ingester.chunk-target-size, we're doing a 10x on the default here. 
          "-ingester.chunks-block-size=2621440",

          # How long chunks should be retained in-memory after they've been flushed.
          # CLI flag: -ingester.chunks-retain-period
          # [chunk_retain_period: <duration> | default = 0s]
          "-ingester.chunks-retain-period=30s",

          # Number of times to try and transfer chunks before falling back to flushing.
          # If set to 0 or negative value, transfers are disabled.
          "-ingester.max-transfer-retries=0",


          # Directory to store chunks in.
          "-local.chunk-directory=/loki/chunks",

          # Only log messages with the given severity or above.
          # Valid levels: [debug, info, warn, error] (default info)
          "-log.level=warn",


          # Backend storage to use for the ring. Supported values are:
          #    consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-query-scheduler.ring.store=inmemory",


          # This replaces query_range // split_queries_by_day
          # Split queries by an interval and execute in parallel, 0 disables it.
          # This also determines how cache keys are chosen when result caching 
          # is enabled (default 30m)
          "-querier.split-queries-by-interval=30m",


          # Enable anonymous usage reporting. (default true)
          # For args this is reporting.enabled or reporting_enabled in config, but failing as arg on 2.6.1
          # "-analytics.reporting.enabled=true",
          # "-reporting.enabled=true",

          # Backend storage to use for the ring. 
          # Supported values are: consul, etcd, inmemory, memberlist, multi.
          # CLI flag: -<prefix>.store
          # [store: <string> | default = "memberlist"]
          # "-ruler.ring.kvstore.store=inmemory",
          # Backend storage to use for the ring. Supported values are: 
          #      consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-ring.store=inmemory",


          # If enabled requests to Alertmanager will utilize the V2 API.
          "-ruler.alertmanager-use-v2=true",

          # Method to use for backend rule storage (configdb, azure, gcs, s3, swift, local) (default "configdb")
          "-ruler.storage.type=local",
  
          # Directory to scan for rules
          # @TODO 2022-08 Consider if we want something to use the repository 
          # 'prometheus-configuration' (or analogue) to manage these programmatically.
          "-ruler.storage.local.directory=/loki/rules",

          # File path to store temporary rule files for the prometheus rule managers (default "/rules")
          "-ruler.rule-path=/loki/scratch",
  
          #  Backend storage to use for the ring. Supported values are:
          #     consul, etcd, inmemory, memberlist, multi. (default "consul")
          "-ruler.ring.store=inmemory",

          # Comma-separated list of URL(s) of the Alertmanager(s) to send notifications to. 
          # Each Alertmanager URL is treated as a separate group in the configuration. 
          # Multiple Alertmanagers in HA per group can be supported by using DNS resolution 
          # via -ruler.alertmanager-discovery.
          #
          # @TODO 2022-08 - this needs a proper rethink - we're a way off multi querier 
          # (frontends) for scaling, and traefik may suffice without discovery.  Currently
          # nomad job is a static to 9093.  Experiment in test-obscol first.
          #
          # "-ruler.alertmanager-url=http://alertmanager.obs.nsw.education:9093",


          # HTTP server listen port. (default 80)
          "-server.http-listen-port=${NOMAD_PORT_port_loki_http}",

          # gRPC server listen port. (default 9095) ### default conflicts with cortex & mimir
          "-server.grpc-listen-port=${NOMAD_PORT_port_loki_grpc}",


          # If true, enables retention deletes of DB tables.
          "-table-manager.retention-deletes-enabled=true",

          # Tables older than this retention period are deleted. Must be either 0 (disabled)
          # or a multiple of 24h. When enabled, be aware this setting is destructive to data!
          "-table-manager.retention-period=168h",

        ]

      }


      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }

      template {
        data = <<EOH

# These items MUST live in the configuration file.

# Refer:  https://grafana.com/docs/loki/latest/configuration/#schema_config

schema_config:
  configs:
  - from: 2021-07-01
    store: boltdb-shipper
    object_store: filesystem
    schema: v11
    index:
      prefix: index_
      period: 24h


# Refer:  https://grafana.com/docs/loki/latest/configuration/#limits_config

limits_config:
  ingestion_burst_size_mb: 1500
  ingestion_rate_mb: 1000
  reject_old_samples: false
  reject_old_samples_max_age: 7d
  retention_period: 720h    # 720h = 30 days
  split_queries_by_interval: 24h
  # This is default (true) since 2.4.2
  unordered_writes: true


# Refer:  https://grafana.com/docs/loki/latest/configuration/#chunk_store_config

chunk_store_config:
  max_look_back_period: 0s


# Refer:  https://grafana.com/docs/loki/latest/configuration/#frontend
# 2022-03 jedd - to reduce the incidence of 'too many outstanding requests' errors on
#                Grafana panels - default value is 100, which is WAY too small.

frontend:
  max_outstanding_per_tenant: 4096


# Refer:  https://grafana.com/docs/loki/latest/configuration/#analytics
# This doesn't work here or in CLI args.

#analytics:
#  reporting_enabled: true


EOH
        destination = "local/loki/config.yaml"
      }

      resources {
        cpu    = 1024
        memory = 4096
      }

      service {
        name = "loki-legacy"
        port = "port_loki_http"
        address_mode = "host"

        check {
          name     = "Loki healthcheck"
          port     = "port_loki_http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"

          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-legacy.rule=Host(`loki-legacy.obs.nsw.education`)",
          "traefik.http.routers.loki-legacy.tls=false",
          "traefik.http.routers.loki-legacy.entrypoints=https,loki-legacy",
        ]

      }
    }
  }
}
