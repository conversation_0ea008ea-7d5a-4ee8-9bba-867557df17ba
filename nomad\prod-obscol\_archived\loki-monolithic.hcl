# Loki-rw in PROD ObsCol cluster

variables {
  image_loki = "quay.education.nsw.gov.au/observability/loki:3.4.2"
  #jaeger_endpoint = "https://localhost/api/traces"
  loki_url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
  env = "prod"
}

job "loki-monolithic" {
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"

    # 2024-09-13 jedd - relocate to AWS
    # value = "pl0992obscol0[123]"
    value = "pl0475obscol.*"
  }

  group "loki-monolithic-group" {
    count = 1

    update { # cant do canary with 1 instance
    #   max_parallel     = 1
    #   min_healthy_time = "10s"
    #   healthy_deadline = "2m"
    #   canary = 1
    #   auto_promote = true
    #   auto_revert = true
    }

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    volume "vol_loki" {
      type      = "host"
      source    = "vol_loki"
      read_only = false
    }

    network {
      port "http" {
       # to = 3100
      }
      port "grpc" {
        #to = 9096
      }
    }

    task "loki-monolithic" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki

        logging {
          type = "loki"

          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=loki-monolithic"
          }
        }

        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=all",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]
      }

      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }

      template {
        data        = file("assets/loki-monolithic.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      service {
        name = "loki-monolithic"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-monolithic.entrypoints=http,https",
          "traefik.http.routers.loki-monolithic.rule=Host(`loki.obs.nsw.education`)",
        ]

        check {
          name     = "Loki monolithic"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu    = 4000
        memory = 16000
      }
    }
  }
}