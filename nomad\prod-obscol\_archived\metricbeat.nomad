job "metricbeat" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  group "metricbeat" {

    network {
      port "prometheus_remote_write" {
        static = 9201
      }
    }

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.1"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                }
            }         
        dns_servers = ["192.168.31.1"]
        ports = ["prometheus_remote_write"]
        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
      }

      service {
        name = "prometheus-remote-write"
        port = "prometheus_remote_write"

        check {
          type = "tcp"
          port = "prometheus_remote_write"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        ELASTICSEARCH_USERNAME = "metricbeat"
        ELASTICSEARCH_PASSWORD = "qtQBFDLSiDXPYoARrvxq"
        ELASTICSEARCH_HOSTS = "https://pl0991obses04.nsw.education:9200,https://pl0991obses05.nsw.education:9200,https://pl0991obses06.nsw.education:9200"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '${ELASTICSEARCH_HOSTS}'
  username: '${ELASTICSEARCH_USERNAME}'
  password: '${ELASTICSEARCH_PASSWORD}'
  ssl:
    verification_mode: "none"

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"
      }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}
