##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

# Mimir Monolithic Deployment

variables {
  image_mimir = "quay.education.nsw.gov.au/observability/mimir:prod-obscol"
}

job "mimir" {
  datacenters = ["dc-cir-un-prod"]


    group "mimir" {
        count = 1

        # 2023-08-23 james - update constraint but keep on govdc nodes
        constraint {
            attribute = "${attr.unique.hostname}"
            operator = "regexp"
            value = "pl0992obscol0[123]"
            }   

        network {
            port "grpc" {
            }
            port "http" {
            }
        }
     
    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }    

    task "mimir" {
      driver = "docker"
        service {
            name = "mimir"
            port = "http"
            address_mode = "host"
            tags = [
                "traefik.enable=true",
                "traefik.http.routers.mimir.rule=Host(`mimir.obs.nsw.education`)",
                "traefik.http.routers.mimir.tls=false",
                # 2023-08-23 james - removed mimir entrypoint
                "traefik.http.routers.mimir.entrypoints=http,https",
            ]

#            check {
#                name     = "Mimir healthcheck"
#                port     = "http"
#                type     = "http"
#                path     = "/ready"
#                interval = "6m"
#                timeout  = "5m"
#                check_restart {
#                    limit           = 3
#                    grace           = "60s"
#                    ignore_warnings = false
#                }
#            }            

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }        
        } 
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }

      resources {
        # 2023-08-23 james - reduced memory from 64gb to 20gb
        cpu = 6000
        memory = 20000
        memory_max = 64000
        }                 

      env {
        #CONSUL_HTTP_ADDR = "http://consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #2023-08-23 jammes - updated tracing endpoint to otel collector
        JAEGER_ENDPOINT = "https://otel-jaeger-thrift.obs.nsw.education/api/traces?format=jaeger.thrift"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
        }
      config {
        # 2023-09-23 james - updated to 2.8.0
        image = var.image_mimir
        #DNS_serves is required for this job to reach out to AWS S3
        dns_servers = ["192.168.31.1"]
        ports = ["http","grpc"]       
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }         
        args = [
            "-target=all",

            "-server.http-listen-port=${NOMAD_PORT_http}",
            "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
            "-server.register-instrumentation=true",

            "-log.level=warn",

            "-querier.iterators=true",
            "-query-frontend.instance-addr=${NOMAD_IP_http}",
            "-query-frontend.instance-port=${NOMAD_PORT_grpc}",
            "-querier.frontend-address=${NOMAD_IP_http}:${NOMAD_PORT_grpc}",
            "-querier.id=${node.unique.name}",

            "-compactor.ring.store=consul",
            "-compactor.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
            "-compactor.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
            "-compactor.ring.instance-addr=${NOMAD_IP_http}",
            "-compactor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-compactor.ring.instance-id=${node.unique.name}",
            

            "-distributor.ring.store=consul",
            "-distributor.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
            "-distributor.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
            "-distributor.ring.instance-addr=${NOMAD_IP_http}",
            "-distributor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-distributor.ring.instance-id=${node.unique.name}",
            "-distributor.ingestion-rate-limit=1000000",
            "-distributor.drop-label=['confluence_request_duration_on_path_bucket']",

            "-ingester.ring.store=consul",
            "-ingester.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
            "-ingester.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
            "-ingester.ring.instance-addr=${NOMAD_IP_http}",
            "-ingester.ring.instance-port=${NOMAD_PORT_grpc}",
            "-ingester.ring.instance-id=${node.unique.name}",
            "-ingester.ring.replication-factor=1",
            "-ingester.ring.min-ready-duration=60s",
            # 2023-09-18 Bumped limit to 5million
            "-ingester.max-global-series-per-user=5000000",
            "-ingester.max-global-series-per-metric=100000",

            "-store-gateway.sharding-ring.store=consul",
            "-store-gateway.sharding-ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
            "-store-gateway.sharding-ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
            "-store-gateway.sharding-ring.instance-addr=${NOMAD_IP_http}",
            "-store-gateway.sharding-ring.instance-port=${NOMAD_PORT_grpc}",
            "-store-gateway.sharding-ring.instance-id=${node.unique.name}",
            "-store-gateway.sharding-ring.replication-factor=1",

            "-blocks-storage.tsdb.dir=/mimir/tsdb/",
            "-blocks-storage.bucket-store.sync-dir=/mimir/tsdb-sync/",

            "-blocks-storage.backend=s3",
            "-blocks-storage.s3.endpoint=s3.ap-southeast-2.amazonaws.com",
            "-blocks-storage.s3.region=ap-southeast-2",
            "-blocks-storage.s3.bucket-name=nswdoe-obs-mimir-blocks-storage-shared",
            "-blocks-storage.s3.secret-access-key=f7bxzOwdYIBkniKv6dikfGXeCURdEWvbtqiyiJ9e",
            "-blocks-storage.s3.access-key-id=AKIA5XHPOKHSEALNFC5G",
            "-blocks-storage.tsdb.flush-blocks-on-shutdown=true",
            
            "-validation.max-label-names-per-series=128",
            "-validation.max-length-label-name=2048",
            "-validation.max-length-label-value=4096",
          ]
      }
    }
  }
}
