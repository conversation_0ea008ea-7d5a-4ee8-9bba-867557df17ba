
# NodePURPLE - for testing independent of production 'nodered'
#
# 2025-02-11 jedd - should be low-risk, as it has isolated /opt/sharednfs
#            and namespace, BUT it probably shares git credentials into prod 
#            nodered repository.


# 2025-03-05 ARCHIVING this - and because node<PERSON> (copy) has 16000 files
# on our sharednfs NFS mount, and _this_ in turn annoys <PERSON><PERSON>, before you
# can run this job again you MUST extract the tarball at /opt/sharednfs/nodepurple.tgz



# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN
# TO BE CLEAR - YOU MUST RECOVER /opt/sharednfs/nodepurple FROM THE ARCHIVE BEFORE THIS WILL RUN




variables {
  image_nodepurple = "quay.education.nsw.gov.au/observability/nodered:v4.0.8"
  image_python = "quay.education.nsw.gov.au/observability/python:3.9-bullseye"
}


# JOB - nodepurple  = = = = = = = = = = = = = = = = = = = = = = = = =
job "nodepurple" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "nodepurple" {
    count = 1

    network {
      port "port_nodepurple" {
        to = 1880
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "nodepurple" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
        "NODE_RED_ENABLE_PROJECTS" = "true"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,172.0.0.0/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = "${var.image_nodepurple}"

        ports = ["port_nodepurple"]

        volumes = [ 
          "/opt/sharednfs/nodepurple:/data"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=Node-Red"
            }
          }
        }

      resources {
        cpu = 500
        memory = 400
        memory_max = 800
      }

      service {
        name = "nodepurple"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.nodepurple.rule=Host(`nodepurple.obs.nsw.education`)",
          "traefik.http.routers.nodepurple.tls=false",
          "traefik.http.routers.nodepurple.entrypoints=https",
        ]
        port = "port_nodepurple"
        check {
          name     = "http"
          type     = "http"
          path     = "/admin"
          interval = "10s"
          timeout  = "2s"
        }
      }

    }  // end-task 'nodepurple'


    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK nodepurple-updater
    task "nodepurple-updater" {
      driver = "docker"

      env {
        # Needed to talk out to bitbucket.org
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      }

      config {
        # image = "python:3.9-bullseye"
        image = "${var.image_python}"

        command = "/data/looper.sh"

        network_mode = "host"

        ports = [ ]

        volumes = [
          "/opt/sharednfs/nodepurple:/data"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu = 30
        memory = 60
        # git can sometimes be hungry for memory
        memory_max = 200
      }

      service {
        name = "nodepurple"
        meta {
          cir_app_id = "obs"
          env = "prod"
        }
      }
    }  // end-task  nodepurple-updater

  }  // end-group 'nodepurple'

}  // end-job 'nodepurple'

