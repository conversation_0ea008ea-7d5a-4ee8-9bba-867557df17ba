// Prometheus for Kafka

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}

job "prometheus-app-kafka" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus-app-kafka" {

    network {
      port "http" {}
    }

    volume "prometheus_app_kafka"  {
      type = "host"
      source = "prometheus_app_kafka"
      read_only = false
    }

    task "prometheus-app-kafka" {
      driver = "docker"

      volume_mount {
        volume = "prometheus_app_kafka"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["http"]
        image = var.image_prometheus
        #dns_servers = ["************"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",

          # "local/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus-app-kafka.obs.nsw.education",
          "--web.page-title=Prometheus for Kafka on DoE ObsCol PROD cluster",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          #"--storage.tsdb.path=/prometheus",
          #"--storage.tsdb.retention.time=1h",
          #"--storage.tsdb.retention.size=256MB",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          #"--storage.tsdb.min-block-duration=1h",
          "--web.enable-lifecycle"
        ]

      }

      service {
        name = "prometheus-app-kafka"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-kafka.rule=Host(`prometheus-app-kafka.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-kafka.tls=false",
          "traefik.http.routers.prometheus-app-kafka.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-kafka

scrape_configs:
  - job_name: 'prometheus-app-kafka'
    static_configs:
      - targets: ['prometheus-app-kafka.obs.nsw.education']

  - job_name: 'kafka'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['openmetrics_kafka']
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: app_id          
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path



remote_write:
- name: mimir
  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 2000
        memory = 10000
        memory_max = 64000
      }

    }
  }
}
