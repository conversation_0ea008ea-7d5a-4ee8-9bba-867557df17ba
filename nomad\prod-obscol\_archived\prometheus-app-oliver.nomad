// obs-col PROD - prometheus-app-oliver

// PROPOSED application/platform-specific Prometheus instance, the first of its name,
// to look at sharding the collection of metrics down into different Prometheus instances,
// in part to isolate changes to one app's collection configuration from others, also to
// reduce the impact of The One Big Prometheus crashing, also to spread the load more sanely
// amongst the Nomad nodes.  
// 
// At the time (2022-10) we are having prometheus OOM, and it's sitting on 30GB allocation 
// (ObsCol prod nodes are 32GB), with the WAL hitting 1GB (on disk).   Long term storage 
// instance - resides on obscol02 to allow concurrent 'prometheus' to run.  


// @TODO remove this dependency on git repo:   prometheus-configuration
//       This is checked out (and cron-updated) at /opt/sharednfs/prometheus-configuration
//       It contains the blackbox_webcheck_oliver.yml file
//       This needs a better mechanism - perhaps a dedicated repository, as per the DFS apps.
//
// @TODO couple this job into a COLLECTOR - combine this prometheus with a dedicated BLACKBOX
//       rather than using the generic / shared 'blackbox.nomad' job.  This oliver job is one
//       of the few (2023-08) that still uses that shared blackbox - it's an uncomfortable
//       and non-obvious dependency.
variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}

job "prometheus-app-oliver" {
  type = "service"

  datacenters = ["dc-cir-un-prod"]

  group "prometheus-app-oliver" {
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  
    network {
      port "port_prometheus_app_oliver" { }
    }

    volume "vol_prometheus-app-oliver"  {
      type = "host"
      source = "prometheus_app_oliver"
      read_only = false
    }


    task "prometheus-app-oliver" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus-app-oliver"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["port_prometheus_app_oliver"]
        image = var.image_prometheus
        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",

          # "local/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d",

          "/opt/sharednfs/prometheus-configuration/prod/blackbox:/etc/prometheus/blackbox"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_oliver}",
          "--web.external-url=https://prometheus-app-oliver.obs.nsw.education",
          "--web.page-title=Prometheus for App Oliver on DoE ObsCol PROD cluster",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=8d",
          # "--storage.local.series-file-shrink-ratio=0.3",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-oliver"
        port = "port_prometheus_app_oliver"

        check {
          type = "http"
          port = "port_prometheus_app_oliver"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-oliver.rule=Host(`prometheus-app-oliver.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-oliver.tls=false",
          "traefik.http.routers.prometheus-app-oliver.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env        = "prod"
          cluster    = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  # These chance the extant queries - we can work around this later, if we want to enable this again.
  #external_labels:
  #  nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
  #  nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
  # 2022-11-14 jedd - stop-gap measure, to replicate the old job & task names, until I work out the 
  #            without () syntax to suppress EITHER this or absence of this in the grafana panels.
  external_labels:
    nomad_job_name: prometheus
    nomad_task_name: prometheus
    provenance: obscol-prometheus-app-oliver

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-oliver'
    static_configs:
      - targets: ['prometheus-app-oliver.obs.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_oliver" }}']

  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
  - job_name: 'webcheck_oliver'
    metrics_path: /probe
    scrape_interval: 2m
    # No need for proxy HERE as we're hitting blackbox...obs.nsw.education (internal)
    # proxy_url: "http://proxy.det.nsw.edu.au:80"
    params:
      module: ["webcheck_oliver"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 1h
      files: 
      # - "/local/prometheus-configuration/prod/blackbox/blackbox_webcheck_oliver.yml"
      - "/etc/prometheus/blackbox/blackbox_webcheck_oliver.yml"
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: blackbox.service.dc-cir-un-prod.collectors.obs.nsw.education:9115


# 2023-06-05 jedd - commenting these out, as we don't need to do any alerting within this prometheus
# rule_files:
#   - /etc/prometheus/rules.d/*.rules
#   - /etc/prometheus/rules.d/*.yaml
#   - /etc/prometheus/rules.d/*.yml


remote_write:
  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 512
        memory = 2048
      }

    }
  }
}
