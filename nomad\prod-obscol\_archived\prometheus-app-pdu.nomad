
// prometheus-app-pdu on ObsCol PROD

// PROPOSED application/platform-specific Prometheus instance.
// Intent is to shard the collection of metrics down into different Prometheus instances,
// in part to isolate changes to one app's collection configuration from others, also to
// reduce the impact of The One Big Prometheus crashing, also to spread the load more sanely
// amongst the Nomad nodes.  

// The long term goal is to replicate the Zabbix 'zagregator' script aka api-aggregator-pdu job on
// classic collectors(tcdnd) - as documented at:  https://confluence.education.nsw.gov.au/display/PM/Monitoring+PDU+(Power+Distribution+Units)+with+Zabbix,+custom+script
// with the Python script living in obs-collector/docker/api-aggregator-pdu/bin/ -- this relies on
// some regex wrangling to aggregate based on feed pairing, room, site, etc.  It's not rocket surgery,
// but the logic is tangled up in the limitations of Zabbix, with a python port of a perl script as
// a neat little bow on that steaming pile of joy.

// http://www.circitor.fr/Mibs/Html/E/ENLOGIC-PDU-MIB.php

job "prometheus-app-pdu" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus-app-pdu" {

    network {
      port "port_prometheus_app_pdu" { }
    }

    volume "vol_prometheus_app_pdu"  {
      type = "host"
      source = "prometheus_app_pdu"
      read_only = false
    }

    task "prometheus-app-pdu" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus_app_pdu"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["port_prometheus_app_pdu"]

        # 2022-11-30 jedd - update to 2.40.3
        # image = "https://docker.io/prom/prometheus:v2.39.1"
        image = "https://docker.io/prom/prometheus:v2.40.3"

        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d",
          "/opt/sharednfs/prometheus-configuration/prod/snmp/snmp_pdu.yml:/etc/prometheus/snmp_pdu.yml"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_pdu}",
          "--web.external-url=https://prometheus-app-pdu.obs.nsw.education",
          "--web.page-title=Prometheus for PDU SNMP collection on DoE ObsCol PROD cluster",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--storage.tsdb.path=/prometheus",

          "--storage.tsdb.retention.time=1d",

          # "--storage.local.series-file-shrink-ratio=0.3",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-pdu"
        port = "port_prometheus_app_pdu"

        check {
          type = "http"
          port = "port_prometheus_app_pdu"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-pdu.rule=Host(`prometheus-app-pdu.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-pdu.tls=false",
          "traefik.http.routers.prometheus-app-pdu.entrypoints=http,https,prometheus-app-pdu",
        ]

        meta {
          cir_app_id = "obs"
          env        = "prod"
          cluster    = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-pdu

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-pdu'
    static_configs:
      - targets: ['prometheus-app-pdu.obs.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_pdu" }}']


  # Using SNMP exporter to do a custom web-check for GovDC PDUs (power distribution units)
  # with custom URLs and regex string match on target page.

  - job_name: 'snmp_pdu'
    metrics_path: /snmp
    scrape_interval: 2m

    # Note there are the following top-level stanzas in our snmp-enlogic-pdu.yml:
    # apcups:
    # arista_sw:
    # cisco_wlc:
    # ddwrt:
    # eaton_epdu:
    # enlogic_pdu:
    # if_mib:
    # infrapower_pdu:
    # keepalived:
    # kemp_loadmaster:
    # liebert_pdu:
    # mikrotik:
    # nec_ix:
    # paloalto_fw:
    # printer_mib:
    # raritan:
    # servertech_sentry3:
    # servertech_sentry4:
    # synology:
    # ubiquiti_airfiber:
    # ubiquiti_airmax:
    # ubiquiti_unifi:
    # wiener_mpod:

    params:
      module: ["enlogic_pdu"]


    file_sd_configs:

    # As of 2023-03-21 we are now reading prometheus-configuration in hourly to a local
    # file system mount, rather than at job start.  So the file_sd_config is relevant.
    - refresh_interval: 1h
      files: 
      - "/local/prometheus-configuration/prod/snmp/snmp_pdu.yml"


    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      # replacement: exporter-snmp-pdu.service.dc-cir-un-prod.collectors.obs.nsw.education:9117
      # replacement: exporter-snmp-pdu.service.dc-cir-un-prod.collectors.obs.nsw.education
      replacement: exporter-snmp-pdu.obs.nsw.education

#rule_files:
#  - /etc/prometheus/rules.d/*.rules
#  - /etc/prometheus/rules.d/*.yaml
#  - /etc/prometheus/rules.d/*.yml

remote_write:

- name: mimir
  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true

- name: mimir-ocp
  url: "https://mimir-ocp.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod-ocp
  tls_config:
    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 512
        memory = 1000
      }

    }
  }
}
