
# prometheus for postgresql  (obscol PROD)

# WIP 2023-08

# Architecture - single prometheus job scrapes an exporter-per-database,
# which somewhat addresses the problem of the official prometheus
# postgresql-exporter (it has some bugs) and allows for migration of
# some of the ~20 or so postgresql databases over to opentelemetry
# collector instead.

job "prometheus-app-postgresql" {
  type = "service"

  datacenters = ["dc-cir-un-prod"]

  group "prometheus-app-postgresql" {

    network {
      port "port_prometheus_app_postgresql" { }
    }

    task "prometheus-app-postgresql" {
      driver = "docker"

      config {
        ports = ["port_prometheus_app_postgresql"]

        # 2022-11-30 jedd - update to 2.40.3
        # image = "https://docker.io/prom/prometheus:v2.39.1"
        image = "https://docker.io/prom/prometheus:v2.40.3"

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_postgresql}",
          "--web.external-url=https://prometheus-app-postgresql.obs.nsw.education",
          "--web.page-title=Prometheus for PostgreSQL Exporter (PROD Cluster)",

          "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml",

          #"--storage.tsdb.path=/prometheus",
          #"--storage.tsdb.retention.time=8d",
          # "--storage.local.series-file-shrink-ratio=0.3",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-postgresql"
        port = "port_prometheus_app_postgresql"

        check {
          type = "http"
          port = "port_prometheus_app_postgresql"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-postgresql.rule=Host(`prometheus-app-postgresql.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-postgresql.tls=false",
          "traefik.http.routers.prometheus-app-postgresql.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env        = "prod"
          cluster    = "obscol"
        }
      }


      template {
        data = <<EOH
global:

  external_labels:
    provenance: prometheus-app-postgresql

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-postgresql'
    scheme: https
    static_configs:
      # - targets: ['prometheus-app-postgresql.obs.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_postgresql" }}']
      - targets: ['prometheus-app-postgresql.obs.nsw.education']


  - job_name: 'postgresql_metrics'
    metrics_path: "/probe"
    scheme: https

    static_configs:
      - targets: [
          'pu0992ttdbs001.hbm.det.nsw.edu.au',
          'pu0991tedbs001.hbm.det.nsw.edu.au',
          'pu0992ttdbd001.hbm.det.nsw.edu.au',
          'pu0991tedbd001.hbm.det.nsw.edu.au'
        ]

    # Need to turn into: 'exporter-postgresql.obs.nsw.education/probe?target=dg-pan-01&auth_module=dg-pan-01'

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: target

      - source_labels: [__address__]
        target_label: __param_auth_module
      - source_labels: [__param_auth_module]
        target_label: auth_module

      - target_label: __address__
        replacement: exporter-postgresql.obs.nsw.education

    # Out of the box we get a metric like:
    #   pg_database_size_bytes{auth_module="pu0991tedbd001.hbm.det.nsw.edu.au", datname="postgres", instance="exporter-postgresql.obs.nsw.education:443", job="postgresql_metrics", target="pu0991tedbd001.hbm.det.nsw.edu.au"}
    #
    # So we want to:
    #    drop the auth_module label
    #    change datname= to database=
    #    change target= to host=
    #    drop instance=
    #    rename all the pg_ prefixes on all metrics to postgresql_  (we should be able to do that with the exporter, but it is broken)
    #
    # Then we end up with this instead:
    #    postgresql_database_size_bytes{database="postgres", host="pu0991tedbd001.hbm.det.nsw.edu.au", job="postgresql_metrics"}

    metric_relabel_configs:

      # Ditch the 'auth_module=' label
      - action: labeldrop
        regex: auth_module

      # Ditch the 'instance=' label
      - action: labeldrop
        regex: instance

      # Change the 'datname=' to 'database='
      - action: labelmap
        regex: "datname"
        replacement: "database"
        # this seems to duplicate datname to database so we drop datname
      - action: labeldrop
        regex: datname

      # Change the 'target=' to 'host='
      - action: labelmap
        regex: "target"
        replacement: "host"
        # this seems to duplicate the original, so drop it
      - action: labeldrop
        regex: target

      # Change pg_ to postgresql_ on all exporter metrics
      - source_labels: [__name__]
        regex: "pg_(.+)"
        target_label: "__name__"
        replacement: "postgresql_$1"

      # We probably don't care about 'template0' and 'template1' databases at all.
      - action: drop
        source_labels: [database]
        regex: "template[01]"

      # We probably don't care about the 'srvobs' db either - it's just to CONNECT to.
      - action: drop
        source_labels: [database]
        regex: "srvobs"



remote_write:

  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 500
        memory = 2000
      }

    }
  }
}
