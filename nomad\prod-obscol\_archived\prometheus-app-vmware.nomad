
// prometheus-app-vmware on ObsCol PROD was prometheus-app-vcenter

// PROPOSED application/platform-specific Prometheus instance.
// Intent is to shard the collection of metrics down into different Prometheus instances,
// in part to isolate changes to one app's collection configuration from others, also to
// reduce the impact of The One Big Prometheus crashing, also to spread the load more sanely
// amongst the Nomad nodes.  

// Initially configured to scrape the VMWare vcenter web frontend services for their availability and certificate expiry
// Long term this prometheus instance will scrape the VMWware ESX host and resource information once account provisioning has occured
// vmware vcenter exporter has been configured with read credentials to scrape silverwater and unanderra vmware infrastructure


job "prometheus-app-vmware" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus-app-vmware" {

    network {
      port "port_prometheus_app_vmware" { }
    }

    task "prometheus-app-vmware" {
      driver = "docker"
      config {
        ports = ["port_prometheus_app_vmware"]
        image = "https://docker.io/prom/prometheus:v2.40.3"

        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_vmware}",
          "--web.page-title=Prometheus for vcenter collection on DoE ObsCol PROD cluster",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          #"--storage.tsdb.retention.time=1h",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-vmware"
        port = "port_prometheus_app_vmware"

        check {
          type = "http"
          port = "port_prometheus_app_vmware"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        meta {
          cir_app_id = "obs"
          env        = "prod"
          cluster    = "obscol"
        }
      }

      artifact {
        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
        destination = "local/prometheus-configuration"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-vmware


scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-vmware'
    static_configs:
      - targets: ['prometheus-app-vmware.obs.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_vcenter" }}']

  - job_name: vcenter_frontend
    metrics_path: /probe
    scrape_timeout: 45s
    params:
      module: ["vcenter"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['vcenter']    
    relabel_configs:
      - source_labels: [__meta_consul_address]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-app-vcenter.obs.nsw.education # Blackbox exporter

  - job_name: 'vmware_vcenter'
    scrape_interval: 2m
    scrape_timeout: 45s # has to be high for the exporter to collect all the metrics from vcenter
    metrics_path: '/metrics'
    params:
      module: ["vcenter"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['vcenter'] 
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-app-vsphere.obs.nsw.education # vmware exporter

remote_write:
- name: mimir
  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true

- name: grafanacloud
  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
  proxy_url: http://proxy.det.nsw.edu.au:80
  basic_auth:
    username: 768052
    password: ********************************************************************************************************************************************************      
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 512
        memory = 1000
      }

    }
  }
}