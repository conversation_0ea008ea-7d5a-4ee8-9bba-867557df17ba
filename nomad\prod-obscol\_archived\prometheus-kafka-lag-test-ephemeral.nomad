
// Prometheus for Kafka lag out of TEST using a double-dodgy path

// This task should be short-lived - created 2023-05-09 (see notes below).
// It this is still running in, say, 2023-08 then we have lost (and should revisit, formalise, despair).




      # 2023-05-08 jedd - this is UGLY and should be short-lived
      # it's a response to not being able to access kafka lag metrics from OCP dev & test environments,
      # as they can't access this env (test obscol) on 80 / 443, and our prod obscol environment can't
      # access kafka lag metrics in dev & test (high port - 9092)
      # this is part of a bodgy work-around to get metrics exposed to prod-obscol for remote-read from
      # there, and then expose in that env.

      # Refer changes to test-obscol job collector-kafka-lag-test




job "prometheus-kafka-lag-test-ephemeral" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus-kafka-lag-test-ephemeral" {

    network {
      port "port_prometheus" {}
    }

    # We don't need persistence here, but JIC we do.
    #volume "prometheus_app_kafka_lag_test_ephemeral"  {
    #  type = "host"
    #  source = "prometheus_app_kafka"
    #  read_only = false
    #}



    task "prometheus-app-kafka-lag-test" {
      driver = "docker"

      #volume_mount {
      #  volume = "prometheus_app_kafka_lag_test_ephemeral"
      #  destination = "/prometheus"
      #  read_only = false
      #}

      config {
        ports = ["port_prometheus"]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            # loki-external-labels = "job=${NOMAD_JOB_ID},env=${var.kafka.clustername},task=${NOMAD_TASK_NAME}"
            loki-external-labels = "job=${NOMAD_JOB_ID},env=test,task=${NOMAD_TASK_NAME}"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.external-url=https://prometheus-app-kafka-lag-test-ephemeral.obs.nsw.education",
          "--web.page-title=Prometheus for Kafka (test) lag on DoE ObsCol PROD cluster",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=72h",
          "--storage.tsdb.retention.size=1024MB",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          "--storage.tsdb.min-block-duration=1h",
        ]

      }

      service {
        name = "prometheus-app-kafka-lag-test-ephemeral"
        port = "port_prometheus"

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-kafka-lag-test-ephemeral.rule=Host(`prometheus-app-kafka-lag-test-ephemeral.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-kafka-lag-test-ephemeral.tls=false",
          "traefik.http.routers.prometheus-app-kafka-lag-test-ephemeral.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:


# NOTE - external_labels should be empty here, otherwise queries will fail to stream through to remote_read end


scrape_configs:

  # Self
  - job_name: 'prometheus-app-kafka-lag-test-ephemeral'
    static_configs:
      - targets: ['prometheus-app-kafka-lag-test-ephemeral.obs.nsw.education']


# The magic sauce - pulling data out of the enforced static highport and obscol02-constrained job in test-obscol
# NOTE - dev is locked to obscol01, test is locked to obscol02 - so we can reuse the same port number
remote_read:
  - url: http://tl0992obscol02.nsw.education:11060/api/v1/read
    read_recent: true


# We don't remote write, but we can
#remote_write:
#- name: mimir
#  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#  headers: 
#    X-Scope-OrgID: prod
#  tls_config:
#    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 100
        memory = 2000
        memory_max = 3000
      }

    }
  }
}
