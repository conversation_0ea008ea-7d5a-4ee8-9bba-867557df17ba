// obscol prod - load data generator for Tempo

// Note - we need /tmp/load-generator.json as jedd didn't bother to work out how
//        to do relative paths for config volume mounts - so it's in the ./shared/ 
//        dir here but needs to be copied to /tmp.  This also explains the constraint.

job "tempo-load-data-generator" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "tempo" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    task "tempo-load-generator" {
      driver = "docker"
      env = {
        "TOPOLOGY_FILE" = "/etc/load-generator.json",
        "JAEGER_COLLECTOR_URL" = "http://tempo.obs.nsw.education:14268"
      }
      config {
        image = "omnition/synthetic-load-generator:1.0.25"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                }
            }         
        volumes = [
          "local/load.json:/etc/load-generator.json"
        ]
      }

      template {
        data = <<EOF
{
  "topology" : {
    "services" : [
      {
        "serviceName" : "poke-mart",
        "instances" : [ "viridian-d847fdcf5-j6s2f", "pallet-79d8c8d6c8-9sbff" ],
        "tagSets" : [
          { "weight": 2, "tags": { "generation" : "v1", "region" : "kanto" }, "tagGenerators": [{"numTags": 32, "numVals": 152, "valLength": 64}] },
          { "tags": { "generation" : "v2", "region" : "johto" }}
        ],
        "routes" : [
          {
            "route" : "/product",
            "downstreamCalls" : { "pokemon-center" : "/Recover", "brock" : "/GetRecommendations" },
            "maxLatencyMillis": 200,
            "tagSets": [
              { "weight": 1, "tags": { "starter" : "charmander"}},
              { "weight": 1, "tags": { "starter" : "squirtle"}},
              { "weight": 1, "tags": { "starter" : "bulbasaur"}}
            ]
          }
        ]
      },
      {
        "serviceName" : "brock",
        "instances" : [ "pewter-a347fe1ce-g4sl1"],
        "tagSets" : [
          { "tags": { "uselss": true }, "inherit": ["region", "starter"]}
        ],
        "routes" : [
          {
            "route" : "/GetRecommendations",
            "downstreamCalls" : { },
            "maxLatencyMillis": 1000,
            "tagSets": [
              { "tags": { "loves" : "jenny"}},
              { "tags": { "loves" : "joy"}}
            ]
          }
        ]
      },
      {
        "serviceName" : "pokemon-center",
        "instances" : [ "cerulean-23kn9aajk-lk12d"],
        "tagSets" : [
          { "tags": { "generation" : "v1", "region" : "kanto"}, "inherit": ["starter"] }
        ],
        "routes" : [
          {
            "route" : "/Recover",
            "downstreamCalls" : { },
            "maxLatencyMillis": 300
          }
        ]
      }
    ]
  },
  "rootRoutes" : [
    {
      "service" : "poke-mart",
      "route" : "/product",
      "tracesPerHour" : 18000
    }
  ]
}
EOF

        destination = "local/load-generator.json"
      }
      resources {
        cpu    = 512
        memory = 512
      }
    }

  }
}

