
// alloy-remote-config-server  (by opsplane)
// alloy-rcs (golang)

// Self-hosted endpoint to provide <PERSON><PERSON> configs over gRPC

// Refer:  https://github.com/opsplane-services/alloy-remote-config-server
//
// Refer:  git repository 'obs-allow-remote-config-server' for modifications to
//         upstream, and the hand-crafted docker container loaded into quay.

// Custom images - to
//   a) update to localAttributes (was attributes)
//   b) change bind for gRPC and HTTP from 127.0.0.1 to 0.0.0.0
//
// Build process - noting we need golang 1.21 or better for 'toolchain':
//     cd ~/src
//     <NAME_EMAIL>:opsplane-services/alloy-remote-config-server.git
//     cd alloy-remote-config-server
//     docker build -t alloy-remote-config-server .
//     skopeo  copy                                                \
//             docker-daemon:alloy-remote-config-server:latest     \
//             docker://quay.education.nsw.gov.au/observability/alloy-remote-config-server:v0.1.1
//     



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

locals {
  # 0.1.3 - experimental 'fall through' to default if hostname template not available.
  image_alloy_rcs = "quay.education.nsw.gov.au/observability/alloy-remote-config-server:v0.1.3"

  loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "alloy_rcs" {
  type = "service"

  datacenters = ["dc-cir-un-prod"]

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "alloy_rcs" {

    network {
      port "port_alloy_rcs" {
        to = 8888
        # static = 8888
      }
      port "port_alloy_rcs_http" {
        to = 8080
        # static = 8080
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "alloy_rcs" {
      driver = "docker"

      env = {
        # It's just easier if we have local timezone inside the container.
        "TZ" = "Australia/Sydney"

        # Format of config folder still not clear - but is mapped to /opt/sharednfs/alloy_rcs
        "CONFIG_FOLDER" = "/configs",
      }

      config {
        image = local.image_alloy_rcs

        ports = ["port_alloy_rcs", "port_alloy_rcs_http"]

        args = [ ]

        # privileged = "true"

        volumes = [
          "local/bashrc:/root/.bashrc",
          "/opt/sharednfs/alloy_rcs/:/configs"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }        

      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]alloy-remote-config-server:\w# \[\e[0m\]"
export TERM=linux
EOH
       destination = "local/bashrc"
      }

      resources {
        cpu = 50
        memory = 100
      }

      service {
        name = "alloyrcs"
        port = "port_alloy_rcs"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alloyrcs.rule=Host(`alloyrcs.obs.nsw.education`)",
          "traefik.http.routers.alloyrcs.tls=false",
          "traefik.http.routers.alloyrcs.entrypoints=https,http",
        ]
      }

      service {
        name = "alloyrcshttp"
        port = "port_alloy_rcs_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alloyrcshttp.rule=Host(`alloyrcshttp.obs.nsw.education`)",
          "traefik.http.routers.alloyrcshttp.tls=false",
          "traefik.http.routers.alloyrcshttp.entrypoints=https,http",
        ]
      }

    }

  }
}

