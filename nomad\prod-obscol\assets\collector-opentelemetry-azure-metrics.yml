receivers:
  azuremonitor/0: #nswdoe-bi-preprod
    subscription_id: 002b9201-409e-48f4-898f-c6d095630cd7
    tenant_id: "05a0e69a-418a-47c1-9c25-9387261bf991"
    client_id: "5af8139d-e906-44f5-9849-2d61c4b7ad83"
    client_secret: "****************************************"
    resource_groups:
    services:
    collection_interval: 60s
    initial_delay: 1s
  azuremonitor/1: #nswdow-bi-preprod-dev-test
    subscription_id: 4f5f7c9d-f107-43f3-adfc-9bf00f09b7ef
    tenant_id: "05a0e69a-418a-47c1-9c25-9387261bf991"
    client_id: "5af8139d-e906-44f5-9849-2d61c4b7ad83"
    client_secret: "****************************************"
    resource_groups:
    services:
    collection_interval: 60s
    initial_delay: 1s

  azuremonitor/2: #nswdow-bi-services
    subscription_id: b9255254-3984-4b85-9801-26e62d0c22dc
    tenant_id: "05a0e69a-418a-47c1-9c25-9387261bf991"
    client_id: "5af8139d-e906-44f5-9849-2d61c4b7ad83"
    client_secret: "****************************************"
    resource_groups:
    services:
    collection_interval: 60s
    initial_delay: 1s

  # Collect own metrics
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-azure-metrics'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-azure-metrics.obs.nsw.education']
        
processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=
  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"
  pprof:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_pprof" }}"
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: debug

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/otlp"
    headers:
      X-Scope-ORGID: prod
# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    metrics:
      receivers: [azuremonitor/0,azuremonitor/1,azuremonitor/2,prometheus]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud]
  telemetry:
    logs:
      level: debug
      initial_fields:
        service: otel-azure-metrics
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed