
# OpenTelemetry Collector configuration for: 
#    'Collector-otlp-jedd-otlp-in-clickhouse-out'
#
# 2025-06 experimental - receiving Alloy logs from obscol04 only.
#
# Extended version - includes additional attributes which probably
# aren't needed, as we're picking up hostname and provenance/service.name
# in the clickhouse column 'LogAttributes' already.

extensions:
  health_check:
    # Explicitly set the health check endpoint
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_healthcheck" }}"

receivers:
  otlp:
    protocols:
      http:
        endpoint: 0.0.0.0:8088

exporters:
  # Send logs to Loki - if we want them there
  #otlphttp/logs:
  #  # Loki has OTLP native ingest, so OpenTel's abandoning a Loki exporter.
  #  endpoint: https://loki.obs.nsw.education/otlp


  # Send logs to ClickHouse
  clickhouse:
    endpoint: https://clickhouse-jedd.obs.nsw.education:443
    database: chdb
    username: jedd
    password: bigsecret
    logs_table_name: otel_logs
    traces_table_name: otel_traces
    metrics_table_name: otel_metrics
    timeout: 30s
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s

processors:
  # Resource processor - adds collector-level metadata
  resource:
    attributes:
      - action: insert
        key: collector.hostname
        value: "${env:HOSTNAME}"
      - action: insert
        key: collector.version
        value: "0.113.0"  # Update to your collector version
      - action: insert
        key: datacenter
        value: "nsw-education"  # Your datacenter/environment
      - action: insert
        key: cluster
        value: "nomad-cluster"  # Your cluster name

  # Resource detection processor - automatically detects system info
  resourcedetection:
    detectors: [env, system, host]
    timeout: 2s
    override: false
    attributes:
      - host.name
      - host.id
      - os.type
      - process.pid

  # Transform processor - for more complex attribute manipulation
  transform:
    log_statements:
      - context: log
        statements:
          # Preserve original hostname from Alloy if it exists
          - set(attributes["original.hostname"], resource.attributes["host.name"]) where resource.attributes["host.name"] != nil
          # Add timestamp-based fields
          - set(attributes["ingestion.timestamp"], Now())
          # Extract and enhance service information
          - set(attributes["service.environment"], "production") where attributes["service.environment"] == nil

  # Attributes processor - for log-level attribute manipulation
  attributes:
    actions:
      # Your existing attributes
      - action: insert
        key: provenance
        value: "otel-logs"
      - action: insert
        key: service.name
        value: "otel-logs"
      
      # Additional useful attributes
      - action: insert
        key: log.source
        value: "grafana-alloy"
      - action: insert
        key: pipeline.version
        value: "v1.0"
      
      # Preserve important resource attributes as log attributes
      - action: insert
        key: source.hostname
        from_attribute: host.name
        from_context: resource
      - action: insert
        key: source.os
        from_attribute: os.type
        from_context: resource

  # Optional: Add batch processor for better ClickHouse performance
  batch:
    timeout: 10s
    send_batch_size: 1024

service:
  extensions:
    - health_check

  pipelines:
    logs:
      receivers: [otlp]
      processors: [resourcedetection, resource, transform, attributes, batch]
      # exporters: [otlphttp/logs, otlphttp/logsrwb, clickhouse]
      exporters: [clickhouse]

  telemetry:
    logs:
      level: debug
    metrics:
      # service:telemetry:metrics:address -- replaced with :readers: from v0.123
      # address: "0.0.0.0:{{ env "NOMAD_PORT_port_metrics" }}"
      readers:
        - pull:
            exporter:
              prometheus:
                host: "0.0.0.0"
                port: {{ env "NOMAD_PORT_port_metrics" }}
