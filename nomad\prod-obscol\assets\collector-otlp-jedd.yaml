
# OpenTelemetry Collector configuration for: 
#    'Collector-otlp-jedd-otlp-in-clickhouse-out'
#
# 2025-06 experimental - receiving Alloy logs from obscol04 only.


# =================================================
extensions:
  health_check:
    # Explicitly set the health check endpoint
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_healthcheck" }}"


# =================================================
# Single ingest point - offering OTLP / HTTP only, for Alloy.
receivers:
  otlp:
    protocols:
      http:
        endpoint: 0.0.0.0:8088


# =================================================
exporters:

  # Send logs to Loki - if we want them there as well
  #otlphttp/logs:
  #  # Loki has OTLP native ingest, so OpenTel's abandoning a Loki exporter.
  #  endpoint: https://loki.obs.nsw.education/otlp


  # Send logs to ClickHouse
  clickhouse:
    endpoint: https://clickhouse-jedd.obs.nsw.education:443
    database: chdb
    username: jedd
    password: bigsecret
    logs_table_name: otel_logs
    traces_table_name: otel_traces
    metrics_table_name: otel_metrics
    timeout: 30s
    retry_on_failure:
      enabled: true
      initial_interval: 5s
      max_interval: 30s
      max_elapsed_time: 300s


# =================================================
processors:
  attributes:
    # These get added to LogAttribute field in Clickhouse
    actions:
      - action: insert
        key: provenance
        value: "otel-logs"
      - action: insert
        key: service.name
        value: "otel-logs"

  # Experimenting with transform to get these into primary attributes - but no joy.
#  transform:
#    log_statements:
#      - context: log
#        statements:
#          - set(resource.attributes["host"], attributes["host"]) where attributes["host"] != nil
#          - set(resource.attributes["job"], attributes["job"]) where attributes["job"] != nil
#          - set(resource.attributes["priority"], attributes["priority"]) where attributes["priority"] != nil
#          - set(resource.attributes["provenance"], "otel-logs")
#          - set(resource.attributes["service.name"], "otel-logs")

#          - set(attributes["host"], body["host"]) where body["host"] != nil
#          - set(attributes["job"], body["job"]) where body["job"] != nil  
#          - set(attributes["priority"], body["priority"]) where body["priority"] != nil
#          - set(attributes["provenance"], "otel-logs")
#          - set(attributes["service.name"], "otel-logs")




  # Add batch processor for better ClickHouse performance - some knobs
  # to twiddle if we ramp up ingest, and don't need quite-so-near-time.
  batch:
    timeout: 10s
    send_batch_size: 1024

# =================================================
service:
  extensions:
    - health_check

  pipelines:
    logs:
      receivers: [otlp]
      processors: [attributes, batch ]
      # exporters: [otlphttp/logs, otlphttp/logsrwb, clickhouse]
      exporters: [ clickhouse ]

  telemetry:
    logs:
      level: debug
    metrics:
      # service:telemetry:metrics:address -- replaced with :readers: from v0.123
      # address: "0.0.0.0:{{ env "NOMAD_PORT_port_metrics" }}"
      readers:
        - pull:
            exporter:
              prometheus:
                host: "0.0.0.0"
                port: {{ env "NOMAD_PORT_port_metrics" }}
              

