# Be aware that receivers used in this config are only available from the otel-contrib fork
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: "0.0.0.0:{{ env "NOMAD_PORT_otlpgrpc" }}"
      http:
        endpoint: "0.0.0.0:{{ env "NOMAD_PORT_otlphttp" }}"
  # jaeger:
  #   protocols:
  #     thrift_compact:
  #     thrift_binary:
  #     thrift_http:
  #zipkin:
  #opencensus:

  # Collect own metrics
  prometheus:
    config:
      global:
        external_labels:
          job: collector-observability
          provenance: collector-telemetry-observability
          env: prod

      scrape_configs:
      - job_name: 'otel-app-observability'
        scrape_interval: 60s
        scheme: https
        static_configs:
        - targets: ['otel-app-observability.obs.nsw.education']

      # - job_name: 'consul_metrics_obsobs'
      #   static_configs:
      #       - targets: ['pl0992obsobs01.nsw.education:8500']
      #   metrics_path: /v1/agent/metrics
      #   params:
      #       format: ['prometheus']

      # - job_name: 'nomad_metrics_obsobs'
      #   scheme: https
      #   static_configs:
      #       - targets: ['pl0992obsobs01.nsw.education:4646']
      #   metrics_path: /v1/metrics
      #   params:
      #       format: ['prometheus']
        # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

      # This section duplicates (at least partially) openmetrics_o job - can probably be removed?
      # 2024-10-03 - James removed this section
    #   - job_name: 'openmetrics_obs'
    #     consul_sd_configs:
    #           - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
    #             datacenter: 'dc-cir-un-prod'
    #             token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
    #             services: ['openmetrics_obs']

      # - job_name: 'alertmanager_metrics'
      #   consul_sd_configs:
      #       - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
      #         datacenter: 'dc-cir-un-prod'
      #         token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
      #         services: ['alertmanager']

      # - job_name: 'influxdb_metrics'
      #   consul_sd_configs:
      #       - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
      #         datacenter: 'dc-cir-un-prod'
      #         token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
      #         services: ['influxdb']

      - job_name: 'nomad_metrics'
        metrics_path: /v1/metrics
        params:
            format: ['prometheus']
        consul_sd_configs:
            - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
              datacenter: 'dc-cir-un-prod'
              token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
              services: ['nomad-client', 'nomad']
              tags: ['http']

      - job_name: 'consul_metrics'
        static_configs:
            - targets: ['pl0992obscol01.nsw.education:8500','pl0992obscol02.nsw.education:8500','pl0992obscol03.nsw.education:8500']
        metrics_path: /v1/agent/metrics
        params:
            format: ['prometheus']

      - job_name: 'loki_metrics'
        metrics_path: /metrics
        consul_sd_configs:
            - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
              datacenter: 'dc-cir-un-prod'
              token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
              services: ['loki-read','loki-write','loki-backend']          
              
      - job_name: 'mimir_metrics'
        metrics_path: /metrics
        consul_sd_configs:
            - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
              datacenter: 'dc-cir-un-prod'
              token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
              services: ['mimir-rwb-write','mimir-rwb-read',mimir-rwb-backend]


  filelog:
    include: [/var/log/*.log]

processors:
  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: prod

  attributes/provenance:
    actions:
      - key: provenance
        action: upsert
        value: collector-telemetry-observability

  attributes/loki:
    actions:
      - action: insert
        key: loki.attribute.labels
        value: env, host, app_id, container_name, job, source, task, unit, service_name

  resource:
    attributes:
        # Add service_name 
        - key: service.name # a gotcha here is that you cant use underscores in the key, it will be converted to dots, just use the dots here.
          action: upsert
          value: "{{env "NOMAD_JOB_NAME"}}"
        - key: job
          action: upsert
          value: "observability"

  batch:
    timeout: 1s
    send_batch_size: 1024
    send_batch_max_size: 4096    
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

extensions:
#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: '533612'
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"

exporters:
  debug:
    verbosity: detailed

  splunk_hec:
    # Splunk HTTP Event Collector token for internal collector
    token: "26eecb65-8dca-4c6c-a287-7998d37b7100"
    #token: "8c337d9a-efe6-4043-b65f-c54a31e2716c" linked to MTM index
    # URL to a Splunk instance to send data to.
    # Splunk cloud endpoint
    endpoint: "https://pl0992splf0001.nsw.education:8088/"
    #endpoint: "https://http-inputs-nswdoe.splunkcloud.com/services/collector/event"
    #internal collector endpoint
    #endpoint: https://splunk-ext-hec.education.nsw.gov.au/services/collector # get error forbidden 403
    # Optional Splunk source: https://docs.splunk.com/Splexicon:Source
    #source: "otel"
    # Optional Splunk source type: https://docs.splunk.com/Splexicon:Sourcetype
    #sourcetype: "otel"
    # Splunk index, optional name of the Splunk index targeted.
    index: "mtm"
    # Maximum HTTP connections to use simultaneously when sending data. Defaults to 100.
    #max_idle_conns: 20
    health_check_enabled: true
    tls:
      # Whether to skip checking the certificate of the HEC endpoint when sending data over HTTPS. Defaults to false.
      insecure_skip_verify: true

# GrafanaCloud has a simple gateway
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

# mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers
  prometheusremotewrite/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers:
      X-Scope-ORGID: prod

# tempo on-prem 
  otlphttp/onpremtempo:
    endpoint: "https://tempo-otlp.obs.nsw.education"  

# loki on-prem
  otlphttp/onpremloki:
    endpoint: "https://loki.obs.nsw.education/otlp"

service:
  extensions: [health_check,basicauth/otlp]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [attributes/env,batch]
      exporters: [otlphttp/onpremtempo]

    metrics:
      receivers: [otlp,prometheus]
      processors: [attributes/env,batch,resource]
      exporters: [prometheusremotewrite/onpremmimir]

    logs:
      receivers: [otlp]
      processors: [attributes/loki,attributes/env,attributes/provenance,batch,resource]
      exporters: [otlphttp/onpremloki]

  telemetry:
    logs:
      processors:
        - batch:
            exporter:
              otlp:
                protocol: http/protobuf
                endpoint: https://loki.obs.nsw.education/otlp/v1/logs
    metrics:
      readers:
        - pull:
            exporter:
              prometheus:
                host: '0.0.0.0'
                port: {{ env "NOMAD_PORT_metrics" }}
      level: detailed