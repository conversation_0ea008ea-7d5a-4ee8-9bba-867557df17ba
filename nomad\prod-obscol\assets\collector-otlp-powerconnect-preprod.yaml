receivers:
  splunk_hec: # This is the default configuration for the Splunk HEC receiver
    endpoint: "0.0.0.0:8088"
    access_token_passthrough: true

  prometheus: # This is the default configuration for Prometheus to scrape metrics from the collector
    config:
      scrape_configs:
      - job_name: powerconnect-preprod
        scrape_interval: 60s
        scheme: https
        metrics_path: /metrics
        static_configs:
        - targets: ['powerconnect-preprod.obs.nsw.education']

processors:
  resource:
    attributes: # This processor adds attributes to logs, we use it to add the service name and provenance to logs
        # Add service_name 
        - key: service.name # a gotcha here is that you cant use underscores in the key, it will be converted to dots, just use the dots here.
          action: upsert
          value: "powerconnect-preprod"

  logdedup:
      interval: 130s
      log_count_attribute: dedup_count
      timezone: 'Australia/Sydney'

  filter/sensitivefields: # known sensitive fields, values for these usually contain PII
    error_mode: ignore
    logs:
      log_record:
        - 'IsMatch(body, ".*ADS_CHECK.*")'
        - 'Is<PERSON>atch(body, ".*TREAD_DYN.*")'
        - 'Is<PERSON>atch(body, ".*SCU3.*")'
        - 'Is<PERSON>atch(body, ".*CDPOS.*")'
        - 'Is<PERSON>atch(body, ".*DEVACCESS.*")'
        - 'IsMatch(body, ".*ROLE_AUTH.*")'
        - 'IsMatch(body, ".*RSUSR003.*")'
        - 'IsMatch(body, ".*RSUSR200.*")'
        - 'IsMatch(body, ".*SCC4.*")'
        - 'IsMatch(body, ".*SE06.*")'
        - 'IsMatch(body, ".*SM20.*")'
        - 'IsMatch(body, ".*SM59_RFCDES.*")'
        - 'IsMatch(body, ".*SNOTE.*")'
        - 'IsMatch(body, ".*STRUST.*")'
        - 'IsMatch(body, ".*BPM_BOR_REL.*")'
        - 'IsMatch(body, ".*E070.*")'
        - 'IsMatch(body, ".*MTR_DDIC.*")'
        - 'IsMatch(body, ".*PC_METRIC_QUEUE.*")'
        - 'IsMatch(body, ".*PC_SCHEDULE.*")'
        - 'IsMatch(body, ".*PC_SENT_STATS.*")'
        - 'IsMatch(body, ".*RZ10.*")'
        - 'IsMatch(body, ".*SICFCHK.*")'
        - 'IsMatch(body, ".*SLG1.*")'
        - 'IsMatch(body, ".*SM19.*")'
        - 'IsMatch(body, ".*SM59.*")'
        - 'IsMatch(body, ".*USER_AUTH.*")'
        - 'IsMatch(body, ".*USH02.*")'

  batch:
    timeout: 1s
    send_batch_size: 1024
    send_batch_max_size: 4096   
  memory_limiter: # This is the default memory limiter configuration, it should be less than the memory limit of the Nomad job
    limit_mib: 4098
    spike_limit_mib: 3500 # This is the maximum memory that the collector can use, it can be the same as the nomad job memory limit
    check_interval: 5s 

extensions:
  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"

#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: '533612'
      password: ****************************************************************************************************************************************************

# Exporters are defined here
exporters:
  debug: # Debug is a otlp native exporter that can be used to debug the collector
    verbosity: detailed

# GrafanaMimir onpremise endpoint
  prometheusremotewrite/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers:
      X-Scope-ORGID: prod

# Loki OSS onpremise endpoint
  otlphttp/onpremloki:
    endpoint: "https://loki.obs.nsw.education/otlp"   

# GrafanaCloud has a simple gateway for OTLP
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

service:
  extensions: [health_check,basicauth/otlp]

  pipelines:
    metrics:
      receivers: [splunk_hec,prometheus]
      processors: [resource]
      exporters: [prometheusremotewrite/onpremmimir] # if you want to debug this collector in the stout you can add the 'debug' exporter here
    logs:
      receivers: [splunk_hec]
      processors: [batch,resource,filter/sensitivefields,logdedup]
      exporters: [otlphttp/onpremloki] # if you want to debug this collector in the stout you can add the 'debug' exporter here

  telemetry:
    logs:
      processors:
        - batch:
            exporter:
              otlp:
                protocol: http/protobuf
                endpoint: https://loki.obs.nsw.education/otlp/v1/logs
    metrics:
      readers:
        - pull:
            exporter:
              prometheus:
                host: '0.0.0.0'
                port: {{ env "NOMAD_PORT_metrics" }}
      level: detailed