global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-idrac
    env: prod

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-idrac'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-idrac.obs.nsw.education']

  - job_name: 'exporter-idrac'
    scrape_interval: 1m
    scheme: 'https'
    scrape_timeout: 45s # has to be high for the exporter to collect all the metrics from the snmp exporter
    metrics_path: '/snmp'
    params:
      module: [dell_idrac]
    static_configs:
        - targets:
            - ************
            - *************
            - *************
            - ************
            - *************
            - *************
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-idrac.obs.nsw.education # idrac exporter

remote_write:
  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
 