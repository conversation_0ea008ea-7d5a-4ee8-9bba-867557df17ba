pageInfo:
  title: Observability links
  description: Observability Nomad and legacy clusters (test and prod)

  navLinks:
    - title: Wiki (confluence)
      path:  https://confluence.education.nsw.gov.au/spaces/viewspace.action?key=PM

    - title: Git (bitbucket)
      path:  https://confluence.education.nsw.gov.au/spaces/viewspace.action?key=PM

    - title: Mimir Configuration docs
      path:  https://grafana.com/docs/mimir/latest/reference-configuration-parameters/

    - title: Mimir API docs
      path:  https://grafana.com/docs/mimir/latest/operators-guide/reference-http-api/

    - title: Prometheus Configuration docs
      path:  https://prometheus.io/docs/prometheus/latest/configuration/configuration/

    - title: Dashy docs
      path:  https://dashy.to/docs

appConfig:
  theme: colorful
  layout: auto
  iconSize: medium
  language: en
  disableConfiguration: true
  statusCheck: true

sections:
  - name: Legacy / Classic space - PROD
    icon: fas fa-rocket
    displayData:
      sectionLayout: grid
      itemCountX: 2

    items:
      - title: Grafana
        description: <PERSON>ana, production, mtm
        icon: hl-grafana
        url: https://grafana.mtm.apps.det.nsw.edu.au/
        target: newtab
        statusCheckAllowInsecure: true

      # - title: Zabbix (DC)
      #   description: Console for Zabbix
      #   icon: https://icon.horse/icon/zabbix.com
      #   url: https://console.mtm.apps.det.nsw.edu.au/zabbix/zabbix.php?action=dashboard.view
      #   target: newtab

      # - title: Zabbix (Schools)
      #   description: Console for Zabbix
      #   icon: https://icon.horse/icon/zabbix.com
      #   url: https://console.schools.mtm.apps.det.nsw.edu.au/zabbix
      #   target: newtab

      # - title: Nomad - tcdnd
      #   description: Job list on Hashicorp Nomad
      #   icon: hl-nomad
      #   url:  http://pu0992tcdnd001.hbm.det.nsw.edu.au:4646/ui/jobs
      #   target: newtab


  - name: Legacy / Classic space - TEST
    icon: fas fa-rocket
    displayData:
      sectionLayout: grid
      itemCountX: 2

    items:
      - title: Grafana
        description: Grafana, test, mtm
        url: https://grafana.obs.test.nsw.education/
        icon: hl-grafana
        target: newtab

      # - title: Zabbix (DC)
      #   description: Console for Zabbix
      #   icon: https://icon.horse/icon/zabbix.com
      #   url: https://console.mtm.apps.test.det.nsw.edu.au/zabbix/zabbix.php?action=dashboard.view
      #   target: newtab

      # - title: Zabbix (Schools)
      #   description: Console for Zabbix
      #   icon: https://icon.horse/icon/zabbix.com
      #   url: https://console.schools.mtm.apps.test.det.nsw.edu.au/zabbix/zabbix.php?action=dashboard.view
      #   target: newtab

      # - title: Nomad - tcdnd
      #   description: Job list on Hashicorp Nomad
      #   icon: hl-nomad
      #   url:  http://tu0992tcdnd001.hbm.det.nsw.edu.au:4646/ui/jobs
      #   target: newtab


  - name: ObsCol cluster - PROD
    icon: fas fa-rocket
    items:
      - title: Consul
        icon: si-consul
        url: http://consul.obs.nsw.education
        # url:  http://pl0992obscol01.nsw.education:8500/ui/dc-cir-un-prod/overview/server-status
        target: newtab

      - title: Nomad
        icon: hl-nomad
        url: http://nomad.obs.nsw.education
        # url:  http://pl0992obscol01.nsw.education:4646
        target: newtab

      - title: Traefik
        icon: hl-traefik
        # url: https://traefik.obs.nsw.education
        url:  http://pl0992obscol01.nsw.education:8081
        target: newtab


  - name: ObsCol cluster - TEST
    icon: fas fa-rocket
    items:
      - title: Consula
        icon: si-consul
        url: https://consul.obs.test.nsw.education
        # url:  http://tl0992obscol01.nsw.education:8500/ui/dc-cir-un-test/overview/server-status
        target: newtab

      - title: Nomad
        icon: hl-nomad
        url: https://nomad.obs.test.nsw.education
        # url:  http://tl0992obscol01.nsw.education:4646
        target: newtab

      - title: Traefik
        icon: hl-traefik
        url: https://traefik.obs.test.nsw.education/dashboard#
        # url:  http://tl0992obscol01.nsw.education:8081
        target: newtab


  - name: DoE Control Panel
    icon: fas fa-rocket
    items:
      - title: Cirrus
        description: Our dashboard for internal VM & DB servers
        icon: https://cirrus.nsw.education/img/brand/logo.png
        url: https://cirrus.nsw.education/dashboard
        target: newtab
        statusCheckUrl: https://cirrus-api.nsw.education:3000/explorer

      - title: Illumio
        description: Our entrypoint for Illumio
        icon: http://docs.illumio.com/_Global/Resources/Images/Assets/Logos/IllumioIcon2022.svg
        url: https://illumio.nsw.education:8443/login
        target: newtab


  - name: Vault Control Panel
    icon: fas fa-key
    items: 
      - title: OBS Hashicorp Vault
        description: Our entry point for OBS Vault 
        icon: si-vault
        url: https://vault.obs.nsw.education/ui/vault/dashboard
        # url: https://vault.nsw.education/ui/vault/auth?with=userpass
        target: newtab
      - title: OBS Azure Key Vault (SA account)
        description: Our entry point for OBS Vault 
        icon: si-vault
        url: https://portal.azure.com/#browse/Microsoft.KeyVault%2Fvaults
        # url: https://vault.nsw.education/ui/vault/auth?with=userpass
        target: newtab


  - name: DoE ServiceNow Control Panel
    icon: fas fa-cloud
    items: 
      - title: EDConnect Catalogue PROD
        description: Our entrypoint for EDConnect Service Catalogue ServiceNow PROD
        icon: https://cdn.icon-icons.com/icons2/2699/PNG/512/servicenow_logo_icon_168837.png
        url: https://nswdoeesmp1.service-now.com/services_gateway
        target: newtab

      - title: ServiceNow PROD (Blue)
        description: Our entrypoint for ServiceNow PROD
        icon: https://cdn.icon-icons.com/icons2/2699/PNG/512/servicenow_logo_icon_168837.png
        url: https://nswdoeesmp1.service-now.com/now/nav/ui/home
        target: newtab
        
      - title: ServiceNow TEST (Green)
        description: Our entrypoint for ServiceNow TEST
        icon: https://cdn.icon-icons.com/icons2/2699/PNG/512/servicenow_logo_icon_168837.png
        url: https://nswdoeesme1.service-now.com/now/nav/ui/home
        target: newtab


  - name: DoE OBS Atlassian Control Panel
    icon: fas fa-cloud
    items:
      - title: Bitbucket
        description: Our entry point for Bitbucket
        icon: https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/44_Bitbucket_logo_logos-64.png
        url: https://bitbucket.org/nsw-education/workspace/projects/OBS
        target: newtab

      - title: Jira
        description: Our entry point for Jira
        icon: hl-atlassian-jira
        url: https://nsw-education.atlassian.net/jira/software/c/projects/OBS/boards/381
        target: newtab

      - title: Confluence
        description: Our entry point for Confluence
        icon: hl-atlassian-confluence
        url: https://nsw-education.atlassian.net/wiki/spaces/PM/overview
        target: newtab

  - name: DoE OBS Quay Control Panel
    icon: fab fa-docker
    items:
      - title: Quay
        description: Our entry point for Quay docker image
        icon: https://avatars.githubusercontent.com/u/38353654?s=200&v=4
        url: https://quay.education.nsw.gov.au/observability
        target: newtab
        statusCheckUrl: https://quay.education.nsw.gov.au/status
        statusCheckAllowInsecure: true


  - name: Vendor control panels
    icon: fas fa-rocket
    items:
      - title: Grafana SaaS
        description: Our entrypoint for Grafana - SaaS
        icon: hl-grafana
        url: https://nsweducation.grafana.net/
        target: newtab

      - title: Grafana Corp
        description: Our entrypoint for Grafana Corp
        icon: hl-grafana
        url: https://grafana.com/orgs/nsweducation
        target: newtab

      - title: NewRelic
        description: Vendor
        icon: https://icon.horse/icon/newrelic.com
        url: https://one.newrelic.com/nr1-core?account=318084
        target: newtab


  - name: Mimir Read Write Mode - PROD
    icon: fas fa-cubes
    items:
      - title: Mimir Read Console
        description: Main console for mimir read mode
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-read.obs.nsw.education
        target: newtab

      - title: Mimir Write Console
        description: Main console for mimir write mode
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-write.obs.nsw.education
        target: newtab

      - title: Mimir Backend Console
        description: Main console for mimir backend 
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-backend.obs.nsw.education
        target: newtab


  - name: Mimir Read Write Mode - TEST
    icon: fas fa-cubes
    items:
      - title: Mimir Read Console
        description: Main console for mimir read mode
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-read.obs.test.nsw.education
        target: newtab

      - title: Mimir Write Console
        description: Main console for mimir write mode
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-write.obs.test.nsw.education
        target: newtab

      - title: Mimir Backend Console
        description: Main console for mimir backend 
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        url: https://mimir-rwb-backend.obs.test.nsw.education
        target: newtab


  # - name: Mimir  - primary
  #   icon: fas fa-rocket
  #   items:
  #     - title: Mimir
  #       description: Main console for mimir status
  #       icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
  #       url: https://mimir.obs.test.nsw.education
  #       target: newtab

  #     - title: Mimir service status
  #       description: Main console for mimir status
  #       icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
  #       url: https://mimir.obs.test.nsw.education/service
  #       target: newtab

  #     - title: Logs - Mimir distributor
  #       description: Straight to Loki-on-Grafana.mtm-test for Distributor
  #       url: https://grafana.mtm.apps.test.det.nsw.edu.au/explore?orgId=1&left=%7B%22datasource%22:%22l9B1UUOVk%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer_name%3D~%5C%22mimir-distributor-%28.%2A%29%5C%22%7D%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22l9B1UUOVk%22%7D,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D 
  #       icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
  #       target: newtab

  #     - title: "Store gateway: tenants"
  #       description: List tenants on the cluster
  #       icon: far fa-book
  #       url: https://store-gateway.obs.test.nsw.education/store-gateway/tenants
  #       target: newtab

  #     - title: Store gateway "anonymous" blocks
  #       description: Blocks in use for tenant 'anonymous'
  #       icon: far fa-book
  #       url: https://mimir.obs.test.nsw.education/store-gateway/tenant/anonymous/blocks
  #       target: newtab

  #     - title: Store gateway ring status
  #       description: Blocks in use for tenant 'anonymous'
  #       icon: far fa-book
  #       url: https://mimir.obs.test.nsw.education/store-gateway/ring
  #       target: newtab

  #     - title: Query service 
  #       description: 
  #       icon: far fa-book
  #       url: https://mimir-query-frontend.obs.test.nsw.education/services
  #       target: newtab

  #     - title: Distributor HA Tracker
  #       description: Status for the HA tracker
  #       icon: far fa-book
  #       url: https://mimir-distributor-ring.obs.test.nsw.education/distributor/ha_tracker
  #       target: newtab

  #     - title: Ingester ring status
  #       description: Status for Ingester components
  #       icon: far fa-book
  #       url: https://mimir.obs.test.nsw.education/ingester/ring
  #       target: newtab

  #     - title: Compactor ring status
  #       description: Status for Compactor components
  #       icon: far fa-book
  #       url: https://mimir-compactor-ring.obs.test.nsw.education/compactor/ring
  #       target: newtab

  #     - title: Cardinality names
  #       description: Label names cardinality across all ingesters
  #       icon: far fa-book
  #       url: https://mimir-query-frontend.obs.test.nsw.education/prometheus/api/v1/cardinality/label_names
  #       target: newtab


  - name: Mimir  - OCP4 UAT
    icon: fas fa-rocket
    items:
      - title: Logs - Mimir UAT ingester
        description: Straight to Loki-on-Grafana.mtm-test for ingester
        url: https://grafana.mtm.apps.test.det.nsw.edu.au/explore?orgId=1&left=%7B%22datasource%22:%22l9B1UUOVk%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer_name%3D~%5C%22mimir-ingester-ocp-%28.%2A%29%5C%22%7D%5Cr%5Cn%22,%22queryType%22:%22range%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22l9B1UUOVk%22%7D,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D
        icon: https://raw.githubusercontent.com/grafana/mimir/843897414dba909dfd44f5b93dad35a8a6694d06/images/logo.png
        target: newtab


  - name: OpenShift OCP4 
    icon: fas fa-rocket
    items:
      - title: PROD
        description: prod - use AzureAD
        icon: si-redhatopenshift
        url: https://console.apps.p0.ocp.education.nsw.gov.au/add/all-namespaces
        target: newtab
      - title: Shared
        description: shared - use AzureAD
        icon: si-redhatopenshift
        url: https://console.apps.p0.ocp.svcs.education.nsw.gov.au/add/all-namespaces
        target: newtab
      - title: Pre
        description: pre - use AzureAD
        icon: si-redhatopenshift
        url: https://console.apps.q0.ocp.pre.education.nsw.gov.au/
        target: newtab
      - title: Dev - use AzureAD
        description: dev
        icon: si-redhatopenshift
        url: https://console.apps.d0.ocp.dev.education.nsw.gov.au
        target: newtab



