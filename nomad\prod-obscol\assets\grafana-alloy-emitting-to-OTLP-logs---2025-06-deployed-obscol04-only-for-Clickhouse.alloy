// <PERSON>ana <PERSON> configuration for journald logs to OTLP

// Routes via a a Loki process so we can do some manipulations on the way through.


// Read logs from journald
loki.source.journal "system_logs" {
  format_as_json = true
  labels = {
    job = "journald",
    host = env("HOSTNAME"),
  }
  forward_to = [loki.process.convert_to_otel.receiver]
}


// Read logs from /var/log/*.log files
loki.source.file "varlog_files" {

  targets = [

    // Purely *.log - to avoid 'lastlog' 
    {
      __path__ = "/var/log/*.log",
      job = "varlog",
      host = env("HOSTNAME"),
    },

    // Example of how to add other targets in sub-directories
    {
      __path__ = "/var/log/consul/consul.log",
      job = "varlogconsul",
      host = env("HOSTNAME"),
    },

  ]

  forward_to = [loki.process.convert_to_otel.receiver]
}


// Convert Loki logs to OpenTelemetry format
loki.process "convert_to_otel" {
  forward_to = [otelcol.receiver.loki.logs.receiver]
  
  stage.json {
    expressions = {
      timestamp = "__REALTIME_TIMESTAMP",
      message = "MESSAGE",
      unit = "_SYSTEMD_UNIT",
      priority = "PRIORITY",
    }
  }
  
  stage.labels {
    values = {
      unit = "unit",
      priority = "priority",
    }
  }
}


// OpenTelemetry receiver for Loki logs
otelcol.receiver.loki "logs" {
  output {
    logs = [otelcol.exporter.otlphttp.default.input]
  }
}


// OTLP HTTP exporter to send logs to your endpoint
otelcol.exporter.otlphttp "default" {
  client {
    endpoint = "https://otlp-jedd-ingest.obs.nsw.education"
    
    // Add headers if authentication is required
    // headers = {
    //   "Authorization" = "Bearer YOUR_TOKEN_HERE",
    // }
    
    tls {
      insecure = false
    }
  }
}

