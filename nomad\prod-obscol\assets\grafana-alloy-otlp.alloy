remotecfg {
        url            = "https://fleet-management-prod-004.grafana.net"
        //#id             = "{{env "NOMAD_TASK_NAME"}}"
        //#name           = "{{env "NOMAD_JOB_NAME"}}"
        poll_frequency = "60s"
        proxy_url      = "http://proxy.det.nsw.edu.au:80"
        attributes     = {
                "env" = "prod",
                "namespace" = "nomad",
                "cluster" = "prod-obscol",
                "job" = "integrations/otlp",
        }

        basic_auth {
                username = "533612"
                password = "***********************************************************************************************************************************************************************="
        }
}

logging {
  level  = "debug"
  format = "logfmt"
}