remotecfg {
        url            = "https://fleet-management-prod-004.grafana.net"
        id             = "{{env "NOMAD_TASK_NAME"}}"
        name           = "{{env "NOMAD_JOB_NAME"}}"
        poll_frequency = "60s"
        proxy_url      = "http://proxy.det.nsw.edu.au:80"
        attributes     = {
                "env" = "prod",
                "namespace" = "nomad",
                "cluster" = "prod-obscol",
                "job" = "integrations/vsphere",
        }

        basic_auth {
                username = "533612"
                password = "***********************************************************************************************************************************************************************="
        }
}
prometheus.exporter.self "integrations_alloy" { }

logging {
  level  = "info"
  format = "logfmt"
}

otelcol.auth.basic "grafanacloud" {
  username = "379508"
  password = "***********************************************************************************************************************************************************************1In19"
}

discovery.relabel "integrations_alloy" {
  targets = prometheus.exporter.self.integrations_alloy.targets

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "alloy_hostname"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "namespace"
    replacement  = "prod-obscol"
  }

  rule {
    target_label = "cluster"
    replacement  = "prod-obscol"
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy-check"
  }

  rule {
    target_label = "env"
    replacement  = "prod"
  }  
}

prometheus.exporter.self "integrations_alloy_health" { }

discovery.relabel "integrations_alloy_health" {
  targets = prometheus.exporter.self.integrations_alloy_health.targets

  rule {
    replacement = "{{ env "node.unique.name" }}"
    target_label  = "instance"
  }

  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }

  rule {
    target_label = "env"
    replacement  = "prod"
  }  

  rule {
    target_label = "namespace"
    replacement  = "prod-obscol"
  }    
}

prometheus.scrape "integrations_alloy_health" {
  targets    = discovery.relabel.integrations_alloy_health.output
  forward_to = [prometheus.relabel.integrations_alloy_health.receiver]
  job_name   = "{{env "NOMAD_JOB_NAME"}}"
}

prometheus.relabel "integrations_alloy_health" {
	forward_to = [prometheus.remote_write.metrics_service.receiver]

	rule {
		source_labels = ["__name__"]
		regex         = "alloy_build_info|alloy_component_controller_evaluating|alloy_component_controller_running_components|alloy_component_dependencies_wait_seconds|alloy_component_dependencies_wait_seconds_bucket|alloy_component_evaluation_seconds|alloy_component_evaluation_seconds_bucket|alloy_component_evaluation_seconds_count|alloy_component_evaluation_seconds_sum|alloy_component_evaluation_slow_seconds|alloy_config_hash|alloy_resources_machine_rx_bytes_total|alloy_resources_machine_tx_bytes_total|alloy_resources_process_cpu_seconds_total|alloy_resources_process_resident_memory_bytes|cluster_node_gossip_health_score|cluster_node_gossip_proto_version|cluster_node_gossip_received_events_total|cluster_node_info|cluster_node_lamport_time|cluster_node_peers|cluster_node_update_observers|cluster_transport_rx_bytes_total|cluster_transport_rx_packet_queue_length|cluster_transport_rx_packets_failed_total|cluster_transport_rx_packets_total|cluster_transport_stream_rx_bytes_total|cluster_transport_stream_rx_packets_failed_total|cluster_transport_stream_rx_packets_total|cluster_transport_stream_tx_bytes_total|cluster_transport_stream_tx_packets_failed_total|cluster_transport_stream_tx_packets_total|cluster_transport_streams|cluster_transport_tx_bytes_total|cluster_transport_tx_packet_queue_length|cluster_transport_tx_packets_failed_total|cluster_transport_tx_packets_total|exporter_send_failed_spans_ratio_total|exporter_sent_spans_ratio_total|go_gc_duration_seconds_count|go_goroutines|go_memstats_heap_inuse_bytes|processor_batch_batch_send_size_ratio_bucket|processor_batch_metadata_cardinality_ratio|processor_batch_timeout_trigger_send_ratio_total|prometheus_remote_storage_bytes_total|prometheus_remote_storage_highest_timestamp_in_seconds|prometheus_remote_storage_metadata_bytes_total|prometheus_remote_storage_queue_highest_sent_timestamp_seconds|prometheus_remote_storage_samples_failed_total|prometheus_remote_storage_samples_retried_total|prometheus_remote_storage_samples_total|prometheus_remote_storage_sent_batch_duration_seconds_bucket|prometheus_remote_storage_sent_batch_duration_seconds_count|prometheus_remote_storage_sent_batch_duration_seconds_sum|prometheus_remote_storage_shards|prometheus_remote_storage_shards_max|prometheus_remote_storage_shards_min|prometheus_remote_write_wal_samples_appended_total|prometheus_remote_write_wal_storage_active_series|receiver_accepted_spans_ratio_total|receiver_refused_spans_ratio_total|rpc_server_duration_milliseconds_bucket|scrape_duration_seconds|up"
		action        = "keep"
	}
}


prometheus.remote_write "metrics_service" {
  endpoint {
    url = "https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push"
    proxy_from_environment = true
    basic_auth {
      username = "768052"
      password = "***********************************************************************************************************************************************************************1In19"
    }
  }
}

loki.write "grafana_cloud_loki" {
  endpoint {
    url = "https://logs-prod-004.grafana.net/loki/api/v1/push"
    proxy_from_environment = true
    basic_auth {
      username = "382995"
      password = "***********************************************************************************************************************************************************************1In19"
    }
  }
}

otelcol.receiver.vcenter "integrations_vsphere" {
    endpoint = "https://pa0991vivcr002.vi.det.nsw.edu.au:443" // vCenter @ Silverwater
    username = "srv_obs-app-vcenter"
    password = "BSxD!gyS9*Kd^6jqK9q3QSgW!aY^V%"

    tls {
        insecure = true
    }

    output {
        metrics = [otelcol.processor.batch.integrations_vsphere.input]
    }
}

otelcol.processor.batch "integrations_vsphere" {
    output {
        metrics = [otelcol.processor.transform.integrations_vsphere.input]
    }
}

otelcol.processor.transform "integrations_vsphere" {
    error_mode = "ignore"

    metric_statements {
        context = "resource"
        statements = [
            `set(attributes["job"], "integrations/vsphere") where attributes["job"] == nil`,
        ]
    }

    output {
        metrics = [otelcol.exporter.prometheus.integrations_vsphere.input]
    }
}

otelcol.exporter.prometheus "integrations_vsphere" {
    forward_to = [prometheus.remote_write.metrics_service.receiver]

    resource_to_telemetry_conversion = true
}

