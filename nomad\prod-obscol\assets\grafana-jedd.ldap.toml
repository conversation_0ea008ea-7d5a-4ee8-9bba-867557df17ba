[[servers]]
host = "adldapnsw.det.nsw.edu.au"
port = 636
use_ssl = true
ssl_skip_verify = true
 root_ca_cert = "/etc/pki/CA/certs/EXT_PROD_CHAIN.crt"

bind_dn = "CN=sys-zbx-app-ro-ldap,OU=Applications,OU=Service Accounts,OU=Management,DC=DETNSW,DC=WIN"
bind_password = 'MH*F77TTV&^5@zQ47Hap$*hQ9Hc^wAruG7w'

search_filter = "(sAMAccountName=%s)"

search_base_dns = ["DC=DETNSW,DC=WIN"]




[servers.attributes]
name = "givenName"
surname = "sn"
username = "sAMAccountName"
member_of = "memberOf"
email =  "mail"

