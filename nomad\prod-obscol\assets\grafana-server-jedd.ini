
# TESTING - recite nomad variablse we're inheriting here for reference:

; persistent_data =  {{ env "PERSISTENT_DATA" }}
; grafana_fqdn =  {{ env "GRAFANA_FQDN" }}
; grafana_domain =  {{ env "GRAFANA_DOMAIN" }}
; postgresql_password =  {{ env "POSTGRESQL_PASSWORD" }}
; POSTGRESQL_PASSWORDa =  {{ env "POSTGRESQL_PASSWORDa" }}
; POSTGRESQL_PASSWORDb =  {{ env "POSTGRESQL_PASSWORDb" }}

; Retrying with 'native' grafana variable substitution
; noting - these are env variables, and are translated at runtime, not launch time
; $__env{GRAFANA_FQDN}
; $__env{POSTGRESQL_PASSWORD}
; $__env{NOMAD_PORT_port_postgresql}




; NOMAD_HOST_PORT_port_grafana = {{ env "NOMAD_HOST_PORT_port_grafana" }}
;
;
##################### Grafana Configuration Example #####################
#
# Everything has defaults so you only need to uncomment things you want to
# change

# possible values : production, development
app_mode = production

# instance name, defaults to HOSTNAME environment variable value or hostname if HOSTNAME var is empty
; instance_name = ${HOSTNAME}

#################################### Paths ####################################
[paths]
# Path to where grafana can store temp files, sessions, and the sqlite3 db (if that is used)
# This is specified in nomad job:  GF_PATHS_DATA = "/persistent/grafana/DATA"
;data = /var/lib/grafana


# Directory where grafana can store logs
# This is specified in nomad job: GF_PATHS_LOGS = "/grafana/log"
;logs = /var/log/grafana

# Directory where grafana will automatically scan and look for plugins
#
;plugins = /var/lib/grafana/plugins

#
#################################### Server ####################################
[server]
# Protocol (http or https)
;protocol = https

# The ip address to bind to, empty will bind to all interfaces
;http_addr = ***********

# This is specified in nomad job BECAUSE we point to random-generated Nomad port, substitute in the variable, and let Traefik handle the rest - GF_SERVER_HTTP_PORT
# The http port  to use
;http_port = 443

# The public facing domain name used to access grafana from a browser
; 2024-03-21 - jedd - migrating attempt from grafana.mtm host to nomad prod
; domain = apps.det.nsw.edu.au
domain = obs.nsw.education

# Redirect to correct domain if host header does not match domain
# Prevents DNS rebinding attacks
;enforce_domain = false

# The full public facing url you use in browser, used for redirects and emails
# If you use reverse proxy and sub path specify full url (with sub path)
; 2024-03-21 - jedd - migrating attempt from grafana.mtm host to nomad prod
; root_url = https://grafana.mtm.apps.det.nsw.edu.au

; 2025-05-15 variablising this
; root_url = https://grafana.obs.nsw.education
root_url = https://{{ env "GRAFANA_FQDN" }}

# jedd - this SHOULD be the default, but wasn't in the boilerplate, and need to be sure Grafana isn't trying to do this - it confuses traefik if not catered for.
serve_from_sub_path = false

# Log web requests
router_logging = true

# the path relative working path
;static_root_path = public

# enable gzip
;enable_gzip = false

# https certs & key file
;cert_file = /etc/pki/tls/certs/pu0992tagf0001.apps.det.nsw.edu.au.crt
;cert_key = /etc/pki/tls/private/pu0992tagf0001.apps.det.nsw.edu.au.key

#################################### Database ####################################
[database]
# You can configure the database connection by specifying type, host, name, user and password
# as separate properties or as on string using the url propertie.


; @TODO - jedd - get this going ONCE the database is imported into sharednfs
; 2024-03-28 - jedd - migrating attempt from grafana.mtm host to nomad prod
; we previously tried using GF_ variables in the env in the nomad job, but
; it's a bit more robust to pull them in via the ini here:
type = postgres
## name = grafanadb_p
## user = grafana_db_admin
# If the password contains # or ; you have to wrap it with trippel quotes. Ex """#password;"""
## password = ************************************************************************************************* "NOMAD_IP_port_postgresql"}}:{{env "NOMAD_PORT_port_postgresql" }}
# host = grafana-postgresql.obs.nsw.education:{{env "NOMAD_PORT_port_postgresql" }}
# host = {{env "NOMAD_ADDR_port_postgresql"}}
host = grafana-postgresql.obs.nsw.education:5432
name = grafanadb_p
user = grafana_db_admin

# password = S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU
# password = S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU
password = $__env{POSTGRESQL_PASSWORD}



# Use either URL or the previous fields to configure the database
# Example: mysql://user:secret@host:port/database
;url =

# For "postgres" only, either "disable", "require" or "verify-full"
; ssl_mode = disable

# For "sqlite3" only, path relative to data_path setting
;path = grafana.db

# Max conn setting default is 0 (mean not set)
;max_conn =
;max_idle_conn =
;max_open_conn =


#################################### Session ####################################
[session]
# Either "memory", "file", "redis", "mysql", "postgres", default is "file"
;provider = file

# Provider config options
# memory: not have any config yet
# file: session dir path, is relative to grafana data_path
# redis: config like redis server e.g. `addr=127.0.0.1:6379,pool_size=100,db=grafana`
# mysql: go-sql-driver/mysql dsn config string, e.g. `user:password@tcp(127.0.0.1:3306)/database_name`
# postgres: user=a password=b host=localhost port=5432 dbname=c sslmode=disable
;provider_config = sessions

# Session cookie name
;cookie_name = grafana_sess

# If you use session in https only, default is false
;cookie_secure = false

# Session life time, default is 86400
;session_life_time = 86400

#################################### Data proxy ###########################
[dataproxy]

# This enables data proxy logging, default is false
;logging = false


#################################### Analytics ####################################
[analytics]
# Server reporting, sends usage counters to stats.grafana.org every 24 hours.
# No ip addresses are being tracked, only simple counters to track
# running instances, dashboard and error counts. It is very helpful to us.
# Change this option to false to disable reporting.
;reporting_enabled = true

# Set to false to disable all checks to https://grafana.net
# for new vesions (grafana itself and plugins), check is used
# in some UI views to notify that grafana or plugin update exists
# This option does not cause any auto updates, nor send any information
# only a GET request to http://grafana.net to get latest versions
; 2024-03-21 - jedd - migrating from grafana.mtm
; check_for_updates = true
check_for_updates = false

# Google Analytics universal tracking code, only enabled if you specify an id here
; 2024-03-21 - jedd - migrating from grafana.mtm
; google_analytics_ua_id = UA-98740077-2

#################################### Security ####################################
[security]
# default admin user, created on startup
;admin_user = admin

# default admin password, can be changed before first start of grafana,  or in profile settings
;admin_password = admin

# used for signing
;secret_key = SW2YcwTIb9zpOOhoPsMm

# Auto-login remember days
;login_remember_days = 7
;cookie_username = grafana_user
;cookie_remember_name = grafana_remember

# disable gravatar profile images
;disable_gravatar = false

# data source proxy whitelist (ip_or_domain:port separated by spaces)
;data_source_proxy_whitelist =

# 2023-02-08 jedd - enabled as this solves a lot of weird edge case CVEs that come up.
content_security_policy = true


[snapshots]
# snapshot sharing options
;external_enabled = true
;external_snapshot_url = https://snapshots-origin.raintank.io
;external_snapshot_name = Publish to snapshot.raintank.io

# remove expired snapshot
;snapshot_remove_expired = true

# remove snapshots after 90 days
;snapshot_TTL_days = 90

#################################### Users ####################################
[users]
# Viewers can edit/inspect dashboard settings in the browser. But not save the dashboard.
viewers_can_edit = true

# disable user signup / registration
;allow_sign_up = true

# Allow non admin users to create organizations
;allow_org_create = true

# Set to true to automatically assign new users to the default organization (id 1)
;auto_assign_org = true

# Default role new users will be automatically assigned (if disabled above is set to true)
;auto_assign_org_role = Viewer

# Background text for the user field on the login page
;login_hint = email or username

# Default UI theme ("dark" or "light")
default_theme = dark

#[auth]
# Set to true to disable (hide) the login form, useful if you use OAuth, defaults to false
;disable_login_form = false

#################################### Anonymous Auth ##########################
[auth.anonymous]
# enable anonymous access
enabled = true

# specify organization name that should be used for unauthenticated users
# org_name = NSW Education
org_name = Main Org.

# specify role for unauthenticated users
org_role = Viewer

#################################### Github Auth ##########################
#[auth.github]
;enabled = false
;allow_sign_up = true
;client_id = some_id
;client_secret = some_secret
;scopes = user:email,read:org
;auth_url = https://github.com/login/oauth/authorize
;token_url = https://github.com/login/oauth/access_token
;api_url = https://api.github.com/user
;team_ids =
;allowed_organizations =

#################################### Google Auth ##########################
#[auth.google]
;enabled = false
;allow_sign_up = true
;client_id = some_client_id
;client_secret = some_client_secret
;scopes = https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email
;auth_url = https://accounts.google.com/o/oauth2/auth
;token_url = https://accounts.google.com/o/oauth2/token
;api_url = https://www.googleapis.com/oauth2/v1/userinfo
;allowed_domains =

#################################### Generic OAuth ##########################
#[auth.generic_oauth]
;enabled = false
;name = OAuth
;allow_sign_up = true
;client_id = some_id
;client_secret = some_secret
;scopes = user:email,read:org
;auth_url = https://foo.bar/login/oauth/authorize
;token_url = https://foo.bar/login/oauth/access_token
;api_url = https://foo.bar/user
;team_ids =
;allowed_organizations =

#################################### Grafana.net Auth ####################
#[auth.grafananet]
;enabled = false
;allow_sign_up = true
;client_id = some_id
;client_secret = some_secret
;scopes = user:email
;allowed_organizations =

#################################### Auth Proxy ##########################
#[auth.proxy]
;enabled = false
;header_name = X-WEBAUTH-USER
;header_property = username
;auto_sign_up = true
;ldap_sync_ttl = 60
;whitelist = ***********, ***********

#################################### Basic Auth ##########################
#[auth.basic]
;enabled = true

#################################### Auth LDAP ##########################
[auth.ldap]
enabled = true

config_file = /etc/grafana/ldap.toml
# config_file = /persistent/grafana/etc/ldap.toml

allow_sign_up = true

# 2023-08-01 jedd - disable trying to sync roles from ldap
skip_org_role_sync = true

#################################### SMTP / Emailing ##########################
[smtp]

; 2025-05-15 jedd @TODO revisit this once I've got configuration sane
enabled = false

; 2024-03-21 - jedd - migrating from grafana.mtm
; @TODO this will need some more thought to get out of the docker container
; may need to specify smarthost at ************:25 ?

; 2025-03-13 jedd - changed (along with nomad constraint to 0992 hosts) 
;    to try to talk to docker / phys host tcp/25 (postfix is running and
;    listening on these boxes, but not rhel9 (obscol 06, 07, 08).
; host = localhost:25
host = ************:25

;user =
# If the password contains # or ; you have to wrap it with trippel quotes. Ex """#password;"""
;password =
;cert_file =
;key_file =
skip_verify = false

; 2023-03-09 jedd - changed from platmon to observability
; from_address = ~<EMAIL>
from_address = ~<EMAIL>
; from_name = IT Infraserv Platform Monitoring
from_name = DoE ITD Observability Grafana

#[emails]
;welcome_email_on_sign_up = false

#################################### Logging ##########################
[log]
# Either "console", "file", "syslog". Default is console and  file
# Use space to separate multiple modes, e.g. "console file"

# We specify this in the Nomad job - to just 'console' - as it's collected by docker/loki plugin just fine from console only.
;mode = console file

# Either "trace", "debug", "info", "warn", "error", "critical", default is "info"
; 2024-03-21 - jedd - migrating from grafana.mtm
; level = debug
level = warn

# optional settings to set different levels for specific loggers. Ex filters = sqlstore:debug
; filters = ldap:debug


# For "console" mode only
[log.console]
;level =

# log line format, valid options are text, console and json
;format = console

# For "file" mode only
[log.file]
;level =

# log line format, valid options are text, console and json
;format = text

# This enables automated log rotate(switch of following options), default is true
;log_rotate = true

# Max line number of single file, default is 1000000
;max_lines = 1000000

# Max size shift of single file, default is 28 means 1 << 28, 256MB
;max_size_shift = 28

# Segment log daily, default is true
;daily_rotate = true

# Expired days of log file(delete after max days), default is 7
max_days = 3

[log.syslog]
;level =

# log line format, valid options are text, console and json
;format = text

# Syslog network type and address. This can be udp, tcp, or unix. If left blank, the default unix endpoints will be used.
;network =
;address =

# Syslog facility. user, daemon and local0 through local7 are valid.
;facility =

# Syslog tag. By default, the process' argv[0] is used.
;tag =


#################################### AMQP Event Publisher ##########################
[event_publisher]
;enabled = false
;rabbitmq_url = amqp://localhost/
;exchange = grafana_events

;#################################### Dashboard JSON files ##########################
[dashboards.json]
;enabled = false
;path = /var/lib/grafana/dashboards

#################################### Alerting ############################
[alerting]
# Disable alerting engine & UI features
enabled = false
# Makes it possible to turn off alert rule execution but alerting UI is visible
;execute_alerts = true

#################################### Internal Grafana Metrics ##########################
# Metrics available at HTTP API Url /api/metrics
[metrics]
# Disable / Enable internal metrics
enabled           = true

# Publish interval
; 2024-03-21 - jedd - migrating from grafana.mtm
; interval_seconds  = 10
interval_seconds  = 60

# Send internal metrics to Graphite
[metrics.graphite]
# Enable by setting the address setting (ex localhost:2003)
;address =
;prefix = prod.grafana.%(instance_name)s.

#################################### Internal Grafana Metrics ##########################
# Url used to to import dashboards directly from Grafana.net
[grafana_net]
;url = https://grafana.net

#################################### External image storage ##########################
[external_image_storage]
# Used for uploading images to public servers so they can be included in slack/email messages.
# you can choose between (s3, webdav)
;provider =

[external_image_storage.s3]
;bucket_url =
;access_key =
;secret_key =

[external_image_storage.webdav]
;url =
;username =
;password =


# Needed 2020-08 for Grafana 7 and Grafan-Zabbix plugin 4.0.0
[plugins]
; 2024-03-21 - jedd - migrating from grafana.mtm
; allow_loading_unsigned_plugins = digrich-bubblechart-panel



# 2023-06-13 jedd - disabling topnav awfulness
[feature_toggles]
# enable features, separated by spaces
;enable =
# 2023-06-01 jedd disable the 9.5 side-view navigation - won't be an option in 10 onwards. : (
topnav = false
prometheusMetricEncyclopedia = true
# 2022-12-16 jedd - These are configured in Environment= lines in the service file
# cloudWatchCrossAccountQuerying = true
# flameGraph = true

# 2023-08-30 jedd - enable OnCall, perhaps
accessControlOnCall = true





