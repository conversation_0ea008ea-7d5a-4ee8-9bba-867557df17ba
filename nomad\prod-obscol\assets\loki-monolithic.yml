auth_enabled: false

server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

common:
  replication_factor: 1
  path_prefix: /loki
  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  max_chunk_age: 6h
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/wal
    flush_on_shutdown: true
    replay_memory_ceiling: "1G"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h
  - from: 2023-07-01
    store: tsdb
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h

storage_config:
  tsdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: "/loki/data/index"
    cache_location: "/loki/data/index-cache"
    #shared_store: s3

  aws:
    bucketnames: "nswdoe-obs-loki-blocks-storage-shared"
    access_key_id: ********************
    secret_access_key: 5rUs+QlPX6Ihb6aESLVM/PpAWl06PBwnWC383Bkb

    s3forcepathstyle: true
    region: "ap-southeast-2"
    insecure: false
    #sse_encryption: true #deprecated

limits_config:
  reject_old_samples: false
  #reject_old_samples_max_age: 168h
  allow_structured_metadata: true
  per_stream_rate_limit: 10MB

compactor:
  working_directory: {{ env "NOMAD_ALLOC_DIR" }}/compactor
  delete_request_store: s3
  compaction_interval: 5m
  retention_enabled: true

ruler:
  alertmanager_url: https://alertmanager.service.consul
  enable_alertmanager_v2: true
  enable_api: true
  external_url: https://loki.obs.nsw.education
  rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/rules
  storage:
    type: local
    local:
      directory: {{ env "NOMAD_TASK_DIR" }}/rules
  wal:
    dir: {{ env "NOMAD_ALLOC_DIR" }}/data/ruler





# auth_enabled: false

# server:
#   # 2023-08-15 jedd - change log level from info to warn
#   log_level: info
#   http_listen_port: {{ env "NOMAD_PORT_http "}}
#   grpc_listen_port: {{ env "NOMAD_PORT_grpc "}}

# common:
#   #compactor_grpc_address: "localhost:9096"
#   path_prefix: /loki
#   replication_factor: 1
#   # Tell Loki which address to advertise
#   instance_addr: "localhost"
#   ring:
#     # Tell Loki which address to advertise in ring
#     instance_addr: {{ env "NOMAD_IP_grpc" }}
#     instance_id: {{ env "node.unique.name" }}
#     instance_port: {{ env "NOMAD_PORT_http "}}
#     kvstore:
#       store: consul
#       prefix: loki/
#       consul:
#         host: {{ env "attr.unique.network.ip-address" }}:8500

# ingester:
#   autoforget_unhealthy: true
#   chunk_block_size: 26214000
#   chunk_target_size: 1572864
#   max_chunk_age: 2h
#   wal:
#     dir: "/loki/data/wal"
#     flush_on_shutdown: true
#     replay_memory_ceiling: "1G"

# schema_config:
#   configs:
#   - from: 2022-05-15
#     store: boltdb-shipper
#     object_store: s3
#     schema: v13
#     index:
#       prefix: index_
#       period: 24h
#   - from: 2023-07-01
#     store: tsdb
#     object_store: s3
#     schema: v13
#     index:
#       prefix: index_
#       period: 24h

# storage_config:
#   tsdb_shipper:
#     # Nomad ephemeral disk is used to store index and cache
#     # it will try to preserve /alloc/data between job updates
#     active_index_directory: "/loki/data/index"
#     cache_location: "/loki/data/index-cache"
#     #shared_store: s3
#   aws:
#     bucketnames: "nswdoe-obs-loki-blocks-storage-shared"
#     access_key_id: ********************
#     secret_access_key: 5rUs+QlPX6Ihb6aESLVM/PpAWl06PBwnWC383Bkb

#     s3forcepathstyle: true
#     region: "ap-southeast-2"
#     insecure: false
#     #sse_encryption: true #deprecated

# limits_config:
#   #enforce_metric_name: false ## deprecated
#   reject_old_samples: false
#   reject_old_samples_max_age: 168h
#   #allow_deletes: true
#   volume_enabled: true
#   #allow_structured_metadata: false
#   s3_sse_type: AES256

# compactor:
#   working_directory: "/loki/compactor"
#   #shared_store: s3 #deprecated
#   retention_enabled: false
#   compactor_ring:
#     instance_addr: "localhost"
#     instance_id: "loki-monolithic"
#     instance_port: 9096  

# #decprecated
# # table_manager:
# #   retention_deletes_enabled: true
# #   retention_period: "1440h" # 2 months

# ruler:
#   alertmanager_url: https://alertmanager.obs.nsw.education
#   enable_alertmanager_v2: true
#   enable_api: true
#   external_url: https://loki.obs.nsw.education
#   rule_path: "/loki/tmp/rules"
#   storage:
#     type: local
#     local:
#       directory: "/loki/rules"
#   wal:
#     dir: "/loki/data/ruler"

# analytics:
#   reporting_enabled: false