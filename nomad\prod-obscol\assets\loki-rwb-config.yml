
# 2025-04-01 <PERSON> sets up RWB model for modern loki version 3.4.x
#
# <PERSON>' notes / gotchas:
# - RWB is easier with a simplier config, I believe g<PERSON><PERSON><PERSON><PERSON> has set suitable defaults
#
# - Compactor address needs to use the http://loki.obs.nsw.education endpoint
#
# - Backend task needs a router other than loki.obs.nsw.education otherwise reads dont work, 
#   similar to how mimir-rwb is setup in Traefik.
#
# - Flushing ingestors on shutdown to s3 can take minutes -- which causes canary type updates 
#   to fail, as there is still a process accessing files on the NFS mount.
#
# - Read & Write nomad tasks can scale as we need, but backend can remain at 1
#   @TODO need to test in a patching cycle to understand if running multiple backends 
#   in a ring impacts the performance of (f.e.) queries.

auth_enabled: false


server:
  # 2023-08-15 jedd - change log level from info to warn
  log_level: info
  # 2025-06-12 james - grpc max message size updated from 58152281 to 108152281 bytes
  grpc_server_max_recv_msg_size: 108152281
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

common:
  compactor_address: loki-compactor.obs.nsw.education:9099 # compactor component in the backend group
  path_prefix: /loki
  replication_factor: 3
  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc "}}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  wal:
    enabled: true
    dir: /loki/tsdb/data/wal
    flush_on_shutdown: false # this flush can take minutes to upload wal to s3, with persistent storage it is not needed
    #replay_memory_ceiling: "1G"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h
  - from: 2023-07-01 #allows loki to know that after this data the schema in the object store is tsdb
    store: tsdb
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h

storage_config:
  tsdb_shipper: #switched from boltdb-shipper to tsdb-shipper
    # active_index_directory: /loki/tsdb/{{ env "node.unique.name" }}/data/data/index
    # cache_location: /loki/tsdb/{{ env "node.unique.name" }}/data/data/index-cache
    # moved cache back to alloc ephemeral storage as cache on nfs is bottlenecking performance
    active_index_directory: /loki/data/index
    cache_location: /loki/data/index-cache
    cache_ttl: 4h
  aws:
    bucketnames: "nswdoe-obs-loki-blocks-storage-shared"
    access_key_id: ********************
    secret_access_key: 5rUs+QlPX6Ihb6aESLVM/PpAWl06PBwnWC383Bkb

    s3forcepathstyle: true
    region: "ap-southeast-2"
    insecure: false

limits_config:
  reject_old_samples: false # allows loki to ingest data that is older than the schema config ie period 24h
  reject_old_samples_max_age: 168h # 7 days
  volume_enabled: true 
  s3_sse_type: AES256
  allow_structured_metadata: true # allows loki to ingest data with structured metadata ie OTLP, there is a specific like of labels that will be indexed by default.
  # these are global ingestion rate limits
  # 2025-08-28 14:22 james - increase max series query from the default to 4000
  max_query_series: 4000

  # 2025-05-19 12:00 jedd - change from 100 to 150 
  ingestion_rate_mb: 150
  # 2025-05-19 12:00 jedd - change from 150 to 200
  ingestion_burst_size_mb: 200
  # 2025-05-19 12:00 jedd - change from 20MB to 30MB
  # 2025-05-19 12:20 jedd - change from 30MB to 50MB
  # 2025-05-19 12:45 jedd - change from 50MB to 80MB
  # 2025-06-12 12:21 james - bumped from 80 to 200
  per_stream_rate_limit: 200MB
  # 2025-06-11 james - adding this as its default is lower then per_stream_rate_limit above
  per_stream_rate_limit_burst: 250MB 
  # 2025-06-11 james - allow maximum line length
  max_line_size: 0
  # 2025-06-11 james - changed ingestion rate from global to per distributor, therefore 200MB x n
  ingestion_rate_strategy: local
  # 2025-06-11 james - testing maximum query length
  max_query_length: 0 #30d1h is default, 0 is disable.

compactor:
  working_directory: /loki/tsdb/data/compactor
  #shared_store: s3
  compaction_interval: 5m
  delete_request_store: s3
  retention_enabled: false

# 2025-06-25 - James noticing redis connection errors in loki, disabling for the time being.
# query_range:
#   results_cache:
#     cache:
#       redis:
#         endpoint: redis.obs.nsw.education:6379
#         db: 0

#decprecated
# table_manager:
#   retention_deletes_enabled: true
#   retention_period: "1440h" # 2 months

# ruler:
#   alertmanager_url: https://alertmanager.obs.nsw.education
#   enable_alertmanager_v2: true
#   enable_api: true
#   external_url: https://loki.obs.nsw.education
#   rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/rules
#   storage:
#     type: local
#     local:
#       directory: {{ env "NOMAD_TASK_DIR" }}/rules
#   wal:
#     dir: {{ env "NOMAD_ALLOC_DIR" }}/data/ruler


analytics:
  reporting_enabled: false

tracing:
  enabled: true
