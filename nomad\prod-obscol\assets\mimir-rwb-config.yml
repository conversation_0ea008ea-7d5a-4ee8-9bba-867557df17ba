multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "info" #2025-06-23 James - logging is cheap and while we troubleshoot

  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

alertmanager:
  sharding_ring:
    kvstore:
      store: "consul"
      prefix: "alertmanagers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  data_dir: /mimir/alertmanager/

alertmanager_storage:
  backend: "filesystem"
  local:
    path: "/mimir/alertmanager/"

distributor:
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    replication_factor: 3 # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

# frontend_worker:
#   scheduler_address: "mimir-rwb-backend.service.dc-cir-un-prod.collectors.obs.nsw.education:8096"
#   id: {{ env "node.unique.name" }}
#   grpc_client_config:
#     grpc_compression: "snappy"

frontend:
  results_cache:
    backend: redis
    redis:
      endpoint: redis.obs.nsw.education:6379
      db: 4
  cache_results: true
  scheduler_address: "mimir-rwb-backend.service.dc-cir-un-prod.collectors.obs.nsw.education:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  port: {{ env "NOMAD_PORT_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"

query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  ring:
    kvstore:
      store: "consul"
      prefix: "rulers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "https://mimir-rwb-read.obs.nsw.education/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  alertmanager_url: "https://alertmanager.obs.nsw.education"
  query_frontend:
    address: "https://mimir-rwb-read.obs.nsw.education"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

querier:
  query_store_after: 12h0m0s #default


compactor: #backend component
  data_dir: /mimir/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway: #backend component
  sharding_ring:
    replication_factor: 3 # Keep this the same as the "count" of ingesters, or mimir-rwb-write group
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage: #only used by ingestors, write-tasks
  backend: s3
  bucket_store:
    #ignore_blocks_within: 10h  # Going to remove this advanced option
    sync_dir: /mimir/tsdb-sync/{{ env "node.unique.name" }}/
  tsdb: #Required to be persistent between restarts
    dir: /mimir/tsdb/{{ env "node.unique.name" }}/
    #retention_period: 13h0m0s Dropping this to help ingester start up faster
    retention_period: 8h0m0s # this might be the cause of the data gaps, it was the last change James made that after the localstore migration
    flush_blocks_on_shutdown: false # If false then incomplete blocks will be used on startup
    memory_snapshot_on_shutdown: false #(experimental) True to enable snapshotting of in-memory TSDB data on disk when shutting down.

common:
  storage:
    backend: s3
    s3:
      # 2024-02-12 jedd - rotating s3 keys
      # access_key_id: ********************
      # secret_access_key: N/kPfaQhU9a4QXNnVqpc2sjOajhQnohhMJC84RdY
      access_key_id: ********************
      secret_access_key: s7UqEIm0Pkzz4hbCx8RBgOhYuGOSFQWrLLlXWLGs

      bucket_name: nswdoe-obs-mimir-blocks-storage-shared
      endpoint: s3.ap-southeast-2.amazonaws.com
      region: ap-southeast-2

limits:
  accept_ha_samples: true
  max_global_series_per_user: 0
  max_label_names_per_series: 200
  out_of_order_time_window:  15m
  ingestion_rate: 200000
  ingestion_burst_size: 3000000
  compactor_blocks_retention_period: 1y
  compactor_block_upload_enabled: true
  cardinality_analysis_enabled: true