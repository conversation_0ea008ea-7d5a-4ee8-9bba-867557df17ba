
# Used by phlare.nomad (PROD obscol) 
#
# Full list of configuration options at:
#   https://grafana.com/docs/phlare/latest/operators-guide/configure/reference-configuration-parameters/

storage:
  backend: filesystem

  filesystem:
    dir: "/data/"

phlaredb:
  # default retention is only 3h
  max_block_duration: 24h

scrape_configs:
  - job_name: "phlare-self"
    scrape_interval: "1m"
    scrape_timeout: 5s
    static_configs:
      - targets: ["phlare.obs.nsw.education"]

    # = = = = = = = = = = = = = = = = = = = = = = = =
    # These are maintained in alphabetical order

  - job_name: "phlare-loki"
    scrape_interval: "20s"
    scrape_timeout: 5s
    # 2023-01-31 jedd - this endpoint is *only* served on https.
    scheme: "https"
    static_configs:
      - targets: ["loki.obs.nsw.education"]

  - job_name: "phlare-mimir"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["mimir.obs.nsw.education"]

  - job_name: "phlare-prom"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["prometheus.obs.nsw.education"]

  - job_name: "phlare-prom-app-kafka"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["prometheus-app-kafka.obs.nsw.education"]

  - job_name: "phlare-tempo"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["tempo.obs.nsw.education"]

  - job_name: "phlare-influxdb"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["influxdb.obs.nsw.education"]

  - job_name: "phlare-consul"
    scrape_interval: "20s"
    scrape_timeout: 5s
    static_configs:
      - targets: ["pl0992obscol01.nsw.education:8500"]