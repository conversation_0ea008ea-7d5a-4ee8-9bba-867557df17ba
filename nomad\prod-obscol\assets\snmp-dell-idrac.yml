dell_idrac:
  walk:
  - *******.4.1.674.10892.5.1.3
  - *******.4.1.674.10892.5.2
  - *******.4.1.674.10892.5.4.1100.30.1
  - *******.4.1.674.10892.5.4.1100.32.1
  - *******.4.1.674.10892.5.4.1100.50.1
  - *******.4.1.674.10892.5.4.1100.80.1
  - *******.4.1.674.10892.5.4.1100.90.1
  - *******.4.1.674.10892.5.4.2000.10.1
  - *******.4.1.674.10892.5.4.300.50.1
  - *******.4.1.674.10892.5.4.300.60.1
  - *******.4.1.674.10892.5.4.300.70.1
  - *******.4.1.674.10892.5.4.600.12.1
  - *******.4.1.674.10892.5.4.600.50.1
  - *******.4.1.674.10892.5.4.700.12.1.5
  - *******.4.1.674.10892.5.4.700.12.1.8
  - *******.4.1.674.10892.5.4.700.20.1
  - *******.4.1.674.10892.********.130.1.1
  - *******.4.1.674.10892.********.130.4.1
  - *******.4.1.674.10892.********.140.1.1
  get:
  - *******.4.1.674.10892.5.1.1.2.0
  - *******.4.1.674.10892.5.1.1.8.0
  - *******.4.1.674.10892.5.4.300.1.0
  metrics:
  - name: racShortName
    oid: *******.4.1.674.10892.5.1.1.2
    type: DisplayString
    help: This attribute defines the short product name of a remote access card. -
      *******.4.1.674.10892.5.1.1.2
  - name: racFirmwareVersion
    oid: *******.4.1.674.10892.5.1.1.8
    type: DisplayString
    help: This attribute defines the firmware version of a remote access card. - *******.4.1.674.10892.5.1.1.8
  - name: systemFQDN
    oid: *******.4.1.674.10892.5.1.3.1
    type: DisplayString
    help: This attribute defines the fully qualified domain name of the system - *******.4.1.674.10892.5.1.3.1
  - name: systemServiceTag
    oid: *******.4.1.674.10892.5.1.3.2
    type: DisplayString
    help: This attribute defines the service tag of the system. - *******.4.1.674.10892.5.1.3.2
  - name: systemExpressServiceCode
    oid: *******.4.1.674.10892.5.1.3.3
    type: DisplayString
    help: This attribute defines the express service code of the system. - *******.4.1.674.10892.5.1.3.3
  - name: systemAssetTag
    oid: *******.4.1.674.10892.*******
    type: DisplayString
    help: This attribute defines the asset tag of the system. - *******.4.1.674.10892.*******
  - name: systemBladeSlotNumber
    oid: *******.4.1.674.10892.*******
    type: OctetString
    help: This attribute defines the slot number of the system in the modular chassis.
      - *******.4.1.674.10892.*******
  - name: systemOSName
    oid: *******.4.1.674.10892.*******
    type: OctetString
    help: This attribute defines the name of the operating system that the host is
      running. - *******.4.1.674.10892.*******
  - name: systemFormFactor
    oid: *******.4.1.674.10892.*******
    type: gauge
    help: This attribute defines the form factor of the system. - *******.4.1.674.10892.*******
    enum_values:
      1: other
      2: unknown
      3: u1
      4: u2
      5: u4
      6: u7
      7: singleWidthHalfHeight
      8: dualWidthHalfHeight
      9: singleWidthFullHeight
      10: dualWidthFullHeight
      11: singleWidthQuarterHeight
      12: u5
      13: u1HalfWidth
      14: u1QuarterWidth
      15: u1FullWidth
  - name: systemDataCenterName
    oid: *******.4.1.674.10892.*******
    type: DisplayString
    help: This attribute defines the Data Center locator of the system. - *******.4.1.674.10892.*******
  - name: systemAisleName
    oid: *******.4.1.674.10892.*******
    type: OctetString
    help: This attribute defines the Aisle locator of the system. - *******.4.1.674.10892.*******
  - name: systemRackName
    oid: *******.4.1.674.10892.5.1.3.10
    type: OctetString
    help: This attribute defines the Rack locator of the system. - *******.4.1.674.10892.5.1.3.10
  - name: systemRackSlot
    oid: *******.4.1.674.10892.5.1.3.11
    type: OctetString
    help: This attribute defines the Rack Slot locator of the system. - *******.4.1.674.10892.5.1.3.11
  - name: systemModelName
    oid: *******.4.1.674.10892.********
    type: OctetString
    help: This attribute defines the model name of the system. - *******.4.1.674.10892.********
  - name: systemSystemID
    oid: *******.4.1.674.10892.********
    type: gauge
    help: This attribute defines the system ID of the system. - *******.4.1.674.10892.********
  - name: systemOSVersion
    oid: *******.4.1.674.10892.********
    type: OctetString
    help: This attribute defines the version of the operating system that the host
      is running. - *******.4.1.674.10892.********
  - name: systemRoomName
    oid: *******.4.1.674.10892.********
    type: OctetString
    help: This attribute defines the Room locator of the system. - *******.4.1.674.10892.********
  - name: systemChassisSystemHeight
    oid: *******.4.1.674.10892.********
    type: gauge
    help: This attribute defines the height of the system, in 'U's - *******.4.1.674.10892.********
  - name: systemBladeGeometry
    oid: *******.4.1.674.10892.********
    type: gauge
    help: This attribute defines the geometry for a modular system - *******.4.1.674.10892.********
    enum_values:
      1: other
      2: unknown
      3: singleWidthHalfHeight
      4: dualWidthHalfHeight
      5: singleWidthFullHeight
      6: dualWidthFullHeight
      7: singleWidthQuarterHeight
      8: u1HalfWidth
      9: u1QuarterWidth
      10: u1FullWidth
  - name: systemNodeID
    oid: *******.4.1.674.10892.********
    type: OctetString
    help: This attribute defines the node ID of the system - *******.4.1.674.10892.********
  - name: globalSystemStatus
    oid: *******.4.1.674.10892.5.2.1
    type: gauge
    help: This attribute defines the overall rollup status of all components in the
      system being monitored by the remote access card - *******.4.1.674.10892.5.2.1
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: systemLCDStatus
    oid: *******.4.1.674.10892.5.2.2
    type: gauge
    help: This attribute defines the system status as it is reflected by the LCD front
      panel - *******.4.1.674.10892.5.2.2
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: globalStorageStatus
    oid: *******.4.1.674.10892.5.2.3
    type: gauge
    help: This attribute defines the overall storage status being monitored by the
      remote access card. - *******.4.1.674.10892.5.2.3
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: systemPowerState
    oid: *******.4.1.674.10892.5.2.4
    type: gauge
    help: This attribute defines the power state of the system. - *******.4.1.674.10892.5.2.4
    enum_values:
      1: other
      2: unknown
      3: "off"
      4: "on"
  - name: systemPowerUpTime
    oid: *******.4.1.674.10892.5.2.5
    type: gauge
    help: This attribute defines the power-up time of the system in seconds. - *******.4.1.674.10892.5.2.5
  - name: processorDevicechassisIndex
    oid: *******.4.1.674.10892.5.4.1100.30.1.1
    type: gauge
    help: 1100.0030.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.1100.30.1.1
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceIndex
    oid: *******.4.1.674.10892.5.4.1100.30.1.2
    type: gauge
    help: 1100.0030.0001.0002 This attribute defines the index (one based) of the
      processor device. - *******.4.1.674.10892.5.4.1100.30.1.2
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceStateCapabilities
    oid: *******.4.1.674.10892.5.4.1100.30.1.3
    type: gauge
    help: 1100.0030.0001.0003 This attribute defines the state capabilities of the
      processor device. - *******.4.1.674.10892.5.4.1100.30.1.3
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: processorDeviceStateSettings
    oid: *******.4.1.674.10892.5.4.1100.30.1.4
    type: gauge
    help: 1100.0030.0001.0004 This attribute defines the state settings of the processor
      device. - *******.4.1.674.10892.5.4.1100.30.1.4
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: processorDeviceStatus
    oid: *******.4.1.674.10892.5.4.1100.30.1.5
    type: gauge
    help: 1100.0030.0001.0005 This attribute defines the status of the processor device.
      - *******.4.1.674.10892.5.4.1100.30.1.5
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: processorDeviceType
    oid: *******.4.1.674.10892.5.4.1100.30.1.7
    type: gauge
    help: 1100.0030.0001.0007 This attribute defines the type of the processor device.
      - *******.4.1.674.10892.5.4.1100.30.1.7
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: deviceTypeIsOther
      2: deviceTypeIsUnknown
      3: deviceTypeIsCPU
      4: deviceTypeIsMathProcessor
      5: deviceTypeIsDSP
      6: deviceTypeIsAVideoProcessor
  - name: processorDeviceManufacturerName
    oid: *******.4.1.674.10892.5.4.1100.30.1.8
    type: OctetString
    help: 1100.0030.0001.0008 This attribute defines the name of the manufacturer
      of the processor device. - *******.4.1.674.10892.5.4.1100.30.1.8
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceStatusState
    oid: *******.4.1.674.10892.5.4.1100.30.1.9
    type: gauge
    help: 1100.0030.0001.0009 This attribute defines the status state of the processor
      device. - *******.4.1.674.10892.5.4.1100.30.1.9
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: enabled
      4: userDisabled
      5: biosDisabled
      6: idle
  - name: processorDeviceFamily
    oid: *******.4.1.674.10892.5.4.1100.30.1.10
    type: gauge
    help: 1100.0030.0001.0010 This attribute defines the family of the processor device.
      - *******.4.1.674.10892.5.4.1100.30.1.10
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
    enum_values:
      1: deviceFamilyIsOther
      2: deviceFamilyIsUnknown
      3: deviceFamilyIs8086
      4: deviceFamilyIs80286
      5: deviceFamilyIsIntel386
      6: deviceFamilyIsIntel486
      7: deviceFamilyIs8087
      8: deviceFamilyIs80287
      9: deviceFamilyIs80387
      10: deviceFamilyIs80487
      11: deviceFamilyIsPentium
      12: deviceFamilyIsPentiumPro
      13: deviceFamilyIsPentiumII
      14: deviceFamilyIsPentiumMMX
      15: deviceFamilyIsCeleron
      16: deviceFamilyIsPentiumIIXeon
      17: deviceFamilyIsPentiumIII
      18: deviceFamilyIsPentiumIIIXeon
      19: deviceFamilyIsPentiumIIISpeedStep
      20: deviceFamilyIsItanium
      21: deviceFamilyIsIntelXeon
      22: deviceFamilyIsPentium4
      23: deviceFamilyIsIntelXeonMP
      24: deviceFamilyIsIntelItanium2
      25: deviceFamilyIsK5
      26: deviceFamilyIsK6
      27: deviceFamilyIsK6Dash2
      28: deviceFamilyIsK6Dash3
      29: deviceFamilyIsAMDAthlon
      30: deviceFamilyIsAMD2900
      31: deviceFamilyIsK6Dash2Plus
      32: deviceFamilyIsPowerPC
      33: deviceFamilyIsPowerPC601
      34: deviceFamilyIsPowerPC603
      35: deviceFamilyIsPowerPC603Plus
      36: deviceFamilyIsPowerPC604
      37: deviceFamilyIsPowerPC620
      38: deviceFamilyIsPowerPCx704
      39: deviceFamilyIsPowerPC750
      40: deviceFamilyIsIntelCoreDuo
      41: deviceFamilyIsIntelCoreDuoMobile
      42: deviceFamilyIsIntelCoreSoloMobile
      43: deviceFamilyIsIntelAtom
      48: deviceFamilyIsAlpha
      49: deviceFamilyIsAlpha21064
      50: deviceFamilyIsAlpha21066
      51: deviceFamilyIsAlpha21164
      52: deviceFamilyIsAlpha21164PC
      53: deviceFamilyIsAlpha21164a
      54: deviceFamilyIsAlpha21264
      55: deviceFamilyIsAlpha21364
      56: deviceFamilyIsAMDTurionIIUltraDualMobileM
      57: deviceFamilyIsAMDTurionIIDualMobileM
      58: deviceFamilyIsAMDAthlonIIDualMobileM
      59: deviceFamilyIsAMDOpteron6100
      60: deviceFamilyIsAMDOpteron4100
      61: deviceFamilyIsAMDOpteron6200
      62: deviceFamilyIsAMDOpteron4200
      64: deviceFamilyIsMIPS
      65: deviceFamilyIsMIPSR4000
      66: deviceFamilyIsMIPSR4200
      67: deviceFamilyIsMIPSR4400
      68: deviceFamilyIsMIPSR4600
      69: deviceFamilyIsMIPSR10000
      80: deviceFamilyIsSPARC
      81: deviceFamilyIsSuperSPARC
      82: deviceFamilyIsmicroSPARCII
      83: deviceFamilyIsmicroSPARCIIep
      84: deviceFamilyIsUltraSPARC
      85: deviceFamilyIsUltraSPARCII
      86: deviceFamilyIsUltraSPARCIIi
      87: deviceFamilyIsUltraSPARCIII
      88: deviceFamilyIsUltraSPARCIIIi
      96: deviceFamilyIs68040
      97: deviceFamilyIs68xxx
      98: deviceFamilyIs68000
      99: deviceFamilyIs68010
      100: deviceFamilyIs68020
      101: deviceFamilyIs68030
      112: deviceFamilyIsHobbit
      120: deviceFamilyIsCrusoeTM5000
      121: deviceFamilyIsCrusoeTM3000
      122: deviceFamilyIsEfficeonTM8000
      128: deviceFamilyIsWeitek
      130: deviceFamilyIsIntelCeleronM
      131: deviceFamilyIsAMDAthlon64
      132: deviceFamilyIsAMDOpteron
      133: deviceFamilyIsAMDSempron
      134: deviceFamilyIsAMDTurion64Mobile
      135: deviceFamilyIsDualCoreAMDOpteron
      136: deviceFamilyIsAMDAthlon64X2DualCore
      137: deviceFamilyIsAMDTurion64X2Mobile
      138: deviceFamilyIsQuadCoreAMDOpteron
      139: deviceFamilyIsThirdGenerationAMDOpteron
      140: deviceFamilyIsAMDPhenomFXQuadCore
      141: deviceFamilyIsAMDPhenomX4QuadCore
      142: deviceFamilyIsAMDPhenomX2DualCore
      143: deviceFamilyIsAMDAthlonX2DualCore
      144: deviceFamilyIsPARISC
      145: deviceFamilyIsPARISC8500
      146: deviceFamilyIsPARISC8000
      147: deviceFamilyIsPARISC7300LC
      148: deviceFamilyIsPARISC7200
      149: deviceFamilyIsPARISC7100LC
      150: deviceFamilyIsPARISC7100
      160: deviceFamilyIsV30
      161: deviceFamilyIsQuadCoreIntelXeon3200
      162: deviceFamilyIsDualCoreIntelXeon3000
      163: deviceFamilyIsQuadCoreIntelXeon5300
      164: deviceFamilyIsDualCoreIntelXeon5100
      165: deviceFamilyIsDualCoreIntelXeon5000
      166: deviceFamilyIsDualCoreIntelXeonLV
      167: deviceFamilyIsDualCoreIntelXeonULV
      168: deviceFamilyIsDualCoreIntelXeon7100
      169: deviceFamilyIsQuadCoreIntelXeon5400
      170: deviceFamilyIsQuadCoreIntelXeon
      171: deviceFamilyIsDualCoreIntelXeon5200
      172: deviceFamilyIsDualCoreIntelXeon7200
      173: deviceFamilyIsQuadCoreIntelXeon7300
      174: deviceFamilyIsQuadCoreIntelXeon7400
      175: deviceFamilyIsMultiCoreIntelXeon7400
      176: deviceFamilyIsM1
      177: deviceFamilyIsM2
      179: deviceFamilyIsIntelPentium4HT
      180: deviceFamilyIsAS400
      182: deviceFamilyIsAMDAthlonXP
      183: deviceFamilyIsAMDAthlonMP
      184: deviceFamilyIsAMDDuron
      185: deviceFamilyIsIntelPentiumM
      186: deviceFamilyIsIntelCeleronD
      187: deviceFamilyIsIntelPentiumD
      188: deviceFamilyIsIntelPentiumExtreme
      189: deviceFamilyIsIntelCoreSolo
      190: deviceFamilyIsIntelCore2
      191: deviceFamilyIsIntelCore2Duo
      192: deviceFamilyIsIntelCore2Solo
      193: deviceFamilyIsIntelCore2Extreme
      194: deviceFamilyIsIntelCore2Quad
      195: deviceFamilyIsIntelCore2ExtremeMobile
      196: deviceFamilyIsIntelCore2DuoMobile
      197: deviceFamilyIsIntelCore2SoloMobile
      198: deviceFamilyIsIntelCorei7
      199: deviceFamilyIsDualCoreIntelCeleron
      200: deviceFamilyIsIBM390
      201: deviceFamilyIsG4
      202: deviceFamilyIsG5
      203: deviceFamilyIsESA390G6
      204: deviceFamilyIszArchitectur
      205: deviceFamilyIsIntelCorei5
      206: deviceFamilyIsIntelCorei3
      210: deviceFamilyIsVIAC7M
      211: deviceFamilyIsVIAC7D
      212: deviceFamilyIsVIAC7
      213: deviceFamilyIsVIAEden
      214: deviceFamilyIsMultiCoreIntelXeon
      215: deviceFamilyIsDualCoreIntelXeon3xxx
      216: deviceFamilyIsQuadCoreIntelXeon3xxx
      217: deviceFamilyIsVIANano
      218: deviceFamilyIsDualCoreIntelXeon5xxx
      219: deviceFamilyIsQuadCoreIntelXeon5xxx
      221: deviceFamilyIsDualCoreIntelXeon7xxx
      222: deviceFamilyIsQuadCoreIntelXeon7xxx
      223: deviceFamilyIsMultiCoreIntelXeon7xxx
      224: deviceFamilyIsMultiCoreIntelXeon3400
      230: deviceFamilyIsEmbeddedAMDOpertonQuadCore
      231: deviceFamilyIsAMDPhenomTripleCore
      232: deviceFamilyIsAMDTurionUltraDualCoreMobile
      233: deviceFamilyIsAMDTurionDualCoreMobile
      234: deviceFamilyIsAMDAthlonDualCore
      235: deviceFamilyIsAMDSempronSI
      236: deviceFamilyIsAMDPhenomII
      237: deviceFamilyIsAMDAthlonII
      238: deviceFamilyIsSixCoreAMDOpteron
      239: deviceFamilyIsAMDSempronM
      250: deviceFamilyIsi860
      251: deviceFamilyIsi960
  - name: processorDeviceMaximumSpeed
    oid: *******.4.1.674.10892.5.4.1100.30.1.11
    type: gauge
    help: 1100.0030.0001.0011 This attribute defines the maximum speed of the processor
      device in MHz - *******.4.1.674.10892.5.4.1100.30.1.11
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceCurrentSpeed
    oid: *******.4.1.674.10892.5.4.1100.30.1.12
    type: gauge
    help: 1100.0030.0001.0012 This attribute defines the current speed of the processor
      device in MHz - *******.4.1.674.10892.5.4.1100.30.1.12
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceExternalClockSpeed
    oid: *******.4.1.674.10892.5.4.1100.30.1.13
    type: gauge
    help: 1100.0030.0001.0013 This attribute defines the speed of the external clock
      for the processor device in MHz - *******.4.1.674.10892.5.4.1100.30.1.13
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceVoltage
    oid: *******.4.1.674.10892.5.4.1100.30.1.14
    type: gauge
    help: 1100.0030.0001.0014 This attribute defines the voltage powering the processor
      device in millivolts - *******.4.1.674.10892.5.4.1100.30.1.14
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceVersionName
    oid: *******.4.1.674.10892.5.4.1100.30.1.16
    type: OctetString
    help: 1100.0030.0001.0016 This attribute defines the version of the processor
      device - *******.4.1.674.10892.5.4.1100.30.1.16
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceCoreCount
    oid: *******.4.1.674.10892.5.4.1100.30.1.17
    type: gauge
    help: 1100.0030.0001.0017 This attribute defines the number of processor cores
      detected for the processor device. - *******.4.1.674.10892.5.4.1100.30.1.17
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceCoreEnabledCount
    oid: *******.4.1.674.10892.5.4.1100.30.1.18
    type: gauge
    help: 1100.0030.0001.0018 This attribute defines the number of processor cores
      enabled for the processor device. - *******.4.1.674.10892.5.4.1100.30.1.18
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceThreadCount
    oid: *******.4.1.674.10892.5.4.1100.30.1.19
    type: gauge
    help: 1100.0030.0001.0019 This attribute defines the number of processor threads
      detected for the processor device. - *******.4.1.674.10892.5.4.1100.30.1.19
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceCharacteristics
    oid: *******.4.1.674.10892.5.4.1100.30.1.20
    type: gauge
    help: 1100.0030.0001.0020 This attribute defines characteristics of the processor
      device - *******.4.1.674.10892.5.4.1100.30.1.20
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceExtendedCapabilities
    oid: *******.4.1.674.10892.5.4.1100.30.1.21
    type: gauge
    help: 1100.0030.0001.0021 This attribute defines extended capabilities of the
      processor device - *******.4.1.674.10892.5.4.1100.30.1.21
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceExtendedSettings
    oid: *******.4.1.674.10892.5.4.1100.30.1.22
    type: gauge
    help: 1100.0030.0001.0022 This attribute defines extended settings of the processor
      device - *******.4.1.674.10892.5.4.1100.30.1.22
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceBrandName
    oid: *******.4.1.674.10892.5.4.1100.30.1.23
    type: OctetString
    help: 1100.0030.0001.0023 This attribute defines the brand of the processor device.
      - *******.4.1.674.10892.5.4.1100.30.1.23
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceFQDD
    oid: *******.4.1.674.10892.5.4.1100.30.1.26
    type: OctetString
    help: 1100.0030.0001.0026 Fully qualified device descriptor (FQDD) of the processor
      device. - *******.4.1.674.10892.5.4.1100.30.1.26
    indexes:
    - labelname: processorDevicechassisIndex
      type: gauge
    - labelname: processorDeviceIndex
      type: gauge
  - name: processorDeviceStatusChassisIndex
    oid: *******.4.1.674.10892.5.4.1100.32.1.1
    type: gauge
    help: 1100.0032.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.1100.32.1.1
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
  - name: processorDeviceStatusIndex
    oid: *******.4.1.674.10892.5.4.1100.32.1.2
    type: gauge
    help: 1100.0032.0001.0002 This attribute defines the index (one based) of the
      processor device status probe. - *******.4.1.674.10892.5.4.1100.32.1.2
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
  - name: processorDeviceStatusStateCapabilities
    oid: *******.4.1.674.10892.5.4.1100.32.1.3
    type: gauge
    help: 1100.0032.0001.0003 This attribute defines the state capabilities of the
      processor device status probe. - *******.4.1.674.10892.5.4.1100.32.1.3
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: processorDeviceStatusStateSettings
    oid: *******.4.1.674.10892.5.4.1100.32.1.4
    type: gauge
    help: 1100.0032.0001.0004 This attribute defines the state settings of the processor
      device status probe. - *******.4.1.674.10892.5.4.1100.32.1.4
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: processorDeviceStatusStatus
    oid: *******.4.1.674.10892.5.4.1100.32.1.5
    type: gauge
    help: 1100.0032.0001.0005 This attribute defines the status of the processor device
      status probe - *******.4.1.674.10892.5.4.1100.32.1.5
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: processorDeviceStatusReading
    oid: *******.4.1.674.10892.5.4.1100.32.1.6
    type: gauge
    help: 1100.0032.0001.0006 This attribute defines the reading of the processor
      device status probe. - *******.4.1.674.10892.5.4.1100.32.1.6
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
    enum_values:
      1: internalError
      2: thermalTrip
      32: configurationError
      128: processorPresent
      256: processorDisabled
      512: terminatorPresent
      1024: processorThrottled
  - name: processorDeviceStatusLocationName
    oid: *******.4.1.674.10892.5.4.1100.32.1.7
    type: OctetString
    help: 1100.0032.0001.0007 This attribute defines the location name of the processor
      device status probe. - *******.4.1.674.10892.5.4.1100.32.1.7
    indexes:
    - labelname: processorDeviceStatusChassisIndex
      type: gauge
    - labelname: processorDeviceStatusIndex
      type: gauge
  - name: memoryDevicechassisIndex
    oid: *******.4.1.674.10892.5.4.1100.50.1.1
    type: gauge
    help: 1100.0050.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.1100.50.1.1
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceIndex
    oid: *******.4.1.674.10892.5.4.1100.50.1.2
    type: gauge
    help: 1100.0050.0001.0002 This attribute defines the index (one based) of the
      memory device. - *******.4.1.674.10892.5.4.1100.50.1.2
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceStateCapabilities
    oid: *******.4.1.674.10892.5.4.1100.50.1.3
    type: gauge
    help: 1100.0050.0001.0003 This attribute defines the state capabilities of the
      memory device. - *******.4.1.674.10892.5.4.1100.50.1.3
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: memoryDeviceStateSettings
    oid: *******.4.1.674.10892.5.4.1100.50.1.4
    type: gauge
    help: 1100.0050.0001.0004 This attribute defines the state settings of the memory
      device. - *******.4.1.674.10892.5.4.1100.50.1.4
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: memoryDeviceStatus
    oid: *******.4.1.674.10892.5.4.1100.50.1.5
    type: gauge
    help: 1100.0050.0001.0005 This attribute defines the status of the memory device.
      - *******.4.1.674.10892.5.4.1100.50.1.5
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: memoryDeviceType
    oid: *******.4.1.674.10892.5.4.1100.50.1.7
    type: EnumAsInfo
    help: 1100.0050.0001.0007 This attribute defines the type of the memory device.
      - *******.4.1.674.10892.5.4.1100.50.1.7
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
    enum_values:
      1: deviceTypeIsOther
      2: deviceTypeIsUnknown
      3: deviceTypeIsDRAM
      4: deviceTypeIsEDRAM
      5: deviceTypeIsVRAM
      6: deviceTypeIsSRAM
      7: deviceTypeIsRAM
      8: deviceTypeIsROM
      9: deviceTypeIsFLASH
      10: deviceTypeIsEEPROM
      11: deviceTypeIsFEPROM
      12: deviceTypeIsEPROM
      13: deviceTypeIsCDRAM
      14: deviceTypeIs3DRAM
      15: deviceTypeIsSDRAM
      16: deviceTypeIsSGRAM
      17: deviceTypeIsRDRAM
      18: deviceTypeIsDDR
      19: deviceTypeIsDDR2
      20: deviceTypeIsDDR2FBDIMM
      24: deviceTypeIsDDR3
      25: deviceTypeIsFBD2
      26: deviceTypeIsDDR4
  - name: memoryDeviceLocationName
    oid: *******.4.1.674.10892.5.4.1100.50.1.8
    type: DisplayString
    help: 1100.0050.0001.0008 This attribute defines the location of the memory device.
      - *******.4.1.674.10892.5.4.1100.50.1.8
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceBankLocationName
    oid: *******.4.1.674.10892.5.4.1100.50.1.10
    type: DisplayString
    help: 1100.0050.0001.0010 This attribute defines the location of the bank for
      the memory device. - *******.4.1.674.10892.5.4.1100.50.1.10
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceSize
    oid: *******.4.1.674.10892.5.4.1100.50.1.14
    type: gauge
    help: 1100.0050.0001.0014 This attribute defines the size in KBytes of the memory
      device - *******.4.1.674.10892.5.4.1100.50.1.14
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceSpeed
    oid: *******.4.1.674.10892.5.4.1100.50.1.15
    type: gauge
    help: 1100.0050.0001.0015 This attribute defines the maximum capable speed in
      megahertz (MHz) of the memory device - *******.4.1.674.10892.5.4.1100.50.1.15
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceManufacturerName
    oid: *******.4.1.674.10892.5.4.1100.50.1.21
    type: DisplayString
    help: 1100.0050.0001.0021 This attribute defines the manufacturer of the memory
      device. - *******.4.1.674.10892.5.4.1100.50.1.21
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDevicePartNumberName
    oid: *******.4.1.674.10892.5.4.1100.50.1.22
    type: DisplayString
    help: 1100.0050.0001.0022 This attribute defines the manufacturer's part number
      for the memory device. - *******.4.1.674.10892.5.4.1100.50.1.22
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceSerialNumberName
    oid: *******.4.1.674.10892.5.4.1100.50.1.23
    type: DisplayString
    help: 1100.0050.0001.0023 This attribute defines the serial number of the memory
      device. - *******.4.1.674.10892.5.4.1100.50.1.23
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceFQDD
    oid: *******.4.1.674.10892.5.4.1100.50.1.26
    type: OctetString
    help: 1100.0050.0001.0026 Fully qualified device descriptor (FQDD) of the memory
      device. - *******.4.1.674.10892.5.4.1100.50.1.26
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: memoryDeviceCurrentOperatingSpeed
    oid: *******.4.1.674.10892.5.4.1100.50.1.27
    type: gauge
    help: 1100.0050.0001.0027 This attribute defines the current operating speed in
      megahertz (MHz) of the memory device - *******.4.1.674.10892.5.4.1100.50.1.27
    indexes:
    - labelname: memoryDevicechassisIndex
      type: gauge
    - labelname: memoryDeviceIndex
      type: gauge
  - name: pCIDevicechassisIndex
    oid: *******.4.1.674.10892.5.4.1100.80.1.1
    type: gauge
    help: 1100.0080.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.1100.80.1.1
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: pCIDeviceIndex
    oid: *******.4.1.674.10892.5.4.1100.80.1.2
    type: gauge
    help: 1100.0080.0001.0002 This attribute defines the index (one based) of the
      PCI device. - *******.4.1.674.10892.5.4.1100.80.1.2
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: pCIDeviceStateCapabilities
    oid: *******.4.1.674.10892.5.4.1100.80.1.3
    type: gauge
    help: 1100.0080.0001.0003 This attribute defines the state capabilities of the
      PCI device. - *******.4.1.674.10892.5.4.1100.80.1.3
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: pCIDeviceStateSettings
    oid: *******.4.1.674.10892.5.4.1100.80.1.4
    type: gauge
    help: 1100.0080.0001.0004 This attribute defines the state settings of the PCI
      device. - *******.4.1.674.10892.5.4.1100.80.1.4
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: pCIDeviceStatus
    oid: *******.4.1.674.10892.5.4.1100.80.1.5
    type: gauge
    help: 1100.0080.0001.0005 This attribute defines the status of the PCI device.
      - *******.4.1.674.10892.5.4.1100.80.1.5
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: pCIDeviceDataBusWidth
    oid: *******.4.1.674.10892.5.4.1100.80.1.7
    type: gauge
    help: 1100.0080.0001.0007 This attribute defines the width of the data bus of
      the PCI device - *******.4.1.674.10892.5.4.1100.80.1.7
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: pCIDeviceManufacturerName
    oid: *******.4.1.674.10892.5.4.1100.80.1.8
    type: DisplayString
    help: 1100.0080.0001.0008 This attribute defines the name of the manufacturer
      of the PCI device. - *******.4.1.674.10892.5.4.1100.80.1.8
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: pCIDeviceDescriptionName
    oid: *******.4.1.674.10892.5.4.1100.80.1.9
    type: DisplayString
    help: 1100.0080.0001.0009 This attribute defines the description of the PCI device.
      - *******.4.1.674.10892.5.4.1100.80.1.9
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: pCIDeviceFQDD
    oid: *******.4.1.674.10892.5.4.1100.80.1.12
    type: DisplayString
    help: 1100.0080.0001.0012 Fully qualified device descriptor (FQDD) of the PCI
      device. - *******.4.1.674.10892.5.4.1100.80.1.12
    indexes:
    - labelname: pCIDevicechassisIndex
      type: gauge
    - labelname: pCIDeviceIndex
      type: gauge
  - name: networkDeviceChassisIndex
    oid: *******.4.1.674.10892.5.4.1100.90.1.1
    type: gauge
    help: 1100.0090.0001.0001 This attribute defines the index (one based) of the
      system chassis that contains the network device. - *******.4.1.674.10892.5.4.1100.90.1.1
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceIndex
    oid: *******.4.1.674.10892.5.4.1100.90.1.2
    type: gauge
    help: 1100.0090.0001.0002 This attribute defines the index (one based) of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.2
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceStatus
    oid: *******.4.1.674.10892.5.4.1100.90.1.3
    type: gauge
    help: 1100.0090.0001.0003 This attribute defines the status of the network device.
      - *******.4.1.674.10892.5.4.1100.90.1.3
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: networkDeviceConnectionStatus
    oid: *******.4.1.674.10892.5.4.1100.90.1.4
    type: gauge
    help: 1100.0090.0001.0004 This attribute defines the connection status of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.4
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
    enum_values:
      1: connected
      2: disconnected
      3: driverBad
      4: driverDisabled
      10: hardwareInitalizing
      11: hardwareResetting
      12: hardwareClosing
      13: hardwareNotReady
  - name: networkDeviceProductName
    oid: *******.4.1.674.10892.5.4.1100.90.1.6
    type: DisplayString
    help: 1100.0090.0001.0006 This attribute defines the product name of the network
      device. - *******.4.1.674.10892.5.4.1100.90.1.6
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceVendorName
    oid: *******.4.1.674.10892.5.4.1100.90.1.7
    type: DisplayString
    help: 1100.0090.0001.0007 This attribute defines the name of the vendor of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.7
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceCurrentMACAddress
    oid: *******.4.1.674.10892.5.4.1100.90.1.15
    type: OctetString
    help: 1100.0090.0001.0015 This attribute defines the current MAC address of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.15
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDevicePermanentMACAddress
    oid: *******.4.1.674.10892.5.4.1100.90.1.16
    type: OctetString
    help: 1100.0090.0001.0016 This attribute defines the permanent MAC address of
      the network device. - *******.4.1.674.10892.5.4.1100.90.1.16
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDevicePCIBusNumber
    oid: *******.4.1.674.10892.5.4.1100.90.1.17
    type: gauge
    help: 1100.0090.0001.0017 This attribute defines the PCI bus number of the network
      device. - *******.4.1.674.10892.5.4.1100.90.1.17
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDevicePCIDeviceNumber
    oid: *******.4.1.674.10892.5.4.1100.90.1.18
    type: gauge
    help: 1100.0090.0001.0018 This attribute defines the PCI device number of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.18
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDevicePCIFunctionNumber
    oid: *******.4.1.674.10892.5.4.1100.90.1.19
    type: gauge
    help: 1100.0090.0001.0019 This attribute defines the PCI function number of the
      network device. - *******.4.1.674.10892.5.4.1100.90.1.19
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceTOECapabilityFlags
    oid: *******.4.1.674.10892.5.4.1100.90.1.23
    type: gauge
    help: 1100.0090.0001.0023 This attribute defines the TCP/IP Offload Engine (TOE)
      capability flags of the network device. - *******.4.1.674.10892.5.4.1100.90.1.23
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
    enum_values:
      1: unknown
      2: available
      4: notAvailable
      8: cannotBeDetermined
      16: driverNotResponding
  - name: networkDeviceiSCSICapabilityFlags
    oid: *******.4.1.674.10892.5.4.1100.90.1.27
    type: gauge
    help: 1100.0090.0001.0027 This attribute defines the Internet Small Computer System
      Interface (iSCSI) capability flags of the network device. - *******.4.1.674.10892.5.4.1100.90.1.27
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
    enum_values:
      1: unknown
      2: available
      4: notAvailable
      8: cannotBeDetermined
      16: driverNotResponding
  - name: networkDeviceiSCSIEnabled
    oid: *******.4.1.674.10892.5.4.1100.90.1.28
    type: gauge
    help: 1100.0090.0001.0028 This attribute defines if iSCSI is enabled for the network
      device. - *******.4.1.674.10892.5.4.1100.90.1.28
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: networkDeviceCapabilities
    oid: *******.4.1.674.10892.5.4.1100.90.1.29
    type: gauge
    help: 1100.0090.0001.0029 This attribute defines the capabilities of the network
      device - *******.4.1.674.10892.5.4.1100.90.1.29
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
    enum_values:
      1: supported
      2: toe
      4: iscsiOffload
      8: fcoeOffload
  - name: networkDeviceFQDD
    oid: *******.4.1.674.10892.5.4.1100.90.1.30
    type: DisplayString
    help: 1100.0090.0001.0030 Fully qualified device descriptor (FQDD) of the network
      device. - *******.4.1.674.10892.5.4.1100.90.1.30
    indexes:
    - labelname: networkDeviceChassisIndex
      type: gauge
    - labelname: networkDeviceIndex
      type: gauge
  - name: fruChassisIndex
    oid: *******.4.1.674.10892.5.4.2000.10.1.1
    type: gauge
    help: 2000.0010.0001.0001 This attribute defines the index (one based) of the
      system chassis containing the field replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.1
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruIndex
    oid: *******.4.1.674.10892.5.4.2000.10.1.2
    type: gauge
    help: 2000.0010.0001.0002 This attribute defines the index (one based) of the
      field replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.2
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruInformationStatus
    oid: *******.4.1.674.10892.5.4.2000.10.1.3
    type: gauge
    help: 2000.0010.0001.0003 This attribute defines the status of the field replaceable
      unit information. - *******.4.1.674.10892.5.4.2000.10.1.3
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: fruManufacturerName
    oid: *******.4.1.674.10892.5.4.2000.10.1.6
    type: DisplayString
    help: 2000.0010.0001.0006 This attribute defines the manufacturer of the field
      replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.6
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruSerialNumberName
    oid: *******.4.1.674.10892.5.4.2000.10.1.7
    type: DisplayString
    help: 2000.0010.0001.0007 This attribute defines the serial number of the field
      replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.7
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruPartNumberName
    oid: *******.4.1.674.10892.5.4.2000.10.1.8
    type: DisplayString
    help: 2000.0010.0001.0008 This attribute defines the part number of the field
      replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.8
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruRevisionName
    oid: *******.4.1.674.10892.5.4.2000.10.1.9
    type: DisplayString
    help: 2000.0010.0001.0009 This attribute defines the revision of the field replaceable
      unit. - *******.4.1.674.10892.5.4.2000.10.1.9
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: fruFQDD
    oid: *******.4.1.674.10892.5.4.2000.10.1.12
    type: DisplayString
    help: 2000.0010.0001.0012 Fully qualified device descriptor (FQDD) of the field
      replaceable unit. - *******.4.1.674.10892.5.4.2000.10.1.12
    indexes:
    - labelname: fruChassisIndex
      type: gauge
    - labelname: fruIndex
      type: gauge
  - name: numEventLogEntries
    oid: *******.4.1.674.10892.5.4.300.1
    type: gauge
    help: 0300.0001.0000 This attribute provides the number of entries currently in
      the eventLogTable. - *******.4.1.674.10892.5.4.300.1
  - name: systemBIOSchassisIndex
    oid: *******.4.1.674.10892.5.4.300.50.1.1
    type: gauge
    help: 0300.0050.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.300.50.1.1
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
  - name: systemBIOSIndex
    oid: *******.4.1.674.10892.5.4.300.50.1.2
    type: gauge
    help: 0300.0050.0001.0002 This attribute defines the index (one based) of the
      system BIOS. - *******.4.1.674.10892.5.4.300.50.1.2
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
  - name: systemBIOSStateCapabilities
    oid: *******.4.1.674.10892.5.4.300.50.1.3
    type: gauge
    help: 0300.0050.0001.0003 This attribute defines the state capabilities of the
      system BIOS. - *******.4.1.674.10892.5.4.300.50.1.3
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: systemBIOSStateSettings
    oid: *******.4.1.674.10892.5.4.300.50.1.4
    type: gauge
    help: 0300.0050.0001.0004 This attribute defines the state settings of the system
      BIOS. - *******.4.1.674.10892.5.4.300.50.1.4
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: systemBIOSStatus
    oid: *******.4.1.674.10892.5.4.300.50.1.5
    type: gauge
    help: 0300.0050.0001.0005 This attribute defines the status of the system BIOS.
      - *******.4.1.674.10892.5.4.300.50.1.5
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: systemBIOSReleaseDateName
    oid: *******.4.1.674.10892.5.4.300.50.1.7
    type: OctetString
    help: 0300.0050.0001.0007 This attribute defines the release date name of the
      system BIOS. - *******.4.1.674.10892.5.4.300.50.1.7
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
  - name: systemBIOSVersionName
    oid: *******.4.1.674.10892.5.4.300.50.1.8
    type: DisplayString
    help: 0300.0050.0001.0008 This attribute defines the version name of the system
      BIOS. - *******.4.1.674.10892.5.4.300.50.1.8
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
  - name: systemBIOSManufacturerName
    oid: *******.4.1.674.10892.5.4.300.50.1.11
    type: DisplayString
    help: 0300.0050.0001.0011 This attribute defines the name of the manufacturer
      of the system BIOS. - *******.4.1.674.10892.5.4.300.50.1.11
    indexes:
    - labelname: systemBIOSchassisIndex
      type: gauge
    - labelname: systemBIOSIndex
      type: gauge
  - name: firmwarechassisIndex
    oid: *******.4.1.674.10892.5.4.300.60.1.1
    type: gauge
    help: 0300.0060.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.300.60.1.1
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: firmwareIndex
    oid: *******.4.1.674.10892.5.4.300.60.1.2
    type: gauge
    help: 0300.0060.0001.0002 This attribute defines the index (one based) of the
      firmware. - *******.4.1.674.10892.5.4.300.60.1.2
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: firmwareStateCapabilities
    oid: *******.4.1.674.10892.5.4.300.60.1.3
    type: gauge
    help: 0300.0060.0001.0003 This attribute defines the state capabilities of the
      firmware. - *******.4.1.674.10892.5.4.300.60.1.3
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: firmwareStateSettings
    oid: *******.4.1.674.10892.5.4.300.60.1.4
    type: gauge
    help: 0300.0060.0001.0004 This attribute defines the state settings of the firmware.
      - *******.4.1.674.10892.5.4.300.60.1.4
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: firmwareStatus
    oid: *******.4.1.674.10892.5.4.300.60.1.5
    type: gauge
    help: 0300.0060.0001.0005 This attribute defines the status of the firmware. -
      *******.4.1.674.10892.5.4.300.60.1.5
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: firmwareSize
    oid: *******.4.1.674.10892.5.4.300.60.1.6
    type: gauge
    help: 0300.0060.0001.0006 This attribute defines the image size of the firmware
      in KBytes - *******.4.1.674.10892.5.4.300.60.1.6
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: firmwareType
    oid: *******.4.1.674.10892.5.4.300.60.1.7
    type: gauge
    help: 0300.0060.0001.0007 This attribute defines the type of firmware. - *******.4.1.674.10892.5.4.300.60.1.7
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      20: lifecycleController
      21: iDRAC7
      22: iDRAC8
  - name: firmwareTypeName
    oid: *******.4.1.674.10892.5.4.300.60.1.8
    type: DisplayString
    help: 0300.0060.0001.0008 This attribute defines the type name of the firmware.
      - *******.4.1.674.10892.5.4.300.60.1.8
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: firmwareUpdateCapabilities
    oid: *******.4.1.674.10892.5.4.300.60.1.9
    type: gauge
    help: 0300.0060.0001.0009 This attribute defines the bitmap of supported methods
      for firmware update. - *******.4.1.674.10892.5.4.300.60.1.9
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: firmwareVersionName
    oid: *******.4.1.674.10892.5.4.300.60.1.11
    type: DisplayString
    help: 0300.0060.0001.0011 This attribute defines the version of the firmware.
      - *******.4.1.674.10892.5.4.300.60.1.11
    indexes:
    - labelname: firmwarechassisIndex
      type: gauge
    - labelname: firmwareIndex
      type: gauge
  - name: intrusionchassisIndex
    oid: *******.4.1.674.10892.5.4.300.70.1.1
    type: gauge
    help: 0300.0070.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.300.70.1.1
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
  - name: intrusionIndex
    oid: *******.4.1.674.10892.5.4.300.70.1.2
    type: gauge
    help: 0300.0070.0001.0002 This attribute defines the index (one based) of the
      intrusion sensor. - *******.4.1.674.10892.5.4.300.70.1.2
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
  - name: intrusionStateCapabilities
    oid: *******.4.1.674.10892.5.4.300.70.1.3
    type: gauge
    help: 0300.0070.0001.0003 This attribute defines the state capabilities of the
      intrusion sensor. - *******.4.1.674.10892.5.4.300.70.1.3
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: intrusionStateSettings
    oid: *******.4.1.674.10892.5.4.300.70.1.4
    type: gauge
    help: 0300.0070.0001.0004 This attribute defines the state settings of the intrusion
      sensor. - *******.4.1.674.10892.5.4.300.70.1.4
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: intrusionStatus
    oid: *******.4.1.674.10892.5.4.300.70.1.5
    type: gauge
    help: 0300.0070.0001.0005 This attribute defines the status of the intrusion sensor.
      - *******.4.1.674.10892.5.4.300.70.1.5
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: intrusionReading
    oid: *******.4.1.674.10892.5.4.300.70.1.6
    type: gauge
    help: 0300.0070.0001.0006 This attribute defines the reading of the intrusion
      sensor. - *******.4.1.674.10892.5.4.300.70.1.6
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
    enum_values:
      1: chassisNotBreached
      2: chassisBreached
      3: chassisBreachedPrior
      4: chassisBreachSensorFailure
  - name: intrusionType
    oid: *******.4.1.674.10892.5.4.300.70.1.7
    type: gauge
    help: 0300.0070.0001.0007 This attribute defines the type of the intrusion sensor.
      - *******.4.1.674.10892.5.4.300.70.1.7
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
    enum_values:
      1: chassisBreachDetectionWhenPowerON
      2: chassisBreachDetectionWhenPowerOFF
  - name: intrusionLocationName
    oid: *******.4.1.674.10892.5.4.300.70.1.8
    type: DisplayString
    help: 0300.0070.0001.0008 This attribute defines the location of the intrusion
      sensor. - *******.4.1.674.10892.5.4.300.70.1.8
    indexes:
    - labelname: intrusionchassisIndex
      type: gauge
    - labelname: intrusionIndex
      type: gauge
  - name: powerSupplychassisIndex
    oid: *******.4.1.674.10892.5.4.600.12.1.1
    type: gauge
    help: 0600.0012.0001.0001 This attribute defines the index (one based) of the
      system chassis. - *******.4.1.674.10892.5.4.600.12.1.1
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyIndex
    oid: *******.4.1.674.10892.5.4.600.12.1.2
    type: gauge
    help: 0600.0012.0001.0002 This attribute defines the index (one based) of the
      power supply. - *******.4.1.674.10892.5.4.600.12.1.2
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyStateCapabilitiesUnique
    oid: *******.4.1.674.10892.5.4.600.12.1.3
    type: gauge
    help: 0600.0012.0001.0003 This attribute defines the state capabilities of the
      power supply. - *******.4.1.674.10892.5.4.600.12.1.3
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: unknown
      2: onlineCapable
      4: notReadyCapable
  - name: powerSupplyStateSettingsUnique
    oid: *******.4.1.674.10892.5.4.600.12.1.4
    type: gauge
    help: 0600.0012.0001.0004 This attribute defines the state settings of the power
      supply. - *******.4.1.674.10892.5.4.600.12.1.4
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: unknown
      2: onLine
      4: notReady
      8: fanFailure
      10: onlineAndFanFailure
      16: powerSupplyIsON
      32: powerSupplyIsOK
      64: acSwitchIsON
      66: onlineandAcSwitchIsON
      128: acPowerIsON
      130: onlineAndAcPowerIsON
      210: onlineAndPredictiveFailure
      242: acPowerAndSwitchAreOnPowerSupplyIsOnIsOkAndOnline
  - name: powerSupplyStatus
    oid: *******.4.1.674.10892.5.4.600.12.1.5
    type: gauge
    help: 0600.0012.0001.0005 This attribute defines the status of the power supply.
      - *******.4.1.674.10892.5.4.600.12.1.5
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: powerSupplyOutputWatts
    oid: *******.4.1.674.10892.5.4.600.12.1.6
    type: gauge
    help: 0600.0012.0001.0006 This attribute defines the maximum sustained output
      wattage of the power supply (in tenths of Watts). - *******.4.1.674.10892.5.4.600.12.1.6
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyType
    oid: *******.4.1.674.10892.5.4.600.12.1.7
    type: gauge
    help: 0600.0012.0001.0007 This attribute defines the type of the power supply.
      - *******.4.1.674.10892.5.4.600.12.1.7
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: powerSupplyTypeIsOther
      2: powerSupplyTypeIsUnknown
      3: powerSupplyTypeIsLinear
      4: powerSupplyTypeIsSwitching
      5: powerSupplyTypeIsBattery
      6: powerSupplyTypeIsUPS
      7: powerSupplyTypeIsConverter
      8: powerSupplyTypeIsRegulator
      9: powerSupplyTypeIsAC
      10: powerSupplyTypeIsDC
      11: powerSupplyTypeIsVRM
  - name: powerSupplyLocationName
    oid: *******.4.1.674.10892.5.4.600.12.1.8
    type: DisplayString
    help: 0600.0012.0001.0008 This attribute defines the location of the power supply.
      - *******.4.1.674.10892.5.4.600.12.1.8
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyMaximumInputVoltage
    oid: *******.4.1.674.10892.5.4.600.12.1.9
    type: gauge
    help: 0600.0012.0001.0009 This attribute defines the maximum input voltage of
      the power supply (in Volts). - *******.4.1.674.10892.5.4.600.12.1.9
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplypowerUnitIndexReference
    oid: *******.4.1.674.10892.5.4.600.12.1.10
    type: gauge
    help: 0600.0012.0001.0010 This attribute defines the index to the associated power
      unit if the power supply is part of a power unit. - *******.4.1.674.10892.5.4.600.12.1.10
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplySensorState
    oid: *******.4.1.674.10892.5.4.600.12.1.11
    type: gauge
    help: 0600.0012.0001.0011 This attribute defines the state reported by the power
      supply sensor - *******.4.1.674.10892.5.4.600.12.1.11
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: presenceDetected
      2: psFailureDetected
      4: predictiveFailure
      8: psACLost
      16: acLostOrOutOfRange
      32: acOutOfRangeButPresent
      64: configurationError
  - name: powerSupplyConfigurationErrorType
    oid: *******.4.1.674.10892.5.4.600.12.1.12
    type: gauge
    help: 0600.0012.0001.0012 This attribute defines the type of configuration error
      reported by the power supply sensor - *******.4.1.674.10892.5.4.600.12.1.12
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
    enum_values:
      1: vendorMismatch
      2: revisionMismatch
      3: processorMissing
  - name: powerSupplyPowerMonitorCapable
    oid: *******.4.1.674.10892.5.4.600.12.1.13
    type: gauge
    help: 0600.0012.0001.0013 This attribute defines a boolean value that reports
      whether the power supply is capable of monitoring power consumption. - *******.4.1.674.10892.5.4.600.12.1.13
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyRatedInputWattage
    oid: *******.4.1.674.10892.5.4.600.12.1.14
    type: gauge
    help: 0600.0012.0001.0014 This attribute defines the rated input wattage of the
      power supply (in tenths of Watts). - *******.4.1.674.10892.5.4.600.12.1.14
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyFQDD
    oid: *******.4.1.674.10892.5.4.600.12.1.15
    type: OctetString
    help: 0600.0012.0001.0015 Fully qualified device descriptor (FQDD) of the power
      supply. - *******.4.1.674.10892.5.4.600.12.1.15
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: powerSupplyCurrentInputVoltage
    oid: *******.4.1.674.10892.5.4.600.12.1.16
    type: gauge
    help: 0600.0012.0001.0016 This attribute defines the current input voltage to
      the power supply (in Volts). - *******.4.1.674.10892.5.4.600.12.1.16
    indexes:
    - labelname: powerSupplychassisIndex
      type: gauge
    - labelname: powerSupplyIndex
      type: gauge
  - name: systemBatteryChassisIndex
    oid: *******.4.1.674.10892.5.4.600.50.1.1
    type: gauge
    help: 0600.0050.0001.0001 This attribute defines the index (one based) of the
      system chassis that contains the battery. - *******.4.1.674.10892.5.4.600.50.1.1
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
  - name: systemBatteryIndex
    oid: *******.4.1.674.10892.5.4.600.50.1.2
    type: gauge
    help: 0600.0050.0001.0002 This attribute defines the index (one based) of the
      battery. - *******.4.1.674.10892.5.4.600.50.1.2
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
  - name: systemBatteryStateCapabilities
    oid: *******.4.1.674.10892.5.4.600.50.1.3
    type: gauge
    help: 0600.0050.0001.0003 This attribute defines the state capabilities of the
      battery. - *******.4.1.674.10892.5.4.600.50.1.3
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: systemBatteryStateSettings
    oid: *******.4.1.674.10892.5.4.600.50.1.4
    type: gauge
    help: 0600.0050.0001.0004 This attribute defines the state settings of the battery.
      - *******.4.1.674.10892.5.4.600.50.1.4
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: systemBatteryStatus
    oid: *******.4.1.674.10892.5.4.600.50.1.5
    type: gauge
    help: 0600.0050.0001.0005 This attribute defines the status of the battery. -
      *******.4.1.674.10892.5.4.600.50.1.5
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: systemBatteryReading
    oid: *******.4.1.674.10892.5.4.600.50.1.6
    type: gauge
    help: 0600.0050.0001.0006 This attribute defines the reading of the battery. -
      *******.4.1.674.10892.5.4.600.50.1.6
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
    enum_values:
      1: predictiveFailure
      2: failed
      4: presenceDetected
  - name: systemBatteryLocationName
    oid: *******.4.1.674.10892.5.4.600.50.1.7
    type: DisplayString
    help: 0600.0050.0001.0007 This attribute defines the location of the battery.
      - *******.4.1.674.10892.5.4.600.50.1.7
    indexes:
    - labelname: systemBatteryChassisIndex
      type: gauge
    - labelname: systemBatteryIndex
      type: gauge
  - name: coolingDeviceStatus
    oid: *******.4.1.674.10892.5.4.700.12.1.5
    type: gauge
    help: 0700.0012.0001.0005 This attribute defines the probe status of the cooling
      device. - *******.4.1.674.10892.5.4.700.12.1.5
    indexes:
    - labelname: coolingDevicechassisIndex
      type: gauge
    - labelname: coolingDeviceIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCriticalUpper
      5: criticalUpper
      6: nonRecoverableUpper
      7: nonCriticalLower
      8: criticalLower
      9: nonRecoverableLower
      10: failed
  - name: coolingDeviceLocationName
    oid: *******.4.1.674.10892.5.4.700.12.1.8
    type: DisplayString
    help: 0700.0012.0001.0008 This attribute defines the location name of the cooling
      device. - *******.4.1.674.10892.5.4.700.12.1.8
    indexes:
    - labelname: coolingDevicechassisIndex
      type: gauge
    - labelname: coolingDeviceIndex
      type: gauge
  - name: temperatureProbechassisIndex
    oid: *******.4.1.674.10892.5.4.700.20.1.1
    type: gauge
    help: 0700.0020.0001.0001 This attribute defines the index (one based) of the
      associated system chassis. - *******.4.1.674.10892.5.4.700.20.1.1
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeIndex
    oid: *******.4.1.674.10892.5.4.700.20.1.2
    type: gauge
    help: 0700.0020.0001.0002 This attribute defines the index (one based) of the
      temperature probe. - *******.4.1.674.10892.5.4.700.20.1.2
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeStateCapabilities
    oid: *******.4.1.674.10892.5.4.700.20.1.3
    type: gauge
    help: 0700.0020.0001.0003 This attribute defines the state capabilities of the
      temperature probe. - *******.4.1.674.10892.5.4.700.20.1.3
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: unknownCapabilities
      2: enableCapable
      4: notReadyCapable
      6: enableAndNotReadyCapable
  - name: temperatureProbeStateSettings
    oid: *******.4.1.674.10892.5.4.700.20.1.4
    type: gauge
    help: 0700.0020.0001.0004 This attribute defines the state settings of the temperature
      probe. - *******.4.1.674.10892.5.4.700.20.1.4
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: unknown
      2: enabled
      4: notReady
      6: enabledAndNotReady
  - name: temperatureProbeStatus
    oid: *******.4.1.674.10892.5.4.700.20.1.5
    type: gauge
    help: 0700.0020.0001.0005 This attribute defines the probe status of the temperature
      probe. - *******.4.1.674.10892.5.4.700.20.1.5
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCriticalUpper
      5: criticalUpper
      6: nonRecoverableUpper
      7: nonCriticalLower
      8: criticalLower
      9: nonRecoverableLower
      10: failed
  - name: temperatureProbeReading
    oid: *******.4.1.674.10892.5.4.700.20.1.6
    type: gauge
    help: 0700.0020.0001.0006 This attribute defines the reading for a temperature
      probe of type other than temperatureProbeTypeIsDiscrete - *******.4.1.674.10892.5.4.700.20.1.6
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeType
    oid: *******.4.1.674.10892.5.4.700.20.1.7
    type: gauge
    help: 0700.0020.0001.0007 This attribute defines the type of the temperature probe.
      - *******.4.1.674.10892.5.4.700.20.1.7
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: temperatureProbeTypeIsOther
      2: temperatureProbeTypeIsUnknown
      3: temperatureProbeTypeIsAmbientESM
      16: temperatureProbeTypeIsDiscrete
  - name: temperatureProbeLocationName
    oid: *******.4.1.674.10892.5.4.700.20.1.8
    type: DisplayString
    help: 0700.0020.0001.0008 This attribute defines the location name of the temperature
      probe. - *******.4.1.674.10892.5.4.700.20.1.8
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeUpperNonRecoverableThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.9
    type: gauge
    help: 0700.0020.0001.0009 This attribute defines the upper nonrecoverable threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.9
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeUpperCriticalThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.10
    type: gauge
    help: 0700.0020.0001.0010 This attribute defines the upper critical threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.10
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeUpperNonCriticalThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.11
    type: gauge
    help: 0700.0020.0001.0011 This attribute defines the upper noncritical threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.11
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeLowerNonCriticalThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.12
    type: gauge
    help: 0700.0020.0001.0012 This attribute defines the lower noncritical threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.12
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeLowerCriticalThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.13
    type: gauge
    help: 0700.0020.0001.0013 This attribute defines the lower critical threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.13
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeLowerNonRecoverableThreshold
    oid: *******.4.1.674.10892.5.4.700.20.1.14
    type: gauge
    help: 0700.0020.0001.0014 This attribute defines the lower nonrecoverable threshold
      of the temperature probe - *******.4.1.674.10892.5.4.700.20.1.14
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
  - name: temperatureProbeProbeCapabilities
    oid: *******.4.1.674.10892.5.4.700.20.1.15
    type: gauge
    help: 0700.0020.0001.0015 This attribute defines the probe capabilities of the
      temperature probe. - *******.4.1.674.10892.5.4.700.20.1.15
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: upperNonCriticalThresholdSetCapable
      2: lowerNonCriticalThresholdSetCapable
      4: upperNonCriticalThresholdDefaultCapable
      8: lowerNonCriticalThresholdDefaultCapable
  - name: temperatureProbeDiscreteReading
    oid: *******.4.1.674.10892.5.4.700.20.1.16
    type: gauge
    help: 0700.0020.0001.0016 This attribute defines the reading for a temperature
      probe of type temperatureProbeTypeIsDiscrete - *******.4.1.674.10892.5.4.700.20.1.16
    indexes:
    - labelname: temperatureProbechassisIndex
      type: gauge
    - labelname: temperatureProbeIndex
      type: gauge
    enum_values:
      1: temperatureIsGood
      2: temperatureIsBad
  - name: controllerNumber
    oid: *******.4.1.674.10892.********.130.1.1.1
    type: gauge
    help: Instance number of this controller entry. - *******.4.1.674.10892.********.130.1.1.1
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerName
    oid: *******.4.1.674.10892.********.130.1.1.2
    type: DisplayString
    help: The controller's name as represented in Storage Management. - *******.4.1.674.10892.********.130.1.1.2
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerRebuildRate
    oid: *******.4.1.674.10892.********.130.1.1.7
    type: gauge
    help: The rebuild rate is the percentage of the controller's resources dedicated
      to rebuilding a failed disk when a rebuild is necessary. - *******.4.1.674.10892.********.130.1.1.7
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerFWVersion
    oid: *******.4.1.674.10892.********.130.1.1.8
    type: DisplayString
    help: The controller's current firmware version. - *******.4.1.674.10892.********.130.1.1.8
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerCacheSizeInMB
    oid: *******.4.1.674.10892.********.130.1.1.9
    type: gauge
    help: The controller's current amount of cache memory in megabytes. - *******.4.1.674.10892.********.130.1.1.9
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerRollUpStatus
    oid: *******.4.1.674.10892.********.130.1.1.37
    type: gauge
    help: Severity of the controller state - *******.4.1.674.10892.********.130.1.1.37
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: controllerComponentStatus
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The status of the controller itself without the propagation of any contained
      component status - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: controllerDriverVersion
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: Currently installed driver version for this controller on the host. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerPCISlot
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The PCI slot on the server where the controller is seated - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerReconstructRate
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The reconstruct rate is the percentage of the controller's resources dedicated
      to reconstructing a disk group after adding a physical disk or changing the
      RAID level of a virtual disk residing on the disk group. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerPatrolReadRate
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The patrol read rate is the percentage of the controller's resources dedicated
      to perform a patrol read on disks participating in a virtual disk or hot spares.
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerBGIRate
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The background initialization (BGI) rate is the percentage of the controller's
      resources dedicated to performing the background initialization of a redundant
      virtual disk after it is created. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerCheckConsistencyRate
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The check consistency rate is the percentage of the controller's resources
      dedicated to performing a check consistency on a redundant virtual disk. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerPatrolReadMode
    oid: *******.4.1.674.10892.********.130.1.1.52
    type: gauge
    help: Identifies the patrol read mode setting for the controller - *******.4.1.674.10892.********.130.1.1.52
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: notSupported
      3: disabled
      4: auto
      5: manual
  - name: controllerPatrolReadState
    oid: *******.4.1.674.10892.********.130.1.1.53
    type: gauge
    help: This property displays the current state of the patrol read process - *******.4.1.674.10892.********.130.1.1.53
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: stopped
      3: active
  - name: controllerPersistentHotSpare
    oid: *******.4.1.674.10892.********.130.1.1.59
    type: gauge
    help: Indicates whether hot spare drives would be restored on insertion into the
      same slot. - *******.4.1.674.10892.********.130.1.1.59
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerSpinDownUnconfiguredDrives
    oid: *******.4.1.674.10892.********.130.1.1.60
    type: gauge
    help: Indicates whether un-configured drives would be put in power save mode by
      the controller. - *******.4.1.674.10892.********.130.1.1.60
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerSpinDownHotSpareDrives
    oid: *******.4.1.674.10892.********.130.1.1.61
    type: gauge
    help: Indicates whether hot spare drives would be put in power save mode by the
      controller. - *******.4.1.674.10892.********.130.1.1.61
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerSpinDownTimeInterval
    oid: *******.4.1.674.10892.********.130.1.1.62
    type: gauge
    help: The duration in minutes after which, the unconfigured or hot spare drives
      will be spun down to power save mode. - *******.4.1.674.10892.********.130.1.1.62
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerPreservedCache
    oid: *******.4.1.674.10892.********.130.1.1.69
    type: gauge
    help: Indicates whether preserved cache or pinned cache is present on the controller.
      - *******.4.1.674.10892.********.130.1.1.69
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerCheckConsistencyMode
    oid: *******.4.1.674.10892.********.130.1.1.70
    type: gauge
    help: The current check consistency mode setting for the controller - *******.4.1.674.10892.********.130.1.1.70
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: unsupported
      3: normal
      4: stopOnError
  - name: controllerCopyBackMode
    oid: *******.4.1.674.10892.********.130.1.1.71
    type: gauge
    help: The current copy back mode setting for the controller - *******.4.1.674.10892.********.130.1.1.71
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: unsupported
      3: "on"
      4: onWithSmart
      5: "off"
  - name: controllerSecurityStatus
    oid: *******.4.1.674.10892.********.130.1.1.72
    type: gauge
    help: The controller's current security/encryption status. - *******.4.1.674.10892.********.130.1.1.72
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: unknown
      2: none
      3: lkm
  - name: controllerEncryptionKeyPresent
    oid: *******.4.1.674.10892.********.130.1.1.73
    type: gauge
    help: Indicates whether encryption key is assigned for the controller. - *******.4.1.674.10892.********.130.1.1.73
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerEncryptionCapability
    oid: *******.4.1.674.10892.********.130.1.1.74
    type: gauge
    help: The type of encryption supported by the controller - *******.4.1.674.10892.********.130.1.1.74
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: none
      3: lkm
  - name: controllerLoadBalanceSetting
    oid: *******.4.1.674.10892.********.130.1.1.75
    type: gauge
    help: The ability of the controller to automatically use both controller ports
      (or connectors) connected to the same enclosure in order to route I/O requests
      - *******.4.1.674.10892.********.130.1.1.75
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: unsupported
      3: auto
      4: none
  - name: controllerMaxCapSpeed
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The maximum speed of the controller.in Gigbits per second (Gbps) - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: unknown
      2: oneDotFiveGbps
      3: threeGbps
      4: sixGbps
      5: twelveGbps
  - name: controllerSASAddress
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The SAS address of the controller. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerFQDD
    oid: *******.4.1.674.10892.********.**********
    type: OctetString
    help: The controller's Fully Qualified Device Descriptor (FQDD) as represented
      in Storage Management. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerDisplayName
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The controller's friendly FQDD as represented in Storage Management. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerT10PICapability
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: Indicates whether the controller supports the T10 PI (Protection Information)
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: capable
      3: notCapable
  - name: controllerRAID10UnevenSpansSupported
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: Indicates whether uneven spans for RAID 10 virtual disk is supported on
      the controller. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerEnhancedAutoImportForeignConfigMode
    oid: *******.4.1.674.10892.********.130.1.1.82
    type: gauge
    help: Indicates the status of enhanced auto-import of foreign configuration property
      of the controller - *******.4.1.674.10892.********.130.1.1.82
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: other
      2: notSupported
      3: disabled
      4: enabled
  - name: controllerBootModeSupported
    oid: *******.4.1.674.10892.********.130.1.1.83
    type: gauge
    help: Indicates whether headless boot mode settings are supported on the controller.
      - *******.4.1.674.10892.********.130.1.1.83
    indexes:
    - labelname: controllerNumber
      type: gauge
  - name: controllerBootMode
    oid: *******.4.1.674.10892.********.130.1.1.84
    type: gauge
    help: Indicates the boot mode of the controller - *******.4.1.674.10892.********.130.1.1.84
    indexes:
    - labelname: controllerNumber
      type: gauge
    enum_values:
      1: notApplicable
      2: user
      3: contOnError
      4: headlessContOnError
      5: headlessSafe
  - name: physicalDiskNumber
    oid: *******.4.1.674.10892.********.130.4.1.1
    type: gauge
    help: Instance number of this physical disk entry. - *******.4.1.674.10892.********.130.4.1.1
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskName
    oid: *******.4.1.674.10892.********.130.4.1.2
    type: DisplayString
    help: The physical disk's name as represented in Storage Management. - *******.4.1.674.10892.********.130.4.1.2
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskManufacturer
    oid: *******.4.1.674.10892.********.130.4.1.3
    type: DisplayString
    help: The name of the physical disk's manufacturer. - *******.4.1.674.10892.********.130.4.1.3
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskState
    oid: *******.4.1.674.10892.********.130.4.1.4
    type: gauge
    help: The current state of this physical disk - *******.4.1.674.10892.********.130.4.1.4
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: ready
      3: online
      4: foreign
      5: offline
      6: blocked
      7: failed
      8: nonraid
      9: removed
      10: readonly
  - name: physicalDiskProductID
    oid: *******.4.1.674.10892.********.130.4.1.6
    type: DisplayString
    help: The model number of the physical disk. - *******.4.1.674.10892.********.130.4.1.6
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskSerialNo
    oid: *******.4.1.674.10892.********.130.4.1.7
    type: DisplayString
    help: The physical disk's unique identification number from the manufacturer.
      - *******.4.1.674.10892.********.130.4.1.7
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskRevision
    oid: *******.4.1.674.10892.********.130.4.1.8
    type: DisplayString
    help: The firmware version of the physical disk. - *******.4.1.674.10892.********.130.4.1.8
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskCapacityInMB
    oid: *******.4.1.674.10892.********.130.4.1.11
    type: gauge
    help: The size of the physical disk in megabytes. - *******.4.1.674.10892.********.130.4.1.11
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskUsedSpaceInMB
    oid: *******.4.1.674.10892.********.130.4.1.17
    type: gauge
    help: The amount of used space in megabytes on the physical disk - *******.4.1.674.10892.********.130.4.1.17
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskFreeSpaceInMB
    oid: *******.4.1.674.10892.********.130.4.1.19
    type: gauge
    help: The amount of free space in megabytes on the physical disk - *******.4.1.674.10892.********.130.4.1.19
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskBusType
    oid: *******.4.1.674.10892.********.130.4.1.21
    type: gauge
    help: The bus type of the physical disk - *******.4.1.674.10892.********.130.4.1.21
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: scsi
      3: sas
      4: sata
      5: fibre
      6: pcie
  - name: physicalDiskSpareState
    oid: *******.4.1.674.10892.********.130.4.1.22
    type: gauge
    help: The status of the disk as a spare - *******.4.1.674.10892.********.130.4.1.22
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: notASpare
      2: dedicatedHotSpare
      3: globalHotSpare
  - name: physicalDiskComponentStatus
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The status of the physical disk itself without the propagation of any contained
      component status - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: physicalDiskPartNumber
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The part number of the disk. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskSASAddress
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The SAS address of the physical disk. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskNegotiatedSpeed
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The data transfer speed that the disk negotiated while spinning up in Gigbits
      per second (Gbps) - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: oneDotFiveGbps
      3: threeGbps
      4: sixGbps
      5: twelveGbps
      6: fiveGTps
      7: eightGTps
  - name: physicalDiskCapableSpeed
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The maximum data transfer speed supported by the disk in Gigbits per second
      (Gbps) - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: oneDotFiveGbps
      3: threeGbps
      4: sixGbps
      5: twelveGbps
      6: fiveGTps
      7: eightGTps
  - name: physicalDiskSmartAlertIndication
    oid: *******.4.1.674.10892.********.130.4.1.31
    type: gauge
    help: Indicates whether the physical disk has received a predictive failure alert.
      - *******.4.1.674.10892.********.130.4.1.31
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskManufactureDay
    oid: *******.4.1.674.10892.********.130.4.1.32
    type: DisplayString
    help: The day of the week (1=Sunday thru 7=Saturday) on which the physical disk
      was manufactured. - *******.4.1.674.10892.********.130.4.1.32
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskManufactureWeek
    oid: *******.4.1.674.10892.********.130.4.1.33
    type: DisplayString
    help: The week (1 thru 53) in which the physical disk was manufactured. - *******.4.1.674.10892.********.130.4.1.33
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskManufactureYear
    oid: *******.4.1.674.10892.********.130.4.1.34
    type: DisplayString
    help: The four digit year in which the physical disk was manufactured. - *******.4.1.674.10892.********.130.4.1.34
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskMediaType
    oid: *******.4.1.674.10892.********.130.4.1.35
    type: gauge
    help: The media type of the physical disk - *******.4.1.674.10892.********.130.4.1.35
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: hdd
      3: ssd
  - name: physicalDiskPowerState
    oid: *******.4.1.674.10892.********.130.4.1.42
    type: gauge
    help: The power state of the physical disk - *******.4.1.674.10892.********.130.4.1.42
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: other
      2: spunUp
      3: spunDown
      4: transition
      5: "on"
  - name: physicalDiskRemainingRatedWriteEndurance
    oid: *******.4.1.674.10892.********.130.4.1.49
    type: gauge
    help: This property is applicable to SSD media type only - *******.4.1.674.10892.********.130.4.1.49
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskOperationalState
    oid: *******.4.1.674.10892.********.130.4.1.50
    type: gauge
    help: The state of the physical disk when there are progressive operations ongoing
      - *******.4.1.674.10892.********.130.4.1.50
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: notApplicable
      2: rebuild
      3: clear
      4: copyback
  - name: physicalDiskProgress
    oid: *******.4.1.674.10892.********.130.4.1.51
    type: gauge
    help: The progress percentage of the operation that is being performed on the
      physical disk - *******.4.1.674.10892.********.130.4.1.51
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskSecurityStatus
    oid: *******.4.1.674.10892.********.130.4.1.52
    type: gauge
    help: The security/encryption status of the physical disk - *******.4.1.674.10892.********.130.4.1.52
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: supported
      2: notSupported
      3: secured
      4: locked
      5: foreign
  - name: physicalDiskFormFactor
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The form factor of the physical disk - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: oneDotEight
      3: twoDotFive
      4: threeDotFive
  - name: physicalDiskFQDD
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The physical disk's Fully Qualified Device Descriptor (FQDD) as represented
      in Storage Management. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskDisplayName
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The physical disk's friendly FQDD as represented in Storage Management.
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskT10PICapability
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: Indicates whether the physical disk supports the T10 PI (Protection Information)
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: other
      2: capable
      3: notCapable
  - name: physicalDiskBlockSizeInBytes
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The block size (in bytes) of the physical disk - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskProtocolVersion
    oid: *******.4.1.674.10892.********.130.4.1.59
    type: DisplayString
    help: Applicable for NVMe devices only - *******.4.1.674.10892.********.130.4.1.59
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
  - name: physicalDiskPCIeNegotiatedLinkWidth
    oid: *******.4.1.674.10892.********.130.4.1.60
    type: gauge
    help: Applicable for NVMe devices only - *******.4.1.674.10892.********.130.4.1.60
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: other
      2: notApplicable
      3: byOne
      4: byTwp
      5: byFour
      6: byEight
      7: bySixteen
      8: byThirtyTwp
  - name: physicalDiskPCIeCapableLinkWidth
    oid: *******.4.1.674.10892.********.130.4.1.61
    type: gauge
    help: Applicable for NVMe devices only - *******.4.1.674.10892.********.130.4.1.61
    indexes:
    - labelname: physicalDiskNumber
      type: gauge
    enum_values:
      1: other
      2: notApplicable
      3: byOne
      4: byTwp
      5: byFour
      6: byEight
      7: bySixteen
      8: byThirtyTwp
  - name: virtualDiskNumber
    oid: *******.4.1.674.10892.********.140.1.1.1
    type: gauge
    help: Instance number of this virtual disk entry. - *******.4.1.674.10892.********.140.1.1.1
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskName
    oid: *******.4.1.674.10892.********.140.1.1.2
    type: DisplayString
    help: The virtual disk's label as entered by the user. - *******.4.1.674.10892.********.140.1.1.2
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskState
    oid: *******.4.1.674.10892.********.140.1.1.4
    type: gauge
    help: 'The current state of this virtual disk (which includes any member physical
      disks.) Possible states: 1: The current state could not be determined - *******.4.1.674.10892.********.140.1.1.4'
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: unknown
      2: online
      3: failed
      4: degraded
  - name: virtualDiskSizeInMB
    oid: *******.4.1.674.10892.********.140.1.1.6
    type: gauge
    help: The size of the virtual disk in megabytes. - *******.4.1.674.10892.********.140.1.1.6
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskWritePolicy
    oid: *******.4.1.674.10892.********.140.1.1.10
    type: gauge
    help: The write policy used by the controller for write operations on this virtual
      disk - *******.4.1.674.10892.********.140.1.1.10
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: writeThrough
      2: writeBack
      3: writeBackForce
  - name: virtualDiskReadPolicy
    oid: *******.4.1.674.10892.********.140.1.1.11
    type: gauge
    help: The read policy used by the controller for read operations on this virtual
      disk - *******.4.1.674.10892.********.140.1.1.11
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: noReadAhead
      2: readAhead
      3: adaptiveReadAhead
  - name: virtualDiskLayout
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The virtual disk's RAID type - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: other
      2: r0
      3: r1
      4: r5
      5: r6
      6: r10
      7: r50
      8: r60
      9: concatRaid1
      10: concatRaid5
  - name: virtualDiskStripeSize
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The stripe size of this virtual disk - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: other
      2: default
      3: fiveHundredAndTwelvebytes
      4: oneKilobytes
      5: twoKilobytes
      6: fourKilobytes
      7: eightKilobytes
      8: sixteenKilobytes
      9: thirtyTwoKilobytes
      10: sixtyFourKilobytes
      11: oneTwentyEightKilobytes
      12: twoFiftySixKilobytes
      13: fiveOneTwoKilobytes
      14: oneMegabye
      15: twoMegabytes
      16: fourMegabytes
      17: eightMegabytes
      18: sixteenMegabytes
  - name: virtualDiskComponentStatus
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The status of the virtual disk itself without the propagation of any contained
      component status - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: other
      2: unknown
      3: ok
      4: nonCritical
      5: critical
      6: nonRecoverable
  - name: virtualDiskBadBlocksDetected
    oid: *******.4.1.674.10892.********.140.1.1.23
    type: gauge
    help: Indicates whether the virtual disk has bad blocks. - *******.4.1.674.10892.********.140.1.1.23
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskSecured
    oid: *******.4.1.674.10892.********.140.1.1.24
    type: gauge
    help: Indicates whether the virtual disk is secured or not. - *******.4.1.674.10892.********.140.1.1.24
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskIsCacheCade
    oid: *******.4.1.674.10892.********.140.1.1.25
    type: gauge
    help: Indicates whether the virtual disk is being used as a secondary cache by
      the controller. - *******.4.1.674.10892.********.140.1.1.25
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskDiskCachePolicy
    oid: *******.4.1.674.10892.********.140.1.1.26
    type: gauge
    help: 'The cache policy of the physical disks that are part of this virtual disk
      Possible values: 1: Enabled - *******.4.1.674.10892.********.140.1.1.26'
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: enabled
      2: disabled
      3: defullt
  - name: virtualDiskOperationalState
    oid: *******.4.1.674.10892.********.140.1.1.30
    type: gauge
    help: The state of the virtual disk when there are progressive operations ongoing
      - *******.4.1.674.10892.********.140.1.1.30
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: notApplicable
      2: reconstructing
      3: resynching
      4: initializing
      5: backgroundInit
  - name: virtualDiskProgress
    oid: *******.4.1.674.10892.********.140.1.1.31
    type: gauge
    help: The progress percentage of the operation that is being performed on the
      virtual disk - *******.4.1.674.10892.********.140.1.1.31
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskAvailableProtocols
    oid: *******.4.1.674.10892.********.140.1.1.32
    type: DisplayString
    help: List of protocols support by physical disks part of this virtual disk -
      *******.4.1.674.10892.********.140.1.1.32
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskMediaType
    oid: *******.4.1.674.10892.********.140.1.1.33
    type: DisplayString
    help: List of media types of the physical disks part of this virtual disk - *******.4.1.674.10892.********.140.1.1.33
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskRemainingRedundancy
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The number of physical disks which can be lost before the virtual disk loses
      its redundancy. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskFQDD
    oid: *******.4.1.674.10892.********.**********
    type: OctetString
    help: The virtual disk's Fully Qualified Device Descriptor (FQDD) as represented
      in Storage Management. - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskDisplayName
    oid: *******.4.1.674.10892.********.**********
    type: DisplayString
    help: The virtual disk's friendly FQDD as represented in Storage Management. -
      *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  - name: virtualDiskT10PIStatus
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: Indicates whether the virtual disk supports the T10 PI (Protection Information)
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
    enum_values:
      1: other
      2: enabled
      3: disabled
  - name: virtualDiskBlockSizeInBytes
    oid: *******.4.1.674.10892.********.**********
    type: gauge
    help: The block size (in bytes) of the physical disk part of the virtual disk
      - *******.4.1.674.10892.********.**********
    indexes:
    - labelname: virtualDiskNumber
      type: gauge
  max_repetitions: 10
  retries: 3
  timeout: 30s
  auth:
    community: eT4L