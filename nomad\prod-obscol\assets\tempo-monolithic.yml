auth_enabled: false
server:
  http_listen_port: 3200
  grpc_listen_port: {{ env "NOMAD_PORT_port_tempo_grpc"}}
  
distributor:
  receivers:
    # This configuration will listen on all ports and protocols that tempo is capable of.
    # Refer: found there: https://github.com/open-telemetry/opentelemetry-collector/tree/master/receiver
    jaeger:                            
      protocols:                       
        thrift_http:
          endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_jaeger_http" }}"
    zipkin:
    otlp:
      protocols:
        http:
          endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_otlp_http" }}"
        grpc:
    opencensus:

ingester:
  max_block_duration: 30m #default is 30m

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together, 1h default
    #max_compaction_objects: 6000000    # maximum size of compacted blocks, 6million is default
    max_block_bytes: 107374182400         # maximum size of compacted blocks, 1GB is default
    block_retention: 336h

storage:
  trace:
    backend: s3                     # backend configuration to use
    wal:
      path: /mnt/tempo/wal
    s3:
      bucket: nswdoe-obs-tempo-blocks-storage-shared
      endpoint: s3.ap-southeast-2.amazonaws.com
      region: ap-southeast-2
      # 2024-02-12 jedd - rotating s3 bucket keys
      # access_key: ********************
      # secret_key: 0eCp7i9Cjc6izHoW3lJtI/1uHKv2gUJaWNXBINCF
      access_key: ********************
      secret_key: /LB9engqSY8ZCkEe8fAhy/aCy9+2HJufhdFRvSoW

    pool:
      # worker pool determines the number of parallel requests to the object store backend
      max_workers: 400 # default is 400
      queue_depth: 20000 # default is 20000
    # 2024-02-21 james - removing for the time being, redis will only improve quering performance
    # cache: redis
    # redis:
    #   endpoint: redis.obs.nsw.education:6379
    #   db: 7

metrics_generator:
  processor:
    service_graphs:
      max_items: 5000
    local_blocks:
      flush_to_storage: true
  registry:
    external_labels:
      source: tempo
      cluster: obscol-prod
  storage:
    path: /mnt/tempo/generator/wal
    remote_write:
      - url: https://mimir-rwb-write.obs.nsw.education/api/v1/push
        headers: 
          X-Scope-OrgID: prod
        tls_config:
          insecure_skip_verify: true
        send_exemplars: true
  traces_storage:
    path: /tmp/tempo/generator/traces

overrides:
  metrics_generator_processors: [service-graphs, span-metrics,local-blocks]