# Prometheus blackbox exporter

# Blackbox exporter can be bounced through like this:
#   curl -XGET "http://blackbox-ping-test.service.dc-cir-un-prod.collectors.obs.nsw.education:9115/probe?target=************&module=icmp"
#
# This uses the icmp module, and hits that target IP address, asking blackbox (exposed
# on port 9115) to peform the ping test.

variables {
  image_blackbox-exporter = "quay.education.nsw.gov.au/observability/blackbox-exporter:prod-obscol"
}

job "blackbox" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "blackbox" {
    network {
      port "port_blackbox" {
        static = 9115
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "blackbox" {
      driver = "docker"
      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        //NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      config {
        # privileged = "true"
        # cap_add = ["net_raw"]
        # trying amongst:   net_admin, net_bind_service, net_broadcast, sys_admin, sys_boot, 
        cap_add = ["net_raw"]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            }
            }
        sysctl = {
          "net.ipv4.ping_group_range" = "0 2147483647"
        }

        image = var.image_blackbox-exporter

        # Note on log.level - there's 'info' and 'debug' and some github discussions around the wonky
        # way debug logging works, basically being a firehose or almost silent.  The &debug=true param
        # can be added to specific probes to enable this, but in practice having log.level=debug enabled
        # here produces an over-abundance of debugging information to stderr.
        args = [
          "--config.file",
          "local/config.yml",
          "--log.level",
          "info"
#          "debug"
        ]

        ports = ["port_blackbox"]

      }

      template {
        data = <<EOH

modules:
  http_with_proxy:
    prober: http
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      fail_if_body_not_matches_regexp:
        - "1 results loaded."
  http_2xx:
    prober: http
  http_post_2xx:
    prober: http
    http:
      method: POST
  tcp_connect:
    prober: tcp
  pop3s_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^+OK"
      tls: true
      tls_config:
        insecure_skip_verify: false
  ssh_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^SSH-2.0-"
      - send: "SSH-2.0-blackbox-ssh-check"
  irc_banner:
    prober: tcp
    tcp:
      query_response:
      - send: "NICK prober"
      - send: "USER prober prober prober :prober"
      - expect: "PING :([^ ]+)"
        send: "PONG ${1}"
      - expect: "^:[^ ]+ 001"
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # = = = = = = = = = = = = = = = = = = = = = = = =
  # Web checks for generic and bespoke applications

  # Oliver School Library System (SLS) requires a 200 and a string 'results found' on the target page.
  webcheck_oliver:
    prober: http
    timeout: 20s
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      valid_status_codes: [200]
      fail_if_not_ssl: true
      fail_if_body_not_matches_regexp:
        - results found
      preferred_ip_protocol: "ip4" 

EOH
        destination = "local/config.yml"
      }

      resources {
        cpu    = 1024
        memory = 512
      }

      service {
        name = "blackbox"
        port = "port_blackbox"
        check {
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }

    }

  }
}
