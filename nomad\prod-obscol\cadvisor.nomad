
variables {
  image_cadvisor = "quay.education.nsw.gov.au/observability/cadvisor:prod-obscol"
}

job "cadvisor" {
  region = "global"
  datacenters = ["dc-cir-un-prod"]
  type = "system"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  group "obscol" {
    task "prod" {
      driver = "docker"
      config {
        image = var.image_cadvisor
        port_map {
          cadvisor = 8080
        }
      }
      service {
        port = "cadvisor"
        check {
          type = "http"
          path = "/"
          interval = "10s"
          timeout = "2s"
        }
      }

      resources {
        cpu = 100
        memory = 32
        network {
          mbits = 100
          port "cadvisor" {
              static = 19403
          }
        }
      }
    }
  }
}