
# collector - prometheus + postgresql exporter (obscol PROD)

# NEEDS  Consul key/value of "postgresql/srvobs_password" to be the value of 
#        the user:srvobs password on each postgresql database.  If we have
#        unique passwords per database, this key/value needs to be augmented,
#        along with the exporter (auth_modules section).

# WIP 2023-08

# Architecture - single prometheus job scrapes a coupled exporter.
# 
# We had contemplated one-exporter-per-database (isolation, and to work
# around some apparent limitation of the prometheus postgresql exporter)
# but assuming we can have a database (srvobs) and a user (also srvobs)
# on each postgresql database, we can use the multi-end-point auth feature
# of the postgresql exporter.
#
# Some known bugs within that exporter can be worked around by the
# relabelling jobs (refer below).

# HOW TO USE
#
# Refer:  https://confluence.education.nsw.gov.au/display/PM/Observing+PostgreSQL
#
# Add an entry to prometheus config - static config targets - and then add a new
# stanza to the exporter auth_modules.
#
# @TODO parameterise these sections.  OTOH there's an upper ceiling of about 25
#       of these databases, so diminishing returns trying to add complexity here.
#       We *could* share an auth_module, but this locks us into a few assumptions.

variables {
    image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:v2.40.3"
    image_postgres-exporter = "quay.io/prometheuscommunity/postgres-exporter:v0.13.2"
  }

job "collector-app-postgresql" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "collector-app-postgresql" {

    network {
      port "port_prometheus_app_postgresql" { }

      port "port_exporter_postgresql" {
        to = 9187
      }

    }

    #  TASK prometheus-app-postgresql = = = = = = = = = = = = = = = = = = = = = =

    task "prometheus-app-postgresql" {
      driver = "docker"

      config {
        ports = ["port_prometheus_app_postgresql"]

        image = var.image_prometheus

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_postgresql}",
          "--web.external-url=https://prometheus-app-postgresql.obs.nsw.education",
          "--web.page-title=Prometheus for PostgreSQL Exporter (PROD Cluster)",

          "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-postgresql"
        port = "port_prometheus_app_postgresql"

        check {
          type = "http"
          port = "port_prometheus_app_postgresql"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-postgresql.rule=Host(`prometheus-app-postgresql.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-postgresql.tls=false",
          "traefik.http.routers.prometheus-app-postgresql.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env        = "prod"
          cluster    = "obscol"
        }
      }


      template {
        data = <<EOH
global:

  external_labels:
    provenance: prometheus-app-postgresql

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-postgresql'
    scheme: https
    static_configs:
      # - targets: ['prometheus-app-postgresql.obs.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_postgresql" }}']
      - targets: ['prometheus-app-postgresql.obs.nsw.education']

  - job_name: 'postgresql_metrics'
    metrics_path: "/probe"
    scheme: https

    static_configs:
      # Add new targets to this group - the hostname should match the auth_module
      # entry in the exporter in the TASK below.
      - targets: [
          'pu0992ttdbs001.hbm.det.nsw.edu.au',
          'pu0991tedbs001.hbm.det.nsw.edu.au',
          'pu0992ttdbd001.hbm.det.nsw.edu.au',
          'pu0991tedbd001.hbm.det.nsw.edu.au'
        ]

    # We need to craft this URL:
    #    exporter-postgresql.obs.nsw.education/probe?target=dg-pan-01&auth_module=dg-pan-01

    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: target

      - source_labels: [__address__]
        target_label: __param_auth_module
      - source_labels: [__param_auth_module]
        target_label: auth_module

      - target_label: __address__
        replacement: exporter-postgresql.obs.nsw.education

    # Manupulate the metric / labels.  Out of the box we get something like this:
    # 
    #   pg_database_size_bytes{auth_module="pu0991tedbd001.hbm.det.nsw.edu.au", 
    #       datname="postgres", instance="exporter-postgresql.obs.nsw.education:443", 
    #       job="postgresql_metrics", target="pu0991tedbd001.hbm.det.nsw.edu.au"}
    #
    # Consequently we want to:
    #    drop the auth_module= label
    #    drop the instance= label
    #    change datname= label to database=
    #    change target= label to host=
    #    rename all the metrics with a pg_ prefix to postgresql_ instead
    #      (This breaks some grafana.com dashboard examples, BUT it's much more
    #      discoverable at our end for our users.)   Note that the exporter is
    #      meant to have this capability, but it is broken.
    #
    # We should then have a metric converted from the example above that looks like:
    #
    #    postgresql_database_size_bytes{database="postgres",
    #        host="pu0991tedbd001.hbm.det.nsw.edu.au", job="postgresql_metrics"}

    metric_relabel_configs:

      # Drop the 'auth_module=' label
      - action: labeldrop
        regex: auth_module

      # Drop the 'instance=' label
      - action: labeldrop
        regex: instance

      # Change the 'datname=' to 'database='
      - action: labelmap
        regex: "datname"
        replacement: "database"
        # this seems to duplicate datname to database so we drop datname
      - action: labeldrop
        regex: datname

      # Change the 'target=' to 'host='
      - action: labelmap
        regex: "target"
        replacement: "host"
        # this seems to duplicate the original, so drop it
      - action: labeldrop
        regex: target

      # Change pg_ to postgresql_ on all exporter metrics
      - source_labels: [__name__]
        regex: "pg_(.+)"
        target_label: "__name__"
        replacement: "postgresql_$1"

      # We probably don't care about 'template0' and 'template1' databases at all.
      - action: drop
        source_labels: [database]
        regex: "template[01]"

      # We probably don't care about the 'srvobs' db either - it's just to CONNECT to.
      - action: drop
        source_labels: [database]
        regex: "srvobs"

remote_write:
  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 500
        memory = 2000
      }

    }  // end-task "prometheus-app-postgresql"


    #  TASK exporter-postgresql = = = = = = = = = = = = = = = = = = = = = = = =

    task "exporter-postgresql" {

# Because this information isn't accurate and/or readily available elsewhere:
#
#  $ /bin/postgres_exporter  --help
#  Flags:
#  -h, --[no-]help                Show context-sensitive help (also try --help-long and --help-man).
#      --[no-]collector.database  Enable the database collector (default: enabled).
#      --[no-]collector.postmaster
#                                 Enable the postmaster collector (default: disabled).
#      --[no-]collector.process_idle
#                                 Enable the process_idle collector (default: disabled).
#      --[no-]collector.replication
#                                 Enable the replication collector (default: enabled).
#      --[no-]collector.replication_slot
#                                 Enable the replication_slot collector (default: enabled).
#      --[no-]collector.stat_bgwriter
#                                 Enable the stat_bgwriter collector (default: enabled).
#      --[no-]collector.stat_database
#                                 Enable the stat_database collector (default: enabled).
#      --[no-]collector.stat_statements
#                                 Enable the stat_statements collector (default: disabled).
#      --[no-]collector.stat_user_tables
#                                 Enable the stat_user_tables collector (default: enabled).
#      --[no-]collector.statio_user_tables
#                                 Enable the statio_user_tables collector (default: enabled).
#      --config.file="postgres_exporter.yml"
#                                 Postgres exporter configuration file.
#      --[no-]web.systemd-socket  Use systemd socket activation listeners instead of port listeners (Linux only).
#      --web.listen-address=:9187 ...
#                                 Addresses on which to expose metrics and web interface. Repeatable for multiple addresses.
#      --web.config.file=""       [EXPERIMENTAL] Path to configuration file that can enable TLS or authentication. See: https://github.com/prometheus/exporter-toolkit/blob/master/docs/web-configuration.md
#      --web.telemetry-path="/metrics"
#                                 Path under which to expose metrics. ($PG_EXPORTER_WEB_TELEMETRY_PATH)
#      --[no-]disable-default-metrics
#                                 Do not include default metrics. ($PG_EXPORTER_DISABLE_DEFAULT_METRICS)
#      --[no-]disable-settings-metrics
#                                 Do not include pg_settings metrics. ($PG_EXPORTER_DISABLE_SETTINGS_METRICS)
#      --[no-]auto-discover-databases
#                                 Whether to discover the databases on a server dynamically. (DEPRECATED) ($PG_EXPORTER_AUTO_DISCOVER_DATABASES)
#      --extend.query-path=""     Path to custom queries to run. (DEPRECATED) ($PG_EXPORTER_EXTEND_QUERY_PATH)
#      --[no-]dumpmaps            Do not run, simply dump the maps.
#      --constantLabels=""        A list of label=value separated by comma(,). (DEPRECATED) ($PG_EXPORTER_CONSTANT_LABELS)
#      --exclude-databases=""     A list of databases to remove when autoDiscoverDatabases is enabled (DEPRECATED) ($PG_EXPORTER_EXCLUDE_DATABASES)
#      --include-databases=""     A list of databases to include when autoDiscoverDatabases is enabled (DEPRECATED) ($PG_EXPORTER_INCLUDE_DATABASES)
#      --metric-prefix="pg"       A metric prefix can be used to have non-default (not "pg") prefixes for each of the metrics ($PG_EXPORTER_METRIC_PREFIX)
#      --[no-]version             Show application version.
#      --log.level=info           Only log messages with the given severity or above. One of: [debug, info, warn, error]
#      --log.format=logfmt        Output format of log messages. One of: [logfmt, json]

      driver = "docker"

      env = {
        # Examples when using single-shot calls - jedd-lab host:
        # DATA_SOURCE_NAME="***************************************************/postgres?sslmode=disable"
        #
        # HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        # HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        # NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
        #
        # This does NOT work as of 2023-08-21 - disabling as we workaround it with a 
        # prometheus relabel above - this would be lower cost if / when it is fixed.
        # "PG_EXPORTER_METRIC_PREFIX" = "postgres"
      }

      config {
        # Current version as of 2023-08
        #   refer https://quay.io/repository/prometheuscommunity/postgres-exporter?tab=tags
        image = var.image_postgres-exporter

        ports = ["port_exporter_postgresql"]

        args = [
          "--config.file=/etc/postgres_exporter.yml",

          # log level: debug, info, warn, error
          "--log.level=warn",

          # Metric prefix - as above, this does not work as of 2023-08.
          # "--metric-prefix=postgres",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          

        volumes = [
          "local/postgres_exporter.yaml:/etc/postgres_exporter.yml",
        ]

      }

      resources {
        cpu    = 300
        memory = 200
      }

      template {
        data = <<EOH
auth_modules:

  # Translate:    psql -h ${hostname} -p 5432 -U srvobs -W srvobs   # password 'secretpw'

  # This key can be anything useful
  pu0992ttdbs001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: "{{ key "postgresql/srvobs_password" }}"
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0991tedbs001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: "{{ key "postgresql/srvobs_password" }}"
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0992ttdbd001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: "{{ key "postgresql/srvobs_password" }}"
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

  pu0991tedbd001.hbm.det.nsw.edu.au:
    type: userpass
    userpass:
      username: srvobs
      password: "{{ key "postgresql/srvobs_password" }}"
    options:
      # options become key=value parameters of the DSN
      sslmode: disable

EOH
        destination = "local/postgres_exporter.yaml"
      }

      service {
        name = "exporter-postgresql"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-postgresql.rule=Host(`exporter-postgresql.obs.nsw.education`)",
          "traefik.http.routers.exporter-postgresql.tls=false",
          "traefik.http.routers.exporter-postgresql.entrypoints=http,https",
        ]

        port = "port_exporter_postgresql"

        check {
          port     = "port_exporter_postgresql"
          type     = "tcp"
          name     = "alive"
          interval = "10s"
          timeout  = "2s"
        }

      }

    } // end-task "exporter-postgresql"

  }
}
