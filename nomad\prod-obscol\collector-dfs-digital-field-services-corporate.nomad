# collector-dfs-corporate - Corporate offices - PROD ObsCol

# Combined standalone prometheus + blackbox exporter for Digital Field Services
# refer:  https://jira.education.nsw.gov.au/browse/OBS-665

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}

job "collector-dfs-corporate" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }


  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-dfs-corporate" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
      port "port_blackbox" { 
        # to = 9115
      }
  	}

    
    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK python container for conversion script  = = = = = = = = = =

    task "task-dfs-corporate-csv-converter" {
      driver = "docker"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "python:3.9-bullseye"

        # dns_servers = [ "************" ]

        command = "/obs-app-dfs/looper.sh"

        network_mode = "host"

        ports = [ ]

        volumes = [
          "/opt/sharednfs/obs-app-dfs-corporate:/obs-app-dfs"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        # 2023-08-18 jedd pulling these down to be about 20% above observed
        cpu = 20
        memory = 10
        memory_max = 20
      }

      service {
        name = "collector-dfs-csv-converter"
        meta {
          cir_app_id = "obs"
          env = "prod"
        }
      }

    }  // end-task task-dfs-corporate-csv-converter


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-corporate-prometheus" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://collector-dfs-corporate-prometheus.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Digital Field Services Collector (Corporate)",
        ]

        image = var.image_prometheus

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/corporate.yaml:/etc/prometheus/corporate.yaml",
          "/opt/sharednfs/prometheus-configuration/prometheus/rules:/etc/prometheus/rules.d",
          "/opt/sharednfs/obs-app-dfs-corporate:/obs-app-dfs"
        ]
      }

      resources {
        # 2023-08-18 jedd pulling these down to be about 20% above observed
        cpu = 50
        memory = 200
        memory_max = 300
      }

      service {
        name = "collector-dfs-corporate-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-corporate-prometheus.rule=Host(`collector-dfs-corporate-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-dfs-corporate-prometheus.tls=false",
          "traefik.http.routers.collector-dfs-corporate-prometheus.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-dfs-corporate"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-dfs-corporate'
    static_configs:
      - targets: ['collector-dfs-corporate-prometheus.obs.nsw.education']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-dfs-corporate'
    static_configs:
      - targets: ['collector-dfs-corporate-blackbox.obs.nsw.education']
    # Drop surplus blackbox series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  - job_name: 'dfs-pinger-corporate'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 15m
      files: 
      - "/obs-app-dfs/dfs-pinger-targets.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-dfs-corporate-blackbox.obs.nsw.education

    metric_relabel_configs:
      # Drop surplus blackbox series.  We receive this set from blackbox with icmp / ping target:
      #   probe_dns_lookup_time_seconds
      #   probe_duration_seconds
      #   probe_icmp_duration_seconds{phase="resolve"}
      #   probe_icmp_duration_seconds{phase="rtt"}
      #   probe_icmp_duration_seconds{phase="setup"}
      #   probe_icmp_reply_hop_limit
      #   probe_ip_addr_hash
      #   probe_ip_protocol
      #   probe_success
      # But we really only care about probe_success, so we'll drop everything else.

#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep

remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers:
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true

#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  

EOH
        destination = "local/prometheus.yaml"
      }

      template {
        data = file("assets/collector-dfs-file-sd-config.yaml")
        destination = "local/corporate.yaml"
      }

    }  // end-task "task-dfs-corporate-prometheus"


    # TASK blackbox for dfs   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-corporate-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        # dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        # 2023-08-18 jedd pulling these down to be about 20% above observed
        cpu = 50
        memory = 50
        memory_max = 100
      }


      service {
        name = "collector-dfs-corporate-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-corporate-blackbox.rule=Host(`collector-dfs-corporate-blackbox.obs.nsw.education`)",
          "traefik.http.routers.collector-dfs-corporate-blackbox.tls=false",
          "traefik.http.routers.collector-dfs-corporate-blackbox.entrypoints=http",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
modules:
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

EOH
        destination = "local/config.yml"
      }

    }  // end-task "task-dfs-corporate-blackbox"

  }

}


