
# JEDD experimentation around blackbox ping AND tcp connect

# Combined standalone prometheus + blackbox exporter for Digital Field Services
# refer:  https://jira.education.nsw.gov.au/browse/OBS-665

variables {
  image_prometheus = "prom/prometheus:v2.47.0"
  image_blackbox_exporter = "prom/blackbox-exporter:v0.24.0"
}

job "collector-dfs-jedd" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  # 2023-08-18 jedd - forcing to the new 0475 cluster members
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0475obscol0[678]"
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-dfs-jedd" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
      port "port_blackbox" { }
  	}

    
    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-jedd-prometheus" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://collector-dfs-jedd-prometheus.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Digital Field Services Collector (Jedd)",
        ]

        # image = "https://docker.io/prom/prometheus:v2.42.0"
        image = var.image_prometheus

        #logging {
        #  type = "loki"
        #  config {
        #    loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
        #    loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
        #  }
        #}          

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/jedd.yaml:/etc/prometheus/jedd.yaml",
          # "/opt/sharednfs/prometheus-configuration/prometheus/rules:/etc/prometheus/rules.d",
          "/opt/sharednfs/obs-app-dfs-jedd:/obs-app-dfs"
        ]
      }

      resources {
        cpu = 50
        memory = 200
        memory_max = 300
      }

      service {
        name = "collector-dfs-jedd-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-jedd-prometheus.rule=Host(`collector-dfs-jedd-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-dfs-jedd-prometheus.tls=false",
          "traefik.http.routers.collector-dfs-jedd-prometheus.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-dfs-jedd"

  scrape_interval: 1m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-dfs-jedd'
    static_configs:
      - targets: ['collector-dfs-jedd-prometheus.obs.nsw.education']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-dfs-jedd'
    static_configs:
      - targets: ['collector-dfs-jedd-blackbox.obs.nsw.education']
    # Drop surplus blackbox series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  - job_name: 'dfs-pinger-jedd'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 15m
      files: 
      - "/obs-app-dfs/dfs-pinger-targets.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-dfs-jedd-blackbox.obs.nsw.education

    metric_relabel_configs:
      # Drop surplus blackbox series.  We receive this set from blackbox with icmp / ping target:
      #   probe_dns_lookup_time_seconds
      #   probe_duration_seconds
      #   probe_icmp_duration_seconds{phase="resolve"}
      #   probe_icmp_duration_seconds{phase="rtt"}
      #   probe_icmp_duration_seconds{phase="setup"}
      #   probe_icmp_reply_hop_limit
      #   probe_ip_addr_hash
      #   probe_ip_protocol
      #   probe_success
      # But we really only care about probe_success, so we'll drop everything else.

#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep




  - job_name: 'dfs-tcpcheck-jedd'
    metrics_path: /probe
    params:
      module: ["tcp_connect"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 15m
      files: 
      - "/obs-app-dfs/dfs-pinger-targets-tcp-connect-3389.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-dfs-jedd-blackbox.obs.nsw.education

    metric_relabel_configs:
      # Drop surplus blackbox series.  We receive this set from blackbox with icmp / ping target:
      #   probe_dns_lookup_time_seconds
      #   probe_duration_seconds
      #   probe_icmp_duration_seconds{phase="resolve"}
      #   probe_icmp_duration_seconds{phase="rtt"}
      #   probe_icmp_duration_seconds{phase="setup"}
      #   probe_icmp_reply_hop_limit
      #   probe_ip_addr_hash
      #   probe_ip_protocol
      #   probe_success
      # But we really only care about probe_success, so we'll drop everything else.

#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep


#remote_write:
#  - name: mimir
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#    headers:
#      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true


EOH
        destination = "local/prometheus.yaml"
      }

      template {
        data = file("assets/collector-dfs-file-sd-config.yaml")
        destination = "local/jedd.yaml"
      }

    }  // end-task "task-dfs-jedd-prometheus"


    # TASK blackbox for dfs   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-dfs-jedd-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        # image = "prom/blackbox-exporter:v0.22.0"
        image = var.image_blackbox_exporter

        # dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        #logging {
        #  type = "loki"
        #  config {
        #    loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
        #    loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
        #  }
        #}

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        # 2023-08-18 jedd pulling these down to be about 20% above observed
        cpu = 50
        memory = 50
        memory_max = 100
      }


      service {
        name = "collector-dfs-jedd-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-jedd-blackbox.rule=Host(`collector-dfs-jedd-blackbox.obs.nsw.education`)",
          "traefik.http.routers.collector-dfs-jedd-blackbox.tls=false",
          "traefik.http.routers.collector-dfs-jedd-blackbox.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH

modules:

  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ip4

  tcp_connect:
    prober: tcp
    tcp:
      ip_protocol_fallback: false
      preferred_ip_protocol: ip4
      tls: false
    timeout: 3s

EOH
        destination = "local/config.yml"
      }

    }  // end-task "task-dfs-jedd-blackbox"

  }

}


