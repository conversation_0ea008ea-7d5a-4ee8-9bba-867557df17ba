
variables {
  image_grafana-alloy = "quay.education.nsw.gov.au/observability/grafana-alloy:prod-obscol"
}

job "collectors-grafana-alloy-prod" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"


 group "collector-group-otlp" {
    constraint {
        operator  = "distinct_hosts"
        value     = "true"
    }
    count = 1

    ephemeral_disk {
      migrate = true
      size    = 500
      sticky  = true
    }
    
    network {
        port "http" {
        }
        port "otlp" {
            static = 4317
        }
        port "memory_port"  {
            static = 12345
        }
    }

    task "grafana-alloy-task-otlp" {
      driver = "docker"

      config {
        privileged = true
        #cap_add = ["NET_RAW","NET_BIND_SERVICE"]
        image = var.image_grafana-alloy
        ports = ["http","otlp"]
        entrypoint = [
            "/bin/alloy",
            "run",
            "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
            #"--server.http.enable-pprof=true",
            "--server.http.memory-addr=${NOMAD_IP_memory_port}:${NOMAD_PORT_memory_port}",
            "--stability.level=public-preview",
            "--cluster.enabled=false",
            "--disable-reporting=true", 
            "/local/config.alloy"
            ]
        # logging {
        #     type = "loki"
        #     config {
        #         loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
        #         loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=collector-grafana-alloy-otlp-receiver"
        #         }
        #     }
         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro",
           "/var/run/docker.sock:/var/run/docker.sock"
         ]
      }
      env {
      #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      #HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
      #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      #GCLOUD_RW_API_KEY = "********************************************************************************************************************************************************************"
    }
      resources {
        cpu    = 500
        memory = 2048

      }
      service {
        name = "grafana-alloy-collector-otlp"
        port = "http"
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy.rule=Host(`grafana-alloy.obs.nsw.education`)",
          "traefik.http.routers.grafana-alloy.tls=false",
          "traefik.http.routers.grafana-alloy.entrypoints=http,https",
        ]        
      }
      service {
        name = "grafana-alloy-otlp-recevier"
        port = "otlp"       
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-otlp-recevier.rule=Host(`otlp.obs.nsw.education`)",
          "traefik.http.routers.grafana-alloy-otlp-recevier.tls=false",
          "traefik.http.routers.grafana-alloy-otlp-recevier.entrypoints=http,https",
        ]        
      }         
      template {
        data        = file("assets/grafana-alloy-otlp.alloy")
        destination = "local/config.alloy"
      }
    }    
  }  
}