// collector-oliver (PROD) - prometheus + blackbox

// 2023-09 - migrated from prometheus-app-oliver which relied on presence of
// common blackbox.nomad, and moved to agent-mode, removing the need for a
// persistent volume, reducing memory footprint.
// 

// @TODO remove this dependency on git repo:   prometheus-configuration
//       This is checked out (and cron-updated) at /opt/sharednfs/prometheus-configuration
//       It contains the blackbox_webcheck_oliver.yml file
//       This needs a better mechanism - perhaps a dedicated repository, as per the DFS apps.

variables {
  image_prometheus = "prom/prometheus:v2.40.3"
  image_blackbox = "prom/blackbox-exporter:v0.22.0"
}

job "collector-oliver" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  group "prometheus-app-oliver" {

    network {
      port "port_prometheus" { }
      port "port_blackbox" { }
    }

    #volume "vol_prometheus-app-oliver"  {
    #  type = "host"
    #  source = "prometheus_app_oliver"
    #  read_only = false
    #}


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    # TASK task-oliver-prometheus = = = = = = = = = = = = = = = = = = = = = =
    task "task-oliver-prometheus" {
      driver = "docker"

      #volume_mount {
      #  volume = "vol_prometheus-app-oliver"
      #  destination = "/prometheus"
      #  read_only = false
      #}

      config {
        ports = ["port_prometheus"]

        image = var.image_prometheus

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",

          # "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d",

          "/opt/sharednfs/prometheus-configuration/prod/blackbox:/etc/prometheus/blackbox"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.external-url=https://prometheus-app-oliver.obs.nsw.education",
          "--web.page-title=Prometheus for Oliver on DoE ObsCol PROD",

          # "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=1d",

          # "--web.console.libraries=/usr/share/prometheus/console_libraries",
          # "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "collector-oliver-prometheus"
        port = "port_prometheus"

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oliver-prometheus.rule=Host(`collector-oliver-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-oliver-prometheus.tls=false",
          "traefik.http.routers.collector-oliver-prometheus.entrypoints=http,https",
        ]

      }


      template {
        data = <<EOH
global:
  # These chance the extant queries - we can work around this later, if we want to enable this again.
  #external_labels:
  #  nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
  #  nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
  # 2022-11-14 jedd - stop-gap measure, to replicate the old job & task names, until I work out the 
  #            without () syntax to suppress EITHER this or absence of this in the grafana panels.
  external_labels:
    # nomad_job_name: prometheus
    # nomad_task_name: prometheus
    # provenance: obscol-prometheus-app-oliver
    provenance: "collector-oliver"

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-oliver'
    scheme: https 
    static_configs:
      - targets: ['collector-oliver-prometheus.obs.nsw.education']

  # Using blackbox (coupled task) exporter to do a custom web-check for Oliver 
  # school library with custom URLs and regex string match on target page.
  - job_name: 'webcheck_oliver'
    scheme: https 
    metrics_path: /probe
    scrape_interval: 2m
    params:
      module: ["webcheck_oliver"]

    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be useful when we separate out file_sd_config entries. Mildly useful
    # now as we can update prometheus-configuration independently of restarting
    # this job.
    - refresh_interval: 1h
      files: 
      - "/etc/prometheus/blackbox/blackbox_webcheck_oliver.yml"
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: collector-oliver-blackbox.obs.nsw.education



remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true

#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu = 512
        memory = 2048
        memory_max = 2048
      }

    } // end-task "task-oliver-prometheus"


    # = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    # TASK task-oliver-blackbox = = = = = = = = = = = = = = = = = = = = = = =
    task "task-oliver-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = var.image_blackbox

        # dns_servers = ["************"]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu = 50
        memory = 50
        memory_max = 100
      }

      service {
        name = "collector-oliver-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oliver-blackbox.rule=Host(`collector-oliver-blackbox.obs.nsw.education`)",
          "traefik.http.routers.collector-oliver-blackbox.tls=false",
          "traefik.http.routers.collector-oliver-blackbox.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
modules:

  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  webcheck_oliver:
    prober: http
    timeout: 20s
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      valid_status_codes: [200]
      fail_if_not_ssl: true
      fail_if_body_not_matches_regexp:
        - results found
      preferred_ip_protocol: ipv4


EOH
        destination = "local/config.yml"
      }

    }  // end-task "task-oliver-blackbox"

  }  // end-group "prometheus-app-oliver"

}  // end-job "collector-oliver" 
