
# collector-oracle - for amstest database

# This consists of two tasks - an Exporter and a Prometheus

# This Nomad Job services a single Oracle database.

# --------------------------------------------------------------------------
# Updates to package (container) are available at Oracle Container Registry
# Search for:   'observability-exporter'
#     https://container-registry.oracle.com/ords/f?p=113:10
# --------------------------------------------------------------------------

# skopeo copy 
#        docker://container-registry.oracle.com/database/observability-exporter:2.0.2 
#        docker://quay.education.nsw.gov.au/observability/oracle:observability-exporter-v2.0.2


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
locals {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
  image_exporter = "quay.education.nsw.gov.au/observability/oracle:observability-exporter-v2.0.2"

  host_constraint = ".*0992obscol.*"
  loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-oracle-amstest" {
  datacenters = ["dc-cir-un-prod"]

  type = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-oracle" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }

      port "port_exporter" { 
        to = 9161
      }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "prometheus" {
      driver = "docker"

      config {
        image = local.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",

          # Enable this once you've confirmed data is scraping sanely.
          "--enable-feature=agent",

          "--web.external-url=http://${NOMAD_JOB_ID}-prometheus.obs.nsw.education",

          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Oracle - ${NOMAD_JOB_ID}",
          # "--log-level=info",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu = 50
        memory = 150
        memory_max = 250
      }

      service {
        name = "${NOMAD_JOB_ID}-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.rule=Host(`${NOMAD_JOB_ID}-prometheus.obs.nsw.education`)",
          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.tls=false",
          "traefik.http.routers.${NOMAD_JOB_ID}-prometheus.entrypoints=http,https",
        ]

        check {
          name = "Oracle exporter prometheus healthcheck"
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    # nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    # nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: {{ env "NOMAD_JOB_NAME" }}
    env: prod

  scrape_interval: 120s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-oracle'
    scheme: 'https'
    static_configs:
      - targets: [
         {{ env "NOMAD_JOB_ID" }}-prometheus.obs.nsw.education
       ]
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*'
        action: drop

  # Job to scrape metrics of the exporter itself
  - job_name: 'exporter-oracle'
    scheme: 'https'
    static_configs:
      - targets: [
         {{ env "NOMAD_JOB_ID" }}-exporter.obs.nsw.education
      ]
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*'
        action: drop

# Enable this once you've confirmed data is scraping sanely
remote_write:
  - name: mimir
    url: https://mimir-rwb-write.obs.nsw.education/api/v1/push
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // end-task "task-oracle-prometheus"



    # TASK exporter = = = = = = = = = = = = = = = = = = = = = = = = =
    task "exporter" {
      driver = "docker"

      env = {
      }

      config {
        image = local.image_exporter

        ports = ["port_exporter"]

        args = [ 
          # We can enable custom metrics - an example file is imported, but unused.
          # "--custom.metrics=/etc/custom-metrics.toml",
          # "--log.level=debug",
          "--log.level=warn",
          "--config.file=/etc/oracle-config.yaml",
        ]

        volumes = [
          "local/custom-metrics.toml:/etc/custom-metrics.toml",
          "local/oracle-config.yaml:/etc/oracle-config.yaml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      
      }

      resources {
        cpu = 50
        memory = 50
        memory_max = 200
      }

      service {
        name = "${NOMAD_JOB_ID}-exporter"
        port = "port_exporter"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.rule=Host(`${NOMAD_JOB_ID}-exporter.obs.nsw.education`)",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.tls=false",
          "traefik.http.routers.${NOMAD_JOB_ID}-exporter.entrypoints=http,https",
        ]

        check {
          name = "Oracle exporter healthcheck"
          port = "port_exporter"
          type = "tcp"
          interval = "60s"
          timeout = "5s"
          check_restart {
            limit = 3
            grace = "60s"
            ignore_warnings = false
          }
        }
      }

      template {
        data = <<EOH
databases:
  default:
    username: "{{ key "oracle/amstest/user" }}"
    password: "{{ key "oracle/amstest/password" }}"
    url: "{{ key "oracle/amstest/connect_string" }}"

EOH
        destination = "local/oracle-config.yaml"
      }

      # Note this is NOT used by default, it's left here for future capabilities.
      # A custom-metrics (TOML format) can run queries and return them
      # in custom named key/value pairs, including customary # comments
      # for HELP and TYPE lines.
      template {
        data = <<EOH

[[metric]]
context = "test"
request = "SELECT 1 as value_1, 2 as value_2 FROM DUAL"
metricsdesc = { value_1 = "Custom key that always returns the value 1.", value_2 = "Custom key that always returns the value 2." }
metricstype = { value_1 = "counter" , value_2 = "counter" }


#[[metric]]
#context = "meta"
#request = "select to_char(startup_time,'YYYY-MM-DD HH24:MI:SS') "DB Startup Time" from sys.v_$instance;"
#metricsdesc = { startup_time = "Time that the database was started." }
#metricstype = { startup_time = "counter" }

EOH
        destination = "local/custom-metrics.toml"
      }
    }
  }
}

