# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
}

job "collector-opentelemetry-azure-metrics" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }
  
  group "otlp-azure-metrics" {
    count = 1

    network {

      port "healthcheck" {
        to = 13133
      }
      port "metrics" {
        to = 8888
      }
      port "influxdb" {
        to = 8086
      }
      port "pprof" {
        to = 1777
      }
      }

    

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-azure-metrics-metrics.entrypoints=https",
        "traefik.http.routers.otel-azure-metrics-metrics.rule=Host(`otel-azure-metrics.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-azure-metrics-metrics.tls=false"
      ]
    }

    service {
      name     = "otel-azure-metrics-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-azure-metrics-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-azure-metrics-healthcheck.rule=Host(`otel-azure-metrics.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otel-azure-metrics-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otel-azure-metrics-pprof"
      port     = "pprof"
      provider = "consul"
     check {
        type = "http"
        path = "/debug/pprof"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-azure-metrics-pprof.entrypoints=https",
        "traefik.http.routers.otel-azure-metrics-pprof.rule=Host(`otel-azure-metrics.obs.nsw.education`) && Path(`/debug/pprof/`)", #gotcha here, the path needs to end with a /
        "traefik.http.routers.otel-azure-metrics-pprof.tls=false"
      ]
    }

    service {
      name     = "otel-azure-metrics-influxdb"
      port     = "influxdb"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-azure-metrics.entrypoints=https",
        "traefik.http.routers.otel-azure-metrics.rule=Host(`otel-azure-metrics.obs.nsw.education`)",
        "traefik.http.routers.otel-azure-metrics.tls=false"
      ]
    }

    task "otel-azure-metrics" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }
        ports = [
            "influxdb",
            "healthcheck",
            "metrics",
            "pprof"
        ]
      }

      resources {
        cpu    = 512
        memory = 2048
      }

      template {
        data        = file("assets/collector-opentelemetry-azure-metrics.yml")
        destination = "local/otel/config.yaml"
      } 
    }
  }
}