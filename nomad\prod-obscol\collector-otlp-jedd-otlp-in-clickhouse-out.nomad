

# 2025-06-01 -- experimental - ingest OTLP (from Alloy) and emit Clickhouse

# Once this is running, you can execute sanity checks like:

#   curl -w "%{http_code}"                                         \
#        -k https://otlp-jedd-ingest.obs.nsw.education/v1/logs     \
#        -X POST                                                   \
#        -H "Content-Type: application/x-protobuf"  --output -
# ... and you should get a '200' back.

variables {
  # image_otel = "registry.obs.int.jeddi.org/opentelemetry-collector-contrib:0.122.1"
  image_otlp = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol" #0.122.0
}


job "collector-otlp-jedd" {
  datacenters = ["dc-cir-un-prod"]

  type        = "service"

  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }  

  group "collector-otlp-jedd" {
    count = 1

    network {
      port "port_metrics" {
        to = 8888
      }
      port "port_ingest" {
        to = 8088
      }
      port "port_healthcheck" {
        to = 13133
      }
      }

    service {
      name     = "otlp-jedd-metrics"
      port     = "port_metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otlp-jedd-metrics.entrypoints=http,https",
        "traefik.http.routers.otlp-jedd-metrics.rule=Host(`otlp-jedd-metrics.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otlp-jedd-metrics.tls=false"
      ]
    }

    service {
      name     = "otlp-jedd-healthcheck"
      port     = "port_healthcheck"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otlp-jedd-healthcheck.entrypoints=https,http",
        "traefik.http.routers.otlp-jedd-healthcheck.rule=Host(`otlp-jedd-healthcheck.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.otlp-jedd-healthcheck.tls=false"
      ]
    }

    service {
      name     = "otlp-jedd-ingest"
      port     = "port_ingest"
      provider = "consul"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otlp-jedd-ingest.entrypoints=https,http",
        "traefik.http.routers.otlp-jedd-ingest.rule=Host(`otlp-jedd-ingest.obs.nsw.education`)",
        "traefik.http.routers.otlp-jedd-ingest.tls=false"
      ]
    }

    task "collector-otlp-jedd" {
      driver = "docker"

      env = {
      }

      config {
        image = var.image_otlp

        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]

        ports = [
          "port_metrics",
          "port_ingest",
          "port_healthcheck"
        ]

      }

      resources {
        cpu    = 300
        memory = 500
      }

      template {
        # data = file("assets/collector-opentelemetry-basic-log-ingest.yaml")
        data = file("assets/collector-otlp-jedd.yaml")
        destination   = "local/otel/config.yaml"
      }
    }
  }
}
