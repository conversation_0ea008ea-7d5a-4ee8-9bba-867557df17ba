# OTLP collector receiver/exporter for Splunk HEC endpoint for the SAP PowerConnect integration

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

# Relatively simple collector this, some of the gotchas I experienced

    # avoid trying to user {{ env NOMAD_{service}_port }} in the collector config.yaml - for some reason when ever this was done the endpoint would not be avoidable.
    # network ports defined to go to there expected defaults inside the container, the exposed port is still a high range dynamic one.
    # /metrics service endpoint did not want to work unless the /healthcheck service was defined and working, might be expected behaviour but I could not recall.
    # 3 instances running on the 0992 hosts I think is safest.


variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol" #0.122.0
}

job "collector-otlp-powerconnect-dev" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }  

  group "powerconnect-dev" {
    count = 1

    network {
      port "metrics" {
        to = 8888
      }
      port "splunkhec-receiver-port" {
        to = 8088
      }
      port "healthcheck" {
        to = 13133
      }
      }

    task "collector-otlp-powerconnect-task-dev" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false
        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        ports = [
            "metrics",
            "splunkhec-receiver-port",
            "healthcheck"
        ]
      }
      resources {
        cpu    = 512
        memory = 4000
        memory_max = 6000

      }
      template {
        data = file("assets/collector-otlp-powerconnect-dev.yaml")
        destination   = "local/otel/config.yaml"
      }
    service {
      name     = "collector-otlp-powerconnect-metrics-dev"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-powerconnect-metrics-dev.entrypoints=http,https",
        "traefik.http.routers.collector-otlp-powerconnect-metrics-dev.rule=Host(`powerconnect-dev.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.collector-otlp-powerconnect-metrics-dev.tls=false"
      ]
    }
    service {
      name     = "collector-otlp-powerconnect-healthcheck-dev"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-powerconnect-healthcheck-dev.entrypoints=https",
        "traefik.http.routers.collector-otlp-powerconnect-healthcheck-dev.rule=Host(`powerconnect-dev.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.collector-otlp-powerconnect-healthcheck-dev.tls=false"
      ]
    }
    service {
      name     = "collector-otlp-powerconnect-receiver-dev"
      port     = "splunkhec-receiver-port"
      provider = "consul"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.powerconnect-dev.entrypoints=https,http",
        "traefik.http.routers.powerconnect-dev.rule=Host(`powerconnect-dev.obs.nsw.education`)",
        "traefik.http.routers.powerconnect-dev.tls=false"
      ]
    }      
    }
  }
}