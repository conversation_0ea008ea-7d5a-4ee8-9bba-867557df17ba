# OTLP collector receiver/exporter for Splunk HEC endpoint for the SAP PowerConnect integration

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml



variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol" #0.123.0
}

job "collector-otlp-vcenter" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }  

  group "vcenter" {
    count = 1

    network {
      port "metrics" {
        to = 8888
      }
      port "healthcheck" {
        to = 13133
      }
      }

    task "vcenter" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false
        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        ports = [
            "metrics",
            "healthcheck"
        ]
      }
      resources {
        cpu    = 200
        memory = 2000
      }
      template {
        data = file("assets/collector-otlp-vcenter.yaml")
        destination   = "local/otel/config.yaml"
      }
    service {
      name     = "collector-otlp-vcenter-metrics"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-metrics.entrypoints=http,https",
        "traefik.http.routers.collector-otlp-vcenter-metrics-metrics.rule=Host(`vcenter.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.collector-otlp-vcenter-metrics-metrics.tls=false"
      ]
    }
    service {
      name     = "collector-otlp-vcenter-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-healthcheck.entrypoints=https",
        "traefik.http.routers.collector-otlp-vcenter-healthcheck.rule=Host(`vcenter.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.collector-otlp-vcenter-healthcheck.tls=false"
      ]
    }  
    }
  }
}