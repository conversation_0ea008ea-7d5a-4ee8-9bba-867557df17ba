# RECONCILIATIOn PDU job - using the OLD (pre-2023-06) list of PDUs taken from Zabbix
#
# 2023-06-20 - relatively short-lived job, will be discarded once side-by-side is done
#
# collector-pdu-reconciliation - Power Distribution Units (SNMP) - PROD ObsCol

# Combined prometheus + exporter for PDU SNMP scraping
# refer:  https://confluence.education.nsw.gov.au/display/PM/Observing+PDU+Power+Distribution+Units+in+GovDC

# Standard collector architecture of two jobs:  prometheus + SNMP exporter

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
  image_snmp-exporter   = "quay.education.nsw.gov.au/observability/prom/snmp-exporter:prod-obscol"
}

job "collector-pdu-reconciliation" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }


  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-pdu-reconciliation" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
      port "port_exporter" { 
        to = 9117
      }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-pdu-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://collector-pdu-reconciliation-prometheus.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for PDU Power Distribution Units",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          # Rules are not used at commissioning (2023-06) but may be in the future.
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d",
          "/opt/sharednfs/prometheus-configuration/prod/snmp/snmp_pdu_pre_2023-06.yml:/etc/prometheus/snmp_pdu.yml"

        ]
      }

      resources {
        cpu    = 150
        memory = 2500
        memory_max = 3000
      }

      service {
        name = "collector-pdu-reconciliation-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-pdu-reconciliation-prometheus.rule=Host(`collector-pdu-reconciliation-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-pdu-reconciliation-prometheus.tls=false",
          "traefik.http.routers.collector-pdu-reconciliation-prometheus.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: collector-pdu-reconciliation

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-pdu'
    static_configs:
      - targets: ['collector-pdu-reconciliation-prometheus.obs.nsw.education']

    # Drop surplus exporter series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: "go_(.*)|prometheus_http_(.*)"
        action: drop

  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'exporter-pdu'
    static_configs:
      - targets: ['collector-pdu-reconciliation-exporter.obs.nsw.education']

    # Drop surplus exporter series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: "go_(.*)|prometheus_http_(.*)"
        action: drop

  # Using SNMP exporter to do a custom web-check for GovDC PDUs (power distribution units)
  # with custom URLs and regex string match on target page.

  - job_name: 'snmp_pdu'
    metrics_path: /snmp
    scrape_interval: 2m

    # Note there are the following top-level stanzas in our snmp-enlogic-pdu.yml:
    # apcups:
    # arista_sw:
    # cisco_wlc:
    # ddwrt:
    # eaton_epdu:
    # enlogic_pdu:
    # if_mib:
    # infrapower_pdu:
    # keepalived:
    # kemp_loadmaster:
    # liebert_pdu:
    # mikrotik:
    # nec_ix:
    # paloalto_fw:
    # printer_mib:
    # raritan:
    # servertech_sentry3:
    # servertech_sentry4:
    # synology:
    # ubiquiti_airfiber:
    # ubiquiti_airmax:
    # ubiquiti_unifi:
    # wiener_mpod:

    params:
      module: ["enlogic_pdu"]

    file_sd_configs:

    # As of 2023-03-21 we are now reading prometheus-configuration in hourly to a local
    # file system mount, rather than at job start.  So the file_sd_config is relevant.
    # This replaced the retrieval of prometheus-configuration from git at job launch.
    - refresh_interval: 1h
      files: 
      - /etc/prometheus/snmp_pdu.yml

    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: collector-pdu-reconciliation-exporter.obs.nsw.education


# 2023-06-20 jedd - we are NOT keeping these duplicate data - we can reconcile
# against the mimir data populated by the original collector-pdu job
#
#remote_write:
#  - name: mimir
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true


EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-pdu-prometheus"


    # TASK exporter for pdu   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-pdu-exporter" {
      driver = "docker"

      config {
        image = var.image_snmp-exporter

        ports = [ "port_exporter" ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

        dns_servers = [ "************" ]

        volumes = [
          "/opt/sharednfs/prometheus-configuration/snmp/snmp-enlogic-pdu.yml:/etc/snmp_exporter/snmp.yml"
        ]

        args = [
          "--web.listen-address=:${NOMAD_PORT_port_exporter}",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn",
          "--config.file=/etc/snmp_exporter/snmp.yml"
        ]
      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu    = 100
        memory = 200
        memory_max = 400
      }

      service {
        name = "collector-pdu-reconciliation-exporter"
        port = "port_exporter"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-pdu-reconciliation-exporter.rule=Host(`collector-pdu-reconciliation-exporter.obs.nsw.education`)",
          "traefik.http.routers.collector-pdu-reconciliation-exporter.tls=false",
          "traefik.http.routers.collector-pdu-reconciliation-exporter.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

        check {
          type = "http"
          port = "port_exporter"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

    }  // END-task "task-pdu-exporter"

  }

}

