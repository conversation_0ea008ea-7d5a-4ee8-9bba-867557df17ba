
// collector-school-routers - prod

# 2025-09 jedd
# refer:  STRY0033113
#
# Poll (via blackbox exporter, via ICMP ping) school routers.
#
# We use a similar method to the DFS blackbox scanners, where the Prometheus
# task uses a `file_sd` scan to a location on the /opt/sharednfs/ that is
# in turn populated (and regularly updated) via a git repository.
#
# Some of the magic is in the conversion of that csv into a format for this
# purpose.




# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
}

locals {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
  image_blackbox = "quay.education.nsw.gov.au/observability/blackbox-exporter:v0.27.0"
  image_python39 = "quay.education.nsw.gov.au/observability/python:3.9-bullseye"

  #host_constraint = ".*0475.*"
  host_constraint = ".*0992.*"
  #host_constraint = ".*"

  loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
}




# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "collector-school-routers" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }


  # GROUP converter  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-school-routers-converter" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }
    
    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK python container for conversion script  = = = = = = = = = =

    task "task-school-routers-csv-converter" {
      driver = "docker"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = local.image_python39

        command = "/obs-app-school-routers/looper.sh"

        network_mode = "host"

        ports = [ ]

        volumes = [
          "/opt/sharednfs/obs-app-school-routers:/obs-app-school-routers"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu = 20
        memory = 50
        memory_max = 120
      }

      service {
        name = "collector-school-routers-csv-converter"
        meta {
          cir_app_id = "obs"
          env = "prod"
        }
      }

    }  // end-task "task-school-routers-csv-converter"
  }  // end-group "task-school-routers-csv-converter"








  # GROUP prometheus  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-school-routers-prometheus" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    network {
      port "port_prometheus" { }
  	}


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-school-routers-prometheus" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://collector-school-routers-prometheus.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for School Routers"
        ]

        image = local.image_prometheus

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/obs-app-school-routers:/obs-app-school-routers"
        ]
      }

      resources {
        cpu = 250
        memory = 4000
        memory_max = 6000
      }

      service {
        name = "collector-school-routers-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-school-routers-prometheus.rule=Host(`collector-school-routers-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-school-routers-prometheus.tls=false",
          "traefik.http.routers.collector-school-routers-prometheus.entrypoints=http,https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-school-routers"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-school-routers'
    static_configs:
      - targets: ['collector-school-routers-prometheus.obs.nsw.education']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-school-routers'
    static_configs:
      - targets: ['collector-school-routers-blackbox.obs.nsw.education']
    # Drop surplus blackbox series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  - job_name: 'school-routers-pinger'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # We are expecting very low rotation of data in this repository, and also
    # note interplay with the settings in looper.sh to pull from git (probably
    # two hourly at most)
    - refresh_interval: 120m
      files: 
      - /obs-app-school-routers/targets.yaml
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-school-routers-blackbox.obs.nsw.education

    metric_relabel_configs:
      # Drop surplus blackbox series.  We receive this set from blackbox with icmp / ping target:
      #   probe_dns_lookup_time_seconds
      #   probe_duration_seconds
      #   probe_icmp_duration_seconds{phase="resolve"}
      #   probe_icmp_duration_seconds{phase="rtt"}
      #   probe_icmp_duration_seconds{phase="setup"}
      #   probe_icmp_reply_hop_limit
      #   probe_ip_addr_hash
      #   probe_ip_protocol
      #   probe_success
      # But we really only care about probe_success, so we'll drop everything else.

#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep

# ENABLE this once we're happy with it
remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers:
      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true

# ENABLE this if we want to throw to cloud also - probably NOT
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  

EOH
        destination = "local/prometheus.yaml"
      }
    }  // end-task "task-school-routers-prometheus"
  }  // end-group "collector-school-routers-prometheus"








  # GROUP blackbox  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-school-routers-blackbox" {

    count = 2
   
    restart {
      interval = "10m"
      attempts = 20
      delay = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = local.host_constraint
    }

    network {
      port "port_blackbox" { }
  	}


    # TASK blackbox for school routers   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-school-routers-blackbox" {
      driver = "docker"


      config {
        ports = [ "port_blackbox" ]

        image = local.image_blackbox

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      env {
        # Might be needed, as some endpoints are on 153.* (though vast majority are 10/8)
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu = 50
        memory = 250
        memory_max = 500
      }


      service {
        name = "collector-school-routers-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-school-routers-blackbox.rule=Host(`collector-school-routers-blackbox.obs.nsw.education`)",
          "traefik.http.routers.collector-school-routers-blackbox.tls=false",
          "traefik.http.routers.collector-school-routers-blackbox.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
modules:
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

EOH
        destination = "local/config.yml"
      }

    }  // end-task "task-school-routers-blackbox"
  }  // end-group "collector-school-routers-blackbox"

}  // end-job "collector-school-routers"


