
# generic go lang env for jedd

# Needs the command / args to sleep indefinitely otherwise it bombs out immediately.

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_golang = "golang:1.24-bookworm"


  #host_constraint = "pl0992obscol.*"
  #host_constraint = "pl0475obscol.*"
  host_constraint = ".*01"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "golang-jedd" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "golang-jedd" {

    network {
      port "port_golang" {
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = var.host_constraint
    }

    ephemeral_disk {
      size = 300
    }

    task "golang-jedd" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
      }

      config {
        image = "${var.image_golang}"
        ports = ["port_golang"]
        command = "/bin/sleep"
        args = [" infinity"]
        privileged = "true"

        volumes = [
          "/opt/sharednfs/jedd:/jedd",
          "local/.bashrc:/root/.bashrc"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]golang-jedd (docker):\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 500 
        memory = 2048
      }

      service {
        name = "golang-jedd"
        port = "port_golang"

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}
