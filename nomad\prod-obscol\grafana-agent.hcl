# Grafana Agent
#
# This is the madness of <PERSON>.
#
# GrafanaCloud integrations are built ontop of the GrafanaAgent integration, this job is to deploy the agent to the cluster.

variables {
  image_grafana-agent = "quay.education.nsw.gov.au/observability/grafana-agent:prod-obscol"
}

job "grafana-agent" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "grafana-agents" { # yes plural, as we can have multiple agents configs running in the same job
    count = 1
    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }
    task "redis-metrics-integration" {
      driver = "docker"

      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = var.image_grafana-agent
        
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=grafana-agent-redis"
                }
            } 
        volumes = [
          "local/etc/agent/agent.yaml:/etc/agent/agent.yaml",
          ]
        }

      template {
        data        = file("assets/grafana-agent-redis.yaml")
        destination = "local/etc/agent/agent.yaml"
      }        

      resources {
        cpu    = 500 #MHz
        memory = 256 #MB
      }
    }    
    task "traefik-integration" {
      driver = "docker"

      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = var.image_grafana-agent
        
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=grafana-agent-traefik"
                }
            } 
        volumes = [
          "local/etc/agent/agent.yaml:/etc/agent/agent.yaml",
          ]
        }

      template {
        data        = file("assets/grafana-agent-traefik.yaml")
        destination = "local/etc/agent/agent.yaml"
      }        

      resources {
        cpu    = 500 #MHz
        memory = 256 #MB
      }
    }     
  }
}
