
# Grafana PDC Private Data Connect agent - PROD ObsCol

# =====================================================================
# NOTES are from Jedd-lab implementation -- circa 2023-11/12 -- these will
# be removed once we're functional in prod-obscol 2024-01.

# Commandline provided by grafana.net to configure to my server is:

# Review processes within the container:
#
# proc 1 - pdc agent proper:
#   /usr/bin/pdc 
#     -token ********************************************************************************************************************************
#     -cluster prod-au-southeast-0 
#     -gcloud-hosted-grafana-id 461795

# proc 15 - ssh tunnel:
#   ssh 
#     -i /home/<USER>/.ssh/grafana_pdc
#     <EMAIL>
#     -p 22
#     -R 0
#     -o CertificateFile=/home/<USER>/.ssh/grafana_pdc-cert.pub
#     -o ConnectTimeout=1
#     -o ServerAliveInterval=15
#     -o UserKnownHostsFile=/home/<USER>/.ssh/grafana_pdc_known_hosts 
#     -vv

### pdc agent takes these parameters (0.0.20)
#
#    Usage of pdc:
#      -cluster string
#            the PDC cluster to connect to use
#      -domain string
#            the domain of the PDC cluster (default "grafana.net")
#      -gcloud-hosted-grafana-id string
#            The ID of the Hosted Grafana instance to connect to
#      -h    Print help
#      -log-level int
#            The level of log verbosity. The maximum is 3. (default 2)
#      -log.level string
#            "debug", "info", "warn" or "error" (default "info")
#      -network string
#            DEPRECATED: The name of the PDC network to connect to
#      -ssh-flag value
#            Additional flags to be passed to ssh. Can be set more than once.
#      -ssh-key-file string
#            The path to the SSH key file. (default "/home/<USER>/.ssh/grafana_pdc")
#      -token string
#            The token to use to authenticate with Grafana Cloud. It must have the pdc-signing:write scope
#    
#    If pdc-agent is run with SSH flags, it will pass all arguments directly through to the "ssh" binary. This is deprecated behaviour.





# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_grafana_pdc = "grafana/pdc-agent:0.0.23"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-pdc" {
  type        = "service"

  datacenters = ["dc-cir-un-prod"]

  group "grafana-pdc" {

    count = 6
    constraint {
      operator = "distinct_hosts"
      value = "true"
    }

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    task "grafana-pdc" {
      driver = "docker"

      config {
        image = "${var.image_grafana_pdc}"

        args = [ 
          #"-token", "********************************************************************************************************************************",
          #"-cluster", "prod-au-southeast-0",
          #"-gcloud-hosted-grafana-id", "461795",
          #"-ssh-flag", "ProxyCommand nc --proxy proxy.det.nsw.edu.au:80 %h %p",


          # token name :  'prod-obscol'
          "-token", "****************************************************************************************************************************",
          "-cluster", "prod-au-southeast-0",
          "-gcloud-hosted-grafana-id", "533612",

          # "-log.level", "debug"
        ]

        volumes = [ 
          "local/ssh_config_grafana.conf:/etc/ssh/ssh_config.d/ssh_config_grafana.conf"
        ]


        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }        

      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        # 2025-05-25 jedd - extending no proxy to include thanos / openshift endpoint domains
        # example:  https://thanos-querier-openshift-monitoring.apps.s0.ocp.svcs.education.nsw.gov.au/
        # NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,1********,10.0.0.0/8,***********/16,**********/16"

        # NO_PROXY = ".education.nsw.gov.au,.nsw.education,.det.nsw.edu.au,.consul,.svc,1********,10.0.0.0/8,***********/16,**********/16"
        # 2025-05-12 jedd - added .education.nsw.gov.au
        # NO_PROXY = "thanos-querier-openshift-monitoring.apps.s0.ocp.svcs.education.nsw.gov.au,.nsw.education,.det.nsw.edu.au,.consul,.svc,1********,10.0.0.0/8,***********/16,**********/16"
        NO_PROXY = "thanos-querier-openshift-monitoring.apps.s0.ocp.svcs.education.nsw.gov.au,.education.nsw.gov.au,.nsw.education,.det.nsw.edu.au,.consul,.svc,1********,10.0.0.0/8,***********/16,**********/16"

      }

      resources {
        cpu = 200
        memory = 200
        memory_max = 1000
      }

      template {
        data = <<EOH

# This file added by grafana-pdc.nomad at launch to try to
# resolve timeouts on SSH connections.  Refer:  OBS-761

TCPKeepAlive yes
ServerAliveInterval 300

EOH
        destination = "local/ssh_config_grafana.conf"
        perms = "644"
        uid = 0
        gid = 0
      }


#      template {
#        data = <<EOH
#Host       private-datasource-connect-prod-au-southeast-0.grafana.net
#  Hostname private-datasource-connect-prod-au-southeast-0.grafana.net
#  User     461795
#  Port     22
#  # ProxyCommand nc --proxy proxy.det.nsw.edu.au:80 %h %p
#
#EOH
#        destination = "local/ssh-config"
#        perms = "755"
#        uid = 30000
#        gid = 30000
#      }

      service {
        name = "grafana-pdc"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-pdc.entrypoints=http,https",
          "traefik.http.routers.grafana-pdc.tls=false",
          "traefik.http.routers.grafana-pdc.rule=Host(`grafana-pdc.obs.nsw.education`)",
        ]
      }

    } // end-task grafana-pdc

  } // end-group grafana-pdc

} // end-job grafana-pdc



