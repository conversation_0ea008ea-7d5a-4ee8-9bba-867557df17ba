
// homepage-sap - ObsCol Prod

// Refer:  https://gethomepage.dev/latest/

variables {
  # consul_hostname = "pl0992obscol01.nsw.education:8500"
  # image_homepage = "ghcr.io/gethomepage/homepage:v0.9.5"
  image_homepage = "quay.education.nsw.gov.au/observability/homepage:v0.9.5"
  image_python = "quay.education.nsw.gov.au/observability/python:3.9-bullseye"
}


# JOB - homepage-sap  = = = = = = = = = = = = = = = = = = = = = = = = =
job "homepage-sap"  {
  datacenters = ["dc-cir-un-prod"]

  type = "service"

  group "homepage-sap" {
    network {
      port "port_http" {
        to = 3000
      }
    }

    restart {
      interval = "10m"
      attempts = 20
      delay = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl.*obscol0[123678]"
    }

    # TASK - homepage-sap  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "homepage-sap" {
      driver = "docker"

      # Politeness
      kill_timeout = "30s"
      kill_signal = "SIGTERM"

      env = {
        # It's just easier if we have local timezone inside the container.
        TZ = "Australia/Sydney"
        # Logs are written to ${HOME}/config/logs/homepage.log
        # Alternatively we can direct the configuration 'logpath:' to /dev/null
        # @TODO jedd - modify sibling task (git pull etc) to delete the log at
        # startup - or something more sophisticated.
        LOG_LEVEL = "warn"

        # Needed for accessing the weather system (remote)
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_homepage}"

        hostname = "homepage-sap"

        ports = ["port_http"]

        args  = [ 
          ]

        volumes = [ 
          "/opt/sharednfs/obs-app-homepage-sap/config:/app/config/",
          "/opt/sharednfs/obs-app-homepage-sap/images:/app/public/images/",
          "/opt/sharednfs/obs-app-homepage-sap/icons:/app/public/icons/",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }        

      }

      resources {
        cpu = 100
        memory = 200
        memory_max = 500
      }

      service {
        name = "homepage-sap"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.homepage-sap.rule=Host(`homepage-sap.obs.nsw.education`)",
          "traefik.http.routers.homepage-sap.tls=false",
          "traefik.http.routers.homepage-sap.entrypoints=http,https",
        ]

      }

    } // end-task homepage-sap


    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK homepage-sap-updater
    task "homepage-sap-updater" {
      driver = "docker"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      }

      config {
        # image = "python:3.9-bullseye"
        image = "${var.image_python}"

        command = "/obs-app-homepage/looper.sh"

        network_mode = "host"

        ports = [ ]

        volumes = [
          "/opt/sharednfs/obs-app-homepage-sap:/obs-app-homepage"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu = 20
        memory = 50
      }

      service {
        name = "obs-app-homepage-sap-updater"
        meta {
          cir_app_id = "obs"
          env = "prod"
        }
      }
    }  // end-task  homepage-sap-updater

  }
}
