// obs-col prod - prometheus-LTS.nomad job definition

// Long term storage instance - resides on obscol02
// to allow concurrent 'prometheus' to run.
// Refer Jedd or ROMEO-764     (2021-07)

// Formerly Prometheus-LTS - Eamon

job "prometheus" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus" {

    network {
      port "http" {
//        static = 9090
      }
    }

//    volume "prometheus"  {
//      type = "host"
//      source = "prometheus"
//      read_only = false
//    }

//    constraint {
//      attribute = "${attr.unique.hostname}"
//      value = "pl0992obscol02.nsw.education"
//    }

    task "prometheus" {
      driver = "docker"

//      volume_mount {
//        volume = "prometheus"
//        destination = "/prometheus"
//        read_only = false
//      }

      config {
        ports = ["http"]
        image = "https://docker.io/prom/prometheus:v2.29.1"
        dns_servers = ["************"]
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/prometheus/rules:/etc/prometheus/rules.d"
        ]
        // "--storage.tsdb.path=/prometheus",
        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus.obs.nsw.education",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]
      }

      service {
        name = "prometheus"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus.rule=Host(`prometheus.obs.nsw.education`)",
          "traefik.http.routers.prometheus.tls=false",
          "traefik.http.routers.prometheus.entrypoints=http,https,prometheus",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }

      artifact {
        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
        destination = "local/prometheus-configuration"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
# Do not add this label it will cause duplicate series every time a prometheus scraper restarts
#    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'alertmanager'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['alertmanager']

  - job_name: 'nomad'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nomad-client', 'nomad']
        tags: ['http']

  - job_name: 'consul_metrics'
    static_configs:
      - targets: ['pl0992obscol01.nsw.education:8500',
                  'pl0992obscol02.nsw.education:8500',
                  'pl0992obscol03.nsw.education:8500' ]
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']

  - job_name: 'oracle_metrics'
    static_configs:
      - targets: ['pl0992obscol02.nsw.education:9161']
    metrics_path: /metrics

  - job_name: 'loki_metrics'
    static_configs:
      - targets: ['loki.obs.nsw.education:3100']
    metrics_path: /metrics

  - job_name: 'tempo_metrics'
    static_configs:
      - targets: ['tempo.obs.nsw.education:3200']
    metrics_path: /metrics

  - job_name: 'traefik_metrics'
    static_configs:
      - targets: ['pl0992obscol01.nsw.education:8081',
                  'pl0992obscol02.nsw.education:8081',
                  'pl0992obscol03.nsw.education:8081' ]
    metrics_path: /metrics



  # 2021-12-01 jedd - static until we get this bedded down
  # native confluent metrics (rather than the API method used by the ccloud nomad job)
  - job_name: Confluent Cloud
    scrape_interval: 2m
    scrape_timeout: 1m
    honor_timestamps: true
    static_configs:
      - targets:
        - api.telemetry.confluent.cloud
    scheme: https
    basic_auth:
      username: "{{ key "ccloud/api-key" }}"
      password: "{{ key "ccloud/api-secret" }}"
    
    proxy_url: "http://proxy.det.nsw.edu.au:80"




    metrics_path: /v2/metrics/cloud/export
    params:
      "resource.kafka.id":
        # DEV-XFI
        # Kafka: lkc-1k503
        # Schema Registry: lsrc-qqz9d
        # Connector: lcc-2zy52
        - lkc-1k503

        # Production cluster (2021-12)
        # Kafka :lkc-73zyj
        # Schema registry : lsrc-o0y3p
        # Connector : lcc-zokzd
        - lkc-73zyj

        # Test
        # Kafka: lkc-z1nrd
        # Schema Registry: lsrc-0vvkq
        # Connector: lcc-11ry5
        - lkc-z1nrd

        # Pre-Prod
        # Kafka : lkc-11qw6
        # Schema registry: lsrc-3ojnw
        # Connector: lcc-ggk0r
        - lkc-11qw6






  # ping targets are ICMP only (no agent) using blackbox exporter:
  # eg. http://blackbox.obs.nsw.education:9115/probe?target=__address__&type=icmp
  - job_name: 'ping_member_servers_sd'
    scrape_interval: 1m
    metrics_path: /probe
    params:
      module: ["icmp"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['pingtarget']
    relabel_configs:
      - source_labels: [__meta_consul_address]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: pl0992obscol01.nsw.education:9115


  # Any external node that runs telegraf
  - job_name: 'telegraf'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['telegraf']
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_env]
        target_label: env


  # Any external node that runs openmetrics (migrated from 'telegraf' 2021-07-30)
  - job_name: 'openmetrics'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['openmetrics']
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path



  # Any standalone exporter that lives in the nomad cluster and not the agent
  - job_name: 'prometheus-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['prometheus-exporter']

  - job_name: 'nifi'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nifi']

  - job_name: 'ssl'
    metrics_path: /probe
    params:
      module: ["https"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['https']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: ssl-exporter.service.dc-cir-un-prod.collectors.obs.nsw.education:9219

#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['alertmanager']

remote_write:
{{ range service "cortex" }}
  - name: {{ .Name }}
    url: "http://{{ .Address }}:{{ .Port }}/api/v1/push"
{{ end }}
#  - name: metricbeat
#    url: "http://prometheus-remote-write.service.dc-cir-un-prod.collectors.obs.nsw.education:9201/write"

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 1500
        memory = 4000
      }

    }
  }
}
