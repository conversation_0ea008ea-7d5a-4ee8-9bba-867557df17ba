
// jedd lab dg - influxdb nomad job

variables {
  image_influxdb = "quay.education.nsw.gov.au/observability/influxdb:prod-obscol"
}

job "influxdb" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "influxdb" {
    count = 1
    
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_influxdb" {
      type = "host"
      source = "vol_influxdb"
      read_only = false
    }

    network {
      port "port_influxdb"  {
        static = 8086
      }
    }

    task "influxdb" {

      driver = "docker"

      volume_mount {
        volume      = "vol_influxdb"
        destination = "/mnt/influxdb"
        read_only   = false
      }

      config {
        image = var.image_influxdb
        privileged = true
        ports = ["port_influxdb"]

        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
                }
            }

        args = [
          "--bolt-path=/var/lib/influxdb2/influxd.bolt",
          "--engine-path=/var/lib/influxdb2/engine",
          "--log-level=info",
          "--flux-log-enabled=true"
        ]
      volumes = [
          "/opt/sharednfs/influxdb/config:/etc/influxdb2",
          "/opt/sharednfs/influxdb/data:/var/lib/influxdb2"
        ]
      }

      env = {
        INFLUXDB_CONFIG_PATH = "/var/lib/influxdb2/influxdb.bolt"
      }

      template {
        data = file("assets/influxdb-config.yml")
        destination = "local/influxdb.yaml"
      }

      resources {
        cpu    = 512
        memory = 2048
        memory_max = 4048
      }

      service {
        name = "influxdb"
        port = "port_influxdb"

        check {
          name     = "Influxdb healthcheck"
          port     = "port_influxdb"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.influxdb.rule=Host(`influxdb.obs.nsw.education`)",
          "traefik.http.routers.influxdb.tls=false",
        ]

      }

    }
  }
}

