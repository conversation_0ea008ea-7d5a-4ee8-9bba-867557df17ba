
# Jupyter docker stack for ObsCol Prod

# https://jupyter-docker-stacks.readthedocs.io/en/latest/index.html

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_jupyter = "jupyter/scipy-notebook:2023-07-25"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "jupyter"  {
  type = "service"

  datacenters = ["dc-cir-un-prod"]

  update {
    # default is 5m, but the 1.2gb takes a while to pull down and then unpack,
    # and health must be < progress.
    healthy_deadline = "10m"
    progress_deadline = "11m"
  }

  group "jupyter" {
    network {
      port "port_jupyter" {
        to = 8888
      }
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }


    task "jupyter" {
      driver = "docker"

      config {
        image = "${var.image_jupyter}"

        ports = ["port_jupyter"]

        # command = "/local/prestart.sh"
        # args  = [ "3000" ]

        volumes = [ 
          "/opt/sharednfs/jupyter:/jupyter"
        ]

        network_mode = "host"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

      }

      resources {
        cpu = 500
        memory = 512
        memory_max = 1500
      }

      service {
        name = "jupyter"
        port = "port_jupyter"

        tags = [
         "traefik.enable=true",
         # "traefik.http.routers.jupyter.rule=Host(`jupyter.obs.nsw.education`)",
         "traefik.http.routers.jupyter.rule=Host(`jupyter.obs.nsw.education`) &&  PathPrefix(`/jupyter{regex:$$|/.*})",
         "traefik.http.routers.jupyter.tls=false",
         "traefik.http.routers.jupyter.entrypoints=http,https",

        ]

      }


    }
  }
}

