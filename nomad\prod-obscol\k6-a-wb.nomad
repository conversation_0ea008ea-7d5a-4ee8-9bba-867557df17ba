
// k6-a-wb - run synthetics checks locally.
// LOCAL Browser (chromium) version

//  This  is a single-shot application, so we use the contents of
//  the file at:/opt/sharednfs/k6/script-a-wb.js

# Variables = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # @TODO replicate to quay
  # image_k6 = "grafana/k6:0.53.0-with-browser"
  # image_k6 = "quay.education.nsw.gov.au/observability/k6-with-browser:0.53.0"
  image_k6 = "quay.education.nsw.gov.au/observability/k6-with-browser:0.51.0"
}

# Job = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "k6-a-wb" {
  datacenters = ["dc-cir-un-prod"]
  
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "k6-a-wb" {
    count = 1

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol03.nsw.education"
    }

    network {
      port "port_k6"  {
        static = 6565
      }
    }

    task "k6-a-wb" {

      driver = "docker"

      env = {
        # This is for Influx V1 -- which is way out-dated.
        # "K6_OUT" = "influxdb=http://dg-pan-01.int.jeddi.org:8086/k6"

        # This is required for Influx V2 - refer https://github.com/grafana/xk6-output-influxdb
        # regrettably not a stock component for k6, so can't use the above (K6_OUT) env approach.
        # Also requires arg parsing (refer arg[] section)
#        K6_INFLUXDB_ORGANIZATION="DG"
#        K6_INFLUXDB_BUCKET="k6"
#        K6_INFLUXDB_TOKEN="Ju3SZTIxuawPOIgoprAn9mI09fWfyBk830a-PB51e7pA7G-POBJmKzoaVYxqR_oqgSzDazFTgA8dJZLFZ5EcYw=="
#        K6_INFLUXDB_ADDRESS="http://dg-pan-01.int.jeddi.org:8086"
#        # Weird-arse inverted logic again - true here means it is INsecure which means it ignore absence of TLS
#        K6_INFLUXDB_INSECURE=true

#        K6_PROMETHEUS_RW_SERVER_URL="http://dg-pan-01.int.jeddi.org:9091/api/v1/write"


        # Note that the loadimpact/k6 and grafana/k6 images both assert:
        #  "invalid output type 'xk6-influxdb', available types are: cloud, csv, experimental-prometheus-rw, influxdb, json, statsd"

        # For the k6 docker image tag that ends with '-with-browser' variant,
        # this env variable is needed to actually enable that feature (so we
        # may be able to just have that image, rather than both?).
        #
        # Refer from 2023-02:
        #   https://k6.io/blog/k6-browser-experimental-module-announcement/
        K6_BROWSER_ENABLED=true
      }

      config {
        # image = "grafana/k6"
        image = var.image_k6
        # image = "loadimpact/k6"

        # For debugging, set an entrypoint and force root user to see
        # what's going on inside an otherwise ephemeral container.
        #
        # entrypoint = "/bin/sh"
        # user = "root"

        # dns_servers = ["************"]

        ports = ["port_k6"]

        logging  {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "/opt/sharednfs/k6:/scripts",

          # example scripts bundled in this nomad job
          # "local/obscol-nomad-jobs.js:/scripts/obscol-nomad-jobs.js",
          # "local/basic-k6-io.js:/scripts/basic-k6-io.js",
        ]

        # command = "k6" 
        # command = "sleep 5m" 
         # command = "sleep 5m" 

        # cwd = "/scripts"

        args = [
          "run" , "/scripts/k6-a-wb.js",

          # "--log-output" , "https://loki.obs.nsw.education/loki/api/v1/push",

          # this should work for influx v2, but requires compilation / non-bundled plugin
          # "-o", "xk6-influxdb=http://dg-pan-01.int.jeddi.org:8086",

          # Target is defined by K6_PROMETHEUS_RW_SERVER_URL environment variable set above.
          # "-o", "experimental-prometheus-rw",


          # "run" , "/mnt/k6/dg-nomad.js",
          # "run" , " '/mnt/k6/basic-k6-io.js'",
          # "run" , " /dg-nomad.js",
          # "run" , " /basic-k6-io.js",
          # "-t simple"
        ]

      }

#      template {
#        data = file("assets/k6-config.yml")
#        destination = "local/k6.yaml"
#      }

      template {
        data = <<EOH

import http from 'k6/http';
import { check } from "k6";

export let options = {
  stages: [
      // 
      { duration: "15s", target: 1 },

      // Stay at rest on 3 VUs for 60s
      { duration: "60s", target: 3 },

      // Ramp-down from 5 to 0 VUs for 5s
      { duration: "5s", target: 0 }
  ]
};

export default function () {
  const response = http.get("http://pl0992obscol.nsw.education:4646/ui/jobs", {headers: {Accepts: "application/json"}});
  check(response, { "status is 200": (r) => r.status === 200 });
};

EOH
        destination = "local/obscol-nomad-jobs.js"
      }

      template {
        data = <<EOH

import http from 'k6/http';
import { sleep } from 'k6';

export default function () {
  http.get('https://test.k6.io');
  sleep(1);
}

EOH

        destination = "local/basic-k6-io.js"
      }

      resources {
        cpu = 300
        memory = 500
        memory_max = 2000
      }

      service {
        name = "k6-a-wb"
        port = "port_k6"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.k6-a-wb.rule=Host(`k6-a-wb.obs.nsw.education`)",
          "traefik.http.routers.k6-a-wb.tls=false",
          "traefik.http.routers.k6-a-wb.entrypoints=http,https",
        ]

      }

    }
  }
}

