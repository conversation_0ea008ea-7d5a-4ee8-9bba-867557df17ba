# https://grafana.com/docs/loki/latest/operations/loki-canary/
# Job to perform canary testing on <PERSON>, as we deploy and potentially leverage this service in production we want to ensure that it is working as expected.


job "loki-canary" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol.*"
}
  group "loki-canary" {
    count = 1

    network {
      port "http" {
        to = 3500
      }
    }

    task "loki-canary" {
      driver = "docker"

      config {
        image = "quay.education.nsw.gov.au/observability/loki-canary:prod-obscol"
        args = [
          "-addr=loki.obs.nsw.education",
        ]

        ports = ["http"]
      }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "loki-canary"
        port = "http"

        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}

#   -addr string
#     	The Loki server URL:Port, e.g. loki:3100. Loki address can also be set using the environment variable LOKI_ADDRESS.
#   -buckets int
#     	Number of buckets in the response_latency histogram (default 10)
#   -ca-file string
#     	Client certificate authority for optional use with TLS connection to Loki
#   -cert-file string
#     	Client PEM encoded X.509 certificate for optional use with TLS connection to Loki
#   -insecure
#     	Allow insecure TLS connections
#   -interval duration
#     	Duration between log entries (default 1s)
#   -key-file string
#     	Client PEM encoded X.509 key for optional use with TLS connection to Loki
#   -labelname string
#     	The label name for this instance of loki-canary to use in the log selector (default "name")
#   -labelvalue string
#     	The unique label value for this instance of loki-canary to use in the log selector (default "loki-canary")
#   -max-wait duration
#     	Duration to keep querying Loki for missing websocket entries before reporting them missing (default 5m0s)
#   -metric-test-interval duration
#     	The interval the metric test query should be run (default 1h0m0s)
#   -metric-test-range duration
#     	The range value [24h] used in the metric test instant-query. Note: this value is truncated to the running time of the canary until this value is reached (default 24h0m0s)
#   -out-of-order-max duration
#     	Maximum amount of time to go back for out of order entries (in seconds). (default 1m0s)
#   -out-of-order-min duration
#     	Minimum amount of time to go back for out of order entries (in seconds). (default 30s)
#   -out-of-order-percentage int
#     	Percentage (0-100) of log entries that should be sent out of order.
#   -pass string
#     	Loki password. This credential should have both read and write permissions to Loki endpoints
#   -port int
#     	Port which loki-canary should expose metrics (default 3500)
#   -pruneinterval duration
#     	Frequency to check sent vs received logs, also the frequency which queries for missing logs will be dispatched to loki (default 1m0s)
#   -push
#     	Push the logs directly to given Loki address
#   -query-timeout duration
#     	How long to wait for a query response from Loki (default 10s)
#   -size int
#     	Size in bytes of each log line (default 100)
#   -spot-check-initial-wait duration
#     	How long should the spot check query wait before starting to check for entries (default 10s)
#   -spot-check-interval duration
#     	Interval that a single result will be kept from sent entries and spot-checked against Loki, e.g. 15min default one entry every 15 min will be saved and then queried again every 15min until spot-check-max is reached (default 15m0s)
#   -spot-check-max duration
#     	How far back to check a spot check entry before dropping it (default 4h0m0s)
#   -spot-check-query-rate duration
#     	Interval that the canary will query Loki for the current list of all spot check entries (default 1m0s)
#   -streamname string
#     	The stream name for this instance of loki-canary to use in the log selector (default "stream")
#   -streamvalue string
#     	The unique stream value for this instance of loki-canary to use in the log selector (default "stdout")
#   -tenant-id string
#     	Tenant ID to be set in X-Scope-OrgID header.
#   -tls
#     	Does the loki connection use TLS?
#   -user string
#     	Loki username.
#   -version
#     	Print this builds version information
#   -wait duration
#     	Duration to wait for log entries on websocket before querying loki for them (default 1m0s)
#   -write-max-backoff duration
#     	Maximum backoff time between retries  (default 5m0s)
#   -write-max-retries int
#     	Maximum number of retries when push a log entry  (default 10)
#   -write-min-backoff duration
#     	Initial backoff time before first retry  (default 500ms)
#   -write-timeout duration
#     	How long to wait write response from Loki (default 10s)