# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_nettools = "quay.education.nsw.gov.au/observability/network-tools:prod"
  host_constraint = "pl0992obscol0[1-3]|[6-7]"
  loki_url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "matt-network-tools" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "network-tools" {
    count = 1
    
    network {
      port "port_nettools" {
        static = 3333
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = var.host_constraint
    }

    ephemeral_disk {
      size = 300
    }

    task "network-tools" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        JAEGER_ENDPOINT = "localhost:14268/api/traces"
      }

      config {
        image = var.image_nettools
        ports = ["port_nettools"]
        command = "/bin/sleep"
        args = [" infinity"]
        privileged = "true"

        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "local/.bashrc:/root/.bashrc"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]network-tools (docker):\w# \[\e[0m\]"
export TERM=linux
export VAULT_USER=root
export VAULT_PASS={{with secret "kv/data/default/nettools/config"}}{{.Data.data.root_password}}{{end}}

EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 500
        memory = 2048
      }

      service {
        name = "network-tools"
        port = "port_nettools"

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}