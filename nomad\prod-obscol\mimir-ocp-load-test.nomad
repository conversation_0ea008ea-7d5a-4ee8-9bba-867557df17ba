##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

variables {
  image_mimir = "quay.education.nsw.gov.au/observability/mimir:prod-obscol"
}

job "mimir-ocp" {
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

    group "mimir-ocp" {
        count = 1

        #constraint {
        #  attribute = "${attr.unique.hostname}"
        #  value = "pl0992obscol03.nsw.education"
        #}

        #constraint {
        #    operator  = "distinct_hosts"
        #    value     = "true"
        #}        

        network {
            port "grpc" {
            }
            port "http" {
            }
        }
     
    volume "vol_mimir_ocp"  {
      type = "host"
      source = "vol_mimir_ocp"
      read_only = false
    }    

    task "mimir_ocp" {

      driver = "docker"


      service {
        name = "mimir-ocp"
        port = "http"
        address_mode = "host"

        tags = [
            "traefik.enable=true",
            "traefik.http.routers.mimir-ocp.rule=Host(`mimir-ocp.obs.nsw.education`)",
            "traefik.http.routers.mimir-ocp.tls=false",
            "traefik.http.routers.mimir-ocp.entrypoints=http,https",
        ]

        check {
            name     = "Mimir healthcheck"
            port     = "http"
            type     = "http"
            path     = "/ready"
            interval = "60s"
            timeout  = "30s"
            check_restart {
                limit           = 3
                grace           = "60s"
                ignore_warnings = false
            }
        }            

        meta {
          cir_app_id = "obs"
          cluster    = "obscol"
          env        = "prod"
        }        
      } 

      volume_mount {
        volume = "vol_mimir_ocp"
        destination = "/mimir"
        read_only = false
      }

      resources {
        # @TODO
        # 2022-11-16 jedd - modest to prove out S3 connectivity, but will need bumping up when we ingest OCP in anger
        cpu = 2000
        memory = 3000
      }                 

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
      }

      config {
        image = var.image_mimir

        dns_servers = ["192.168.31.1"]

        ports = ["http","grpc"]       

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            }
        }         

        args = [
          "-target=all",

          "-server.http-listen-port=${NOMAD_PORT_http}",
          "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
          "-server.register-instrumentation=true",

          "-log.level=info",

          "-querier.iterators=true",
          "-query-frontend.instance-addr=${NOMAD_IP_http}",
          "-query-frontend.instance-port=${NOMAD_PORT_grpc}",
          "-querier.frontend-address=${NOMAD_IP_http}:${NOMAD_PORT_grpc}",
          "-querier.id=${node.unique.name}",

          "-compactor.ring.store=consul",
          "-compactor.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
          "-compactor.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
          "-compactor.ring.prefix=collectors-ocp/",
          "-compactor.ring.instance-addr=${NOMAD_IP_http}",
          "-compactor.ring.instance-port=${NOMAD_PORT_grpc}",
          "-compactor.ring.instance-id=${node.unique.name}",
          

          "-distributor.ring.store=consul",
          "-distributor.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
          "-distributor.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
          "-distributor.ring.prefix=collectors-ocp/",
          "-distributor.ring.instance-addr=${NOMAD_IP_http}",
          "-distributor.ring.instance-port=${NOMAD_PORT_grpc}",
          "-distributor.ring.instance-id=${node.unique.name}",
          "-distributor.ingestion-rate-limit=1000000",
          "-distributor.drop-label=['confluence_request_duration_on_path_bucket']",

          "-ingester.ring.store=consul",
          "-ingester.ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
          "-ingester.ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
          "-ingester.ring.prefix=collectors-ocp/",
          "-ingester.ring.instance-addr=${NOMAD_IP_http}",
          "-ingester.ring.instance-port=${NOMAD_PORT_grpc}",
          "-ingester.ring.instance-id=${node.unique.name}",
          "-ingester.ring.replication-factor=1",
          "-ingester.max-global-series-per-user=300000",
          "-ingester.max-global-series-per-metric=50000",

          "-store-gateway.sharding-ring.store=consul",
          "-store-gateway.sharding-ring.consul.acl-token=b68e1c4b-dac2-990b-1743-0d13056b56a5",
          "-store-gateway.sharding-ring.consul.hostname=consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500",
          "-store-gateway.sharding-ring.prefix=collectors-ocp/",
          "-store-gateway.sharding-ring.instance-addr=${NOMAD_IP_http}",
          "-store-gateway.sharding-ring.instance-port=${NOMAD_PORT_grpc}",
          "-store-gateway.sharding-ring.instance-id=${node.unique.name}",
          "-store-gateway.sharding-ring.replication-factor=1",

          "-blocks-storage.tsdb.dir=/mimir/tsdb/",
          "-blocks-storage.bucket-store.sync-dir=/mimir/tsdb-sync/",

          "-blocks-storage.backend=s3",
          "-blocks-storage.s3.endpoint=s3.ap-southeast-2.amazonaws.com",
          "-blocks-storage.s3.region=ap-southeast-2",
          # NOTE -mimirocp- is a separate s3 bucket to the conventional -mimir- bucket but uses the same credentials
          "-blocks-storage.s3.bucket-name=nswdoe-obs-mimirocp-blocks-storage-shared",
          "-blocks-storage.s3.secret-access-key=f7bxzOwdYIBkniKv6dikfGXeCURdEWvbtqiyiJ9e",
          "-blocks-storage.s3.access-key-id=AKIA5XHPOKHSEALNFC5G",
          "-blocks-storage.tsdb.flush-blocks-on-shutdown=true",

          "-validation.max-length-label-name=2048",
          "-validation.max-length-label-value=4096",
        ]
      }
    }
  }
}
