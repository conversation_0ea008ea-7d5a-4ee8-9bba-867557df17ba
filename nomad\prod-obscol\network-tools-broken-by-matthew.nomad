variables {
  image_nettools = "quay.education.nsw.gov.au/observability/nicolaka/netshoot:v0.13"
}

job "nettools" {
  datacenters = ["dc-obstst"]
  type = "service"

update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "nettools" {
    count = 1

    network {
      port "http" {
        static = 9321
      }
    }

    restart {
      interval = "10m"
      attempts = 5
      delay = "30s"
    }

    service {
      name = "nettools"
      port = "http"
    }

    task "nettools" {
      driver = "docker"

      kill_signal = "SIGTERM"

      config {
        image = var.image_nettools
        ports = ["http"]
      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
      }

      resources {
        cpu    = 500
        memory = 256
      }
    }

  }
}

