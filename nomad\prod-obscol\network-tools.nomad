
# generic linux container for jedd in obscol PROD

# Needs the command / args to sleep indefinitely otherwise it bombs out immediately.

# Stolen then heavily modified from https://github.com/leowmjw/nomad-box

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_nettools = "jonlabelle/network-tools"


  #host_constraint = "pl0992obscol.*"
  #host_constraint = "pl0475obscol.*"
  host_constraint = "pl0475obscol07"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "network-tools" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "network-tools" {

    network {
      port "port_nettools" {
        static = 3333
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = var.host_constraint
    }

    ephemeral_disk {
      size = 300
    }

    task "network-tools" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
      }

      config {
        image = "${var.image_nettools}"
        ports = ["port_nettools"]
        command = "/bin/sleep"
        args = [" infinity"]
        privileged = "true"

        volumes = [
          "/opt/sharednfs/jedd:/jedd",
          "local/.bashrc:/root/.bashrc"
        ]
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]network-tools (docker):\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }

      resources {
        cpu = 500 
        memory = 2048
      }

      service {
        name = "network-tools"
        port = "port_nettools"

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}
