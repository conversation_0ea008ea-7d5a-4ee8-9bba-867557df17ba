# NodeRED
#
# <PERSON>olen then heavily modified from https://github.com/leowmjw/nomad-box

# 2025-02-11 jedd - major changes - adding secondary task for handling git, moving to quay

variables {
  image_nodered = "quay.education.nsw.gov.au/observability/nodered:v4.0.8"
  image_python = "quay.education.nsw.gov.au/observability/python:3.9-bullseye"
}


# JOB - nodered  = = = = = = = = = = = = = = = = = = = = = = = = =
job "nodered" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "nodered" {
    count = 1

    network {
      port "port_nodered" {
        to = 1880
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    volume "nodered" {
      type = "host"
      source = "nodered"
      read_only = false
    }

    task "nodered" {
      driver = "docker"

      volume_mount {
        volume      = "nodered"
        destination = "/data"
        read_only   = false
      }

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true"
        "NODE_RED_ENABLE_PROJECTS" = "true"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        # 2025-02-11 jedd - replicated current obscol01 (7w old) image to quay. 
        # In-app it was showing 'v4.0.8' but there are many variants of this version on quay.
        # This shouldn't matter though - as long as we have the _same_ version if we restart this job.
        # image = "nodered/node-red:latest"
        image = "${var.image_nodered}"

        ports = ["port_nodered"]

        logging {
          type = "loki"
          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=Node-Red"
            }
          }
        }

      resources {
        cpu = 500
        memory = 400
        memory_max = 800
      }

      service {
        name = "nodered"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.nodered.rule=Host(`nodered.obs.nsw.education`)",
          "traefik.http.routers.nodered.tls=false",
          "traefik.http.routers.nodered.entrypoints=https",
        ]
        port = "port_nodered"
        check {
          name     = "http"
          type     = "http"
          path     = "/admin"
          interval = "10s"
          timeout  = "2s"
        }
      }

    }  // end-task 'nodered'


    #  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = 
    # TASK nodered-updater
    task "nodered-updater" {
      driver = "docker"

      # This aligns with the UID of the nodered task (existing),
      # which maps on SOE RHEL to 'srvimagebuild:srvimagebuild',
      # and should probably be revisited at some point.
      user = "1000:1000"

      env {
        # Needed to talk out to bitbucket.org
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      }

      config {
        # image = "python:3.9-bullseye"
        image = "${var.image_python}"

        command = "/data/looper.sh"

        network_mode = "host"

        ports = [ ]

        volumes = [
          "/opt/sharednfs/nodered:/data"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      resources {
        cpu = 30
        memory = 60
        # git can sometimes be hungry for memory
        memory_max = 200
      }

      service {
        name = "nodered"
        meta {
          cir_app_id = "obs"
          env = "prod"
        }
      }
    }  // end-task  nodered-updater

  }  // end-group 'nodered'

}  // end-job 'nodered'

