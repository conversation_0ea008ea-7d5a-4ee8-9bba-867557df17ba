# PGExport
# # Start an example database
# docker run --net=host -it --rm -e POSTGRES_PASSWORD=password postgres
# Connect to it
# docker run \
#  --net=host \
#  -e DATA_SOURCE_NAME="postgresql://postgres:password@localhost:5432/postgres?sslmode=disable" \
#  quay.io/prometheuscommunity/postgres-exporter
#

#docker run --net=host -e DATA_SOURCE_NAME="postgresql://postgres:password@localhost:5432/postgres?sslmode=disable" quay.io/prometheuscommunity/postgres-exporter

variables {
  image_postgres-exporter = "quay.io/prometheuscommunity/postgres-exporter:latest"
}

job "postgresql-exporter" {
  datacenters = ["dc-un-prod"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "pgexport" {

    network {
        port "pgexport"{
            to = 9187
        }
        }
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "pgexport" {
      driver = "docker"

      env = {
        DATA_SOURCE_NAME="******************************************/postgres?sslmode=disable"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = var.image_postgres-exporter
        ports = ["pgexport"]
        }

      resources {
        cpu    = 500 # 500 MHz
        memory = 256 # 256MB
      }

      service {
        name = "pgexport"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.pgexport.rule=Host(`pgexport.obs.nsw.education`)",
          "traefik.http.routers.pgexport.tls=false",
          "traefik.http.routers.pgexport.entrypoints=http,pgexport",
        ]
        #port = "port_pgexport"
        check {
          port     = "pgexport"
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}
