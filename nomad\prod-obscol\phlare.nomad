// phlare - for obscol PROD environment

variables {
  image_phlare = "quay.education.nsw.gov.au/observability/phlare:prod-obscol"
}

job "phlare" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "phlare" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_phlare" {
      type = "host"
      source = "vol_phlare"
      read_only = false
    }

    network {
      port "port_phlare"  {
        to = 4100
      }
    }

    task "phlare" {

      driver = "docker"

      volume_mount {
        volume      = "vol_phlare"
        destination = "/data"
        read_only   = false
      }

      config {
        image = var.image_phlare
        dns_servers = ["************"]
        ports = ["port_phlare"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }
        args = [
          "--config.file",
          "local/phlare.yaml",
        ]
      }

      template {
        data = file("assets/phlare-config.yml")
        destination = "local/phlare.yaml"
      }

      resources {
        cpu    = 512
        memory = 16000
      }

      service {
        name = "phlare"
        port = "port_phlare"

        check {
          name     = "Phlare healthcheck"
          port     = "port_phlare"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.phlare.rule=Host(`phlare.obs.nsw.education`)",
          "traefik.http.routers.phlare.tls=false",
          "traefik.http.routers.phlare.entrypoints=http,https",
        ]

      }

    }
  }
}

