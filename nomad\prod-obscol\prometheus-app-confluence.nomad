// Prometheus - Confluence (on-prem)

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}

job "prometheus-app-confluence" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "prometheus-app-confluence" {

    network {
      port "http" {}
    }

    volume "prometheus_app_confluence"  {
      type = "host"
      source = "prometheus_app_confluence"
      read_only = false
    }



    task "prometheus-app-confluence" {
      driver = "docker"

      volume_mount {
        volume = "prometheus_app_confluence"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["http"]
        image = var.image_prometheus
        #dns_servers = ["************"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus-app-confluence.obs.nsw.education",
          "--web.page-title=Prometheus for Confluence on DoE ObsCol PROD cluster",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=1h",
          "--storage.tsdb.retention.size=256MB",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          "--storage.tsdb.min-block-duration=1h",
        ]

      }

      service {
        name = "prometheus-app-confluence"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-confluence.rule=Host(`prometheus-app-confluence.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-confluence.tls=false",
          "traefik.http.routers.prometheus-app-confluence.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-confluence
  scrape_interval: "1m"

scrape_configs:
  - job_name: 'prometheus-app-confluence'
    static_configs:
      - targets: ['prometheus-app-confluence.obs.nsw.education']


###    # 2022-10-21 T 0745 jedd - disabling grafana_metrics and confluence metrics entirely
###    # 2022-05-23 jedd - Jeffrey enabled this plugin and provide a token - refer Vault /Confluence or Consul k/v
  - job_name: 'confluence'
    scheme: https 
    proxy_url: "http://proxy.det.nsw.edu.au:80"
    metrics_path: '/plugins/servlet/prometheus/metrics'
    scrape_interval: "1m"
    params:
      token: [ '{{ key "confluence/token" }}' ]
    static_configs:
      # Note we can't hit the hosts directly, only the LB'd front end
      - targets: ['confluence.education.nsw.gov.au']
    # 2022-10-20 - jedd - reducing the 12k confluence metrics by dropping these surpluse metrics:
    #     confluence_plugin_enabled_count
    #     confluence_request_duration_on_path_bucket
    #     confluence_request_duration_on_path_count
    #     confluence_request_duration_on_path_sum       
    #     confluence_user_failed_login_count
    #     confluence_user_login_count
    #     confluence_user_logout_count
    metric_relabel_configs:
    - source_labels: [__name__]
      # regex: confluence_request_duration_on_path_bucket

      # 2023-02-03 jedd - re-enable login / logout metrics now we're on a separate prom, not inserting into mimir
      # regex: confluence_(request_duration_on_path_bucket|request_duration_on_path_count|request_duration_on_path_sum|user_failed_login_count|user_login_count|user_logout_count|plugin_enabled_count)
      regex: confluence_(request_duration_on_path_bucket|request_duration_on_path_count|request_duration_on_path_sum|user_failed_login_count|plugin_enabled_count)

      action: drop




#rule_files:
#  - /etc/prometheus/rules.d/confluence.rules

#alerting:
#  alertmanagers:
#    - consul_sd_configs:
#      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
#        datacenter: 'dc-cir-un-prod'
#        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
#        services: ['alertmanager']

remote_write:
- name: mimir
  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 150
        memory = 1000
        memory_max = 16000
      }

    }
  }
}
