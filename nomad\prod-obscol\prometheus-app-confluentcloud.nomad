// obs-col prod - Prometheus for Confluent Cloud (Kafka)

// 2023-06-20 - split out from prometheus.nomad (primary)
//              add relabel section to add env= label

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-app-confluentcloud" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }

  group "prometheus-app-confluentcloud" {

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

    network {
      port "port_prometheus" {}
    }

    task "prometheus-app-confluentcloud" {
      driver = "docker"

      config {
        image = var.image_prometheus

        ports = ["port_prometheus"]

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }     

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.external-url=https://prometheus-app-confluentcloud.obs.nsw.education",
          "--web.page-title=Prometheus for Confluent Cloud (Kafka SaaS) - PROD",
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
          "--web.enable-lifecycle"
        ]

      }

      service {
        name = "prometheus-app-confluentcloud"
        port = "port_prometheus"

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-confluentcloud.rule=Host(`prometheus-app-confluentcloud.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-confluentcloud.tls=false",
          "traefik.http.routers.prometheus-app-confluentcloud.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-confluentcloud

scrape_configs:

  # 2021-12-01 jedd - static until we get this bedded down
  # native confluent metrics (rather than the API method used by the ccloud nomad job)
  - job_name: Confluent Cloud
    scrape_interval: 2m
    scrape_timeout: 1m

    # 2023-06-20 jedd - this is specified in the example code from Confluent, but
    # is generally a really really bad idea, so removing this
    # honor_timestamps: true

    static_configs:
      - targets:
        - api.telemetry.confluent.cloud
    scheme: https
    basic_auth:
      username: "{{ key "ccloud/api-key" }}"
      password: "{{ key "ccloud/api-secret" }}"
    proxy_url: "http://proxy.det.nsw.edu.au:80"
    metrics_path: /v2/metrics/cloud/export

    params:
      "resource.kafka.id":
        # DEV-XFI
        # Kafka: lkc-1k503
        # Schema Registry: lsrc-qqz9d
        # Connector: lcc-2zy52
        - lkc-1k503

        # Production cluster (2021-12)
        # Kafka :lkc-73zyj
        # Schema registry : lsrc-o0y3p
        # Connector : lcc-zokzd
        - lkc-73zyj

        # Test
        # Kafka: lkc-z1nrd
        # Schema Registry: lsrc-0vvkq
        # Connector: lcc-11ry5
        - lkc-z1nrd

        # Pre-Prod
        # Kafka : lkc-11qw6
        # Schema registry: lsrc-3ojnw
        # Connector: lcc-ggk0r
        - lkc-11qw6


    # If we want to minimise the number of scrapes to the API, we need to relabel
    # based on the above kafka_id entries - the alternative is a separate scrape job
    # for each environment - which in theory should max out at 4 or 5.
    #
    # Jedd's not 100% clear on integrating this with file_sd or another self-service mechanism.

    # Example metric (raw) we get back is:
    # confluent_kafka_server_request_bytes{instance="api.telemetry.confluent.cloud:443", 
    #         job="Confluent Cloud", kafka_id="lkc-73zyj", principal_id="sa-09mp1q", 
    #         type="ApiVersions"}

    metric_relabel_configs:
      - source_labels: [kafka_id]
        regex: lkc-1k503
        target_label: env
        replacement: dev

      - source_labels: [kafka_id]
        regex: lkc-73zyj
        target_label: env
        replacement: prod

      - source_labels: [kafka_id]
        regex: lkc-z1nrd
        target_label: env
        replacement: test

      - source_labels: [kafka_id]
        regex:  lkc-11qw6
        target_label: env
        replacement: pre


# REMOTE WRITE disabled until we're happy with this job replica - mostly around resources stanza
#              and label additions - perhaps multiple job_names to replace the previous single
#              job_name, but allowing for env= label insertion for each job

#remote_write:
#- name: mimir
#  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#  headers: 
#    X-Scope-OrgID: prod
#  tls_config:
#    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 300
        memory = 300
        memory_max = 1000
      }

    }
  }
}
