// obs-col prod - prometheus-app-grafana.nomad job definition
// LTS Version of Prometheus
// Collecting Grafana metrics

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-app-grafana" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "collector-app-grafana" {

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }

    network {
      port "http" {
      }
    }


    task "task-prometheus-app-grafana" {
      driver = "docker"
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        ports = ["http"]

        image = var.image_prometheus

        #dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus.obs.nsw.education",
          "--web.page-title=Prometheus for Grafana Enterprise",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-grafana"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-grafana.rule=Host(`prometheus-app-grafana.obs.nsw.education`)",
          "traefik.http.routers.prometheus-app-grafana.tls=false",
          "traefik.http.routers.prometheus-app-grafana.entrypoints=https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-app-grafana
    env: prod

scrape_configs:
  # ObsCol instance - monitoring this job by itself effectively.
  - job_name: 'prometheus-app-grafana'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-app-grafana.obs.nsw.education']

  - job_name: 'grafana_metrics'
    scheme: 'https'
    tls_config:
      insecure_skip_verify: true
    metrics_path: /metrics
    static_configs:
      - targets: [
        'grafana.mtm.apps.det.nsw.edu.au',
        'grafana.obs.test.nsw.education'
      ]
###      # 2022-10-20 - jedd - reducing the 3405 grafan metrics by dropping these excessively verbose items
###      #     grafana_http_request_duration_seconds_bucket         (1248)
###      #     grafana_datasource_request_duration_seconds_bucket    (510)
###      #     grafana_http_request_duration_seconds_count            (96)
###      #     grafana_datasource_request_duration_seconds_count      (34)
###      #     grafana_datasource_request_duration_seconds_sum        (34)
###      metric_relabel_configs:
###      - source_labels: [__name__]
###        regex: grafana_(datasource_request_duration_seconds_bucket|datasource_request_duration_seconds_count|datasource_request_duration_seconds_sum|http_request_duration_seconds_bucket|http_request_duration_seconds_count)
###        action: drop

remote_write:
  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 500 # MHz
        memory = 512 # MAWR dedicated ram!
      }

    }
  }
}
