
# collector-appid-a - Collector for a app_ids and system metrics - PROD ObsCol

# Standard collector architecture 1 job: prometheus


variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-appid-a" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-appid-a" { 
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl0992obscol0[123]"
    }
    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-appid-a-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        # 2024-08-08 jedd - attempting to work with unbound dns cache to take load
        # off calls to external DNS, failing regularly presumably due to load.
        # Refer: https://nsw-education.atlassian.net/wiki/spaces/PM/pages/303956139/Unbound+-+caching+DNS
        # dns_servers = ["************"]

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-appid-a.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for appid-a",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 1000
        memory_max = 3000
      }

      service {
        name = "prometheus-appid-a"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-appid-a.rule=Host(`prometheus-appid-a.obs.nsw.education`)",
          "traefik.http.routers.prometheus-appid-a.tls=false",
          "traefik.http.routers.prometheus-appid-a.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-appid-a
    env: prod

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-appid-a'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-appid-a.obs.nsw.education']

  - job_name: 'openmetrics_a'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: [
    "openmetrics_adds",
    "openmetrics_awsi",
    "openmetrics_adcnb",
    "openmetrics_admu",
    "openmetrics_aadc",
    "openmetrics_admim",
    "openmetrics_aurora",
    "openmetrics_adfs",
    "openmetrics_adrdl",
    "openmetrics_aap",
    "openmetrics_astp",
    "openmetrics_adrad",
    "openmetrics_addc",
    "openmetrics_adcs",
    "openmetrics_aahw",
    "openmetrics_access",
    "openmetrics_aufm",
    "openmetrics_aproto",
    "openmetrics_acaa",
    "openmetrics_ansb",
    "openmetrics_amso",
    "openmetrics_arap",
    "openmetrics_auaa",
    "openmetrics_amfa",
    "openmetrics_addsc",
    "openmetrics_azmig",
    "openmetrics_adpoc"
]
    relabel_configs:
      - source_labels: [__meta_consul_service_metadata_app_id]
        target_label: app_id       
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_op_window]
        target_label: op_window
      - source_labels: [__meta_consul_service_metadata_os_manager]
        target_label: os_manager
      - source_labels: [__meta_consul_service_metadata_support_team]
        target_label: support_team               
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path
        
remote_write:
  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-prometheus-appid-a"
  }
}

