# collector-appid-s - Collector for a app_ids and system metrics - PROD ObsCol

# Cirrus-app-id's starting with 's...' but NOT including 'sq...'
# appid's starting with 'sq' should go into prometheus-appid-sq.nomad

# Standard collector architecture 1 job: prometheus

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-appid-s" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-appid-s" {
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl0992obscol0[123]"
    }  

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-appid-s-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        # 2024-08-07 jedd - attempting to work with unbound dns cache to take load
        # off calls to external DNS, failing regularly presumably due to load.
        # Refer: https://nsw-education.atlassian.net/wiki/spaces/PM/pages/303956139/Unbound+-+caching+DNS
        # dns_servers = ["************"]

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-appid-s.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for appid-s",
          "--log.level=warn", 
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        # 2024-05-29 jedd - bumped cpu 150-250, memory 1000-2000 - after adding many new sap related targets
        # and seeing memory redlining around 1200, and a lot of failing / timeout connections.
        # 2024-06-04 jedd - bumped cpu 250-300, and memory 2000-3500 - after splitting more sap groups, adding
        # a few more hosts, and seeing a) dropped metrics on prometheus Targets page, and b) 1900/2000 mb usage
        # on nomad allocation view.

        # 2024-06-17 jedd - bumped cpu 300->800 (was consistently seeing 400-500, peaks to 1200), and memory
        # (saw high of 3200 out of 3500) from 3500 to 4000.

        # 2024-07-29 jedd - matthew investigated some occasionally missing metrics on many hosts tracked
        # by this scraper, so we're bumping this again as we were seeing microbursts.  cpu from 800 to 1000,
        # memory from 4000 to 5000.  We may need to modify max_samples_per_send (defined in prom config below)
        # as we're at 8000 there (default is 500-2000). Best Practices for prom -> grafana mimir are not readily
        # discoverable.  There's some interplay with max_shards, which we aren't defining, but perhaps should
        # (and we may actually want to *reduce* that to increase the size of payloads, reducing network 
        # connection/tear-down costs). Default is 200 (TBC) and could be reduced to 100 perhaps. This might
        # be done in coordination with queue_config/capacity setting (default 10,000).  Alternatively we may 
        # be hitting a more basic network constraint - docker or physical interface - and may need to
        # consider a location constraint to specific hosts, or specific DC's in the nomad cluster.
        #    Refer:  https://prometheus.io/docs/practices/remote_write/

        # 2024-08-19 jedd - split out the 'openmetrics_sq...' app-id's to an 'sq' nomad
        # job. These amount to about 500 hosts, out of the ~ 1200 that start with 's'.
        # We'll retain `memory = 5000` on BOTH jobs until we've seen how this works out.

        cpu = 1000
        memory = 5000
        memory_max = 12000
      }

      service {
        name = "prometheus-appid-s"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-appid-s.rule=Host(`prometheus-appid-s.obs.nsw.education`)",
          "traefik.http.routers.prometheus-appid-s.tls=false",
          "traefik.http.routers.prometheus-appid-s.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-appid-s
    env: prod

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-appid-s'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-appid-s.obs.nsw.education']

  - job_name: 'openmetrics_s'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: [
          "openmetrics_saclus",
          "openmetrics_salm",
          "openmetrics_sap114",
          "openmetrics_sapa",
          "openmetrics_sapcc",
          "openmetrics_sapd",
          "openmetrics_sapd52",
          "openmetrics_sapd54",
          "openmetrics_sapdh",
          "openmetrics_sapia",
          "openmetrics_sapiq",
          "openmetrics_sapwd",
          "openmetrics_sas114",
          "openmetrics_sas115",
          "openmetrics_satc",
          "openmetrics_sats",
          "openmetrics_sc0",
          "openmetrics_sc9",
          "openmetrics_scne",
          "openmetrics_scoma",
          "openmetrics_scomd",
          "openmetrics_scome",
          "openmetrics_scomq",
          "openmetrics_scord",
          "openmetrics_score",
          "openmetrics_sd0",
          "openmetrics_sd1",
          "openmetrics_sd7",
          "openmetrics_sd8",
          "openmetrics_sdbo",
          "openmetrics_sdetst",
          "openmetrics_se1",
          "openmetrics_sedrms",
          "openmetrics_seismo",
          "openmetrics_sems",
          "openmetrics_serg",
          "openmetrics_sfile",
          "openmetrics_sibi",
          "openmetrics_sima",
          "openmetrics_siobi",
          "openmetrics_siop",
          "openmetrics_sip114",
          "openmetrics_sis115",
          "openmetrics_slam",
          "openmetrics_smaw",
          "openmetrics_smbps",
          "openmetrics_smcms",
          "openmetrics_smft1a",
          "openmetrics_smqa",
          "openmetrics_smtp4a",
          "openmetrics_smtp4o",
          "openmetrics_soapts",
          "openmetrics_sp1",
          "openmetrics_spgps",
          "openmetrics_splnk",
          "openmetrics_splnkc",
          "openmetrics_splt",
          "openmetrics_spsr",
          "openmetrics_spwl",


          "openmetrics_srpa",
          "openmetrics_ss0",
          "openmetrics_sshr",
          "openmetrics_ssis",
          "openmetrics_st0",
          "openmetrics_st1",
          "openmetrics_st2",
          "openmetrics_st7",
          "openmetrics_stmg",
          "openmetrics_stpcc",
          "openmetrics_swrepo",
          "openmetrics_swstes",
          "openmetrics_syslog",
          "openmetrics_syss"
        ]

    relabel_configs:
      - source_labels: [__meta_consul_service_metadata_app_id]
        target_label: app_id       
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_op_window]
        target_label: op_window
      - source_labels: [__meta_consul_service_metadata_os_manager]
        target_label: os_manager
      - source_labels: [__meta_consul_service_metadata_support_team]
        target_label: support_team               
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_host_group_id]
        target_label: host_group_id
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path
        
remote_write:

  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true
    queue_config:
      max_samples_per_send: 8000    
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }

    }  // end-task "task-appid-s-prometheus"

  } // end-group "collector-appid-s"

}

