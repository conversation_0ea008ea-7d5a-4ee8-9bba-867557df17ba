# collector-appid-s - Collector for a app_ids and system metrics - PROD ObsCol

# Cirrus-app-id's starting with 'sq...'
# other appid's starting with 's' should go into prometheus-appid-s.nomad

# Standard collector architecture 1 job: prometheus

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-appid-sq" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-appid-sq" {
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl0992obscol0[123]"
    }  

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-appid-sq-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        # 2024-08-07 jedd - attempting to work with unbound dns cache to take load
        # off calls to external DNS, failing regularly presumably due to load.
        # Refer: https://nsw-education.atlassian.net/wiki/spaces/PM/pages/303956139/Unbound+-+caching+DNS
        # dns_servers = ["************"]

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-appid-sq.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for appid-sq",
          "--log.level=warn",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        # 2024-05-29 jedd - bumped cpu 150-250, memory 1000-2000 - after adding many new sap related targets
        # and seeing memory redlining around 1200, and a lot of failing / timeout connections.
        # 2024-06-04 jedd - bumped cpu 250-300, and memory 2000-3500 - after splitting more sap groups, adding
        # a few more hosts, and seeing a) dropped metrics on prometheus Targets page, and b) 1900/2000 mb usage
        # on nomad allocation view.

        # 2024-06-17 jedd - bumped cpu 300->800 (was consistently seeing 400-500, peaks to 1200), and memory
        # (saw high of 3200 out of 3500) from 3500 to 4000.

        # 2024-07-29 jedd - matthew investigated some occasionally missing metrics on many hosts tracked
        # by this scraper, so we're bumping this again as we were seeing microbursts.  cpu from 800 to 1000,
        # memory from 4000 to 5000.  We may need to modify max_samples_per_send (defined in prom config below)
        # as we're at 8000 there (default is 500-2000). Best Practices for prom -> grafana mimir are not readily
        # discoverable.  There's some interplay with max_shards, which we aren't defining, but perhaps should
        # (and we may actually want to *reduce* that to increase the size of payloads, reducing network 
        # connection/tear-down costs). Default is 200 (TBC) and could be reduced to 100 perhaps. This might
        # be done in coordination with queue_config/capacity setting (default 10,000).  Alternatively we may 
        # be hitting a more basic network constraint - docker or physical interface - and may need to
        # consider a location constraint to specific hosts, or specific DC's in the nomad cluster.
        #    Refer:  https://prometheus.io/docs/practices/remote_write/

        # 2024-08-19 jedd - split out the 'openmetrics_sq...' app-id's to an 'sq' nomad
        # job. These amount to about 500 hosts, out of the ~ 1200 that start with 's'.
        # We'll retain `memory = 5000` on BOTH jobs until we've seen how this works out.

        cpu = 1000
        memory = 5000
        memory_max = 12000
      }

      service {
        name = "prometheus-appid-sq"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-appid-sq.rule=Host(`prometheus-appid-sq.obs.nsw.education`)",
          "traefik.http.routers.prometheus-appid-sq.tls=false",
          "traefik.http.routers.prometheus-appid-sq.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-appid-sq
    env: prod

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-appid-sq'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-appid-sq.obs.nsw.education']

  - job_name: 'openmetrics_sq'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: [
          "openmetrics_sq0",
          "openmetrics_sq2",
          "openmetrics_sq7",
          "openmetrics_sq8",
          "openmetrics_sqa",
          "openmetrics_sqan",
          "openmetrics_sqf019",
          "openmetrics_sqf03a",
          "openmetrics_sqf03c",
          "openmetrics_sqf101",
          "openmetrics_sqf102",
          "openmetrics_sqf104",
          "openmetrics_sqf105",
          "openmetrics_sqf106",
          "openmetrics_sqf107",
          "openmetrics_sqf108",
          "openmetrics_sqf110",
          "openmetrics_sqf111",
          "openmetrics_sqfc01",
          "openmetrics_sqfc10",
          "openmetrics_sqfc2",
          "openmetrics_sqfc3",
          "openmetrics_sqfc9",
          "openmetrics_sqfe01",
          "openmetrics_sqfe02",
          "openmetrics_sqfe03",
          "openmetrics_sqfe04",
          "openmetrics_sqfe05",
          "openmetrics_sqfe06",
          "openmetrics_sqfe07",
          "openmetrics_sqfe09",
          "openmetrics_sqfe20",
          "openmetrics_sqfe21",
          "openmetrics_sqfe25",
          "openmetrics_sqff",
          "openmetrics_sqffsw",
          "openmetrics_sqfl",
          "openmetrics_sqld",
          "openmetrics_sqlfsw",
          "openmetrics_sqlp",
          "openmetrics_sqlp11",
          "openmetrics_sqlq4b",
          "openmetrics_sqlq7c",
          "openmetrics_sqls",
          "openmetrics_sqlt",
          "openmetrics_sqmg",
          "openmetrics_sqmgmt",
          "openmetrics_sqn",
          "openmetrics_sqp03b",
          "openmetrics_sqp078",
          "openmetrics_sqp07a",
          "openmetrics_sqp101",
          "openmetrics_sqp102",
          "openmetrics_sqp103",
          "openmetrics_sqp104",
          "openmetrics_sqp106",
          "openmetrics_sqp108",
          "openmetrics_sqp109",
          "openmetrics_sqp111",
          "openmetrics_sqp112",
          "openmetrics_sqp113",
          "openmetrics_sqp114",
          "openmetrics_sqp115",
          "openmetrics_sqp116",
          "openmetrics_sqp118",
          "openmetrics_sqp119",
          "openmetrics_sqp120",
          "openmetrics_sqpe01",
          "openmetrics_sqpe20",
          "openmetrics_sqpe21",
          "openmetrics_sqpfsw",
          "openmetrics_sqpl",
          "openmetrics_sqs0",
          "openmetrics_sqs038",
          "openmetrics_sqs103",
          "openmetrics_sqs104",
          "openmetrics_sqs105",
          "openmetrics_sqs106",
          "openmetrics_sqs112",
          "openmetrics_sqs114",
          "openmetrics_sqs115",
          "openmetrics_sqs116",
          "openmetrics_sqs117",
          "openmetrics_sqs118",
          "openmetrics_sqse02",
          "openmetrics_sqsfsw"
        ]

    relabel_configs:
      - source_labels: [__meta_consul_service_metadata_app_id]
        target_label: app_id       
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_op_window]
        target_label: op_window
      - source_labels: [__meta_consul_service_metadata_os_manager]
        target_label: os_manager
      - source_labels: [__meta_consul_service_metadata_support_team]
        target_label: support_team               
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_host_group_id]
        target_label: host_group_id
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path
        
remote_write:

  - name: mimir-rwb-write                                                                                                                                                                                                                     
    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true
    queue_config:
      max_samples_per_send: 8000    
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }

    }  // end-task "task-appid-sq-prometheus"

  } // end-group "collector-appid-sq"

}

