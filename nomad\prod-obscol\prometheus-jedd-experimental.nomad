
# prometheus jedd experimental - an infrequently used prometheus instance to experiment with scrape,
#                                and should NOT have remote_write into prod mimir enabled.


variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-jedd-experimental" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "prometheus-jedd-experimental" { 
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl.*obscol0[8]"
    }

    network {
      port "port_prometheus" { }
      port "port_blackbox" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://prometheus-jedd-experimental.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for jedd-experimental",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 400
        memory_max = 800
      }

      service {
        name = "prometheus-jedd-experimental"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-jedd-experimental.rule=Host(`prometheus-jedd-experimental.obs.nsw.education`)",
          "traefik.http.routers.prometheus-jedd-experimental.tls=false",
          "traefik.http.routers.prometheus-jedd-experimental.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-jedd-experimental
    env: prod

  scrape_interval: 60s

# scrape configs are almost exclusively static references to hosts, to experiment with
# relabelling, dropping, etc.

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-jedd-experimental'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-jedd-experimental.obs.nsw.education']

  - job_name: 'jedd-experimental'
    scheme: 'http'
    metrics_path: /metrics
    static_configs:
      - targets: [
        'pw0991stmgut01.nsw.education:11055',
        'pw0992stmgut01.nsw.education:11055',
#        'pl0992stmgsn01.nsw.education:11055',
#        'pl0991stmgbs01.nsw.education:11055',
        ]
    relabel_configs:
      - action: labeldrop
        regex: job

      - action: labeldrop
        regex: host

      - source_labels: [__address__]
        separator: ':'
        regex: '(.*):(.*)'  # This regex captures everything before the colon
        replacement: '$1'
        # replacement: '${1}'
        target_label: __tmp_hostname

      - source_labels: [__tmp_hostname]
        action: lowercase
     #   target_label: hostname    # works but forces exported_host label to exist with 'Pw0991stmgut01'
        target_label: host

      - action: labeldrop
        regex: ^exported_host$

      #- action: replace
      #  source_labels: [__tmp_hostname]
      #  target_label: host

      #- action: lowercase
      #  regex: exported_host
      #- action: labeldrop
      #  source_labels: [host]


  - job_name: 'ping_hosts_for_jedd_experimental'
    static_configs:
      - targets: [ 
        'pw0991stmgut01.nsw.education',
        'pw0992stmgut01.nsw.education',
        'pl0992obscol01.nsw.education',
        'pl0992obscol02.nsw.education',
        'pl0992obscol03.nsw.education',
        'pl0475obscol06.nsw.education',
        'pl0475obscol07.nsw.education',
        'pl0475obscol08.nsw.education',
        ]
    metrics_path: /probe
    params:
      module: [icmp]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        # blackbox binds to public ethernet, not loopback
        replacement: blackbox-jedd-experimental.obs.nsw.education


  - job_name: 'tcp11055_check_for_jedd_experimental'
    static_configs:
      - targets: [ 
        'pw0991stmgut01.nsw.education:11055',
        'pw0992stmgut01.nsw.education:11055',
        'pl0992obscol01.nsw.education:11055',
        'pl0992obscol02.nsw.education:11055',
        'pl0992obscol03.nsw.education:11055',
        'pl0475obscol06.nsw.education:11055',
        'pl0475obscol07.nsw.education:11055',
        'pl0475obscol08.nsw.education:11055',
        ]
    metrics_path: /probe
    params:
      module: [tcp_connect]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        # blackbox binds to public ethernet, not loopback
        replacement: blackbox-jedd-experimental.obs.nsw.education



  - job_name: 'scrape_sq7_erp'
    scheme: 'http'
    metrics_path: /metrics
    static_configs:
      - targets: [
          "***********:11055",
          "***********:11055",
          "**********:11055",
          "**********:11055",
          "***********:11055",
          "***********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055"
        ]

  - job_name: 'scrape_sq8_erp'
    scheme: 'http'
    metrics_path: /metrics
    static_configs:
      - targets: [

          "**********:11055",
          "**********:11055",
          "**********:11055",
          "**********:11055",
          "10.9.39.50:11055",
          "10.9.39.51:11055",
          "10.9.39.52:11055",
          "10.9.39.53:11055",
          "10.9.39.54:11055",
          "10.9.39.55:11055",
          "10.9.39.56:11055",
          "10.9.39.57:11055",
          "10.9.39.58:11055",
          "10.9.39.59:11055",
          "10.9.39.73:11055",
          "10.9.39.74:11055",
          "10.9.39.75:11055",
          "10.9.39.76:11055",
          "10.9.39.34:11055",
          "10.9.39.35:11055",
          "10.9.39.36:11055",
          "10.9.39.37:11055",
          "10.9.39.38:11055",
          "10.9.39.39:11055",
          "10.9.39.40:11055",
          "10.9.39.41:11055",
          "10.9.39.42:11055",
          "10.9.39.43:11055",
          "10.9.39.45:11055",
          "10.9.39.44:11055",
          "10.9.39.46:11055",
          "10.9.39.62:11055",
          "10.9.39.64:11055",
          "10.9.39.65:11055",
          "10.9.39.66:11055",
          "10.9.39.67:11055",
          "10.9.39.68:11055",
          "10.9.39.69:11055",
          "10.9.39.70:11055",
          "10.9.39.71:11055"

        ]



#  - job_name: 'jedd_experimental_openmetrics_sq'
#    consul_sd_configs:
#      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
#        datacenter: 'dc-cir-un-prod'
#        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
#        services: [
#          "openmetrics_sq0",
#          "openmetrics_sq2",
#          "openmetrics_sq7",
#          "openmetrics_sq8",
#          "openmetrics_sqa",
#          "openmetrics_sqan",
#          "openmetrics_sqf019",
#          "openmetrics_sqf03a",
#          "openmetrics_sqf03c",
#          "openmetrics_sqf101",
#          "openmetrics_sqf102",
#          "openmetrics_sqf104",
#          "openmetrics_sqf105",
#          "openmetrics_sqf106",
#          "openmetrics_sqf107",
#          "openmetrics_sqf108",
#          "openmetrics_sqf110",
#          "openmetrics_sqf111",
#          "openmetrics_sqfc01",
#          "openmetrics_sqfc10",
#          "openmetrics_sqfc2",
#          "openmetrics_sqfc3",
#          "openmetrics_sqfc9",
#          "openmetrics_sqfe01",
#          "openmetrics_sqfe02",
#          "openmetrics_sqfe03",
#          "openmetrics_sqfe04",
#          "openmetrics_sqfe05",
#          "openmetrics_sqfe06",
#          "openmetrics_sqfe07",
#          "openmetrics_sqfe09",
#          "openmetrics_sqfe20",
#          "openmetrics_sqfe21",
#          "openmetrics_sqfe25",
#          "openmetrics_sqff",
#          "openmetrics_sqffsw",
#          "openmetrics_sqfl",
#          "openmetrics_sqld",
#          "openmetrics_sqlfsw",
#          "openmetrics_sqlp",
#          "openmetrics_sqlp11",
#          "openmetrics_sqlq4b",
#          "openmetrics_sqlq7c",
#          "openmetrics_sqls",
#          "openmetrics_sqlt",
#          "openmetrics_sqmg",
#          "openmetrics_sqmgmt",
#          "openmetrics_sqn",
#          "openmetrics_sqp03b",
#          "openmetrics_sqp078",
#          "openmetrics_sqp07a",
#          "openmetrics_sqp101",
#          "openmetrics_sqp102",
#          "openmetrics_sqp103",
#          "openmetrics_sqp104",
#          "openmetrics_sqp106",
#          "openmetrics_sqp108",
#          "openmetrics_sqp109",
#          "openmetrics_sqp111",
#          "openmetrics_sqp112",
#          "openmetrics_sqp113",
#          "openmetrics_sqp114",
#          "openmetrics_sqp115",
#          "openmetrics_sqp116",
#          "openmetrics_sqp118",
#          "openmetrics_sqp119",
#          "openmetrics_sqp120",
#          "openmetrics_sqpe01",
#          "openmetrics_sqpe20",
#          "openmetrics_sqpe21",
#          "openmetrics_sqpfsw",
#          "openmetrics_sqpl",
#          "openmetrics_sqs0",
#          "openmetrics_sqs038",
#          "openmetrics_sqs103",
#          "openmetrics_sqs104",
#          "openmetrics_sqs105",
#          "openmetrics_sqs106",
#          "openmetrics_sqs112",
#          "openmetrics_sqs114",
#          "openmetrics_sqs115",
#          "openmetrics_sqs116",
#          "openmetrics_sqs117",
#          "openmetrics_sqs118",
#          "openmetrics_sqse02",
#          "openmetrics_sqsfsw"
#        ]


#    relabel_configs:
#      # - source_labels: [__meta_host]
#      # - source_labels: [__host__]
#      - source_labels: [host]
#        target_label: host2
#        action: uppercase


#    relabel_configs:
#      - source_labels: [__meta_consul_metadata_cir_app_id]
#        target_label: cir_app_id
#        regex: '(.+)'  # Do not perform the replace if there was no metrics path
        
# DO NOT ENABLE THIS
#remote_write:
#  - name: mimir-rwb-write
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-prometheus-jedd-experimental"



    # TASK blackbox-jedd-experimental = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-blackbox-jedd-experimental" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }          

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu = 100
        memory = 50
        memory_max = 100
      }


      service {
        name = "blackbox-jedd-experimental"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.blackbox-jedd-experimental.rule=Host(`blackbox-jedd-experimental.obs.nsw.education`)",
          "traefik.http.routers.blackbox-jedd-experimental.tls=false",
          "traefik.http.routers.blackbox-jedd-experimental.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
modules:
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  http_2xx:
    prober: http


  tcp_connect:
    prober: tcp

EOH
        destination = "local/config.yml"
      }

    }  // end-task "task-blackbox-jedd-experimental







  }
}

