# Infrequently used prometheus instance to experiment with scrape and should NOT have remote_write into prod mimir enabled.


variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-matt-experimental" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "prometheus-matt-experimental" { 
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://prometheus-matt-experimental.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for matt-experimental",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 400
        memory_max = 800
      }

      service {
        name = "prometheus-matt-experimental"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-matt-experimental.rule=Host(`prometheus-matt-experimental.obs.nsw.education`)",
          "traefik.http.routers.prometheus-matt-experimental.tls=false",
          "traefik.http.routers.prometheus-matt-experimental.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-matt-experimental
    env: prod

  scrape_interval: 60s


# scrape configs are almost exclusively static references to hosts, to experiment with relabelling, dropping, etc.

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-matt-experimental'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-matt-experimental.obs.nsw.education']

  - job_name: 'matt-experimental'
    scheme: 'http'
    metrics_path: /metrics
    static_configs:
      - targets: [
        'pl0992sp1na00.apps.det.nsw.edu.au:11055',
        'pl0992sp1na13.apps.det.nsw.edu.au:11055',
        ]
    relabel_configs:
      - action: labeldrop
        regex: job

      - action: labeldrop
        regex: host

      - source_labels: [__address__]
        separator: ':'
        regex: '(.*):(.*)'  # This regex captures everything before the colon
        replacement: '$1'
        # replacement: '${1}'
        target_label: __tmp_hostname

      - source_labels: [__tmp_hostname]
        action: lowercase
     #   target_label: hostname    # works but forces exported_host label to exist with 'Pw0991stmgut01'
        target_label: host

      - action: labeldrop
        regex: ^exported_host$

      #- action: replace
      #  source_labels: [__tmp_hostname]
      #  target_label: host

      #- action: lowercase
      #  regex: exported_host
      #- action: labeldrop
      #  source_labels: [host]



#  - job_name: 'ping_member_servers_static'
#    # @TODO in production we're happy with very low poll rate
#    # scrape_interval: 10m
#    scrape_interval: 1m
#    static_configs:

#      Matt - add in some IP addresses here
#      - targets: ['**************', '**************', '**************']
#    metrics_path: /probe
#    params:
#      module: [icmp]
#    relabel_configs:
#      - source_labels: [__address__]
#        target_label: __param_target
#      - source_labels: [__param_target]
#        target_label: instance
#      - target_label: __address__
#        # blackbox binds to public ethernet, not loopback
#        replacement: pl0475obscol08.nsw.education:9115


#    relabel_configs:
#      # - source_labels: [__meta_host]
#      # - source_labels: [__host__]
#      - source_labels: [host]
#        target_label: host2
#        action: uppercase


#    relabel_configs:
#      - source_labels: [__meta_consul_metadata_cir_app_id]
#        target_label: cir_app_id
#        regex: '(.+)'  # Do not perform the replace if there was no metrics path
        
# DO NOT ENABLE THIS
#remote_write:
#  - name: mimir-rwb-write
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-prometheus-matt-experimental"
  }
}

