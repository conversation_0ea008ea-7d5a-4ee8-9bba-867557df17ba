
# prometheus for mimir introspection
#
# 2024-05-30 jedd - temporary, non-remote-write, will PDC up to grafana.net to investigate s3 bucket getquery getobject problem


variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-mimir-introspection" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "prometheus-mimir-introspection" { 
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://prometheus-mimir-introspection.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for mimir introspection (temporary)",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/prometheus-mimir-introspection:/prometheus"
        ]

        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 400
        memory = 1000
        memory_max = 2000
      }

      service {
        name = "prometheus-mimir-introspection"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-mimir-introspection.rule=Host(`prometheus-mimir-introspection.obs.nsw.education`)",
          "traefik.http.routers.prometheus-mimir-introspection.tls=false",
          "traefik.http.routers.prometheus-mimir-introspection.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-mimir-introspection
    env: prod

  # set to 15s as this is what all their dashboards expect
  scrape_interval: 15s

# scrape configs are almost exclusively static references to hosts, to experiment with
# relabelling, dropping, etc.

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-mimir-introspection'
    scheme: 'https'
    metrics_path: /metrics
    static_configs:
      - targets: ['prometheus-mimir-introspection.obs.nsw.education']

  - job_name: 'mimir-introspection-read'
    scheme: 'https'
    metrics_path: /metrics
    static_configs:
      - targets: [
          'mimir-rwb-read.obs.nsw.education'
        ]
        labels:
          cluster: obscol
          namespace: obscol
          job: obscol/mimir-read

  - job_name: 'mimir-introspection-write'
    scheme: 'https'
    metrics_path: /metrics
    static_configs:
      - targets: [
          'mimir-rwb-write.obs.nsw.education'
        ]
        labels:
          cluster: obscol
          namespace: obscol
          job: obscol/mimir-write

  - job_name: 'mimir-introspection-backend'
    scheme: 'https'
    metrics_path: /metrics
    static_configs:
      - targets: [
          'mimir-rwb-backend.obs.nsw.education'
        ]
        labels:
          cluster: obscol
          namespace: obscol
          job: obscol/mimir-backend

        
# DO NOT ENABLE THIS
#remote_write:
#  - name: mimir-rwb-write
#    url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: prod
#    tls_config:
#      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-prometheus-mimir-introspection"
  }
}

