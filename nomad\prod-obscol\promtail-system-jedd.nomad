// promtail - for ObsCol (prod) nomad - system job (all nodes)
// Tightly coupled with loki.nomad (to receive these logs
// 2021-07-21 jedd - @TODO use .31.1 DNS rather than hard code to placement
//                   constraints for loki on obscol01.

variables {
    image_promtail = "quay.education.nsw.gov.au/observability/promtail:prod-obscol"
  }

job "promtail-system" {
  datacenters = ["dc-cir-un-prod"]
  # type = system == forces to run on all nomad nodes
  type        = "system"
  # type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "promtail-system" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    network {
      port  "promtail_port" {
        static = 3200
      }
      # While port 3200 is regarded as the ingest port, it does not work as a
      # service check port, let alone with /health as a path - instead we do:
      port  "promtail_9080" {
        static = 9080
      }
    }

#    constraint {
#      attribute = "${attr.unique.hostname}"
#      value = "pl0992obscol01.nsw.education"
#    }
#
    task "promtail" {
      driver = "docker"

      config {
        image = var.image_promtail

        dns_servers = ["************"]

        volumes = [
          "/var/log:/alloc/var/log",
          "/var/lib/docker/containers:/var/lib/docker/containers",
          "local/config.yaml:/etc/promtail/config.yml",
          "/etc/promtail/promtail-targets.yaml:/etc/promtail/promtail-targets.yaml"
        ]

        network_mode = "host"
      }

      template {
        data = <<EOH
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

client:
  url: http://loki.obs.nsw.education:3100/api/prom/push

scrape_configs:

# OS (host) logs - the full /var/log collection
- job_name: system
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlogs
      __path__: /alloc/logs/*.log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}


- job_name: varlogsubdirs
#  entry_parser: raw
  static_configs:
  - targets:
      - localhost
    labels:
      job: varlogsubs
      __path__: /alloc/var/log/*/*.log
      host:     {{ env "attr.unique.hostname" }}
      nodename: {{ env "attr.unique.hostname" }}
      node_id:  {{ env "node.unique.id" }}
      dc:       {{ env "node.datacenter" }}


# DOCKER logs - with naive TAGGING (requires external job to provide file),
# specifically this needs to be done and present, and mapped (volume above)
# docker ps --format '- targets: ["{{.ID}}"]\n  labels:\n    container_name: "{{.Names}}"' > /etc/promtail/promtail-targets.yaml
- job_name: dockertagged
  file_sd_configs:
  - files:
    - /etc/promtail/promtail-targets.yaml
  relabel_configs:
  - source_labels: [__address__]
    target_label: container_id
  - source_labels: [container_id]
    target_label: __path__
    replacement: /var/lib/docker/containers/$1*/*.log

EOH

        destination = "local/config.yaml"
      }

      resources {
        cpu    = 100
        memory = 4000
      }

      service {
        name = "promtail"
        port = "promtail_port"

        check {
          type     = "http"
          port     = "promtail_9080"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }

      }
    }
  }
}
