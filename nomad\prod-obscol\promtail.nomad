
variables {
    image_promtail = "quay.education.nsw.gov.au/observability/promtail:prod-obscol"
  }

job "promtail" {
  datacenters = ["dc-cir-un-prod"]
  type = "system"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "promtail" {
    count = 1

    network {
      dns {
        servers = ["************"]
      }
      port "http" { }
    }

    restart {
      attempts = 3
      delay    = "20s"
      mode     = "delay"
    }

    volume "nomad" {
      type = "host"
      source = "nomad"
      read_only = true
    }

    volume "promtail" {
      type = "host"
      source = "promtail"
      read_only = false
    }

    task "promtail" {
      driver = "docker"

      env {
        HOSTNAME = "${attr.unique.hostname}"
      }

      template {
        data        = <<EOTC
positions:
  filename: /data/positions.yaml

clients:
  - url: http://loki.service.dc-cir-un-prod.collectors.obs.nsw.education:3100/loki/api/v1/push

scrape_configs:
- job_name: 'nomad-logs'
  consul_sd_configs:
    - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
      datacenter: 'dc-cir-un-prod'
      token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
  relabel_configs:
    - source_labels: [__meta_consul_node]
      target_label: __host__
    - source_labels: [__meta_consul_service_metadata_external_source]
      target_label: source
      regex: (.*)
      replacement: '$1'
    - source_labels: [__meta_consul_service_id]
      regex: '_nomad-task-([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})-.*'
      target_label:  'task_id'
      replacement: '$1'
    - source_labels: [__meta_consul_tags]
      regex: ',(app|monitoring),'
      target_label:  'group'
      replacement:   '$1'
    - source_labels: [__meta_consul_service]
      target_label: job
    - source_labels: ['__meta_consul_node']
      regex:         '(.*)'
      target_label:  'instance'
      replacement:   '$1'
    - source_labels: [__meta_consul_service_id]
      regex: '_nomad-task-([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})-.*'
      target_label:  '__path__'
      replacement: '/nomad/alloc/$1/alloc/logs/*std*.{?,??}'
EOTC
        destination = "/local/promtail.yml"
      }

      volume_mount {
        volume = "promtail"
        destination = "/data"
        read_only = false
      }

      volume_mount {
        volume = "nomad"
        destination = "/nomad"
        read_only = true
      }

      config {
        image = var.image_promtail
        ports = ["http"]
        args = [
          "-config.file=/local/promtail.yml",
          "-server.http-listen-port=${NOMAD_PORT_http}",
        ]
//        volumes = [
//          "/var/promtail:/data",
//          "/var/nomad/:/nomad/"
//        ]
      }

      resources {
        cpu    = 50
        memory = 100
      }

      service {
        name = "promtail"
        port = "http"
        tags = ["monitoring","prometheus"]

        check {
          name     = "Promtail HTTP"
          type     = "http"
          path     = "/targets"
          interval = "5s"
          timeout  = "2s"

          check_restart {
            limit           = 2
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }
    }
  }
}
