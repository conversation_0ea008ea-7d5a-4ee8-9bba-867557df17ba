// obscol-prod - Oracle-in-a-container version 11g

// primary purpose is to test the prometheus oracle exporter, as an endpoint we can play with

// docker commandline is:
//   docker run -d -p 1521:1521 -p 8888:8080 -e ORACLE_ALLOW_REMOTE=true -e ORACLE_DISABLE_ASYNCH_IO=true  oracleinanutshell/oracle-xe-11g

// Docker hub page:
//   https://hub.docker.com/r/oracleinanutshell/oracle-xe-11g

// Should expose on 1521 - login with:
//     username:   system
//     password:   oracle

variables {
  image_oracle = "quay.education.nsw.gov.au/observability/oracle:inanutshell-xe-11g"
}

job "sandbox-oracle-xe-11g" {
  datacenters = ["dc-cir-un-prod"]

  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "oracle" {
    count = 1
    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    # We don't have DNS for this, and it's hefty, so we lock it in to obscol02
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol02.nsw.education"
    }

    network {
      port "port_oracle"  {
        static = 1521
        to     = 1521
      }
      port "port_apex"  {
        static = 8888
        to     = 8080
      }
    }

    task "sandbox-oracle-xe-11g" {
      driver = "docker"
      env = {
        "ORACLE_ALLOW_REMOTE" = "true",
        "ORACLE_DISABLE_ASYNCH_IO" = "true"
      }

      config {
        image = var.image_oracle
        # We typically don't need Apex enabled during testing.
        # ports = ["port_oracle", "port_apex"]
        ports = ["port_oracle"]
      }

      resources {
        cpu    = 512
        memory = 3000
        memory_max = 8000
      }

      service {
        name = "oracle"
        port = "port_oracle"

        #check {
        #  name     = "Oracle healthcheck"
        #  port     = "port_oracle"
        #  type     = "tcp"
        #  interval = "60s"
        #  timeout  = "5s"
        #  check_restart {
        #    limit           = 3
        #    grace           = "60s"
        #    ignore_warnings = false
        #  }
        #}

#        #  Traefik may be enabled one day, but probably won't be needed.
#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.oracle.tls=true",
#          # the middleware has to be declared somewhere else, we only attach it here
#          "traefik.http.routers.oracle.middlewares=oracle-basicauth@file",
#        ]

      }
    }
  }
}
