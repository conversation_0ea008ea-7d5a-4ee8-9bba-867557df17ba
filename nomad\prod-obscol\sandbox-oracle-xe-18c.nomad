// sandbox-oracle xe 18 - prod-obscol

// This is to test oracle collector(s) by providing a known host, port number,
// and service name to connect to, without risking account locking.

// docker commandline is:
//    docker run -d -p 8080:8080 -p 1521:1521 -v /my/oracle/data:/u01/app/oracle sath89/oracle-12c
//    docker run -d -p 8080:8080 -p 1521:1521 -v /my/oracle/data:/u01/app/oracle -e DBCA_TOTAL_MEMORY=1024 sath89/oracle-12c



// Docker hub page:
//    https://hub.docker.com/r/gvenzl/oracle-xe

// Should expose on 1521 - login with:
//     sqlplus system/oracle@//localhost:1521/xe.oracle.docker
//     username:   SYS or SYSTEM
//     password:   oracle

variables {
  image_oracle = "quay.education.nsw.gov.au/observability/oracle:gvenzl-18-slim"
}

job "sandbox-oracle-xe-18c" {
  datacenters = ["dc-cir-un-prod"]

  type = "service"

  #update {
  #  max_parallel      = 1
  #  health_check      = "checks"
  #  min_healthy_time  = "10s"
  #  healthy_deadline  = "3m"
  #  progress_deadline = "5m"
  #}

  group "oracle" {

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "pl0992obscol0[123]"
      value = "pl.*obscol0[3]"
    }

    count = 1

    network {
      port "port_oracle"  {
        static = 1521
        to     = 1521
      }
      port "port_apex"  {
        # static = 8080
        to     = 8080
      }
    }

    task "sandbox-oracle-xe-18c" {
      driver = "docker"
      env = {
        "ORACLE_PASSWORD" = "oracle",
        "ORACLE_ALLOW_REMOTE" = "true",
        "ORACLE_DISABLE_ASYNCH_IO" = "true"
      }

      config {
        # image = "sath89/oracle-12c"
        # image = "konnecteam/docker-oracle-13c"
        # image = "galam/oracle-xe-12c"
        image = var.image_oracle
        ports = ["port_oracle", "port_apex"]
        args = [ ]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      resources {
        cpu = 512
        memory = 4000
        memory_max = 8000
      }

      service {
        name = "oracle"
        port = "port_oracle"

        #check {
        #  name     = "Oracle healthcheck"
        #  port     = "port_oracle"
        #  type     = "tcp"
        #  interval = "60s"
        #  timeout  = "5s"
        #  check_restart {
        #    limit           = 3
        #    grace           = "60s"
        #    ignore_warnings = false
        #  }
        #}

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.oracle18.rule=Host(`oracle18.obs.int.jeddi.org`)",
          "traefik.http.routers.oracle18.tls=false",
          "traefik.http.routers.oracle18.entrypoints=http",
        ]

      }

    }

  }
}

