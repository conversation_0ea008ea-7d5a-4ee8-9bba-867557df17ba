// sandbox-oracle xe 21 - prod-obscol

// This is to test oracle collector(s) by providing a known host, port number,
// and service name to connect to, without risking account locking.

// docker commandline is:
//    Run a new db container - basic:
//     docker run -d -p 1521:1521 -e ORACLE_PASSWORD=<your password> gvenzl/oracle-xe

// Database will reside under /u01/app/oracle/oradata/XE

// Docker hub page:
//    https://hub.docker.com/r/gvenzl/oracle-xe


// Should expose on 1521 - login with:
//     sqlplus system/oracle@//localhost:1521/xe.oracle.docker
//     username:   SYS or SYSTEM
//     password:   oracle
//     service name:  XEPDB1
//     database app user:  my_user
//     database app password:  my_password_which_I_really_should_change

// Environment variables:
//    ORACLE_PASSWORD - for SYS and SYSTEM users
//    ORACLE_RANDOM_PASSWORD - if non-empty, eg 'yes', will create and display at startup
//    ORACLE_DATABASE - optional, create new pluggable db with this name
//    APP_USER - optional, create new db schema user, defaulting to XEPDB1 db
//    APP_USER_PASSWORD - optional, if non-empty set password for APP_USER above

variables {
  image_oracle = "gvenzl/oracle-xe:21-slim"
  }



job "sandbox-oracle-xe-21c" {
  datacenters = ["dc-cir-un-prod"]

  type = "service"

  #update {
  #  max_parallel      = 1
  #  health_check      = "checks"
  #  min_healthy_time  = "10s"
  #  healthy_deadline  = "3m"
  #  progress_deadline = "5m"
  #}

  group "oracle" {

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "pl0992obscol0[123]"
      value = "pl0475obscol0[6]"
    }

    count = 1

    network {
      port "port_oracle"  {
        static = 1521
        # to     = 1521
      }
      port "port_apex"  {
        # static = 8080
        to     = 8080
      }
    }

    task "sandbox-oracle-xe-21c" {
      driver = "docker"
      env = {
        "ORACLE_PASSWORD" = "oracle",
        "ORACLE_ALLOW_REMOTE" = "true",
        "ORACLE_DISABLE_ASYNCH_IO" = "true"
      }

      config {
        # image = "gvenzl/oracle-xe"
        image = var.image_oracle

        ports = ["port_oracle", "port_apex"]

        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

      }

      resources {
        cpu = 512
        memory = 4500
        memory_max = 7000
      }

      service {
        name = "oracle"
        port = "port_oracle"

        #check {
        #  name     = "Oracle healthcheck"
        #  port     = "port_oracle"
        #  type     = "tcp"
        #  interval = "60s"
        #  timeout  = "5s"
        #  check_restart {
        #    limit           = 3
        #    grace           = "60s"
        #    ignore_warnings = false
        #  }
        #}

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.sandbox-oracle-xe-21c.rule=Host(`sandbox-oracle-xe-21c.obs.nsw.education`)",
          "traefik.http.routers.sandbox-oracle-xe-21c.tls=false",
          "traefik.http.routers.sandbox-oracle-xe-21c.entrypoints=http",
        ]

      }

    }  // end-task "sandbox-oracle-xe-21c"

  } // end-group
}

