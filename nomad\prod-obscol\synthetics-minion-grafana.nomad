
// Synthetics minion (from Grafana Corp) for on-prem proxying of synthetic web calls

variables {
  image_grafana-synthetic-monitoring-agent = "quay.education.nsw.gov.au/observability/grafana-synthetic-monitoring-agent:prod-obscol"
}

job "synthetics-minion-grafana" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "synthetics-minion-grafana" {

    task "synthetics-minion-grafana" {
      driver = "docker"

      config {
        image = var.image_grafana-synthetic-monitoring-agent
        #dns_servers = ["************"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        args = [
          "-verbose=true",
          "-debug=true",
          "-api-insecure=false",
          "-api-server-address=${API_SERVER}",
          "-api-token=${API_TOKEN}"
        ]            
      }

      env {
        # See https://grafana.com/docs/grafana-cloud/synthetic-monitoring/private-probes/#deploy-the-agent-using-docker
        # 
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        API_SERVER  = "synthetic-monitoring-grpc-au-southeast.grafana.net:443"
        #API_SERVER  = "https://synthetic-monitoring-api-au-southeast.grafana.net"
        API_TOKEN   = "pcAp1VblVECR0KOILE3cUF8Dwniw5J2hmXoAyKClsPYAphK+2EVx3r2ItpGAjsQJ9/sj3vzidhW2toPVAeSEoBQcYqK6cyp2yaR/5fRS+FBMIYD8A5GrsT2b88sFAqSR/eIcmEp7rgi+ScWGWDQEk+ituHFOtazlOFdyZSRhBJk="
      } 

      resources {
        cpu = 250
        memory = 250
      }

    }  // end-task

  }  // end-group

}
