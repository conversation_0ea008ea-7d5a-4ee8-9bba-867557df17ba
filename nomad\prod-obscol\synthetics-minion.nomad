
variables {
  image_synthetics-minion = "quay.education.nsw.gov.au/observability/synthetics-minion:prod-obscol"
}

job "synthetics-minion" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "synthetics-minion" {
    # MINION NAME = Central Ave 2
    # Private Location ID = 318084-central_ave_2-177
 
    task "synthetics-minion" {
      driver = "docker"

      config {
        image = var.image_synthetics-minion
        dns_servers = ["192.168.31.1"]
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }        
        volumes = [
          "local/tmp:/tmp:rw",
          "/var/run/docker.sock:/var/run/docker.sock:rw"
        ]
      }

      env {
        # See https://docs.newrelic.com/docs/synthetics/synthetic-monitoring/private-locations/containerized-private-minion-cpm-configuration/

        # Registered 26/09/2021
        MINION_PRIVATE_LOCATION_KEY = "NRSP-us0180CBEF5865F3BD74C5E92A9DD76265D"

        # Variables available to synthetic script via env
        # MINION_USER_DEFINED_VARIABLES
        # Can be used as $env.USER_DEFINED_VARIABLES.MY_VARIABLE

        MINION_API_PROXY = "proxy.det.nsw.edu.au:80"

      }

      resources {
        cpu = 800
        memory = 2500
      }
    }
  }
}

