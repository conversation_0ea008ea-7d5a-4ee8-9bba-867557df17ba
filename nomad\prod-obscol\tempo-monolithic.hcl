// Grafana <PERSON> (prod obscol)

variables {
    image_tempo = "quay.education.nsw.gov.au/observability/tempo:prod-obscol"
  }

job "tempo" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"
  
  update {
    max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true
    healthy_deadline = "5m"
    min_healthy_time = "5s"
  }

  group "tempo" {
    count = 1
    
    constraint {
        attribute = "${attr.unique.hostname}"
        operator = "regexp"
        value = "pl0992obscol0[123]"
    }

    network {
      port "port_tempo" { to = 3200 }
      port "port_tempo_grpc" {}
      port "port_otlp_grpc" { 
        to = 4317
        }
      port "port_otlp_http" {
        to = 4318
      }
      port "port_jaeger_http" {
        static = 14268
        }
    }

    service {
      name = "tempo-ui"
      port = "port_tempo"

      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo.rule=Host(`tempo.obs.nsw.education`)",
          "traefik.http.routers.tempo.tls=false",
          "traefik.http.routers.tempo.entrypoints=http,https",
        ]      
    }

    service {
      name = "otlp-traces"
      port = "port_otlp_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.otlp-traces.rule=Host(`tempo-otlp.obs.nsw.education`) && Path(`/v1/traces`)",
          "traefik.http.routers.otlp-traces.tls=false",
          "traefik.http.routers.otlp-traces.entrypoints=http,https",
        ]      
    }

    service {
      name = "jaeger-traces"
      port = "port_jaeger_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger-traces.rule=Host(`tempo-jaeger.obs.nsw.education`) && Path(`/api/traces`)",
          "traefik.http.routers.jaeger-traces.tls=false",
          "traefik.http.routers.jaeger-traces.entrypoints=http,https",
        ]      
    }

    task "tempo" {
      driver = "docker"
      kill_signal = "SIGTERM"      

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        JAEGER_ENDPOINT = "localhost:14268/api/traces"     
      }

      config {
        image = var.image_tempo
        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-monolithic"
          }    
        } 

        args = [
          "--config.file=/etc/tempo/config/tempo.yml",
          "--config.expand-env=true"
        ]

        ports = [
          "port_tempo",
          "port_otlp_http",
          "port_otlp_grpc",
          "port_jaeger_http"
        ]

        volumes = [
          "local/config:/etc/tempo/config",
          #"/opt/localstore/tempo/:/tempo_data", # Dedicated path for ingester WAL
          "/opt/localstore/tempo/:/mnt/", # Dedicated path for ingester WAL

        ]
        
      }


      template {
        data        = file("assets/tempo-monolithic.yml")
        destination = "local/config/tempo.yml"
      }
      resources {
        cpu    = 512
        memory = 4096
        memory_max = 12000
      }

    }
  }
}
