## extracted and modified as per https://github.com/hashicorp/nomad-open-telemetry-getting-started


variables {
  otel_image = "otel/opentelemetry-collector:0.38.0"
}

variables {
  image_loki = "quay.education.nsw.gov.au/observability/loki:prod-obscol"
  image_redis = "quay.education.nsw.gov.au/observability/redis:prod-obscol"
  loki_redis_endpoint = "loki-rw-redis.obs.nsw.education"
  jaeger_endpoint = "https://otel-jaeger-thrift.obs.nsw.education/api/traces"
  loki_url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
  env = "prod"
  }

job "tracing_demo" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  # Synthetic load generators
  group "load-generators" {
    network {
      port "port_frontend" {
        to = 8080
      }
      port "port_route" {
        to = 8083
      }
      port "port_customer"{
        to = 8081
      }
    }
      service {
        name = "hotrod-frontend"
        port = "port_frontend"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.hotrod.entrypoints=https",
          "traefik.http.routers.hotrod.rule=Host(`hotrod.obs.nsw.education`)",
        ]

        check {
          name     = "Loki read"
          port     = "port_frontend"
          type     = "http"
          path     = "/"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }    
    task "hotrod_tempo" {
        driver = "docker"

        config {
            image = "jaegertracing/example-hotrod:latest"
        logging {
            type = "loki"
            config {
                loki-url = var.loki_url
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
                }
            }
            ports = [
                "port_frontend",
                "port_route",
                "port_customer"
            ]
        args = [
            "all",
            "--otel-exporter=otlp"
        ]                     
        }
        resources {
            cpu = 200
            memory = 128
        }
        env {
            OTEL_EXPORTER_JAEGER_ENDPOINT = var.jaeger_endpoint
        }
    }
  }
}