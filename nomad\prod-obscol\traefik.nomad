// traefik LB load balancer - for ObsCol (prod) nomad - system job (all nodes)

variables {
    image_traefik = "quay.education.nsw.gov.au/observability/traefik:v3.4.3"
  }

job "traefik" {
  region      = "global"
  datacenters = ["dc-cir-un-prod"]
  type        = "system"
  
  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "traefik" {
    network {
      # 2023-08-22 jedd - commenting this out to try to resolve some dns resolution issue
      # dns {
      #   servers = ["192.168.31.1"]
      # }

      port "http" {
        static = 80
      }

      port "https" {
        static = 443
      }

      port "traefik" {
        static = 8081
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      kill_signal = "SIGTERM"

      config {
        image = var.image_traefik

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=traefik"
          }
        }        

        network_mode = "host"

        cap_add = ["net_bind_service"]

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
        ]

      }

      template {
        data = <<EOF
debug = false
logLevel = "INFO"
defaultEntryPoints = ["https","http"]

[entryPoints]
    [entryPoints.http]
    address = ":80"
    [entryPoints.https]
    address = ":443"
      [entryPoints.https.http.tls]
        [[entryPoints.https.http.tls.domains]]
        main = "obscol.obs.nsw.education"
        sans = ["*.obs.nsw.education"]

    [entryPoints.traefik]
    address = ":8081"

    # [entryPoints.prometheus]
    # address = ":9090"

    # [entryPoints.cortex]
    # address = ":9095"

    # [entryPoints.jaegergrpc]
    # address = ":14250"

    # [entryPoints.nodered]
    # address = ":1880"
    
    # [entryPoints.loki]
    # address = ":3100"

    # [entryPoints.loki-legacy]
    # address = ":3101"

    # [entryPoints.tempo]
    # address = ":3200"

    # [entryPoints.mimir]
    # address = ":8080"

    # [entryPoints.mimir-ocp]
    # address = ":8082"

    # [entryPoints.pastebin]
    # address = ":8027"

    # Reserved
    # [entryPoints.exporter-idrac]
    # address = ":9116"

    [entryPoints.exporter-snmp-pdu]
    address = ":9117"
    

    ## 2025-07-11 James disableding these entrypoints, they were early day configurations
    #[entryPoints.exporter-app-vcenter]
    #address = ":9118"

    #[entryPoints.exporter-app-vsphere]
    #address = ":9272"

[api]
    dashboard = true
    insecure  = true

# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = false
    refreshInterval = "1s"

    [providers.consulCatalog.endpoint]
      address = "{{ env "attr.unique.network.ip-address" }}:8500"
      scheme = "http"

    [providers.nomad.endpoint]
      address = "{{ env "attr.unique.network.ip-address" }}:4646"
      token = "77f66275-c8c3-d208-e39e-26453670a1a9"

# Enable File Provider for "Dynamic Configuration" elements
[providers.file]
  directory = "/local/traefik.d"

# 2021-08-23 jedd - enable metrics out of traefik
#[metrics]
#  [metrics.prometheus]
#    buckets = [0.1, 0.3, 1.2, 5.0]
#[tracing]
#  [tracing.jaeger.collector]
#    endpoint = "https://otel-jaeger-thrift.obs.nsw.education/api/traces?format=jaeger.thrift"
## 2025-07-11 James - Updated config to OTLP
[metrics]
  [metrics.otlp.http]
    endpoint = "http://otel-collector-http.obs.nsw.education"

[tracing]
  [tracing.otlp.http]
    endpoint = "http://otel-collector-http.obs.nsw.education"

#[accesslog.otlp.http]
#  endpoint = "http://otel-collector-http.obs.nsw.education"

EOF

        destination = "local/traefik.toml"
      }

      template {
        destination = "local/traefik.d/certificates.toml"
        data = <<EOF
[tls.stores]
  [tls.stores.default]
    # This one should be used if the client did not perform an SNI handshake.
    [tls.stores.default.defaultCertificate]
      certFile = "/local/obscol.obs.nsw.education.pem"
      keyFile = "/local/obscol.obs.nsw.education.key"

[[tls.certificates]]
  certFile = "/local/obscol.obs.nsw.education.pem"
  keyFile = "/local/obscol.obs.nsw.education.key"

EOF
      }

      template {
        destination = "local/obscol.obs.nsw.education.pem"
        data = <<EOF
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7e
SPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOF
      }

      template {
        destination = "local/obscol.obs.nsw.education.key"
        data = <<EOF
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
      }

      resources {
        # 2023-08-22 jedd - bumped from 100 to 600 - as we're seeing redlining on these allocations
        cpu = 600
        memory = 256
      }
    }
  }
}

