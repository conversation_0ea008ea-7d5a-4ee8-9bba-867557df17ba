
// Obs-Obs watcher instance of Alertmanager in the PROD env

job "alertmanager" {
  type = "service"

  datacenters = ["dc-obsobs-prod"]

  group "alertmanager" {

    network {
      port "http" {
        static = 9093
      }
    }

    task "alertmanager" {
      driver = "docker"

      config {
        ports = ["http"]

        image = "https://docker.io/prom/alertmanager:v0.24.0"

        dns_servers = ["192.168.31.1"]

        logging {
          type = "loki"
          config {
            loki-url = "http://pl0992obsobs01.nsw.education:3100/loki/api/v1/push"
          }
        }

        volumes = [
          "local/alertmanager.yaml:/etc/alertmanager/alertmanager.yml",
          "local/prometheus-configuration/prod/alertmanager/templates:/etc/alertmanager/template",
        ]

        args = [
          "--web.external-url=http://pl0992obsobs.nsw.education:9093"
        ]

        #args = [
        #  "--web.external-url=https://alertmanager.obsobs.nsw.education"
        #]

      }

      service {
        name = "alertmanager"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.alertmanager.rule=Host(`alertmanager.obsobs.nsw.education`)",
          "traefik.http.routers.alertmanager.tls=false",
        ]
      }

#      artifact {
#        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
#        destination = "local/prometheus-configuration"
#
#        options {
#          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
#        }
#      }

      template {
        data = <<EOH

global:
  # mutethischannel hook
  slack_api_url: *****************************************************************************
  http_config:
    proxy_url: http://proxy.det.nsw.edu.au:80/

templates:
- '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['alertname', 'service']
  group_wait: 1m
  group_interval: 15m
  repeat_interval: 3h  # 3 hours is the time it takes for xMatters to expire an incident, by default
  receiver: default
#  routes:
#  - receiver: xmatters-dev-np

#    matchers:
#    - "severity = critical"

receivers:
- name: default
  slack_configs:
    - channel: "#mutethischannel"
      send_resolved: true
      title: '{{`{{ template "custom_title" . }}`}}'
      text: '{{`{{ template "custom_slack_message" . }}`}}'
#- name: xmatters-dev-np
#  slack_configs:
#    - channel: "#mutethischannel"
#      send_resolved: true
#      title: '{{`{{ template "custom_title" . }}`}}'
#      text: '{{`{{ template "custom_slack_message" . }}`}}'
#  webhook_configs:
#  - url: https://nswdepartmentofeducation-dev-np.xmatters.com/api/integration/1/functions/e7d3f7c2-0d00-4a4c-9fa4-e6b4449b7d71/triggers?apiKey=3b098a70-65a9-4c80-934d-596d7197d890

EOH
        destination = "local/alertmanager.yaml"
      }

    }
  }
}
