global:
  external_labels:
    provenance: "obsobs-prometheus"

  scrape_interval: 1m


scrape_configs:
  - job_name: 'prometheus-obsobs-prod'
    scheme: 'https'
    static_configs:
      - targets: ["pl0992obsobs01.nsw.education:9090"]

#  - job_name: 'prometheus-obscol-prod'
#    scheme: 'https'
#    static_configs:
#      - targets: ['prometheus.obs.nsw.education']
#    metrics_path: /metrics


  - job_name: 'nomad_metrics'
    static_configs:
      - targets: ['pl0992obscol01.nsw.education:4646',
                  'pl0992obscol02.nsw.education:4646',
                  'pl0992obscol03.nsw.education:4646',
                  'pl0475obscol06.nsw.education:4646',
                  'pl0475obscol07.nsw.education:4646',
                  'pl0475obscol08.nsw.education:4646',
                 ]
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']

  - job_name: 'consul_metrics'
    static_configs:
      - targets: ['pl0992obscol01.nsw.education:8500',
                  'pl0992obscol02.nsw.education:8500',
                  'pl0992obscol03.nsw.education:8500',
                  'pl0475obscol06.nsw.education:8500',
                  'pl0475obscol07.nsw.education:8500',
                  'pl0475obscol08.nsw.education:8500',
                 ]
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']

  # for TLS / SSHl endpoints
  - job_name: 'nomad_metrics_https'
    scheme: 'https'
    static_configs:
      - targets: ['pl0992obsobs01.nsw.education:4646',
                 ]
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']

#  - job_name: 'nomad-obsobs-prod'
#    metrics_path: /v1/metrics
#    params:
#      format: ['prometheus']
#    consul_sd_configs:
#      - server: 'consul.service.dc-obsobs-prod.collectors.obs.nsw.education:8500'
#        datacenter: 'dc-obsobs-prod'
#        @TODO fix up the token for OBSOBS
#        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
#        services: ['nomad-client', 'nomad']
#        tags: ['http']


# Loki locally on ObsObs
  - job_name: 'loki-obsobs-prod'
    static_configs:
      - targets: ['pl0992obsobs01.nsw.education:3100']
    scheme: 'http'
    metrics_path: /metrics

# Loki on ObsCol prod
  - job_name: 'loki-obscol-prod'
    static_configs:
      - targets: ['loki.obs.nsw.education']
    scheme: 'https'
    metrics_path: /metrics


# Telegraf targets - host metrics of ourself (obsobs) and target (obscol)
  # Elsewhere this job name might be called 'openmetrics' but it may be nice
  # to distinguish this.  Perhaps 'telegraf-static' or some variant.
  - job_name: 'telegraf'
    static_configs:
      - targets: ['pl0992obsobs01.nsw.education:11055',

                  'pl0992obscol01.nsw.education:11055',
                  'pl0992obscol02.nsw.education:11055',
                  'pl0992obscol03.nsw.education:11055',

                  'pl0475obscol06.nsw.education:11055',
                  'pl0475obscol07.nsw.education:11055',
                  'pl0475obscol08.nsw.education:11055',
                  ]



#rule_files:
#  - /etc/prometheus/rules.d/*.rules
#  - /etc/prometheus/rules.d/*.yaml
#  - /etc/prometheus/rules.d/*.yml

# 2024-11-28 jedd - commenting out all alerting in prom
#alerting:
#  alertmanagers:
#    - static_configs:
#      - targets:
#        - pl0992obsobs01.nsw.education:9093

#    - consul_sd_configs:
#      - server: 'consul.service.dc-obsobs-prod.collectors.obsobs.nsw.education:8500'
#        datacenter: 'dc-obsobs-prod'
#        # @TODO fix up token
#        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
#        services: ['alertmanager']


