
// Synthetics 'minion' / monitoring agent - from Grafana Corp for running
// on-prem proxying of Synthetics web calls out of grafana.net instance.


#  = = = = = = = = = = = = = = = = = = = = = = = = =
variables {

  # We RETAIN a docker hub reference in case of emergency (quay failure during recovery of our systems)
  # image_synthetic_agent = "quay.education.nsw.gov.au/observability/grafana-synthetic-monitoring-agent:0.29.4"

  image_synthetic_agent = "https://quay.education.nsw.gov.au/observability/grafana-synthetic-monitoring-agent:v0.29.4"

}



# JOB  = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-synthetic-monitoring-agent" {
  type = "service"

  datacenters = ["dc-obsobs-prod"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "grafana-synthetic-monitoring-agent" {
    task "grafana-synthetic-monitoring-agent" {
      driver = "docker"

      config {
        image = var.image_synthetic_agent

        # dns_servers = ["************"]

        logging {   
          type = "loki"
          config {
              loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
          }
        }

        args = [
          "-verbose=true",
          "-debug=true",
          "-api-insecure=false",
          "-api-server-address=${API_SERVER}",
          "-api-token=${API_TOKEN}"
        ]            
      }

      env {
        # See https://grafana.com/docs/grafana-cloud/synthetic-monitoring/private-probes/#deploy-the-agent-using-docker
        # 
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"

        API_SERVER  = "synthetic-monitoring-grpc-au-southeast.grafana.net:443"
        #API_SERVER  = "https://synthetic-monitoring-api-au-southeast.grafana.net"
        API_TOKEN   = "ihcOVBDunQqxThNLeT1hPX6pq6bXiawR0jpTZ1k0lv/18CalDkqPzBWxLUjDZu4/g/R5V6XgEyNSXTlzxJ4EBk5sGob/VLW6kv+8v/TggeG14ajfTfnWyO4MFTlikzXv0dFCaxAACJ41f/NIt15xwAnFtJz0inctDunL1l+aIYM="
        } 

      resources {
        cpu = 250
        memory = 250
      }

    } // end-task
  } // end-group
}
