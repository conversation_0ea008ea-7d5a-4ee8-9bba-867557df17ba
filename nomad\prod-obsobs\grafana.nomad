
// Grafana for ObsObs-PROD

// This is for looking at ObsObs Prometheus data source, configuring alerts.
// It's intended to be very small footprint, very small functionality - as a
// way to look at metrics for the primary ObsCol and Zabbix clusters in an
// incident / emergency / patching / PVR scenario.

job "grafana" {

  datacenters = ["dc-obsobs-prod"]

  type = "service"

  group "grafana" {

    network {
      port "http" {
        static = 3000
        }
    }

    volume "vol_grafana" {
      type = "host"
      source = "vol_grafana"
      read_only = false
    }

    task "grafana" {
      driver = "docker"

      user = "root:root"

      volume_mount {
        volume = "vol_grafana"
        destination = "/grafana"
        read_only = false
      }

      config {
        image = "https://docker.io/grafana/grafana:latest"

        logging {
            type = "loki"
            config {
                # 0loki-url = "https://loki.obsobs.nsw.education:3100/loki/api/v1/push"
                loki-url = "http://pl0992obsobs01.nsw.education:3100/loki/api/v1/push"
                }
            }        

        dns_servers = ["************"]

        ports = ["http"]

        args = [
          "--config",
          "/etc/grafana/grafana.ini",
        ]

        volumes = [
          "local/grafana.ini:/etc/grafana/grafana.ini",
          "local/tls.crt:/etc/grafana/tls.crt",
          "local/tls.key:/etc/grafana/tls.key",

        ]
      }

      env {
        # Expose Grafana on this port - as with other ObsObs jobs we're sticking with standard ports for each job.
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_http}"

        # This points to /grafana is our persistent mount point - this directory defaults to /var/lib/grafana, 
        # and would contain several sub-directories: alerting/, csv/, plugins/, and png/
        # as well as the default location for the sqlite database file:   grafana.db
        GF_PATHS_DATA         = "/grafana/data"

        # This defaults to /var/log/grafana - we may have better ways of extracting logs via nomad (docker/loki) but
        # for troubleshooting it should be convenient. Frustratingly Grafana employees like to pluralise everythings.
        GF_PATHS_LOGS         = "/grafana/log"
        # GF_LOG_LEVEL          = "DEBUG"

        # We can send logs to console (captured by loki above), or file (dumped to GF_PATHS_LOGS above), or both with "console file"
        GF_LOG_MODE           = "console"


        # We need this for Slack out-going webhooks for alerts, and obtaining grafana.com dashboards
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY  = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY    = "10.0.0.0/8,172.0.0.0/8,192.168/16.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.consul"

        # Pass the plugins you want installed to Docker with the GF_INSTALL_PLUGINS environment variable as a 
        # comma-separated list. This sends each plugin name to grafana-cli plugins install ${plugin} and installs 
        # them when Grafana starts.
        GF_INSTALL_PLUGINS = ""

        # We can load this up via the template (below) to autogenerate two datasources, though we prefer to keep
        # those in the SQLite database in GF_PATHS_DATA (/opt/grafana in host) so we can modify them, and more
        # conveniently add new items.  Commented out (also the template below) and create from within the UI.
        # GF_PATHS_PROVISIONING = "/local/grafana/provisioning"

        # I can't even find a reference to this on the googles in 2022-11
        # GF_LIVE_ALLOWED_ORIGINS = "http://*"

        # This is another poorly documented feature, and I'm doubtful that we need it.
        # GF_FEATURE_TOGGLES_ENABLE = "ngalert"
      }

      service {
        name = "grafana"
        port = "http"

        check {
          # 2024-11-28 jedd - add protocol (https) and tls_skip_verify - because
          # these endpoints are now talking HTTPS ... BUT ... they are also being
          # accessed via ************/24 network, so the SANs aren't going to be
          # there.  Doing this means the services are registered in Consul, but
          # don't insta-fail (to red / critical) state.
          type = "http"
          port = "http"
          protocol = "https"
          tls_skip_verify = "true"
          path = "/api/health"
          interval = "5s"
          timeout = "2s"
        }

        #tags = [
        #  "traefik.enable=true",
        #  "traefik.http.routers.grafana.rule=Host(`grafana.obsobs.nsw.education`)",
        #  "traefik.http.routers.grafana-ssl.rule=Host(`grafana.obsobs.nsw.education`)",
        #  "traefik.http.routers.grafana-ssl.tls=true",
        #  "traefik.http.routers.grafana-ssl.tls.domains[0].main=obsobs.nsw.education",
        #  "traefik.http.routers.grafana-ssl.tls.domains[0].sans=*.obsobs.nsw.education",
        #]
      }

      resources {
        cpu    = 600
        memory = 2500
      }

      template {
        data = file("assets/grafana.ini")
        destination = "local/grafana.ini"
      }

      template {
        data = file("assets/tls.crt")
        destination = "local/tls.crt"
      }
      template {
        data = file("assets/tls.key")
        destination = "local/tls.key"
      }

#### Disabling this entirely as we will create the data sources and persist them in the /grafana
#### storage mapped above onto host's /opt/grafana structure.  

#      template {
#        data        = <<EOH
#
#apiVersion: 1
#datasources:
#
#  - name: ObsObsPrometheus
#    type: prometheus
#    access: proxy
#    url: http://pl0992obsobs01.obs.nsw.education:9090
#    #jsonData:
#    #    httpHeaderName1: 'X-Scope-OrgID'
#    #secureJsonData:
#    #    httpHeaderValue1: 'prodobsobs'
#
#  - name: ObsObsLoki
#    type: loki
#    access: proxy
#    url: http://pl0992obsobs01.obs.nsw.education:3100
#
#
#EOH
#        destination = "/local/grafana/provisioning/datasources/ds.yaml"
#      }

    }
  }
}
