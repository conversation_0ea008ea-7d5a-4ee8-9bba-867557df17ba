
// Obs-Obs watcher instance of Prometheus in the PROD env.


job "prometheus" {
  type = "service"

  datacenters = ["dc-obsobs-prod"]

  group "prometheus" {
    count = 1

    network {
      port "http" {
        static = 9090
        }
    }

    volume "vol_prometheus"  {
      type = "host"
      source = "vol_prometheus"
      read_only = false
    }

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["http"]

        # image = "https://docker.io/prom/prometheus:v2.39.0"
        image = "https://docker.io/prom/prometheus:v3.0.0"

        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://tl0992obsobs01.test.nsw.education:3100/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yml:/etc/prometheus/prometheus.yml",
          "local/web-config.yml:/etc/prometheus/web-config.yml",
          "local/tls.crt:/etc/prometheus/tls.crt",
          "local/tls.key:/etc/prometheus/tls.key",
          # "local/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          # "--web.external-url=https://prometheus.obsobs.nsw.education",
          "--web.external-url=http://pl0992obsobs01.nsw.education",
          "--web.page-title=ObsObs Prod Prometheus",

          "--config.file=/etc/prometheus/prometheus.yml",
          "--web.config.file=/etc/prometheus/web-config.yml",

          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=28d",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus"
        port = "http"

        check {
          # 2024-11-28 jedd - add protocol (https) and tls_skip_verify - because
          # these endpoints are now talking HTTPS ... BUT ... they are also being
          # accessed via ************/24 network, so the SANs aren't going to be
          # there.  Doing this means the services are registered in Consul, but
          # don't insta-fail (to red / critical) state.
          type = "http"
          port = "http"
          protocol = "https"
          tls_skip_verify = "true"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.prometheus.rule=Host(`prometheus.obsobs.nsw.education`)",
#          "traefik.http.routers.prometheus.tls=false",
#          "traefik.http.routers.prometheus.entrypoints=http,https,prometheus",
#        ]

        meta {
          cir_app_id = "obs"
          cluster    = "obsobs"
          env        = "prod"
        }
      }

      template {
        data = file("assets/prometheus.yml")
        destination = "local/prometheus.yml"
      }

      template {
        data = file("assets/prometheus-web-config.yml")
        destination = "local/web-config.yml"
      }

      template {
        data = file("assets/tls.crt")
        destination = "local/tls.crt"
      }
      template {
        data = file("assets/tls.key")
        destination = "local/tls.key"
      }


      resources {
        cpu    = 600
        memory = 2048
      }

    }
  }
}
