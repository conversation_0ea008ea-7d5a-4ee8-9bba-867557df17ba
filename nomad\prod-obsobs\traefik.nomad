
// Obs-Obs watcher instance of Trae<PERSON><PERSON>  in the PROD env - a PoC to monitor ObsCol stack


job "traefik" {
  type        = "system"

  datacenters = ["dc-obsobs-prod"]
  
  group "traefik" {

    network {
      dns {
        servers = ["192.168.31.1"]
      }

      port "http" {
        static = 80
      }

      port "https" {
        static = 443
      }

      port "traefik" {
        static = 8081
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        image        = "https://docker.io/library/traefik:latest"

#        logging {
#            type = "loki"
#            config {
#                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
#                }
#            }        

        network_mode = "host"

        cap_add = ["net_bind_service"]

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
        ]
      }

      template {
        data = <<EOF
debug = false
logLevel = "INFO"
defaultEntryPoints = ["https","http"]

[entryPoints]
    [entryPoints.http]
    address = ":80"

    [entryPoints.https]
    address = ":443"

      [entryPoints.https.http.tls]
        [[entryPoints.https.http.tls.domains]]
        main = "obscol.obsobs.nsw.education"
        sans = ["*.obsobs.nsw.education"]

    [entryPoints.traefik]
    address = ":8081"



    # Experimental 2022-08 primarily for loki.
    # [entryPoints.alertmanager]
    # address = ":9093"

    [entryPoints.loki]
    address = ":3100"    

    [entryPoints.mimir]
    address = ":8080"    

    [entryPoints.prometheus]
    address = ":9090"


[api]
    dashboard = true
    insecure  = true



# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = false

    [providers.consulCatalog.endpoint]
      address = "consul.service.dc-obsobs-prod.collectors.obsobs.nsw.education:8500"
      scheme  = "http"

# Enable File Provider for "Dynamic Configuration" elements
[providers.file]
  directory = "/local/traefik.d"

[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]

EOF

        destination = "local/traefik.toml"
      }

      template {
        destination = "local/traefik.d/certificates.toml"
        data = <<EOF
[tls.stores]
  [tls.stores.default]
    # This one should be used if the client did not perform an SNI handshake.
    [tls.stores.default.defaultCertificate]
      certFile = "/local/obscol.obsobs.nsw.education.pem"
      keyFile = "/local/obscol.obsobs.nsw.education.key"

[[tls.certificates]]
  certFile = "/local/obscol.obsobs.nsw.education.pem"
  keyFile = "/local/obscol.obsobs.nsw.education.key"

EOF
      }

      template {
        destination = "local/obscol.obsobs.nsw.education.pem"
        data = <<EOF
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7eSPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOF
      }

      template {
        destination = "local/obscol.obsobs.nsw.education.key"
        data = <<EOF
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
      }

      resources {
        cpu    = 300
        memory = 256
      }
    }
  }
}

