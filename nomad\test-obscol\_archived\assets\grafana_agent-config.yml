server:
  log_level: info

metrics:
  global:
    scrape_interval: 1m
    remote_write:
      - url: https://mimir-distributor.obs.test.nsw.education/api/v1/push
        headers: 
            X-Scope-OrgID: prod
        tls_config:
            insecure_skip_verify: true
  configs:
    - name: grafana_agent_metrics_nomad
      scrape_configs:
        - job_name: agent
          static_configs:
            - targets: ['127.0.0.1:12345']

logs:
  configs:
  - name: grafana_agent_loki_nomad
    positions:
      filename: /tmp/positions.yaml
    scrape_configs:
      - job_name: varlogs
        static_configs:
          - targets: [localhost]
            labels:
              job: varlogs
              __path__: /var/log/*log
    clients:
      - url: https://loki.obs.test.nsw.education/loki/api/v1/push

traces:
  configs:
  - name: grafana_agent_tempo_nomad
    receivers:
      jaeger:
        protocols:
          grpc: # listens on the default jaeger grpc port: 14250
    remote_write:
      - endpoint: https://jaeger.obs.test.nsw.education
        insecure: true  # only add this if TLS is not required
    batch:
      timeout: 5s
      send_batch_size: 100

integrations:
  node_exporter:
    enabled: true