multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "info"

  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

distributor:
  ha_tracker:
    enable_ha_tracker: false

    kvstore:
      store: "consul"
      prefix: "mimir/ha-tracker/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    replication_factor: 1
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: "consul"
      # 2023-02-13 this mount is now backed by local disk, not NFS
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

frontend_worker:
  scheduler_address: "mimir-query-scheduler.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
  id: {{ env "node.unique.name" }}
  grpc_client_config:
    grpc_compression: "snappy"

frontend:
  scheduler_address: "mimir-query-scheduler.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"

query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "https://mimir-ruler.obs.test.nsw.education/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  # alertmanager_url: "dns:///alertmanager.obs.test.nsw.education:9876"
  alertmanager_url: "https://alertmanager.obs.test.nsw.education"
  query_frontend:
    address: "https://mimir-query-frontend.obs.test.nsw.education"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

compactor:
  data_dir: /mimir/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway:
  sharding_ring:
    replication_factor: 1
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage:
  bucket_store:
    sync_dir: /mimir/tsdb-sync/
  tsdb:
    dir: /mimir/tsdb/
    flush_blocks_on_shutdown: true

common:
  storage:
    backend: s3
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp

limits:
  compactor_blocks_retention_period: "25w"
  accept_ha_samples: true
  
  # 2023-02-09 jedd - attempting to resolve ingester out of order errors
  out_of_order_time_window:  5m

