multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "info"

  graceful_shutdown_timeout: "90s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

alertmanager:
  sharding_ring:
    kvstore:
      store: "consul"
      prefix: "alertmanagers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  data_dir: /mimir/alertmanager/

alertmanager_storage:
  backend: "filesystem"
  local:
    path: "/mimir/alertmanager/"

distributor:
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    replication_factor: 1
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}    
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

frontend_worker:
  scheduler_address: "mimir-rwb-backend.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
  id: {{ env "node.unique.name" }}
  grpc_client_config:
    grpc_compression: "snappy"

frontend:
  results_cache:
    backend: redis
    redis:
      endpoint: mimir-rwb-redis.obs.test.nsw.education:6379
      db: 2  
  scheduler_address: "mimir-rwb-backend.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  port: {{ env "NOMAD_PORT_grpc" }}
  log_queries_longer_than: "5s"
  cache_results: true
  grpc_client_config:
    grpc_compression: "snappy"

query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  ring:
    kvstore:
      store: "consul"
      prefix: "rulers/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "https://mimir-rwb-backend.obs.test.nsw.education/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  alertmanager_url: "https://alertmanager.obs.test.nsw.education"
  query_frontend:
    address: "https://mimir-rwb-read.obs.test.nsw.education"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

querier:
  query_store_after: 4h

compactor:
  data_dir: /mimir/data/compactor
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway:
  sharding_ring:
    replication_factor: 1
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage:
  backend: s3
  bucket_store:
    ignore_blocks_within: 10h
    sync_dir: /mimir/tsdb-sync/
    index_cache:
      backend: redis
      redis:
        endpoint: mimir-rwb-redis.obs.test.nsw.education:6379
        db: 0
    chunks_cache:
      backend: redis
      redis:
        endpoint: mimir-rwb-redis.obs.test.nsw.education:6379
        db: 1  
  tsdb: #Required to be persistent between restarts
    dir: /mimir/tsdb/
    flush_blocks_on_shutdown: true
    memory_snapshot_on_shutdown: true
    retention_period: 5h

common:
  storage:
    backend: s3
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp

limits:
  compactor_blocks_retention_period: "25w"
  accept_ha_samples: true
  max_global_series_per_user: 0
  max_label_names_per_series: 200
  out_of_order_time_window:  5m