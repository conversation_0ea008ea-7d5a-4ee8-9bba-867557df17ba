
# Used by phlare.nomad (TEST obscol) 
#
# Full list of configuration options at:
#   https://grafana.com/docs/phlare/latest/operators-guide/configure/reference-configuration-parameters/

server:
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc "}}

storage:
  backend: filesystem

  filesystem:
    dir: "/data/"

phlaredb:
  # default retention is only 3h
  max_block_duration: 24h

ingester:
  lifecycler:
    ring:
      kvstore:
        store: "consul"
        prefix: "phlare/"
        consul:
          host: {{ env "attr.unique.network.ip-address" }}:8500
    address: {{ env "attr.unique.network.ip-address" }}
    port: {{ env "NOMAD_PORT_grpc" }}
    id: {{ env "node.unique.name" }}

scrape_configs:
  - job_name: "phlare-self"
    scrape_interval: "15s"
    scrape_timeout: 30s
    static_configs:
      - targets: ["phlare.obs.test.nsw.education"]

    # = = = = = = = = = = = = = = = = = = = = = = = =
    # These are maintained in alphabetical order

  - job_name: "phlare-collector-oliver-blackbox"
    scrape_interval: 60s
    static_configs:
      - targets: ["collector-oliver-blackbox.obs.test.nsw.education"]

  - job_name: "phlare-collector-oliver-prometheus"
    scrape_interval: 60s
    static_configs:
      - targets: ["collector-oliver-prometheus.obs.test.nsw.education"]

  - job_name: "phlare-exporter-blackbox"
    scrape_interval: 60s
    static_configs:
      - targets: ["exporter-blackbox.obs.test.nsw.education"]

  - job_name: "phlare-exporter-idrac"
    scrape_interval: 60s
    static_configs:
      - targets: ["exporter-idrac.obs.test.nsw.education"]

  - job_name: "phlare-exporter-snmp-pdu"
    scrape_interval: 60s
    static_configs:
      - targets: ["exporter-snmp-pdu.obs.test.nsw.education"]

  - job_name: "phlare-loki"
    scrape_interval: 60s
    # 2023-01-31 jedd - this is the only endpoint that is *only* served on https, so watch
    #                   for these types.
    scheme: "https"
    static_configs:
      - targets: ["loki.obs.test.nsw.education"]

  - job_name: "phlare-mimir"
    scrape_interval: 60s
    static_configs:
      - targets: ["mimir.obs.test.nsw.education"]

  - job_name: "phlare-prom"
    scrape_interval: 60s
    static_configs:
      - targets: ["prometheus.obs.test.nsw.education"]

  - job_name: "phlare-tempo"
    scrape_interval: 60s
    static_configs:
      - targets: ["tempo.obs.test.nsw.education"]

