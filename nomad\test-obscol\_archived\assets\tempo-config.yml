auth_enabled: false
server:
  http_listen_port: {{ env "NOMAD_PORT_port_tempo" }}
distributor:
  log_received_spans:
    enabled: false
  receivers:                           
    # This configuration will listen on all ports and protocols that tempo is capable of.
    # Refer: found there: https://github.com/open-telemetry/opentelemetry-collector/tree/master/receiver
    jaeger:                            
      protocols:                       
        thrift_http:
          endpoint: "0.0.0.0:14268"
        grpc:                          
        thrift_binary:
        thrift_compact:
    otlp:
      protocols:
        http:
        grpc:
    opencensus:

ingester:
  # length of time after a trace has not received spans to consider it complete and flush it
  trace_idle_period: 10s
  max_block_duration: 5m

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together
    max_compaction_objects: 1000000    # maximum size of compacted blocks
    block_retention: 336
    compacted_block_retention: 700h

storage:
  trace:
    backend: s3
    wal:
      path: /mnt/tempo/wal
    s3:
      bucket: nswdoe-obs-tempo-blocks-storage-dev
      endpoint: s3.ap-southeast-2.amazonaws.com
      region: ap-southeast-2
      access_key: ********************
      secret_key: RryworGGNUUb+YQOOndKe+On8/FdzkCmXMxKK7Gr
    pool:
      # worker pool determines the number of parallel requests to the object store backend
      max_workers: 100                 
      queue_depth: 10000

metrics_generator:
  processor:
    service_graphs:
      max_items: 50000
  registry:
    external_labels:
      source: tempo
      cluster: obscol-prod
  storage:
    path: /mnt/tempo/generator/wal
    remote_write:
      - url: https://mimir-distributor.obs.test.nsw.education/api/v1/push
        headers: 
          X-Scope-OrgID: test
        tls_config:
          insecure_skip_verify: true

overrides:
  metrics_generator_processors: [service-graphs, span-metrics]