
// obscol-test - OTEL Astronomy Shop Demo - GROUP B
//
// WIP attempt to get the OTEL demo suite running in Nomad.
//
// <PERSON><PERSON> has two posts about this:
//    OpenTelemetry.io  2022-12 :  https://opentelemetry.io/blog/2022/otel-demo-app-nomad/
//    her Medium site   2021-12 :  https://storiesfromtheherd.com/just-in-time-nomad-running-the-opentelemetry-collector-on-hashicorp-nomad-with-hashiqube-4eaf009b8382
//
// The newer one starts with the suggestion it's her first attempt at setting this up, and both
// instances rely on bespoke traefik, grafana, jaeger, OTEL collector etc - plus they rely on
// HashiQube, which we don't want to touch.  She tends to run them all as separate nomad jobs,
// relying on traefik to get them talking to each other.  There's some divergence in the approaches,
// the older version uses honeycomb.  In any case, despite being the only attempt on the net I could
// find, I'm mostly going to be starting from scratch.  Insert generic grumble aboue people that don't
// put ANY FLIPPING COMMENTS in their code / jobs / etc.

// Consistency is the hobgoblin of small minds .. yada yada.  I like the consistency of using port name
// of 'containerport' everywhere (though I note <PERSON>a doesn't do that for the grafana task - there the
// port is just called 'http') but on the other hand I abhor the overloading of variables that are hard
// to track down later if you're not breathing Nomad HCL, and specifically a foreign recipe, daily.  And
// obviously with lots of tasks in one big group (or a handful of groups with many tasks in each, as we'll
// probably end up with here) this naming scheme obviously wouldn't work.
//
// At work we've adopted a prefix of port_ everywhere for port names - it makes NOMAD_PORT_port_... look
// a bit redundant, but it's *obvious* later on when you see it with or without context.
//
// Similarly our job, group, and task names need to make sense when reviewing Loki logs, which pick up
// from the Loki driver for docker.  I've added lots of loki-exporting stanzas here.


// Gotcha 1 - you will likely get some inotify failures causing docker to bomb out, this is related to
// the sysctl settable value for user.max_inotify_instances -- has a default of 128, which is way too
// small apparently.
// exact error:  Unhandled exception. System.IO.IOException: The configured user limit (128) on the number of inotify instances has been reached, or the per-process limit on the number of open file descriptors has been reached

// Gotcha 2 - Loki driver - read up: https://grafana.com/docs/loki/latest/clients/docker-driver/
// This means you need to run:
// docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
// alternatively - comment out all the logging stanzas below.

// Gotcha 3 - I run my own prometheus, grafana, and otel collector - so duplicating all that again
// in a phat job with ephemeral-by-default storage, etc, is not ideal.  Initial cut of this play
// will include everything from Adriana's jobs, taken in turn from the upstream helmchart, and then
// can be stripped back to use local resources.  I hope it's a safe assumption that if you're playing
// with this you already have grafana, at least, and probably a prometheus in play.

// Gotcha 4 - usual constraints - service names max 63 chars and can't contain underscores. (@TODO confirm 
// what can't contain hyphens but needs underscores instead? - why did we start using underscores in ports?)

// Gotcha 5 -- ordering is important, and Nomad has only some very basic task-based ordering (refer:
// https://developer.hashicorp.com/nomad/docs/job-specification/lifecycle ) - which facilitates only
// three categories - pre, post-start, and post-stop, which won't help us here, as our dependency graph
// is a bit more complicated than that.
//
// Raw ordering / dependencies taken from docker-compose.yml :
//      
//  C     adservice - depends on otelcol
//      
//  C     cartservice - depends on otelcol, redis-cart
//      
//  D     checkoutservice - depends on cartservice, currencyservice, emailservice, otelcol, paymentservice,
//                        productcatalogservice, shippingservice
//      
//  C     currencyservice - depends on otelcol
//      
//  C     emailservice - depends on  otelcol
//      
//  B     featureflagservice - depends on ffs_postgres
//
//  A     ffs_postgres - no dependencies
//      
//  E     frontend - depends on adservice, cartservice, checkoutservice, currencyservice, otelcol, 
//                 productcatalogservice, quoteservice, recommendationservice, shippingservice
//      
//  G     frontendproxy - depends on  featureflagservice, frontend, grafana, loadgenerator
//
//  A     grafana - no dependencies
//
//  A     jaeger - no dependencies
//      
//  F     loadgenerator - depends on frontend
//      
//  B     otelcol - depends on jaeger
//      
//  C     paymentservice - depends on otelcol
//      
//  C     productcatalogservice - depends on otelcol
//
//  A     prometheus - no dependencies
//      
//  C     quoteservice - depends on otelcol
//      
//  D     recommendationservice - depends on featureflagservice, otelcol, productcatalogservice
//      
//  A     redis-cart - no dependencies
//      
//  C     shippingservice - depends on otelcol
//      
// Interpreting the above, since docker-compose-viz failed to satisfy:
//      a) ffs_postgres , grafana , jaeger , prometheus , redis-cart
//      b) featureflagservice , otelcol 
//      c) adservice , cartservice , currencyservice , emailservice , paymentservice
//         productcatalogservice , quoteservice , shippingservice
//      d) checkoutservice , recommendationservice
//      e) frontend
//      f) loadgenerator
//      g) frontendproxy


variables  {
  # These make it easier to adjust this to local resource names.
  loki = {
    url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
  }

  domain = {
    obs = "obs.test.nsw.education"
  }
}


job "astronomy-shop-group-b" {
  #  Group B = featureflagservice , otelcollector 

  datacenters = ["dc-cir-un-test"]

  type = "service"

  group "astronomy-shop-group-b" {

    network {

      # featureflagservice
      port "port_ffs_http" { 
        to = 8081
      }
      port "port_ffs_grpc" { 
        to = 50053
      }

      # otel collector has A LOT
      port "port_otel_collector_healthcheck" {
        to = 13133
      }
      port "port_otel_collector_jaeger_compact" {
        to = 6831
        // UDP???
      }
      port "port_otel_collector_jaeger_grpc" {
        to = 14250
      }
      port "port_otel_collector_jaeger_thrift" {
        to = 14268
      }
      port "port_otel_collector_metrics" {
        to = 8888
      }
      port "port_otel_collector_otlp" {
        to = 4317
      }
      port "port_otel_collector_otlp_http" {
        to = 4318
      }
      port "port_otel_collector_prometheus" {
        to = 9464
      }
      port "port_otel_collector_zipkin" {
        to = 9411
      }

    }


    # TASK -- featureflagservice  = = = = = = = = = = = = = = = = = = = = 
    task "demo-featureflagservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-featureflagservice"
        ports = ["port_ffs_http", "port_ffs_grpc"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        FEATURE_FLAG_GRPC_SERVICE_PORT = "${NOMAD_PORT_port_ffs_grpc}"
        FEATURE_FLAG_SERVICE_PATH_ROOT = "\"/feature\""
        FEATURE_FLAG_SERVICE_PORT = "${NOMAD_PORT_port_ffs_http}"
        OTEL_EXPORTER_OTLP_TRACES_PROTOCOL = "grpc"
        OTEL_SERVICE_NAME = "featureflagservice"
      }      

      service {
        name = "featureflagservice-http"
        port = "port_ffs_http"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.featureflagservice.rule=Host(`featureflagservice.obs.test.nsw.education`)",
          "traefik.http.routers.featureflagservice.tls=false",
          "traefik.http.routers.featureflagservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_ffs_http"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      service {
        name = "featureflagservice-grpc"
        port = "port_ffs_grpc"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.featureflagservice-grpc.rule=Host(`featureflagservice-grpc.obs.test.nsw.education`)",
          "traefik.http.routers.featureflagservice-grpc.tls=false",
          "traefik.http.routers.featureflagservice-grpc.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_ffs_grpc"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 60
        # memory = 250
        memory = 150
        memory_max = 400
      }

      template {
        ### @TODO jedd for some reason domain isn't being picked up - so hardcoding temporarily
        ### DATABASE_URL = "ecto://ffs:<EMAIL>:80/ffs"
        data = <<EOF

# DATABASE_URL = "ecto://ffs:<EMAIL>:80/ffs"
DATABASE_URL = "ecto://ffs:<EMAIL>:6543/ffs"


OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.obs.test.nsw.education"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-featureflagservice"


    # TASK -- otel-collector  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-otel-collector" {
      driver = "docker"

      config {
        image = "otel/opentelemetry-collector-contrib:0.64.1"

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/config/otel-collector-config.yaml",
        ]

        ports = [
          "port_otel_collector_healthcheck" ,
          "port_otel_collector_jaeger_compact" ,
          "port_otel_collector_jaeger_grpc" ,
          "port_otel_collector_jaeger_thrift" ,
          "port_otel_collector_metrics" ,
          "port_otel_collector_otlp" ,
          "port_otel_collector_otlp_http" ,
          "port_otel_collector_prometheus",
          "port_otel_collector_zipkin" 
        ]

        volumes = [ ]

        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        HOST_DEV = "/hostfs/dev"
        HOST_ETC = "/hostfs/etc"
        HOST_PROC = "/hostfs/proc"
        HOST_RUN = "/hostfs/run"
        HOST_SYS = "/hostfs/sys"
        HOST_VAR = "/hostfs/var"
      }      


      service {
        name = "otel-collector-healthcheck" 
        port = "port_otel_collector_healthcheck" 
        #check {
        #  type     = "http"
        #  path     = "/"
        #  interval = "10s"
        #  timeout  = "5s"
        #}
      }
      service {
        name = "otel-collector-jaeger-compact"
        port = "port_otel_collector_jaeger_compact" 
        tags = ["jaeger-compact"]
      }
      service {
        name = "otel-collector-jaeger-grpc" 
        port = "port_otel_collector_jaeger_grpc" 
        tags = ["jaeger-grpc"]
      }
      service {
        name = "otel-collector-jaeger-thrift" 
        port = "port_otel_collector_jaeger_thrift" 
        tags = ["jaeger-thrift"]
      }
      service {
        name = "otel-collector-metrics" 
        port = "port_otel_collector_metrics" 
        tags = ["metrics",
          "traefik.http.routers.otel-collector-metrics.rule=Host(`otel-collector-metrics.obs.test.nsw.education`)",
          "traefik.http.routers.otel-collector-metrics.entrypoints=web",
          "traefik.http.routers.otel-collector-metrics.tls=false",
          "traefik.enable=true",
        ]        

      }
      service {
        name = "otel-collector-otlp" 
        port = "port_otel_collector_otlp" 
        tags = [
          "traefik.tcp.routers.otel-collector-grpc.rule=HostSNI(`*`)",
          "traefik.tcp.routers.otel-collector-grpc.entrypoints=grpc",
          "traefik.enable=true",
        ]        
      }
      service {
        name = "otel-collector-otlp-http" 
        port = "port_otel_collector_otlp_http" 
        tags = [
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.test.nsw.education`)",
          "traefik.http.routers.otel-collector-http.entrypoints=web",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
      }
      service {
        name = "otel-collector-prometheus"
        port = "port_otel_collector_prometheus"
        tags = ["prometheus"]
      }
      service {
        name = "otel-collector-zipkin" 
        port = "port_otel_collector_zipkin" 
        tags = ["zipkin"]
      }


      resources {
        cpu = 60
        memory = 150
        memory_max = 300
      }


      template {
        data = <<EOH
receivers:
  otlp:
    protocols:
      grpc:
      http:
        endpoint: "0.0.0.0:4318"

processors:
  batch:
    timeout: 10s
  spanmetrics:
    metrics_exporter: prometheus

  memory_limiter:
    # 75% of maximum memory up to 4G
    limit_mib: 1536
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

exporters:
  logging:
    verbosity: detailed

  prometheus:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_port_otel_collector_prometheus" }}"

  otlp:
    endpoint: 'jaeger.obs.test.nsw.education:80'
    tls:
      insecure: true

extensions:
  health_check:
    endpoint: 0.0.0.0:{{ env "NOMAD_PORT_port_otel_collector_healthcheck" }}

service:
  extensions: [health_check]
  pipelines:
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [prometheus, logging]
    traces:
      receivers: [otlp]
      processors: [spanmetrics, batch]
      exporters: [logging, otlp]

EOH

        change_mode   = "restart"
        destination = "local/config/otel-collector-config.yaml"
      }


    } // end-task "demo-otel-collector" 

  }  // end-group "astronomy-shop"

}

