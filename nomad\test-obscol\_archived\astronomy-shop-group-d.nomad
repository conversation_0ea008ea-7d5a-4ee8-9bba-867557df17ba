
// obscol-test - OTEL Astronomy Shop Demo - GROUP D
//
// WIP attempt to get the OTEL demo suite running in Nomad.
//
// <PERSON><PERSON> has two posts about this:
//    OpenTelemetry.io  2022-12 :  https://opentelemetry.io/blog/2022/otel-demo-app-nomad/
//    her Medium site   2021-12 :  https://storiesfromtheherd.com/just-in-time-nomad-running-the-opentelemetry-collector-on-hashicorp-nomad-with-hashiqube-4eaf009b8382
//
// The newer one starts with the suggestion it's her first attempt at setting this up, and both
// instances rely on bespoke traefik, grafana, jaeger, OTEL collector etc - plus they rely on
// HashiQube, which we don't want to touch.  She tends to run them all as separate nomad jobs,
// relying on traefik to get them talking to each other.  There's some divergence in the approaches,
// the older version uses honeycomb.  In any case, despite being the only attempt on the net I could
// find, I'm mostly going to be starting from scratch.  Insert generic grumble aboue people that don't
// put ANY FLIPPING COMMENTS in their code / jobs / etc.

// Consistency is the hobgoblin of small minds .. yada yada.  I like the consistency of using port name
// of 'containerport' everywhere (though I note <PERSON>a doesn't do that for the grafana task - there the
// port is just called 'http') but on the other hand I abhor the overloading of variables that are hard
// to track down later if you're not breathing Nomad HCL, and specifically a foreign recipe, daily.  And
// obviously with lots of tasks in one big group (or a handful of groups with many tasks in each, as we'll
// probably end up with here) this naming scheme obviously wouldn't work.
//
// At work we've adopted a prefix of port_ everywhere for port names - it makes NOMAD_PORT_port_... look
// a bit redundant, but it's *obvious* later on when you see it with or without context.
//
// Similarly our job, group, and task names need to make sense when reviewing Loki logs, which pick up
// from the Loki driver for docker.  I've added lots of loki-exporting stanzas here.


// Gotcha 1 - you will likely get some inotify failures causing docker to bomb out, this is related to
// the sysctl settable value for user.max_inotify_instances -- has a default of 128, which is way too
// small apparently.
// exact error:  Unhandled exception. System.IO.IOException: The configured user limit (128) on the number of inotify instances has been reached, or the per-process limit on the number of open file descriptors has been reached

// Gotcha 2 - Loki driver - read up: https://grafana.com/docs/loki/latest/clients/docker-driver/
// This means you need to run:
// docker plugin install grafana/loki-docker-driver:latest --alias loki --grant-all-permissions
// alternatively - comment out all the logging stanzas below.

// Gotcha 3 - I run my own prometheus, grafana, and otel collector - so duplicating all that again
// in a phat job with ephemeral-by-default storage, etc, is not ideal.  Initial cut of this play
// will include everything from Adriana's jobs, taken in turn from the upstream helmchart, and then
// can be stripped back to use local resources.  I hope it's a safe assumption that if you're playing
// with this you already have grafana, at least, and probably a prometheus in play.

// Gotcha 4 - usual constraints - service names max 63 chars and can't contain underscores. (@TODO confirm 
// what can't contain hyphens but needs underscores instead? - why did we start using underscores in ports?)

// Gotcha 5 -- ordering is important, and Nomad has only some very basic task-based ordering (refer:
// https://developer.hashicorp.com/nomad/docs/job-specification/lifecycle ) - which facilitates only
// three categories - pre, post-start, and post-stop, which won't help us here, as our dependency graph
// is a bit more complicated than that.
//
// Raw ordering / dependencies taken from docker-compose.yml :
//      
//  C     adservice - depends on otelcol
//      
//  C     cartservice - depends on otelcol, redis-cart
//      
//  D     checkoutservice - depends on cartservice, currencyservice, emailservice, otelcol, paymentservice,
//                        productcatalogservice, shippingservice
//      
//  C     currencyservice - depends on otelcol
//      
//  C     emailservice - depends on  otelcol
//      
//  B     featureflagservice - depends on ffs_postgres
//
//  A     ffs_postgres - no dependencies
//      
//  E     frontend - depends on adservice, cartservice, checkoutservice, currencyservice, otelcol, 
//                 productcatalogservice, quoteservice, recommendationservice, shippingservice
//      
//  G     frontendproxy - depends on  featureflagservice, frontend, grafana, loadgenerator
//
//  A     grafana - no dependencies
//
//  A     jaeger - no dependencies
//      
//  F     loadgenerator - depends on frontend
//      
//  B     otelcol - depends on jaeger
//      
//  C     paymentservice - depends on otelcol
//      
//  C     productcatalogservice - depends on otelcol
//
//  A     prometheus - no dependencies
//      
//  C     quoteservice - depends on otelcol
//      
//  D     recommendationservice - depends on featureflagservice, otelcol, productcatalogservice
//      
//  A     redis-cart - no dependencies
//      
//  C     shippingservice - depends on otelcol
//      
// Interpreting the above, since docker-compose-viz failed to satisfy:
//      a) ffs_postgres , grafana , jaeger , prometheus , redis-cart
//      b) otelcol , featureflagservice
//      c) adservice , cartservice , currencyservice , emailservice , paymentservice
//         productcatalogservice , quoteservice , shippingservice
//      d) checkoutservice , recommendationservice
//      e) frontend
//      f) loadgenerator
//      g) frontendproxy


variables  {
  # These make it easier to adjust this to local resource names.
  loki = {
    url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
  }

  domain = {
    obs = "obs.test.nsw.education"
  }
}


job "astronomy-shop-group-d" {
  # Group D = checkoutservice , recommendationservice

  datacenters = ["dc-cir-un-test"]

  type = "service"

  group "astronomy-shop-group-d" {

    network {

      port "port_checkoutservice" { 
        to = 9555
      }

      port "port_recommendationservice" {
        to = 9001
        static = 9002
      }

    }

    # TASK -- checkoutservice  = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-checkoutservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-checkoutservice"
        image_pull_timeout = "25m"
        ports = ["port_checkoutservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        CHECKOUT_SERVICE_PORT = "${NOMAD_PORT_port_checkoutservice}"
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_SERVICE_NAME = "checkoutservice"
      }      

      service {
        name = "checkoutservice"
        port = "port_checkoutservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.checkoutservice.rule=Host(`checkoutservice.obs.test.nsw.education`)",
          "traefik.http.routers.checkoutservice.tls=false",
          "traefik.http.routers.checkoutservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_checkoutservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 30
        memory = 280
        memory_max = 400
      }

      template {
        data = <<EOF

CART_SERVICE_ADDR = "cartservice.obs.test.nsw.education:80"

CURRENCY_SERVICE_ADDR = "currencyservice.obs.test.nsw.education:80"

EMAIL_SERVICE_ADDR = "emailservice.obs.test.nsw.education:80"

PAYMENT_SERVICE_ADDR = "paymentservice.obs.test.nsw.education"

PRODUCT_CATALOG_SERVICE_ADDR = "productcatalogservice.obs.test.nsw.education:80"

SHIPPING_SERVICE_ADDR = "shippingservice.obs.test.nsw.education:80"

OTEL_EXPORTER_OTLP_METRICS_ENDPOINT = "http://otel-collector-otlp.obs.test.nsw.education"
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT  = "http://otel-collector-otlp.obs.test.nsw.education"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-checkoutservice"


    # TASK -- recommendationservice  = = = = = = = = = = = = = = = = = = = = = = = =
    task "demo-recommendationservice" {
      driver = "docker"

      config {
        image = "otel/demo:v1.1.0-recommendationservice"
        image_pull_timeout = "25m"
        ports = ["port_recommendationservice"]
        volumes = [ ]
        args = [ ]

        logging {
          type = "loki"
          config {
            loki-url = "${var.loki.url}"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
      }

      env {
        OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE = "cumulative"
        OTEL_METRICS_EXPORTER = "otlp"
        OTEL_PYTHON_LOG_CORRELATION = "true"
        OTEL_SERVICE_NAME = "recommendationservice"
        OTEL_TRACES_EXPORTER = "otlp"
        PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION = "python"
        RECOMMENDATION_SERVICE_PORT = "${NOMAD_PORT_port_recommendationservice}"
      }


      service {
        name = "recommendationservice"
        port = "port_recommendationservice"
      
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.recommendationservice.rule=Host(`recommendationservice.obs.test.nsw.education`)",
          "traefik.http.routers.recommendationservice.tls=false",
          "traefik.http.routers.recommendationservice.entrypoints=http",
        ]

        #check {
        #  type = "tcp"
        #  port = "port_recommendationservice"
        #  interval = "20s"
        #  timeout = "10s"
        #}
      }

      resources {
        cpu = 50
        memory = 170
        memory_max = 256
      }

      template {
        data = <<EOF
FEATURE_FLAG_GRPC_SERVICE_ADDR = "featureflagservice-grpc.obs.test.nsw.education:80"

PRODUCT_CATALOG_SERVICE_ADDR = "productcatalogservice.obs.test.nsw.education:80"

OTEL_EXPORTER_OTLP_ENDPOINT = "http://otel-collector-otlp.obs.test.nsw.education"

EOF
        destination = "local/env"
        env         = true
      }
    } // end-task "demo-recommendationservice"

  }  // end-group "astronomy-shop"

}

