# Requires some additional volumes to work: refer - https://github.com/google/cadvisor#quick-start-running-cadvisor-in-a-docker-container
job "cadvisor" {
  datacenters = ["dc-cir-un-test"]

  type = "system"

  group "infra" {
    network {
        port "cadvisor"{
            to = 8080
        }
    }
    volume "vol_var_run" {
        type = "host"
        source = "vol_var_run"
        read_only = true
    }

    task "cadvisor" {
      driver = "docker"
      config {

        image = "google/cadvisor:v0.31.0"

        # dns_servers = ["************"]
        
        ports = ["cadvisor"]

        privileged = true
      }
      volume_mount {
        volume = "vol_var_run"
        destination = "/var/run"
        read_only = true
      }       
      service {
        port = "cadvisor"
        check {
          type = "http"
          path = "/"
          interval = "10s"
          timeout = "2s"
        }
      }
      resources {
        cpu = 100
        memory = 32
        }
      }
    }
  }
