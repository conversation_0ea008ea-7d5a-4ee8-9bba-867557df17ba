// This job requires consul kv to access credentials.
// Please set the api key using:
// $ consul kv put ccloud/api-key "value"
// $ consul kv put ccloud/api-secret "value"

job "ccloudexporter" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  group "ccloudexporter" {
    network {
      port "http" {}
    }

    task "ccloudexporter" {
      driver = "docker"

      config {
        image = "https://docker.io/dabz/ccloudexporter:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }          
        args = ["-config", "/local/config.yaml"]
        ports = ["http"]

        # dns_servers = ["192.168.31.1"]
      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
        CCLOUD_CLUSTER = "lkc-1k503"
      }

      service {
        name = "openmetrics"
        port = "http"

        meta {
          cir_app_id = "kfka"
          env = "dev"
        }
      }

      template {
        destination = "secrets/file.env"
        env = true
        data = <<EOH
CCLOUD_API_KEY="{{ key "ccloud/api-key" }}"
CCLOUD_API_SECRET="{{ key "ccloud/api-secret" }}"

EOH
      }

      template {
        destination = "local/config.yaml"

        data = <<EOH
config:
  http:
    baseurl: https://api.telemetry.confluent.cloud/
    timeout: 60
  listener: 0.0.0.0:{{ env "NOMAD_PORT_http" }}
  noTimestamp: false
  delay: 60
  granularity: PT1M
  cachedSecond: 30
rules:
  - clusters:
      # DEV (xfi_cluster_0)
      - lkc-1k503
      # DEV/TEST
      - pkc-ldvj1
    connectors: []
    ksqls: []
    schemaRegistries:
      # DEV
      - psrc-epkz2
      # TEST
      - psrc-3r6ym
    metrics:
      - io.confluent.kafka.server/received_bytes
      - io.confluent.kafka.server/sent_bytes
      - io.confluent.kafka.server/received_records
      - io.confluent.kafka.server/sent_records
      - io.confluent.kafka.server/retained_bytes
      - io.confluent.kafka.server/active_connection_count
      - io.confluent.kafka.server/request_count
      - io.confluent.kafka.server/partition_count
      - io.confluent.kafka.server/successful_authentication_count
      - io.confluent.kafka.connect/sent_bytes
      - io.confluent.kafka.connect/received_bytes
      - io.confluent.kafka.connect/received_records
      - io.confluent.kafka.connect/sent_records
      - io.confluent.kafka.connect/dead_letter_queue_records
      - io.confluent.kafka.ksql/streaming_unit_count
      - io.confluent.kafka.schema_registry/schema_count
    labels:
      - kafka_id
      - topic
      - type
EOH
      }
    }
  }
}
