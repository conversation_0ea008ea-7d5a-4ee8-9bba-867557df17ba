
# collector-cyberark-privilege-cloud   -  TEST ObsCol

# refer:  https://jira.education.nsw.gov.au/browse/OBS-598



job "collector-cyberark-privilege-cloud" {

  type = "service"

  datacenters = ["dc-cir-un-test"]

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-cyberark-privilege-cloud" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
  	}


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus-cyberark-privilege-cloud" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://collector-cyberark-privilege-cloud.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Cyberark Privilege Cloud",
        ]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki-s3.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          #
          # "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]
      }

      resources {
        cpu    = 256
        memory = 256
      }

      service {
        name = "collector-cpc-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-cpc-prometheus.rule=Host(`collector-cpc-prometheus.obs.test.nsw.education`)",
          "traefik.http.routers.collector-cpc-prometheus.tls=false",
          "traefik.http.routers.collector-cpc-prometheus.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-cyberark-privilege-cloud"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-cyberark-privilege-cloud'
    static_configs:
      - targets: ['collector-cpc-prometheus.obs.test.nsw.education']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop





#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep

#remote_write:
#  - name: mimir
#    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: test
#    tls_config:
#      insecure_skip_verify: true


EOH
        destination = "local/prometheus.yaml"
      }


    }


  }

}


