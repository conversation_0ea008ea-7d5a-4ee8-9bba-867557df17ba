
// ObsCol TEST - Kafka lag collector (NOT all of kafka) 
//
// PROPOSED REWRITE to a) bump up to modern version of lag-exporter, and
//                     b) avoid using git checkout of mbd repository.
//
// WIP 2023-04-02 jedd


// PREREQUISITE -- a copy of the old bitbucket git repo 'mbd/kafka-lagexporter' (sic) into /opt/sharednfs/kafka-lag-exporter
//     chmod 755 the dir, and 644 the contents.  Requires obscol01:/root/.ssh/id_ed25519.pub put into repository's access key list.
// THIS will change - we'll move to git project obs, and fix up the name, and set up auto-pull via cron.

// Also required - the Consul key/value kv pair under /kafka/ folder for keys 'username' and 'password'


variables {
  kafka = {
    # Create vars for abstracting the bulk of the config:
    # ${var.kafka.brokers} etc

    # PROD
    # domain = "obs.nsw.education"
    # brokers = "pl0991kfkab0001.nsw.education:9092,pl0991kfkab0002.nsw.education:9092,pl0991kfkab0003.nsw.education:9092"
    # clustername = "prod"

    # PRE
    # domain = "obs.nsw.education"
    # brokers = "ql0992kfkab001.nsw.education:9092,ql0992kfkab002.nsw.education:9092,ql0992kfkab003.nsw.education:9092,"
    # clustername = "pre"

    # TEST - note this does not work from OBSCOL-PROD - so the traefik FQDN tags, scrape targets, etc will need to reflect this if we run this job in OBSCOL-TEST
    # domain = "obs.test.nsw.education"
    # brokers = "tl0992kfkab001.nsw.education:9092,tl0992kfkab002.nsw.education:9092,tl0992kfkab003.nsw.education:9092,"
    # clustername = "test"

    # DEV - note this does not work from OBSCOL-PROD - so the traefik FQDN tags, scrape targets, etc will need to reflect this if we run this job in OBSCOL-TEST
    # domain = "obs.test.nsw.education"
    # brokers = "dl0991kfkab201.nsw.education:9092,dl0991kfkab205.nsw.education:9092,dl0992kfkab202.nsw.education:9092,dl0992kfkab206.nsw.education:9092"
    # clustername = "dev"

    # CNB - Crash and Burn - note this does not work from OBSCOL-PROD - so the traefik FQDN tags, scrape targets, etc will need to reflect this if we run this job in OBSCOL-TEST
    domain = "obs.test.nsw.education"
    brokers = "dl0991kfkab0001.nsw.education:9092,dl0991kfkab0002.nsw.education:9092"
    clustername = "cnb"

  }

}

job "collector-kafka-lag-exporter-cnb" {

  datacenters = ["dc-cir-un-test"]

# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
#
#  Everything below here should be generic - identical between the various (4?)
#  collector-kafka-lag-exporter-*.nomad files, one per environment.   Abstracting
#  the four jobs automatically is probably Too Much Complexity, but keeping these
#  configurations below updated to be identical should be easy / would be ideal.
#
# = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =


  type = "service"


  group "collector-kafka-lag-exporter" {
    count = 1

    constraint {
      # 2023-05-08 jedd - this is UGLY and should be short-lived
      # it's a response to not being able to access kafka lag metrics from OCP dev & test environments,
      # as they can't access this env (test obscol) on 80 / 443, and our prod obscol environment can't
      # access kafka lag metrics in dev & test (high port - 9092)
      # this is part of a bodgy work-around to get metrics exposed to prod-obscol for remote-read from
      # there, and then expose in that env.
      # Note - DEV kafka exposes on obscol01, test on obscol02, cnb on obscol03
      attribute = "${attr.unique.hostname}"
      value = "tl0992obscol03.nsw.education"
    }

    network {

      port "port_prometheus" {
        # 2023-05-08 jedd - this is UGLY and should be short-lived
        # it's a response to not being able to access kafka lag metrics from OCP dev & test environments,
        # as they can't access this env (test obscol) on 80 / 443, and our prod obscol environment can't
        # access kafka lag metrics in dev & test (high port - 9092)
        # this is part of a bodgy work-around to get metrics exposed to prod-obscol for remote-read from
        # there, and then expose in that env.
        # Note - DEV kafka exposes on obscol01, test on obscol02
        static = 11060
      }

      port "port_lag_exporter" { }
    }


    # TASK -- lag exporter  = = = = = = = = = = = = = = = = = = = = = = =

    task "kafka-lag-exporter" {
      driver = "docker"


      config {
        # image = "https://docker.io/lightbend/kafka-lag-exporter:0.6.7"
        image = "seglo/kafka-lag-exporter:0.8.2"

        ports = ["port_lag_exporter"]

        volumes = [
          # This was the git checkout of mbd/kafka-lagexporter.git
          # "local/conf:/opt/docker/conf/",

          # This is the checkout done outside nomad/docker - maintained on /opt/sharednfs
          "/opt/sharednfs/kafka-lag-exporter:/opt/docker/conf/",

          # Template file (below) goes to local
          "local/application.conf:/opt/docker/conf/application.conf"
        ]

        args = [
          "/opt/docker/bin/kafka-lag-exporter", 

          "-Dconfig.file=/opt/docker/conf/application.conf"
          # -Dlogback.configurationFile=/opt/docker/conf/logback.xml
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.${var.kafka.domain}/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},env=${var.kafka.clustername},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      env {
        # Available defaults
        # https://github.com/lightbend/kafka-lag-exporter/blob/master/src/main/resources/reference.conf
        #
        # KAFKA_LAG_EXPORTER_PORT = 8000
        # KAFKA_LAG_EXPORTER_POLL_INTERVAL_SECONDS = 30
        # KAFKA_LAG_EXPORTER_LOOKUP_TABLE_SIZE = 60
        # KAFKA_LAG_EXPORTER_CLIENT_GROUP_ID = "kafkalagexporter"
        # KAFKA_LAG_EXPORTER_KAFKA_CLIENT_TIMEOUT_SECONDS = "10 seconds"
        # KAFKA_LAG_EXPORTER_CLUSTERS = []
        # KAFKA_LAG_EXPORTER_STRIMZI = "false"

        # KAFKA_LAG_EXPORTER_KAFKA_LOG_LEVEL = "debug"

        KAFKA_BROKERS = "${var.kafka.brokers}"
        KAFKA_CLUSTER_NAME = "${var.kafka.clustername}"

        KAFKA_CIR_APP_ID = "kfka"
        PROMETHEUS_PORT = "${NOMAD_PORT_port_lag_exporter}"
        TRUSTSTORE_PASS = "123456"
      }


      template {
        data = <<EOH
kafka-lag-exporter {
  reporters.prometheus.port = {{ env "PROMETHEUS_PORT" }}
  clusters = [
    {
      name = "{{ env "KAFKA_CLUSTER_NAME" }}"
      bootstrap-brokers = "{{ env "KAFKA_BROKERS" }}"
      labels = {
        cir_app_id = "{{ env "KAFKA_CIR_APP_ID" }}"
      }

    }
  ]
}
EOH
        # destination = "local/conf/application.conf"
        destination = "local/application.conf"
      }

      service {
        # In pre-2023-03 versions this was set to [openmetrics] which was how Prometheus picked
        # it up - we have bundled prometheus with this collector, and need to NOT have the main
        # Prometheus pick these jobs up automatically.
        name = "kafka-lag-exporter-${var.kafka.clustername}"
        port = "port_lag_exporter"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-kafka-lag-exporter-${var.kafka.clustername}.rule=Host(`collector-kafka-lag-exporter-${var.kafka.clustername}.${var.kafka.domain}`)",
          "traefik.http.routers.collector-kafka-lag-exporter-${var.kafka.clustername}.tls=false",
          "traefik.http.routers.collector-kafka-lag-exporter-${var.kafka.clustername}.entrypoints=http",
        ]

        check {
          type = "tcp"
          port = "port_lag_exporter"
          interval = "20s"
          timeout = "10s"
        }
      }

      resources {
        cpu = 400
        memory = 700
      }
    }


    # TASK -- prometheus = = = = = = = = = = = = = = = = = = = = = = = =

    task "task-prometheus" {
      driver = "docker"

      config {
        image = "https://docker.io/prom/prometheus:v2.43.0"

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Kafka Lag Exporter ${var.kafka.clustername}",

          "--storage.tsdb.retention.time=48h",
          # "--storage.tsdb.retention.size=256MB",

          # We *will* flip this to agent-mode once we are sending to Mimir.
          # "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.${var.kafka.domain}/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},env=${var.kafka.clustername},task=${NOMAD_TASK_NAME}"
          }
        }

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        network_mode = "host"
      }

      resources {
       cpu    = 500
        memory = 3000
      }

      service {
        name = "collector-kafka-lag-prometheus-${var.kafka.clustername}"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-kafka-lag-prometheus-${var.kafka.clustername}.rule=Host(`collector-kafka-lag-prometheus-${var.kafka.clustername}.${var.kafka.domain}`)",
          "traefik.http.routers.collector-kafka-lag-prometheus-${var.kafka.clustername}.tls=false",
          "traefik.http.routers.collector-kafka-lag-prometheus-${var.kafka.clustername}.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}

  scrape_interval: 1m

  # This creates a log of *all* queries, and will show up in /metrics as 
  #     prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log



scrape_configs:

  # Self - our own prometheus metrics
  - job_name: 'prometheus_lag_exporter_${var.kafka.clustername}'
    metrics_path: /metrics
    static_configs:
      # - targets: [ "collector-kafka-lag-prometheus-${var.kafka.clustername}:{{ env "NOMAD_PORT_port_prometheus" }}" ]
      - targets: [ "collector-kafka-lag-prometheus-${var.kafka.clustername}.${var.kafka.domain}" ]

  - job_name: 'kafka_lag_exporter_${var.kafka.clustername}'
    metrics_path: /
    static_configs:
      # - targets: [ "collector-kafka-lag-exporter-${var.kafka.clustername}:{{ env "NOMAD_PORT_port_lag_exporter" }}" ]
      - targets: [ "collector-kafka-lag-exporter-${var.kafka.clustername}.${var.kafka.domain}" ]


remote_write:
- name: mimir
  # url: "https://mimir.${var.kafka.domain}/api/v1/push"
  url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
  headers:
    X-Scope-OrgID: test
  tls_config:
    insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }
    }

  }
}

