
# Collector-Oliver

# Combined standalone prometheus + blackbox exporter for Oliver webchecks




job "collector-oliver" {
  type = "service"
  datacenters = ["dc-cir-un-test"]


  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-oliver" {
    
    # Shared persistent storage for all tasks in this collector
    # NB we don't use persistent storage, though we *could* keep the 
    # agent-mode prometheus WAL here - it might save us 2h of potential
    # data loss, but adds job complexity (new volume registration).
#    volume "vol_collector_oliver"  {
#      type = "host"
#      source = "vol_collector_oliver"
#      read_only = false
#      }


    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
      port "port_blackbox" { 
        to = 9115
      }
  	}


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus" {
      driver = "docker"

#      volume_mount {
#        volume = "vol_collector_oliver"
#        destination = "/data"
#        read_only = false
#      }

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=https://collector-oliver-prometheus.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Oliver Collector",
        ]

        image = "https://docker.io/prom/prometheus:v2.41.0"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki-s3.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]

      }

      resources {
        cpu    = 256
        memory = 256
      }

      service {
        name = "collector-oliver-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oliver-prometheus.rule=Host(`collector-oliver-prometheus.obs.test.nsw.education`)",
          "traefik.http.routers.collector-oliver-prometheus.tls=false",
          "traefik.http.routers.collector-oliver-prometheus.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }



      template {
        data = <<EOH
global:
  external_labels:
  # These chance the extant queries - we can work around this later, if we want to enable this again.
  #  nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
  #  nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: "collector-oliver"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-oliver'
    static_configs:
      - targets: ['collector-oliver-prometheus.obs.test.nsw.education:{{ env "NOMAD_PORT_port_prometheus" }}']

  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
  - job_name: 'webcheck_oliver'
    metrics_path: /probe
    # No need for proxy HERE as we're hitting blackbox...obs.nsw.education (internal)
    # proxy_url: "http://proxy.det.nsw.edu.au:80"
    params:
      module: ["webcheck_oliver"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 1h
      files: 
      - "/local/prometheus-configuration/test/blackbox/blackbox_webcheck_oliver.yml"
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: collector-oliver-blackbox.obs.test.nsw.education

remote_write:
  - name: mimir
    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: test
    tls_config:
      insecure_skip_verify: true


EOH
        destination = "local/prometheus.yaml"
      }
    }


    # TASK blackbox for oliver   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-blackbox" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        # 2023-03-29 - removed as part of consul / roundrobin resolution
        # dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--log.level",      "debug"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          

      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu    = 200
        memory = 256
      }


      service {
        name = "collector-oliver-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oliver-blackbox.rule=Host(`collector-oliver-blackbox.obs.test.nsw.education`)",
          "traefik.http.routers.collector-oliver-blackbox.tls=false",
          "traefik.http.routers.collector-oliver-blackbox.entrypoints=http",
        ]

        meta {
          cir_app_id = "obs"
          env = "dev"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

      }


      template {
        data = <<EOH
modules:
  http_with_proxy:
    prober: http
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      fail_if_body_not_matches_regexp:
        - "1 results loaded."
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # = = = = = = = = = = = = = = = = = = = = = = = =
  # Web checks - bespoke, for Oliver School Library System (SLS) 
  # >>> requires a 200 and a string 'results found' on the target page.

  webcheck_oliver:
    prober: http
    timeout: 20s
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      valid_status_codes: [200]
      fail_if_not_ssl: true
      fail_if_body_not_matches_regexp:
        - results found
      preferred_ip_protocol: "ip4" 

EOH
        destination = "local/config.yml"
      }


    }

  }

}


