# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "otel/opentelemetry-collector-contrib:latest"
}

job "collector-opentelemetry" {
  datacenters = ["dc-cir-un-test"]
  type        = "service"

  group "otel-collector" {

    count = 1
  
  update {
    max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true #if job does not go healthy nomad will revert to previous version
    healthy_deadline = "5m" #Time allowed for job to report as healthy
    min_healthy_time = "5s" #Time to wait before checking job health
  }

    network {

      port "healthcheck" {
        to = 13133
      }
      port "jaeger-grpc" {
        to = 14250
      }
      port "jaeger-thrift-http" {
        to = 14268
      }
      port "metrics" {
        to = 8888
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "zipkin" {
        to = 9411
      }
      port "zpages" {
        to = 55679
      }
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http,https",
        "traefik.http.routers.otel.rule=Host(`otel.obs.test.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

    service {
      name     = "jaeger"
      port     = "jaeger-grpc"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger.rule=Host(`otel-jaeger.obs.test.nsw.education`)",
        "traefik.http.routers.otel-jaeger.tls=false"
      ]
    }

    service {
      name     = "jaeger-thrift"
      port     = "jaeger-thrift-http"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger-thrift.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger-thrift.rule=Host(`otel-jaeger-thrift.obs.test.nsw.education`)",
        "traefik.http.routers.otel-jaeger-thrift.tls=false"
      ]
    }

      service {
        tags = [
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.test.nsw.education`)",
          "traefik.http.routers.otel-collector-http.entrypoints=http",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
        port = "otlphttp"
      }

    task "otel-collector" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        ports = [
        "otlphttp",
        "zipkin",
        "zpages",
        "healthcheck",
        "jaeger-grpc",
        "jaeger-thrift-http",
        "metrics",
        "otlp"
        ]
      }

      resources {
        cpu    = 2000
        memory = 4092
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      grpc:
      http:
  jaeger:
    protocols:
      thrift_compact:
      thrift_binary:
      thrift_http:
  zipkin:
  opencensus:

  hostmetrics:
    scrapers:
      cpu:
      disk:
      filesystem:
      load:
      memory:
      network:
      process:
      processes:
      paging:

#### Collect own metrics ####
  prometheus:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 60s
        static_configs:
        - targets: ['0.0.0.0:8888']
  filelog:
    include: [/var/log/*.log]

processors:
  resourcedetection/system:
    # Modify the list of detectors to match the cloud environment
    detectors: [env, system, gcp, ec2, azure]
    timeout: 2s
    override: false

  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test

  attributes/logs:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.attribute.labels
        value: log_file_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

  probabilistic_sampler:
    sampling_percentage: 15

extensions:
### basic auth to grafanacloud OTLP gateway ###
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
  pprof:
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: error

### NewRelic ###
  otlp/newrelic:
    headers:
     "api-key": 52f01b42d596b903330e1e9f21845466FFFFNRAL
    endpoint: https://otlp.nr-data.net:4317

### GrafanaCloud has a simple gateway ##
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

### mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers ###
  otlphttp/onpremmimir:
    endpoint: "https://mimir-distributor.obs.test.nsw.education/otlp"
    headers:
      X-Scope-ORGID: test

### loki onprem ###
  loki/onpremloki:
    endpoint: "https://loki.obs.test.nsw.education/loki/api/v1/push"

### tempo on-prem for jaeger traces ###
  jaeger_thrift/onpremtempo:
    endpoint: http://tempo-jaeger.obs.test.nsw.education/api/traces
    tls:
      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    traces:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [probabilistic_sampler,resourcedetection/system,attributes/env,memory_limiter,batch]
      exporters: [jaeger_thrift/onpremtempo,otlphttp/grafanacloud,otlp/newrelic]

    traces/local:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [resourcedetection/system,attributes/env,memory_limiter,batch]
      exporters: [jaeger_thrift/onpremtempo,otlphttp/grafanacloud,otlp/newrelic]

    metrics:
      receivers: [otlp,prometheus]
      processors: [resourcedetection/system,attributes/env,batch]
      exporters: [otlphttp/onpremmimir,otlphttp/grafanacloud,otlp/newrelic]

    logs:
      receivers: [filelog,otlp]
      processors: [resourcedetection/system,attributes/env,attributes/logs]
      exporters: [logging,loki/onpremloki,otlphttp/grafanacloud,otlp/newrelic]

  telemetry:
    logs:
      level: error
      initial_fields:
        service: obscol-test
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}