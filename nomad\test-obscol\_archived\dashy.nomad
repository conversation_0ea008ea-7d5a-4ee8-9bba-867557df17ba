
// dashy - quick-access dashboard - test obscol

// Refer:  https://dashy.to/docs/quick-start

// Prod obscol is primary - test-obscol uses *THE SAME* config (assets/dashy-config.yml)

job "dashy"  {
  datacenters = ["dc-cir-un-test"]
  type = "service"

// Constraint added as a temporary fix to the coexistance issues across the test boxes
  // constraint {
  //   attribute = "${attr.unique.hostname}"
  //   operator = "regexp"
  //   value = "tl0992obscol0[5]"
  // }

  group "dashy" {
    network {
      port "port_http" {
        to = 80
      }
    }

    #volume "vol_dashy"  {
    #  type = "host"
    #  source = "vol_dashy"
    #  read_only = false
    #}

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    task "dashy" {
      driver = "docker"

      #volume_mount {
      #  volume = "vol_dashy"
      #  destination = "/dashy"
      #  read_only = false
      #}

      config {
        image = "lissy93/dashy:latest"

        hostname = "dashy"

        # dns_servers = ["************"]
        # 2023-03-29 - removed as part of consul / roundrobin resolution

        ports = ["port_http"]

        args  = [ ]
        volumes = [
#          "/dashy/conf.yml:/app/public/conf.yml",
           "local/dashy-config.yml:/app/public/conf.yml",
#          "local/dashy/public:/app/public",
#          "local/main.py:/main.py",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

      }

      template {
        # NOTE we use the same configuration for prod and test - test is basically a fallback option, and may be removed
        data = file("../prod-obscol/assets/dashy-config.yml")
        destination = "local/dashy-config.yml"
      }

      resources {
        cpu = 200
        memory = 1500
      }

      service {
        name = "dashy"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.dash.entrypoints=https",
          "traefik.http.routers.dash.rule=Host(`dash.obs.test.nsw.education`)"
        ]

        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }

      }

    }
  }
}
