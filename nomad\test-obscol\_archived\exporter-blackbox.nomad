
# Prometheus blackbox exporter for ObsCol TEST env

# Blackbox exporter can be bounced through like this:
#   curl -XGET "http://blackbox-ping-test.service.dc-cir-un-prod.collectors.obs.nsw.education:9115/probe?target=************&module=icmp"
#
# This uses the icmp module, and hits that target IP address, asking blackbox (exposed
# on port 9115) to peform the ping test.


job "exporter-blackbox" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "exporter-blackbox" {
    network {
      port "port-exporter-blackbox" {
        to = 9115
      }
    }

    task "exporter-blackbox" {
      driver = "docker"

      config {
        image = "prom/blackbox-exporter:v0.22.0"

        ports = ["port-exporter-blackbox"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          

        # 2023-03-29 - removed as part of consul / roundrobin resolution
        # dns_servers = [ "************" ]

        args = [
          "--config.file",
          "local/config.yml",
          "--log.level",
          "debug"
        ]

        cap_add = ["net_raw"]

      }

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,**********/16"
      }


      template {
        data = <<EOH

modules:
  http_with_proxy:
    prober: http
    http:
      proxy_url: "http://proxy.det.nsw.edu.au:80"
      fail_if_body_not_matches_regexp:
        - "1 results loaded."
  http_2xx:
    prober: http
  http_post_2xx:
    prober: http
    http:
      method: POST
  tcp_connect:
    prober: tcp
  pop3s_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^+OK"
      tls: true
      tls_config:
        insecure_skip_verify: false
  ssh_banner:
    prober: tcp
    tcp:
      query_response:
      - expect: "^SSH-2.0-"
      - send: "SSH-2.0-blackbox-ssh-check"
  irc_banner:
    prober: tcp
    tcp:
      query_response:
      - send: "NICK prober"
      - send: "USER prober prober prober :prober"
      - expect: "PING :([^ ]+)"
        send: "PONG ${1}"
      - expect: "^:[^ ]+ 001"
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

  # = = = = = = = = = = = = = = = = = = = = = = = =
  # Web checks for generic and bespoke applications

  # 2023-01-23 jedd - this is being replaced by 'collector-oliver' - removing to ensure it's not in use.
  # Oliver School Library System (SLS) requires a 200 and a string 'results found' on the target page.
#  webcheck_oliver:
#    prober: http
#    # 2022-08-29 jedd - bump up slightly from default (15s?) to see if this resolves flapping issue
#    timeout: 20s
#    http:
#      proxy_url: "http://proxy.det.nsw.edu.au:80"
#      valid_status_codes: [200]
#      fail_if_not_ssl: true
#      fail_if_body_not_matches_regexp:
#        - results found
#      preferred_ip_protocol: "ip4" 


EOH
        destination = "local/config.yml"
      }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "exporter-blackbox"
        port = "port-exporter-blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-blackbox.rule=Host(`exporter-blackbox.obs.test.nsw.education`)",
          "traefik.http.routers.exporter-blackbox.tls=false",
          "traefik.http.routers.exporter-blackbox.entrypoints=http",
        ]

        meta {
          cir_app_id = "obs"
          env = "dev"
        }


      }


      }
    }
  }
