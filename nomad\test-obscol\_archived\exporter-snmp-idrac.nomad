
# Prometheus SNMP exporter for ObsCol TEST env

# We use the assets from repo: prometheus-configuration / snmp / 


job "exporter-snmp-idrac" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  group "exporter-snmp-idrac" {
    network {
      port "port-exporter-idrac" {
        to = 9116 //default
      }
    }

    task "exporter-snmp-idrac" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = ["port-exporter-idrac"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          

        # 2023-03-29 - removed as part of consul / roundrobin resolution
        # dns_servers = [ "192.168.31.1" ]

        volumes = [
          "/opt/sharednfs/prometheus-configuration/snmp/snmp-dell-idrac.yml:/etc/snmp_exporter/snmp.yml"
        ]

      }

      service {
        name = "exporter-idrac"
        port = "port-exporter-idrac"
//
//        check {
//          type = "http"
//          port = "port-exporter-snmp"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-idrac.rule=Host(`exporter-idrac.obs.test.nsw.education`)",
          "traefik.http.routers.exporter-idrac.tls=false",
          "traefik.http.routers.exporter-idrac.entrypoints=http",
        ]

      }



    }
  }
}
