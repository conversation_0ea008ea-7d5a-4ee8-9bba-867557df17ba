
# Prometheus SNMP exporter for GovDC PDU's (ObsCol TEST)

# This job is NON FUNCTIONAL - as only PROD has routable access to the PDU subnets.

# Primarily used for confirming multiple exporters and port allocation (traefik).

# We use the assets from repo: prometheus-configuration / snmp / 

job "exporter-snmp-pdu" {

  type = "service"

  datacenters = ["dc-cir-un-test"]

  group "exporter-snmp-pdu" {
    network {
      port "port_exporter_snmp_pdu" {
        # static = 9117   // explicit, unique per exporter
        # to = 9116         // rely on traefik
        to = 9117         // rely on traefik
      }
    }

    task "exporter-snmp-pdu" {
      driver = "docker"

      config {
        image = "docker.io/prom/snmp-exporter:latest"

        ports = ["port_exporter_snmp_pdu"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          

        # 2023-03-29 - removed as part of consul / roundrobin resolution
        # dns_servers = [ "************" ]

        volumes = [
          "/opt/sharednfs/prometheus-configuration/snmp/snmp-enlogic-pdu.yml:/etc/snmp_exporter/snmp.yml"
        ]

        args = [
          "--web.listen-address=:${NOMAD_PORT_port_exporter_snmp_pdu}",

          "--config.file=/etc/snmp_exporter/snmp.yml"
        ]

      }

      service {
        name = "exporter-snmp-pdu"
        port = "port_exporter_snmp_pdu"
//
//        check {
//          type = "http"
//          port = "port_exporter_snmp_pdu"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-snmp-pdu.rule=Host(`exporter-snmp-pdu.obs.test.nsw.education`)",
          "traefik.http.routers.exporter-snmp-pdu.tls=false",
          "traefik.http.routers.exporter-snmp-pdu.entrypoints=http",
        ]

      }


    }
  }
}
