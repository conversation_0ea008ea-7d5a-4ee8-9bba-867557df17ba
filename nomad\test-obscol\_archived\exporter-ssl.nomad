job "exporter-ssl" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  group "exporter-ssl" {
    network {
      port "exporter-ssl-port" {
        to = 9219 // The default is 9219 for the ssl_exporter
      }
    }

    task "ssl-exporter" {
      driver = "docker"

      config {
        ports = ["exporter-ssl-port"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }            
        image = "https://docker.io/ribbybibby/ssl-exporter:latest"

        # dns_servers = [ "192.168.31.1" ]

        volumes = [
          "local/exporter_ssl.yaml:/etc/ssl_exporter.yaml"
        ]
      }

      service {
        name = "exporter-ssl"
        port = "exporter-ssl-port"
//
//        check {
//          type = "http"
//          port = "http"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }
      }

      template {
        data = <<EOH
modules:
  prober: https
  timeout: 10s
  https:
    proxy_url: http://proxy.det.nsw.edu.au:80

EOH
        destination = "local/exporter_ssl.yaml"
      }
    }
  }
}
