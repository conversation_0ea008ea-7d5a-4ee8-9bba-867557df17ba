job "grafana" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  group "grafana" {
    count = 1

    network {
      port "http" {}
    }

    volume "grafana" {
      type = "host"
      source = "grafana"
      read_only = false
    }

    task "grafana" {
      driver = "docker"

      config {
        image = "https://docker.io/grafana/grafana:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }        

        # dns_servers = ["************"]

        ports = ["http"]

        volumes = []
      }

      env {
        GF_LOG_LEVEL          = "DEBUG"
        GF_LOG_MODE           = "console"
        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_http}"

        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"

        GF_INSTALL_PLUGINS = ""
        GF_PATHS_PROVISIONING = "/local/grafana/provisioning"
        # GF_PATHS_DATA = "/data/data"
        GF_LIVE_ALLOWED_ORIGINS = "http://*"
        GF_FEATURE_TOGGLES_ENABLE = "ngalert"
      }

      service {
        name = "grafana"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/api/health"
          interval = "5s"
          timeout = "2s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-ssl.rule=Host(`grafana.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-ssl.tls=true",
          #"traefik.http.routers.grafana-ssl.tls.domains[0].main=obscol.obs.test.nsw.education",
          #"traefik.http.routers.grafana-ssl.tls.domains[0].sans=*.obs.test.nsw.education",
        ]
      }

      volume_mount {
        volume = "grafana"
        destination = "/data"
        read_only = false
      }

      resources {
        cpu    = 100
        memory = 100
      }

      template {
        data        = <<EOTC
apiVersion: 1
datasources:

  - name: mimir
    type: prometheus
    access: proxy
    # It is necessary to use /api/prom legacy endpoint for mimir Managed Alerts in Grafana 8.1
    url: https://mimir.obs.test.nsw.education/prometheus
    jsonData:
        httpHeaderName1: 'X-Scope-OrgID'
    secureJsonData:
        httpHeaderValue1: 'test'

  - name: Tempo
    type: tempo
    access: proxy
    url: https://tempo.obs.test.nsw.education
    uid: tempo

  - name: Loki_Tempo
    type: loki
    access: proxy
    url: https://loki.obs.test.nsw.education
    jsonData:
      derivedFields:
        - datasourceUid: tempo
          matcherRegex: (?:traceID|trace_id)=(\w+)
          name: TraceID
          url: $$${__value.raw}
EOTC
        destination = "/local/grafana/provisioning/datasources/ds.yaml"
      }
    }
  }
}
