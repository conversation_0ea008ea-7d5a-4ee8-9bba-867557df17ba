// test - obscol - nomad - metricbeat

job "metricbeat" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  group "metricbeat" {

    network {
      port "prometheus_remote_write" {
        static = 9201
      }
    }

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.1"
        dns_servers = ["************"]
        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
      }

      service {
        name = "prometheus-remote-write"
        port = "prometheus_remote_write"
      }

      env {
        ELASTICSEARCH_HOSTS = "tl0992obses01.nsw.education"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '${ELASTICSEARCH_HOSTS}'

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"
      }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}
