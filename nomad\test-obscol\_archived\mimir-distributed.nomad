##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

// Each component or module is invoked using its -target parameter ie -target=compactor
// server module is loaded with each module by default if it is needed

// Required Group Modules and the components they load by default
// compactor + server, memberlist-kv modules, sanity-checker, activity-tracker
// distributor + server, activity-tracker, memberlist-kv, ring modules, sanity-checker 
// ingester + activity-tracker, server, sanity-check, memberlist-kv, ingester-service
// querier + server, activity-tracker, memberlist-kv, store-queryable, ring
// querier-frontend + sanity-check, activity-tracker, server, query-frontend
// store-gateway + sanity-check, activity-tracker, server, memberlist-kv, store-gateway

// TODO
// Add entrypoints to Traefik job
// S3 config for the store-gateway and ingester
// Correct all network port values


          // "-blocks-storage.backend=s3",
          // "-blocks-storage.s3.endpoint=",
          // "-blocks-storage.s3.region=",
          // "-blocks-storage.s3.bucket-name=nswdoe-obs-mimir-blocks-storage-dev",
          // "-blocks-storage.s3.secret-access-key=",
          // "-blocks-storage.s3.access-key-id="

job "mimir-distributed" {
  datacenters = ["dc-cir-un-test"]

group "server" {
        network {
            port "grpc" {
              to = 9095
            }
            port "http" {
              to = 8080
            }
        }
    
    service {
        name = "server"
        port = "http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.mimir.rule=Host(`mimir.obs.test.nsw.education`)",
          "traefik.http.routers.mimir.tls=false",
          "traefik.http.routers.mimir.entrypoints=http,https,mimir",
        ]
    }

    task "server" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=server",
          "-server.http-listen-port=8080",
          "-server.grpc-listen-port=9095"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }         
    }
  }

  group "query-frontend" {
        network {
            port "grpc" {
              to = 9095
            }
            port "http" {
              to = 8080
            }
        }

    task "query-frontend" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=query-frontend"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }         
    }
  }
  group "compactor" {
        network {
            port "grpc" {}
            port "http" {}
        }
    
    service {
        name = "compactor"
        port = "grpc"
        tags = ["grpc","http"]
    }

    task "compactor" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=compactor",
          "-compactor.ring.store=consul",
          "-compactor.ring.prefix=collectors/",
          "-compactor.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
          "-compactor.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"          
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }         
    }
  }
  group "distributor" {
    count = 3
        network {
            port "grpc" {}
            port "http" {}
        }
    
    service {
        name = "distributor"
        port = "grpc"
        tags = ["grpc","http"]
    }

    task "distributor" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=distributor",
          "-server.grpc-listen-port=${NOMAD_PORT_http}",
          "-distributor.ring.store=consul",
          "-distributor.ring.prefix=collectors/",
          "-distributor.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
          "-distributor.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }       
    }
  }
  group "ingester" {
        network {
            port "grpc" {}
            port "http" {}
        }
    
    service {
        name = "ingester"
        port = "grpc"
        tags = ["grpc","http"]
    }

    task "ingester" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=ingester",
          "-server.grpc-listen-port=${NOMAD_PORT_http}",
          "-ingester.ring.store=consul",
          "-ingester.ring.prefix=collectors/",
          "-ingester.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
          "-ingester.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500",
          "-blocks-storage.filesystem.dir=blocks"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }         
    }
  }     
  group "store-gateway" {
        network {
            port "grpc" {}
            port "http" {}
        }
    
    service {
        name = "store-gateway"
        port = "grpc"
        tags = ["grpc","http"]
    }

    task "store-gateway" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=store-gateway",
          "-store-gateway.sharding-ring.store=consul",
          "-store-gateway.sharding-ring.prefix=collectors/",
          "-store-gateway.sharding-ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
          "-store-gateway.sharding-ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }         
    }
  }   
  group "querier" {
        network {
            port "grpc" {}
            port "http" {}
        }
    
    service {
        name = "querier"
        port = "grpc"
        tags = ["grpc","http"]
    }

    task "querier" {
      driver = "docker"

      config {
        image = "grafana/mimir:latest"
        dns_servers = ["************"]
        args = [
          "-target=querier"
        ]
      }   
      resources {
        cpu    = 500
        memory = 256
      }
    }    
  }  
}