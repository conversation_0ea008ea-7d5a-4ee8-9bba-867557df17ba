##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

// Each component or module is invoked using its -target parameter ie -target=compactor
// server module is loaded with each module by default if it is needed

// Required Group Modules and the components they load by default
// compactor + server, memberlist-kv modules, sanity-checker, activity-tracker
// distributor + server, activity-tracker, memberlist-kv, ring modules, sanity-checker 
// ingester + activity-tracker, server, sanity-check, memberlist-kv, ingester-service
// querier + server, activity-tracker, memberlist-kv, store-queryable, ring
// querier-frontend + sanity-check, activity-tracker, server, query-frontend
// store-gateway + sanity-check, activity-tracker, server, memberlist-kv, store-gateway

variables {
  versions = {
    mimir = "2.4.0"
  }
}



job "mimir-s3-microservices" {
  datacenters = ["dc-cir-un-test"]

  group "mimir-microservices" {
        network {
            port "server_http" {}
            port "server_grpc" {}
            port "compactor"{}
            port "compactor_grpc"{}
            port "distributor" {}
            port "distributor_grpc" {}
            port "ingester"{}
            port "ingester_grpc"{}
            port "store_gateway"{}
            port "store_gateway_grpc"{}
            port "query_frontend"{}
            port "query_frontend_grpc"{}
            //port "querier"{}
            //port "querier_grpc"{}
        }

        volume "vol_mimir"  {
            type = "host"
            source = "vol_mimir"
            read_only = false
        }    
        
    task "mimir-query-frontend" {
      driver = "docker"
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir"
        ports = ["query_frontend","query_frontend_grpc"]     
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=query-frontend",
            "-config.file=local/mimir.yaml"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }
      template {
        data = <<EOH
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
compactor:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_compactor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_compactor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
distributor:
    ring:
        instance_addr: {{env "NOMAD_IP_distributor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_distributor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
frontend:
    address: {{env "NOMAD_IP_frontend"}}
    port: {{env "NOMAD_PORT_frontend"}}
frontend_worker:
    frontend_address: "{{env "NOMAD_IP_frontend"}}:{{env "NOMAD_PORT_frontend"}}"
    id: {{env "node.unique.name"}}
ingester:
    ring:
        instance_addr: {{env "NOMAD_IP_ingester"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_ingester"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
querier:
    iterators: true
server:
    grpc_listen_port: {{env "NOMAD_PORT_query_frontend_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_query_frontend"}}
    log_level: debug
store_gateway:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_store_gateway"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_store_gateway"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
EOH
        destination = "local/mimir.yaml"
    }     
    }

    task "mimir-compactor" {
      driver = "docker"
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:latest"
        ports = ["compactor","compactor_grpc"]     
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=compactor",
            "-config.file=/local/mimir.yaml"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }
      template {
        data = <<EOH
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
compactor:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_compactor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_compactor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
distributor:
    ring:
        instance_addr: {{env "NOMAD_IP_distributor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_distributor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
frontend:
    address: {{env "NOMAD_IP_frontend"}}
    port: {{env "NOMAD_PORT_frontend"}}
frontend_worker:
    frontend_address: "{{env "NOMAD_IP_frontend"}}:{{env "NOMAD_PORT_frontend"}}"
    id: {{env "node.unique.name"}}
ingester:
    ring:
        instance_addr: {{env "NOMAD_IP_ingester"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_ingester"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
querier:
    iterators: true
server:
    grpc_listen_port: {{env "NOMAD_PORT_compactor_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_compactor"}}
    log_level: debug
store_gateway:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_store_gateway"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_store_gateway"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
EOH
        destination = "local/mimir.yaml"
    }
  }

    task "mimir-querier" {
      driver = "docker"
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:latest"
        ports = ["querier","querier_grpc"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=querier",
            "-config.file=local/mimir.yaml"
        ]
      }   
      resources {
        cpu    = 500
        memory = 256
      }
      template {
        data = <<EOH
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
compactor:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_compactor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_compactor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
distributor:
    ring:
        instance_addr: {{env "NOMAD_IP_distributor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_distributor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
frontend:
    address: {{env "NOMAD_IP_frontend"}}
    port: {{env "NOMAD_PORT_frontend"}}
frontend_worker:
    frontend_address: "{{env "NOMAD_IP_frontend"}}:{{env "NOMAD_PORT_frontend"}}"
    id: {{env "node.unique.name"}}
ingester:
    ring:
        instance_addr: {{env "NOMAD_IP_ingester"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_ingester"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
querier:
    iterators: true
server:
    grpc_listen_port: {{env "NOMAD_PORT_querier_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_querier"}}
    log_level: debug
store_gateway:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_store_gateway"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_store_gateway"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
EOH
        destination = "local/mimir.yaml"
    }
    }    

    task "mimir-distributor" {
      driver = "docker"
      service {
            name = "distributor"
            port = "distributor"
            address_mode = "host"
            tags = [
                "traefik.enable=true",
                "traefik.http.routers.mimir.rule=Host(`mimir.obs.test.nsw.education`)",
                "traefik.http.routers.mimir.tls=false",
                "traefik.http.routers.mimir.entrypoints=http,https,mimir",
            ]
            check {
                name     = "Mimir healthcheck"
                port     = "distributor"
                type     = "http"
                path     = "/ready"
                interval = "20s"
                timeout  = "5s"
                check_restart {
                    limit           = 3
                    grace           = "60s"
                    ignore_warnings = false
                }
            }            
        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }        
        } 
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:latest"
        ports = ["distributor","distributor_grpc"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=distributor",
            "-config.file=local/mimir.yaml"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }
      template {
        data = <<EOH
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
compactor:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_compactor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_compactor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
distributor:
    ring:
        instance_addr: {{env "NOMAD_IP_distributor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_distributor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
frontend:
    address: {{env "NOMAD_IP_frontend"}}
    port: {{env "NOMAD_PORT_frontend"}}
frontend_worker:
    frontend_address: "{{env "NOMAD_IP_frontend"}}:{{env "NOMAD_PORT_frontend"}}"
    id: {{env "node.unique.name"}}
ingester:
    ring:
        instance_addr: {{env "NOMAD_IP_ingester"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_ingester"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
querier:
    iterators: true
server:
    grpc_listen_port: {{env "NOMAD_PORT_distributor_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_distributor"}}
    log_level: debug
store_gateway:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_store_gateway"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_store_gateway"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
EOH
        destination = "local/mimir.yaml"
    }     
    }
  


        task "mimir-ingester" {
        driver = "docker"
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
        config {
        image = "grafana/mimir:latest"
        ports = ["ingester","ingester_grpc"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=ingester",
            "-config.file=local/mimir-ingester.yaml"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }
template {
        data = <<EOH
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
compactor:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_compactor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_compactor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
distributor:
    ring:
        instance_addr: {{env "NOMAD_IP_distributor"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_distributor"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
frontend:
    address: {{env "NOMAD_IP_frontend"}}
    port: {{env "NOMAD_PORT_frontend"}}
frontend_worker:
    frontend_address: "{{env "NOMAD_IP_frontend"}}:{{env "NOMAD_PORT_frontend"}}"
    id: {{env "node.unique.name"}}
server:
    grpc_listen_port: {{env "NOMAD_PORT_ingester_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_ingester"}}
    log_level: debug
ingester:
    ring:
        instance_addr: {{env "NOMAD_IP_ingester"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_ingester"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1

EOH
        destination = "local/mimir-ingester.yaml"
    }        
    }

    task "mimir-store-gateway" {
      driver = "docker"
        env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:latest"
        ports = ["store_gateway","store_gateway_grpc"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
        #dns_servers = ["************"]
        args = [
            "-target=store-gateway", 
            "-config.file=local/store-gateway.yaml"
        ]
      }
      resources {
        cpu    = 500
        memory = 256
      }
      template {
        data = <<EOH
server:
    grpc_listen_port: {{env "NOMAD_PORT_store_gateway_grpc"}}
    http_listen_port: {{env "NOMAD_PORT_store_gateway"}}
    log_level: debug
store_gateway:
    sharding_ring:
        instance_addr: {{env "NOMAD_IP_store_gateway"}}
        instance_id: {{env "node.unique.name"}}
        instance_port: {{env "NOMAD_PORT_store_gateway"}}
        kvstore:
            consul:
                acl_token: a5515cc3-0ae7-ff5d-84f5-676b8088eabf
                host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
            store: consul
        replication_factor: 1
blocks_storage:
    backend: s3
    bucket_store:
        sync_dir: /mimir/tsdb-sync/
    s3:
        access_key_id: ********************
        bucket_name: nswdoe-obs-mimir-blocks-storage-dev
        endpoint: s3.ap-southeast-2.amazonaws.com
        region: ap-southeast-2
        secret_access_key: JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp
    tsdb:
        dir: /mimir/tsdb/
        flush_blocks_on_shutdown: true
        retention_period: 48h0m0s
EOH
        destination = "local/store-gateway.yaml"
    }      

}

}
}