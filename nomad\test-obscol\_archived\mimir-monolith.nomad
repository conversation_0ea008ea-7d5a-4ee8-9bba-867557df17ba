##  https://grafana.com/docs/mimir/latest/operators-guide/configuring/reference-configuration-parameters/

job "mimir-monolith" {
  datacenters = ["dc-cir-un-test"]

    group "mimir" {
        count = 3
        network {
            port "grpc" {
                to = 10901
                }
            port "http" {
                to = 9009
                }
        }
    
    service {
        name = "mimir"
        port = "grpc"
        tags = ["grpc"]
    }
    service {
        name = "mimir"
        port = "http"
        tags = ["http"]
    }    
    volume "mimir"  {
      type = "host"
      source = "mimir"
      read_only = false
    }    

    task "mimir" {
      driver = "docker"

      volume_mount {
        volume = "mimir"
        destination = "/mnt/mimir"
        read_only = false
      }      

      template {
        change_mode = "noop"
        destination = "local/server.yml"

        data = <<EOH
server:
  http_listen_port: 9009

  # Configure the server to allow messages up to 100MB.
  grpc_server_max_recv_msg_size: 104857600
  grpc_server_max_send_msg_size: 104857600
  grpc_server_max_concurrent_streams: 1000

distributor:
  ring:
    kvstore:
      store: consul
  pool:
    health_check_ingesters: true

consul:
  host: consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500
  acl_token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'

ingester:
  ring:
    # The address to advertise for this ingester.  Will be autodiscovered by
    # looking up address on eth0 or en0; can be specified if this fails.
    # address: 127.0.0.1

    # We want to start immediately and flush on shutdown.
    join_after: 0
    min_ready_duration: 0s
    final_sleep: 0s
    num_tokens: 512

    # Use an in memory ring store, so we don't need to launch a Consul.
    kvstore:
      store: consul
    replication_factor: 1

blocks_storage:
  tsdb:
    dir: /mnt/mimir/tsdb

  bucket_store:
    sync_dir: /mnt/mimir/tsdb-sync

  # You can choose between local storage and Amazon S3, Google GCS and Azure storage. Each option requires additional configuration
  # as shown below. All options can be configured via flags as well which might be handy for secret inputs. backend: s3 # s3, gcs, azure or filesystem are valid options
#  s3:
#    bucket_name: mimir
#    endpoint: s3.dualstack.us-east-1.amazonaws.com
#    # Configure your S3 credentials below.
#    # secret_access_key: "TODO"
#    # access_key_id:     "TODO"
#  gcs:
#    bucket_name: mimir
#    service_account: # if empty or omitted Mimir will use your default service account as per Google's fallback logic
#  azure:
#    account_name:
#    account_key:
#    container_name:
#    endpoint_suffix:
#    max_retries: # Number of retries for recoverable errors (defaults to 20)
  filesystem:
    dir: /data/tsdb

compactor:
  data_dir: /mnt/mimir/compactor

ruler_storage:
  backend: local
  local:
    directory: /mnt/mimir/rules
EOH
      }

      config {
        image = "grafana/mimir:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                }
            }         
        volumes = [
          "local/server.yml:/etc/mimir/server.yml",
        ]
        args = [
          "-target=all",
          ]
      }
    }
  }
}