
// mimir for OCP -- a single instance monolith for Test ObsCol, to serve on:
//     mimirocp.obs.test.nsw.education
// This is primarily to test ingest rates from OpenShift OCP4
// This is expected to be in service for 1 month (2022-11).




job "mimirocp" {
  datacenters = ["dc-cir-un-test"]
  
    group "mimirocp" {
# 2022-10-14 jedd - disabling this to recover cluster / mimir
#        count = 2
        count = 1
#        update {
#            max_parallel     = 1
#            canary           = 1
#            min_healthy_time = "30s"
#            healthy_deadline = "5m"
#            auto_revert      = true
#            auto_promote     = false
#        }        

# 2022-10-14 jedd - disabling this to recover cluster / mimir
#        constraint {
#            operator  = "distinct_hosts"
#            value     = "true"
#        }        

        network {
            port "grpc" {
            }
            port "http" {
            }
            #port "port_alertmanager" {
            #}
        }
     
    volume "vol_mimirocp"  {
      type = "host"
      source = "vol_mimirocp"
      read_only = false
    }    

    task "mimirocp" {
      driver = "docker"
        service {
            name = "mimirocp"
            port = "http"
            address_mode = "host"
            tags = [
                "traefik.enable=true",
                "traefik.http.routers.mimirocp.rule=Host(`mimirocp.obs.test.nsw.education`)",
                "traefik.http.routers.mimirocp.tls=false",
                "traefik.http.routers.mimirocp.entrypoints=http,https,mimirocp",
            ]
            check {
                name     = "Mimir healthcheck"
                port     = "http"
                type     = "http"
                path     = "/ready"
                interval = "240s"
                timeout  = "60s"
                check_restart {
                    limit           = 3
                    grace           = "60s"
                    ignore_warnings = false
                }
            }            
        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }        
        } 
      volume_mount {
        volume = "vol_mimirocp"
        destination = "/mimir"
        read_only = false
      }

      resources {
        cpu = 2000
        memory = 5500
        }                 
        env {
        CONSUL_HTTP_ADDR = "http://consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,192.168.0.0/16,172.16.0.0/16"
        }
      config {
        # 2022-09-23 jedd - explicitly specify penultimate version, as 2.3.x (2022-09 release) fails with our current nomad config
        # image = "grafana/mimir:latest"
        image = "grafana/mimir:2.2.0"

        dns_servers = ["192.168.31.1"]

        # ports = ["http","grpc","port_alertmanager"]
        ports = ["http","grpc"]

        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }         

        args = [
            # "-target=all",     ## Note that this does NOT include 'alertmanager'.
            #
            # Full list of modules we can pull in with -target= as follows:
            # all,       alertmanager,        compactor,  distributor,  flusher,   
            # ingester,  overrides-exporter,  purger,     querier,      query-frontend,   
            # query-scheduler,                ruler,      store-gateway,   
            #
            # Note that we do NOT want:flusher  , overrides-exporter , query-scheduler
            # For MimirOCP we are disabling alertmanager
            "-target=compactor,distributor,ingester,purger,querier,query-frontend,ruler,store-gateway",

            "-server.http-listen-port=${NOMAD_PORT_http}",
            "-server.grpc-listen-port=${NOMAD_PORT_grpc}",
            "-server.register-instrumentation=true",

            "-log.level=debug",


            # Ruler and alertmanager configuration for mimir/grafana managed
            "-ruler-storage.backend=filesystem",
            "-ruler-storage.filesystem.dir=ruler",
            "-ruler-storage.local.directory=/mimir/rules",
            #  "-ruler.alertmanager-url=http://${NOMAD_IP_http}:${NOMAD_PORT_port_alertmanager}/alertmanager",
            "-ruler.ring.instance-id=${node.unique.name}",
            "-alertmanager.sharding-ring.replication-factor=1",
            "-alertmanager.sharding-ring.instance-id=${node.unique.name}",
            "-alertmanager-storage.filesystem.dir=alertmanager",
            "-alertmanager-storage.local.path=/mnt/mimir/alertmanager",
            # "-alertmanager.web.external-url=http://${NOMAD_IP_http}:${NOMAD_PORT_port_alertmanager}/alertmanager",


            "-querier.iterators=true",
            "-query-frontend.instance-addr=${NOMAD_IP_http}",
            "-query-frontend.instance-port=${NOMAD_PORT_grpc}",
            "-querier.frontend-address=${NOMAD_IP_http}:${NOMAD_PORT_grpc}",
            "-querier.id=${node.unique.name}",

            "-compactor.ring.store=consul",
            "-compactor.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
            "-compactor.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500",
            "-compactor.ring.instance-addr=${NOMAD_IP_http}",
            "-compactor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-compactor.ring.instance-id=${node.unique.name}",

            "-distributor.ring.store=consul",
            "-distributor.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
            "-distributor.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500",
            "-distributor.ring.instance-addr=${NOMAD_IP_http}",
            "-distributor.ring.instance-port=${NOMAD_PORT_grpc}",
            "-distributor.ring.instance-id=${node.unique.name}",

            "-ingester.ring.store=consul",
            "-ingester.ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
            "-ingester.ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500",
            "-ingester.ring.instance-addr=${NOMAD_IP_http}",
            "-ingester.ring.instance-port=${NOMAD_PORT_grpc}",
            "-ingester.ring.instance-id=${node.unique.name}",
            "-ingester.ring.replication-factor=1",

            "-store-gateway.sharding-ring.store=consul",
            "-store-gateway.sharding-ring.consul.acl-token=a5515cc3-0ae7-ff5d-84f5-676b8088eabf",
            "-store-gateway.sharding-ring.consul.hostname=consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500",
            "-store-gateway.sharding-ring.instance-addr=${NOMAD_IP_http}",
            "-store-gateway.sharding-ring.instance-port=${NOMAD_PORT_grpc}",
            "-store-gateway.sharding-ring.instance-id=${node.unique.name}",
            "-store-gateway.sharding-ring.replication-factor=1",

            "-blocks-storage.tsdb.dir=/mimir/tsdb/",
            "-blocks-storage.bucket-store.sync-dir=/mimir/tsdb-sync/",

          
            "-blocks-storage.backend=filesystem",
            "-blocks-storage.filesystem.dir=/mimir/blocks",
            "-blocks-storage.tsdb.flush-blocks-on-shutdown=true",
            "-blocks-storage.tsdb.retention-period=48h"

            # "-blocks-storage.backend=s3",
            # "-blocks-storage.s3.endpoint=s3.ap-southeast-2.amazonaws.com",
            # "-blocks-storage.s3.region=ap-southeast-2",
            # "-blocks-storage.s3.bucket-name=nswdoe-obs-mimir-blocks-storage-dev",
            # "-blocks-storage.s3.secret-access-key=JDw46wSW8orE7DFdCQEBLMhcB1LMcEBuxJkDbvpp",
            # "-blocks-storage.s3.access-key-id=********************",
            # "-blocks-storage.tsdb.flush-blocks-on-shutdown=true",
            # "-blocks-storage.tsdb.retention-period=48h"

        ]

      }
    }
  }
}
