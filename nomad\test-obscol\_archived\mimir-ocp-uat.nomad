
// mimir-ocp - microservices version - for ObsCol TEST
// FORK exclusively for OpenShift OCP UAT load ingest testing

// Refer:  
// https://confluence.education.nsw.gov.au/display/PM/Discussion+and+evaluation+for+metrics+ingestion+and+retention
// -- for discussion on sizing (CPU & memory) based on ingest volumes.


variables {
  versions = {
    # ${var.versions.mimir}
    mimir = "2.6.0" 
  }
}

job "mimir-ocp" {

  datacenters = [
    "dc-cir-un-test"
  ]


  # COMPACTOR = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "compactor-ocp" {
    count = 1

    volume "vol_mimirocp"  {
      type = "host"
      source = "vol_mimirocp"
      read_only = false
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-compactor-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "compactor-ocp"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-compactor-ring-ocp.entrypoints=https",
         "traefik.http.routers.mimir-compactor-ring-ocp.rule=Host(`mimir-compactor-ocp.obs.test.nsw.education`) && Path(`/compactor/ring`)",
       ]      

      check {
        name            = "Mimir compactor"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-compactor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }
      volume_mount {
        volume = "vol_mimirocp"
        destination = "/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=compactor",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      
      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 1 core + 4GB RAM
        cpu        = 1200
        memory     = 7000
        memory_max = 9000
      }
    }
  }


  # RULER = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "ruler-ocp" {
    count = 1

    constraint {
      distinct_property = node.unique.name
    }

    volume "vol_mimirocp"  {
      type = "host"
      source = "vol_mimirocp"
      read_only = false
    }  

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-ruler-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "ruler-ocp"
      }
      tags = [
        "traefik.enable=true",

        "traefik.http.routers.mimir-ruler-ocp.entrypoints=https",
        "traefik.http.routers.mimir-ruler-ocp.rule=Host(`mimir-query-frontend-ocp.obs.test.nsw.education`) && PathPrefix(`/prometheus/api/v1/rules`)",

        "traefik.http.routers.mimir-ruler-ring-ocp.entrypoints=https",
        "traefik.http.routers.mimir-ruler-ring-ocp.rule=Host(`mimir-ruler-ocp.obs.test.nsw.education`) && Path(`/ruler/ring`)",
      ]   

      check {
        name            = "Mimir ruler"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-ruler" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      volume_mount {
        volume = "vol_mimirocp"
        destination = "/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=ruler",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended ?
        cpu        = 1024
        memory     = 1024
        memory_max = 4096
      }
    }
  }


  # DISTRIBUTOR  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "distributor-ocp" {
    count = 3

    constraint {
      distinct_property = node.unique.name
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-distributor-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "distributor-ocp"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-distributor-ocp.entrypoints=https",
        "traefik.http.routers.mimir-distributor-ocp.rule=Host(`mimir-distributor-ocp.obs.test.nsw.education`)",
        
        "traefik.http.routers.mimir-distributor-ring-ocp.entrypoints=https",
        "traefik.http.routers.mimir-distributor-ring-ocp.rule=Host(`mimir-distributor-ocp.obs.test.nsw.education`) && Path(`/distributor/ring`)",
    ]      

      check {
        name            = "Mimir distributor"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-distributor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=distributor",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 1 core, 1GB 
        cpu        = 1200
        memory     = 2048
        memory_max = 4096
      }
    }
  }


  # INGESTER = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "ingester-ocp" {
    count = 1

    constraint {
      distinct_property = node.unique.name
    }

    volume "vol_mimirocp"  {
      type = "host"
      source = "vol_mimirocp"
      read_only = false
    }  

    ephemeral_disk {
      size    = 4000
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-ingester-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "ingester-ocp"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-ingester-ocp.rule=Host(`mimir-ingester-ocp.obs.test.nsw.education`)",
        "traefik.http.routers.mimir-ingester-ocp.tls=false",
        "traefik.http.routers.mimir-ingester-ocp.entrypoints=http,https",
      ]

      check {
        name            = "Mimir ingester"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-ingester-ocp" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      volume_mount {
        volume = "vol_mimirocp"
        destination = "/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=ingester",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 2 cores and 5GB RAM
        cpu        = 2048
        memory     = 5000
        memory_max = 7000
      }
    }
  }


  # QUERIER  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "querier-ocp" {
    count = 2

    constraint {
      distinct_property = node.unique.name
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-querier-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "querier-ocp"
      }

      check {
        name            = "Mimir querier"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-querier-ocp" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=querier",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
          "-querier.cardinality-analysis-enabled=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 1 core and 1GB per 10 qps
        cpu        = 1200
        memory     = 1500
        memory_max = 2000
      }
    }
  }


  # QUERY SCHEDULER = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "query-scheduler-ocp" {
    count = 2

    network {
      port "http" {}
      port "grpc" {
        to     = 8096
        # static = 8096
      }
    }

    service {
      name = "mimir-query-scheduler-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "query-scheduler-ocp"
      }

      check {
        name            = "Mimir query-scheduler"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.query-scheduler-ocp.rule=Host(`query-scheduler-ocp.obs.test.nsw.education`)",
        "traefik.http.routers.query-scheduler-ocp.tls=false",
        "traefik.http.routers.query-scheduler-ocp.entrypoints=http,https",
      ]      

    }

    task "mimir-query-scheduler-ocp" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=query-scheduler",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",

          "-querier.scheduler-address=query-scheduler-ocp.obs.test.nsw.education:${NOMAD_PORT_grpc}"

        ]
      }
      

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 5 cores and 1GB RAM
        cpu        = 4000
        memory     = 1500
        memory_max = 2000
      }
    }
  }


  # QUERY FRONTEND = = = = = = = = = = = = = = = = = = = = = = = = = = 
  group "query-frontend-ocp" {
    count = 2

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-query-frontend-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "query-frontend-ocp"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-query-frontend-ocp.entrypoints=https",
        "traefik.http.routers.mimir-query-frontend-ocp.rule=Host(`mimir-query-frontend-ocp.obs.test.nsw.education`)"
      ]    

      check {
        name            = "Mimir query-frontend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-query-frontend-ocp" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=query-frontend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 5 cores and 5GB RAM
        cpu        = 4000
        memory     = 6000
        memory_max = 8000
      }
    }
  }


  # STORE GATEWAY  = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "store-gateway-ocp" {
    count = 1

    constraint {
      distinct_property = node.unique.name
    }
    volume "vol_mimirocp"  {
      type = "host"
      source = "vol_mimirocp"
      read_only = false
    }  
    ephemeral_disk {
      size    = 1000
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-store-gateway-ocp"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "store-gateway-ocp"
      }

      check {
        name            = "Mimir store-gateway"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-store-gateway-ocp" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }
      volume_mount {
        volume = "vol_mimirocp"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=store-gateway",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-ocp-uat-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        # Recommended 5 cores and 5GB RAM
        cpu        = 4000
        memory     = 6000
        memory_max = 7000
      }
    }
  }
}
