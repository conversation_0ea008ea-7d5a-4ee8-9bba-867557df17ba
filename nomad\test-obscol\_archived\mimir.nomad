
// mimir - microservices version - for ObsCol TEST

variables {
  versions = {
    # ${var.versions.mimir}
    mimir = "2.8.0" 
  }
}

job "mimir" {
  datacenters = [
    "dc-cir-un-test"
  ]


  # COMPACTOR = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "compactor" {
    count = 1

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-compactor"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "compactor"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-compactor-ring.entrypoints=https",
         "traefik.http.routers.mimir-compactor-ring.rule=Host(`mimir-compactor.obs.test.nsw.education`) && Path(`/compactor/ring`)",
       ]      

      check {
        name            = "Mimir compactor"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-compactor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=compactor",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      
      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 3000
        memory     = 256
        memory_max = 1024
      }
    }
  }


  # RULER = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "ruler" {
    count = 1

    constraint {
      distinct_property = node.unique.name
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }  

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-ruler"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "ruler"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-ruler.entrypoints=https",
        "traefik.http.routers.mimir-ruler.rule=Host(`mimir-query-frontend.obs.test.nsw.education`) && PathPrefix(`/prometheus/api/v1/rules`)",
        "traefik.http.routers.mimir-ruler-ring.entrypoints=https",
        "traefik.http.routers.mimir-ruler-ring.rule=Host(`mimir-ruler.obs.test.nsw.education`) && Path(`/ruler/ring`)",
      ]   

      check {
        name            = "Mimir ruler"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-ruler" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=ruler",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 1000
        memory     = 256
        memory_max = 512
      }
    }
  }


  # DISTRIBUTOR  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "distributor" {
    count = 3

    constraint {
      distinct_property = node.unique.name
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-distributor"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "distributor"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-distributor.entrypoints=https",
        "traefik.http.routers.mimir-distributor.rule=Host(`mimir-distributor.obs.test.nsw.education`)",
        
        "traefik.http.routers.mimir-distributor-ring.entrypoints=https",
        "traefik.http.routers.mimir-distributor-ring.rule=Host(`mimir-distributor.obs.test.nsw.education`) && Path(`/distributor/ring`)",
    ]      

      check {
        name            = "Mimir distributor"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-distributor" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=distributor",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 4024
      }
    }
  }


  # INGESTER = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "ingester" {
    count = 3

    # WARNING - if we increase ingesters > cluster members, this will insta-break
    constraint {
      distinct_property = node.unique.name
    }

    #volume "vol_mimir"  {
    #  type = "host"
    #  source = "vol_mimir"
    #  read_only = false
    #}  

    ephemeral_disk {
      size    = 4000
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-ingester"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "ingester"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir.rule=Host(`mimir.obs.test.nsw.education`)",
        "traefik.http.routers.mimir.tls=false",
        "traefik.http.routers.mimir.entrypoints=http,https",
      ]

      check {
        name            = "Mimir ingester"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-ingester" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      #volume_mount {
      #  volume = "vol_mimir"
      #  destination = "/mimir"
      #  read_only = false
      #}

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=ingester",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
        
        mount {
          type = "tmpfs"
          target = "/mimir"
          readonly = false
          #tmpfs_options {
          #  # size is in bytes - it's unlimited by default
          #  size = 100000
          #}
        }




      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 500
        memory     = 512
        memory_max = 4096
      }
    }
  }


  # QUERIER  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "querier" {
    count = 2

    constraint {
      distinct_property = node.unique.name
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-querier"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "querier"
      }

      check {
        name            = "Mimir querier"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-querier" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=querier",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
          "-querier.cardinality-analysis-enabled=true",
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 2048
      }
    }
  }


  # QUERY SCHEDULER = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "query-scheduler" {
    count = 2

    network {
      port "http" {}
      port "grpc" {
        to     = 8096
        static = 8096
      }
    }

    service {
      name = "mimir-query-scheduler"
      port = "grpc"

      meta {
        alloc_id  = node.unique.name
        component = "query-scheduler"
      }

      check {
        name            = "Mimir query-scheduler"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-query-scheduler" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=query-scheduler",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 100
        memory     = 64
        memory_max = 128
      }
    }
  }


  # QUERY FRONTEND = = = = = = = = = = = = = = = = = = = = = = = = = = 
  group "query-frontend" {
    count = 2

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-query-frontend"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "query-frontend"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-query-frontend.entrypoints=https",
        "traefik.http.routers.mimir-query-frontend.rule=Host(`mimir-query-frontend.obs.test.nsw.education`)"
      ]    

      check {
        name            = "Mimir query-frontend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-query-frontend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }

      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        #dns_servers = ["************"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=query-frontend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 500
        memory     = 256
        memory_max = 1028
      }
    }
  }


  # STORE GATEWAY  = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "store-gateway" {
    count = 1

    constraint {
      distinct_property = node.unique.name
    }
    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }  
    ephemeral_disk {
      size    = 1000
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-store-gateway"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "store-gateway"
      }

      check {
        name            = "Mimir store-gateway"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-store-gateway" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"
      }
      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      config {
        image = "grafana/mimir:${var.versions.mimir}"
        ports = [
          "http",
          "grpc",
        ]
        
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }

        args = [
          "-target=store-gateway",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true"
        ]
      }

      template {
        data = file("assets/mimir-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 200
        memory     = 128
        memory_max = 1024
      }
    }
  }
}
