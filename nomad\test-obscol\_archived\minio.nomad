
# minio - single node instance for test obscol environment - block storage on local
#         file system.

# Use Grafana dashboard 13502 - https://grafana.com/grafana/dashboards/13502-minio-dashboard/

job "minio" {

  datacenters = ["dc-cir-un-test"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "minio" {

    network {
      # With Traefik we can just hit the api dns with the URL path of :{api}/minio/v2/metrics/cluster
      # but for the initial build on obscol-test we just hard code it to a unique port.
      # port "api" {}
      port "port_api" {
        static = 9888
      }

      port "port_embedded_console" {}

      # I gather the console port is a throwback, as the embedded_console param has been the
      # preferred approach since version 2021-07-08
      # port "console"  {
      #   to = 9090
      # }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    volume "vol_minio" {
      # Buckets are top-level directories in here, and config is stored under .minio.sys/ 
      type = "host"
      source = "vol_minio"
      read_only = false
    }

    #ephemeral_disk {
    #  size = 300
    #}

    task "minio" {
      driver = "docker"

      user = "root"

      volume_mount {
        volume      = "vol_minio"
        destination = "/data"
        read_only   = false
      }

      env = {
        "MINIO_ROOT_USER"                    = "admin",
        "MINIO_ROOT_PASSWORD"                = "bigsecret",

        "MINIO_PROMETHEUS_AUTH_TYPE"         = "public",
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true",

        "MINIO_PROMETHEUS_URL"               = "http://prometheus.obs.test.nsw.education",
        "MINIO_PROMETHEUS_JOB_ID"            = "minio_metrics"
      }

      config {
        image = "minio/minio:latest"

        # ports = ["port_minio_console", "port_minio_api"]
        ports = ["port_api", "port_embedded_console"]

        args = [ 
          "server", "/data",
          "--address",           "0.0.0.0:${NOMAD_HOST_PORT_port_api}",
          "--console-address",   "0.0.0.0:${NOMAD_HOST_PORT_port_embedded_console}"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }        

        }

      resources {
        cpu    = 500
        memory = 2048
      }

      service {
        name = "minio"
        port = "port_embedded_console"

        tags = [
           "traefik.enable=true",
           "traefik.http.routers.minio.rule=Host(`minio.obs.test.nsw.education`)",
           "traefik.http.routers.minio.tls=false",
        ]

        check {
          name     = "minio_alive"
          type     = "http"
          port     = "port_embedded_console"
          path     = "/minio/health/live"
          interval = "30s"
          timeout  = "5s"
        }

#        check {
#          name     = "minio_alive"
#          type     = "http"
#          port     = "port_minio_console"
#          path     = "/minio/health/live"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
