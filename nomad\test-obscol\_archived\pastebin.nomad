
# Pastebin for ObsCol TEST


job "pastebin" {
  type        = "service"

  datacenters = ["dc-cir-un-test"]

  group "pastebin" {
    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    ephemeral_disk {
      migrate = true
      size    = 300
      sticky  = true
    }

    network {
      port "port_pastebin" {
        to = 8027
        }
      }

    task "pastebin" {
      driver = "docker"

      config {
        image = "mkodockx/docker-pastebin"
        ports = ["port_pastebin"]
      }

      env = {
        "PORT" = "8027"
      }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "pastebin"
        port = "port_pastebin"

        check {
          type     = "tcp"
          interval = "120s"
          timeout  = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.pastebin.rule=Host(`pastebin.obs.test.nsw.education`)",
          "traefik.http.routers.pastebin.tls=false",
          "traefik.http.routers.pastebin.entrypoints=http",
        ]

      }


    }
  }
}

