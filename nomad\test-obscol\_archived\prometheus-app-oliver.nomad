
// obs-col TEST - prometheus-app-oliver

// 2022-12-13 moved from: standard prometheus with unused alerting
//                  to:   prometheus-agent (no alerting, no query/graph on GUI).

job "prometheus-app-oliver" {
  type = "service"

  datacenters = ["dc-cir-un-test"]

  group "prometheus-app-oliver" {

    network {
      port "port_prometheus_app_oliver" { }
    }

    volume "vol_prometheus-app-oliver"  {
      type = "host"
      source = "prometheus_app_oliver"
      read_only = false
    }

    task "prometheus-app-oliver" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus-app-oliver"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["port_prometheus_app_oliver"]

        # 2022-11-29 jedd - bumping from 2.39.1 to 2.40.3 due to memory leak advised by <PERSON>
        # image = "https://docker.io/prom/prometheus:v2.39.1"
        image = "https://docker.io/prom/prometheus:v2.40.3"

        dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki-s3.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_app_oliver}",
          "--web.external-url=https://prometheus-app-oliver.obs.test.nsw.education",
          "--web.page-title=Prometheus for App Oliver on DoE ObsCol TEST cluster",

          "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml",

          # These are meaningless with 'agent' mode, and break startup.
          # "--storage.tsdb.path=/prometheus",
          # "--storage.tsdb.retention.time=8d",
          # "--storage.local.series-file-shrink-ratio=0.3",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-oliver"
        port = "port_prometheus_app_oliver"

        check {
          type = "http"
          port = "port_prometheus_app_oliver"
          path = "/-/healthy"
          interval = "20s"
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-oliver.rule=Host(`prometheus-app-oliver.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-app-oliver.tls=false",
          "traefik.http.routers.prometheus-app-oliver.entrypoints=http,https,prometheus-app-oliver",
        ]

        meta {
          cir_app_id = "obs"
          env        = "test"
          cluster    = "obscol"
        }
      }

      artifact {
        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
        destination = "local/prometheus-configuration"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
  # These chance the extant queries - we can work around this later, if we want to enable this again.
  #  nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
  #  nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: "obscol-prometheus-app-oliver"

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-app-oliver'
    static_configs:
      - targets: ['prometheus-app-oliver.obs.test.nsw.education:{{ env "NOMAD_PORT_port_prometheus_app_oliver" }}']

  # Using blackbox exporter to do a custom web-check for Oliver school library
  # with custom URLs and regex string match on target page.
  - job_name: 'webcheck_oliver'
    metrics_path: /probe
    scrape_interval: 2m
    # No need for proxy HERE as we're hitting blackbox...obs.nsw.education (internal)
    # proxy_url: "http://proxy.det.nsw.edu.au:80"
    params:
      module: ["webcheck_oliver"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 1h
      files: 
      - "/local/prometheus-configuration/test/blackbox/blackbox_webcheck_oliver.yml"
    relabel_configs:
    - source_labels: [__address__]
      target_label: __param_target
    - source_labels: [__param_target]
      target_label: instance
    - target_label: __address__
      replacement: exporter-blackbox.obs.test.nsw.education

remote_write:
  - name: mimir
    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: test
    tls_config:
      insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 512
        memory = 1024
      }

    }
  }
}
