
// prometheus-remote-write - obscol TEST

// target for k6 load testing

job "prometheus-remotewrite" {

  datacenters = ["dc-cir-un-test"]

  type = "service"

  group "prometheus-remotewrite" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
      }
  	}

    task "prometheus-remotewrite" {
      driver = "docker"

      config {
        image = "https://docker.io/prom/prometheus:v2.42.0"

        args = [
          "--storage.tsdb.retention.time=24h",

          "--config.file=/etc/prometheus/prometheus.yml",
           

          # Address to listen on for UI, API, and telemetry.
          # "--web.listen-address="0.0.0.0:9091",
          # "--web.listen-address=**************:${NOMAD_PORT_port_prometheus}",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",

          "--storage.tsdb.min-block-duration=1h",

          # max web connections defaults to 512 
          # No change to VSS on restart
          "--web.max-connections=12",

          # query max concurrency defaults 20 - in theory should match or be less than cores
          # VSS from 10.1 GB to 10.0 on restart
          # Res dropped from 248 MB to 193 on restart but soon (5-10m) returned to the same volume
          "--query.max-concurrency=5",

          # Remote-write capability lets us actively send k6 metrics here.
          "--web.enable-remote-write-receiver"

          # Memory consumption args to experiment with include:
          # --web.max-connections=512  Maximum number of simultaneous connections.
          # --storage.agent.retention.max-time=STORAGE.AGENT.RETENTION.MAX-TIME
          # --storage.remote.read-max-bytes-in-frame=1048576
          # --query.lookback-delta=5m  The maximum lookback duration for retrieving
          # --query.max-concurrency=20
          # --query.max-samples=50000000

        ]

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [ 
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        network_mode = "host"
      }

      resources {
        cpu    = 140
        memory = 400
      }

      service {
        name = "prometheus-http"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-remotewrite.rule=Host(`prometheus-remotewrite.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-remotewrite.tls=false",
          "traefik.http.routers.prometheus-remotewrite.entrypoints=http,https"
        ]

       check {
         type = "http"
         port = "port_prometheus"
         path = "/-/healthy"
         interval = "20s"
         timeout = "10s"
       }
      }

      template {
        data = <<EOH

global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
  scrape_interval: 1m
  # This creates a log of *all* queries, and will show up in /metrics as prometheus_engine_query_log_enabled=1
  query_log_file:  /prometheus/query.log


scrape_configs:
  - job_name: 'prometheus-remotewrite'
    static_configs:
      - targets: ['prometheus-remotewrite.obs.test.nsw.education:${NOMAD_PORT_port_prometheus}']
    metrics_path: /metrics




# 2023-02-21 jedd - disabling until we've experimented a bit more.
#remote_write:
#  - name: mimir
#    url: "http://dg-pan-01.int.jeddi.org:19009/api/v1/push"


EOH
        destination = "local/prometheus.yaml"
      }
    }
  }

}
