
// obs-col TEST - prometheus-sd-telegraf

// 2022-12-13 split from primary prometheus instance, to run AGENT mode for
//            service-discovery (consul) telegraf/openmetrics targets only.


job "prometheus-sd-telegraf" {
  type = "service"

  datacenters = ["dc-cir-un-test"]

  group "prometheus-sd-telegraf" {
    count = 1

    constraint {
        operator  = "distinct_property"
        attribute = "${node.datacenter}"
        value     = "1"
        }

    network {
      port "port_prometheus_sd_telegraf" { }
    }

    volume "vol_prometheus-sd-telegraf"  {
      type = "host"
      source = "prometheus_sd_telegraf"
      read_only = false
    }

    task "prometheus-sd-telegraf" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus-sd-telegraf"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["port_prometheus_sd_telegraf"]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus_sd_telegraf}",
          "--web.external-url=https://prometheus-sd-telegraf.obs.test.nsw.education",
          "--web.page-title=Prometheus SD Telegraf on DoE ObsCol TEST cluster",

          "--enable-feature=agent",

          "--config.file=/etc/prometheus/prometheus.yml",

          # These are meaningless with 'agent' mode, and break startup.
          #"--storage.tsdb.path=/prometheus",
          #"--storage.tsdb.retention.time=8d",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus"
        port = "port_prometheus_sd_telegraf"

        check {
          type = "http"
          port = "port_prometheus_sd_telegraf"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-sd-telegraf.rule=Host(`prometheus-sd-telegraf.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-sd-telegraf.tls=false",
          "traefik.http.routers.prometheus-sd-telegraf.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    provenance: "obscol-prometheus-sd-telegraf"

  scrape_interval: 1m

scrape_configs:
  # Scrape ourselves.
  - job_name: 'prometheus-sd-telegraf'
    static_configs:
      - targets: ['prometheus-sd-telegraf.obs.test.nsw.education:{{ env "NOMAD_PORT_port_prometheus_sd_telegraf" }}']

  # Scrape all external nodes (via SD) running telegraf.
  - job_name: 'openmetrics'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['openmetrics']
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_env]
        target_label: env


remote_write:
  - name: mimir
    url: "https://mimir.obs.test.nsw.education/api/v1/push"
    headers: 
        X-Scope-OrgID: test
    tls_config:
        insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 16000
      }

    }
  }
}
