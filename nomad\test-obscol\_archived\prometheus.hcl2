// test - obscol - nomad - prometheus

job "prometheus" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  group "prometheus" {

	network {
	  port "prometheus" {
		static = 9090
	  }
	}

    task "prometheus" {
      driver = "docker"

      config {
        image = "https://docker.io/prom/prometheus:v2.27.1"
        dns_servers = ["192.168.31.1"]
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

        check {
          type = "http"
          port = "prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

//      remote_write:
//      - url: "http://metricbeat-remote-write.service.dc1.consul:9201/write"

      template {
        data = <<EOH

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'nomad'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.consul:8500'
        datacenter: 'dc-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['nomad-client', 'nomad']

    relabel_configs:
      - source_labels: ['__meta_consul_tags']
        regex: '(.*)http(.*)'
        action: keep

    metrics_path: /v1/metrics
    params:
      format: ['prometheus']

EOH
        destination = "local/prometheus.yaml"
      }
    }
  }
}
