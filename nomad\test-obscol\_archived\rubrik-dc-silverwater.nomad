// Rubrik have released an official prometheus client image fr the api
// Pass it cdm username and password along with a cdm endpoint for scrpaing
// There will be one exporter for each of the datacenter clusters
// docker run -d -t -e rubrik_cdm_node_ip=$rubrik_cdm_node_ip 
//   -e rubrik_cdm_username=$rubrik_cdm_username 
//   -e rubrik_cdm_password=$rubrik_cdm_password 
//   -p 8080:8080 rubrikinc/prometheus-client

job "rubrik-silverwater" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  group "rubrik-exporter" {

    task "rubrik-exporter" {
      driver = "docker"

      resources {
          network {
              port "http"{
                  static = 19402
              }
          }
      }

      config {
        image = "https://artifacts.mtm.nsw.education/rubrik-prometheus-client:latest"
        dns_servers = ["************"]
      }
      service {
          name = "exporter"
          port = "http"
      }

      env {
        CONSUL_HTTP_ADDR = "http://consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        rubrik_cdm_node_ip = "ps0991brik1000.nsw.education"
        rubrik_cdm_username = "svc-obs"
        rubrik_cdm_password = "NQsQ?kYye#P0"
        RUBRIK_PROMETHEUS_PORT = "19402"
      }
      } 
    }
}
