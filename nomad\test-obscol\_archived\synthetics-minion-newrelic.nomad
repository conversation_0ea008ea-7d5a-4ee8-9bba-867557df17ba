job "synthetics-minion-newrelic" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  group "synthetics-minion-newrelic" {
    # MINION NAME = obscol-devtest-unanderra-govdc
    # Private Location ID 2294590-obscol_devtest_unanderra_govdc-411

    task "synthetics-minion-newrelic" {
      driver = "docker"

      config {
        image = "https://quay.io/newrelic/synthetics-minion:latest"
        dns_servers = ["************"]
        volumes = [
          "local/tmp:/tmp:rw",
          "/var/run/docker.sock:/var/run/docker.sock:rw"
        ]
      }

      env {
        # See https://docs.newrelic.com/docs/synthetics/synthetic-monitoring/private-locations/containerized-private-minion-cpm-configuration/

        # Registered 26/09/2021
        MINION_PRIVATE_LOCATION_KEY = "NRSP-us01E2D7CC343B413BFFF93BE04ABDCAB64"

        # Variables available to synthetic script via env
        # MINION_USER_DEFINED_VARIABLES
        # Can be used as $env.USER_DEFINED_VARIABLES.MY_VARIABLE

        MINION_API_PROXY = "proxy.det.nsw.edu.au:80"

      }

      resources {
        cpu = 400
        memory = 1500
      }
    }
  }
}

