
// TEMPO (test obscol)

// 2023-02-01 heavily revised based on prod-obscol job effective rewrite

job "tempo" {
  type = "service"

  datacenters = ["dc-cir-un-test"]
  update {
    max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true
    healthy_deadline = "5m"
    min_healthy_time = "5s"
  }
  group "tempo" {
    count = 1

    network {
      
      port "port_jaeger" {
        to = 14268
      }
      port "port_tempo" {
        to = 3200
      }
    }

    volume "vol_tempo"  {
      type = "host"
      source = "vol_tempo"
      read_only = false
    }

    service {
      name = "tempo"
      port = "port_tempo"

      check {
        type     = "tcp"
        interval = "17s"
        timeout  = "5s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo.rule=Host(`tempo.obs.test.nsw.education`)",
          "traefik.http.routers.tempo.tls=false",
          "traefik.http.routers.tempo.entrypoints=http,https",
        ]      
    }


    service {
      name = "tempo-jaeger"
      port = "port_jaeger"
      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo-jaeger.rule=Host(`tempo-jaeger.obs.test.nsw.education`)",
          "traefik.http.routers.tempo-jaeger.tls=false",
          "traefik.http.routers.tempo-jaeger.entrypoints=http,https",
        ]      
    }

    task "tempo" {

      driver = "docker"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"             
      }

      config {
        image = "grafana/tempo:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            } 
        
        args = [
          "--config.file=/etc/tempo/config/tempo.yml",
        ]

        ports = [
          "port_jaeger",
          "port_tempo",
        ]

        volumes = [
          "local/config:/etc/tempo/config",
        ]
        
      }

      volume_mount {
        volume = "vol_tempo"
        destination = "/mnt/tempo"
        read_only = false
      }

      template {
        data = <<EOH

auth_enabled: false
server:
  http_listen_port: {{ env "NOMAD_PORT_port_tempo" }}
distributor:
  log_received_spans:
    enabled: false
  receivers:                           
    # This configuration will listen on all ports and protocols that tempo is capable of.
    # Refer: found there: https://github.com/open-telemetry/opentelemetry-collector/tree/master/receiver
    jaeger:                            
      protocols:                       
        thrift_http:
          endpoint: "0.0.0.0:14268"
        grpc:                          
        thrift_binary:
        thrift_compact:
    otlp:
      protocols:
        http:
        grpc:
    opencensus:

ingester:
  # length of time after a trace has not received spans to consider it complete and flush it
  trace_idle_period: 10s
  max_block_duration: 5m

compactor:
  compaction:
    compaction_window: 1h              # blocks in this time window will be compacted together
    max_compaction_objects: 1000000    # maximum size of compacted blocks
    block_retention: 336
    compacted_block_retention: 700h

storage:
  trace:
    backend: s3
    wal:
      path: /mnt/tempo/wal
    s3:
      bucket: nswdoe-obs-tempo-blocks-storage-dev
      endpoint: s3.ap-southeast-2.amazonaws.com
      region: ap-southeast-2
      access_key: ********************
      secret_key: RryworGGNUUb+YQOOndKe+On8/FdzkCmXMxKK7Gr
    pool:
      # worker pool determines the number of parallel requests to the object store backend
      max_workers: 100                 
      queue_depth: 10000

metrics_generator:
  processor:
    service_graphs:
      max_items: 50000
  registry:
    external_labels:
      source: tempo
      cluster: obscol-prod
  storage:
    path: /mnt/tempo/generator/wal
    remote_write:
      - url: https://mimir-distributor.obs.test.nsw.education/api/v1/push
        headers: 
          X-Scope-OrgID: test
        tls_config:
          insecure_skip_verify: true

overrides:
  metrics_generator_processors: [service-graphs, span-metrics]
EOH

        change_mode   = "signal"
        change_signal = "SIGHUP"
        destination   = "local/config/tempo.yml"
      }

      resources {
        cpu    = 3500
        memory = 8000
      }
    }
  }
}
