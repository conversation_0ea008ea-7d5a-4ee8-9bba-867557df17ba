
line 143

              "EmbeddedTmpl": "auth_enabled: false\r\n\r\nserver:\r\n  log_level: info\r\n  http_listen_port: {{ env \"NOMAD_PORT_http\" }}\r\n  grpc_listen_port: {{ env \"NOMAD_PORT_grpc\" }}\r\n\r\ncommon:\r\n  replication_factor: 1\r\n  # Tell Loki which address to advertise\r\n  instance_addr: {{ env \"NOMAD_IP_http\" }}\r\n  ring:\r\n    # Tell Loki which address to advertise in ring\r\n    instance_addr: {{ env \"NOMAD_IP_http\" }}\r\n    instance_id: {{ env \"node.unique.name\" }}\r\n    instance_port: {{ env \"NOMAD_PORT_http \"}}\r\n    kvstore:\r\n      store: consul\r\n      prefix: loki/\r\n      consul:\r\n        host: {{ env \"attr.unique.network.ip-address\" }}:8500\r\n\r\ningester:\r\n  wal:\r\n    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/wal\r\n    flush_on_shutdown: true\r\n    replay_memory_ceiling: \"1G\"\r\n\r\nschema_config:\r\n  configs:\r\n  - from: 2022-05-15\r\n    store: boltdb-shipper\r\n    object_store: s3\r\n    schema: v12\r\n    index:\r\n      prefix: index_\r\n      period: 24h\r\n\r\nstorage_config:\r\n  boltdb_shipper:\r\n    # Nomad ephemeral disk is used to store index and cache\r\n    # it will try to preserve /alloc/data between job updates\r\n    active_index_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index\r\n    cache_location: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index-cache\r\n    shared_store: s3\r\n  aws:\r\n    bucketnames: \"nswdoe-obs-loki-blocks-storage-dev\"\r\n    # 2024-02-12 jedd - rotating secrets\r\n    # access_key_id: ********************\r\n    # secret_access_key: CAqbkC8O4hoB0qZTi3CFw5TPbcEXbdPvlqW5UNcx\r\n    access_key_id:  ********************\r\n    secret_access_key: ZapKZfBPOjFllvF86XK4jap1/8Thk+IUPQFaLBX9\r\n\r\n    s3forcepathstyle: true\r\n    region: \"ap-southeast-2\"\r\n    insecure: false\r\n    sse_encryption: true\r\n\r\nlimits_config:\r\n  enforce_metric_name: false\r\n  reject_old_samples: true\r\n  reject_old_samples_max_age: 168h\r\n\r\ncompactor:\r\n  working_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/compactor\r\n  shared_store: s3\r\n  compaction_interval: 5m\r\n  retention_enabled: true\r\n\r\nruler:\r\n  alertmanager_url: https://alertmanager.obs.test.nsw.education\r\n  enable_alertmanager_v2: true\r\n  enable_api: true\r\n  external_url: https://loki.obs.test.nsw.education\r\n  rule_path: {{ env \"NOMAD_ALLOC_DIR\" }}/tmp/rules\r\n  storage:\r\n    type: local\r\n    local:\r\n      directory: {{ env \"NOMAD_TASK_DIR\" }}/rules\r\n  wal:\r\n    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/ruler\r\n",



line 460 (should be identical)


              "EmbeddedTmpl": "auth_enabled: false\r\n\r\nserver:\r\n  log_level: info\r\n  http_listen_port: {{ env \"NOMAD_PORT_http\" }}\r\n  grpc_listen_port: {{ env \"NOMAD_PORT_grpc\" }}\r\n\r\ncommon:\r\n  replication_factor: 1\r\n  # Tell Loki which address to advertise\r\n  instance_addr: {{ env \"NOMAD_IP_http\" }}\r\n  ring:\r\n    # Tell Loki which address to advertise in ring\r\n    instance_addr: {{ env \"NOMAD_IP_http\" }}\r\n    instance_id: {{ env \"node.unique.name\" }}\r\n    instance_port: {{ env \"NOMAD_PORT_http \"}}\r\n    kvstore:\r\n      store: consul\r\n      prefix: loki/\r\n      consul:\r\n        host: {{ env \"attr.unique.network.ip-address\" }}:8500\r\n\r\ningester:\r\n  wal:\r\n    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/wal\r\n    flush_on_shutdown: true\r\n    replay_memory_ceiling: \"1G\"\r\n\r\nschema_config:\r\n  configs:\r\n  - from: 2022-05-15\r\n    store: boltdb-shipper\r\n    object_store: s3\r\n    schema: v12\r\n    index:\r\n      prefix: index_\r\n      period: 24h\r\n\r\nstorage_config:\r\n  boltdb_shipper:\r\n    # Nomad ephemeral disk is used to store index and cache\r\n    # it will try to preserve /alloc/data between job updates\r\n    active_index_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index\r\n    cache_location: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index-cache\r\n    shared_store: s3\r\n  aws:\r\n    bucketnames: \"nswdoe-obs-loki-blocks-storage-dev\"\r\n    # 2024-02-12 jedd - rotating secrets\r\n    # access_key_id: ********************\r\n    # secret_access_key: CAqbkC8O4hoB0qZTi3CFw5TPbcEXbdPvlqW5UNcx\r\n    access_key_id:  ********************\r\n    secret_access_key: ZapKZfBPOjFllvF86XK4jap1/8Thk+IUPQFaLBX9\r\n\r\n    s3forcepathstyle: true\r\n    region: \"ap-southeast-2\"\r\n    insecure: false\r\n    sse_encryption: true\r\n\r\nlimits_config:\r\n  enforce_metric_name: false\r\n  reject_old_samples: true\r\n  reject_old_samples_max_age: 168h\r\n\r\ncompactor:\r\n  working_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/compactor\r\n  shared_store: s3\r\n  compaction_interval: 5m\r\n  retention_enabled: true\r\n\r\nruler:\r\n  alertmanager_url: https://alertmanager.obs.test.nsw.education\r\n  enable_alertmanager_v2: true\r\n  enable_api: true\r\n  external_url: https://loki.obs.test.nsw.education\r\n  rule_path: {{ env \"NOMAD_ALLOC_DIR\" }}/tmp/rules\r\n  storage:\r\n    type: local\r\n    local:\r\n      directory: {{ env \"NOMAD_TASK_DIR\" }}/rules\r\n  wal:\r\n    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/ruler\r\n",

