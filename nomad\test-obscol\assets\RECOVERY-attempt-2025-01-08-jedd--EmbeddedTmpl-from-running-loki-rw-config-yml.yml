

# 2025-01-08 jedd 
# Recovered from running job (line 143) - need to reconcile this against old 
# git assets (late 2023?) but I believe the file was changed significantly 
# through 2024, hence caution.

server:
  log_level: info
  http_listen_port: {{ env \"NOMAD_PORT_http\" }}
  grpc_listen_port: {{ env \"NOMAD_PORT_grpc\" }}

common:
  replication_factor: 1
  # Tell Loki which address to advertise
  instance_addr: {{ env \"NOMAD_IP_http\" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env \"NOMAD_IP_http\" }}
    instance_id: {{ env \"node.unique.name\" }}
    instance_port: {{ env \"NOMAD_PORT_http \"}}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env \"attr.unique.network.ip-address\" }}:8500

ingester:
  wal:
    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/wal
    flush_on_shutdown: true
    replay_memory_ceiling: \"1G\"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v12
    index:
      prefix: index_
      period: 24h

storage_config:
  boltdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index
    cache_location: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index-cache
    shared_store: s3
  aws:
    bucketnames: \"nswdoe-obs-loki-blocks-storage-dev\"
    # 2024-02-12 jedd - rotating secrets
    # access_key_id: ********************
    # secret_access_key: CAqbkC8O4hoB0qZTi3CFw5TPbcEXbdPvlqW5UNcx
    access_key_id:  ********************
    secret_access_key: ZapKZfBPOjFllvF86XK4jap1/8Thk+IUPQFaLBX9

    s3forcepathstyle: true
    region: \"ap-southeast-2\"
    insecure: false
    sse_encryption: true

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h

compactor:
  working_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/compactor
  shared_store: s3
  compaction_interval: 5m
  retention_enabled: true

ruler:
  alertmanager_url: https://alertmanager.obs.test.nsw.education
  enable_alertmanager_v2: true
  enable_api: true
  external_url: https://loki.obs.test.nsw.education
  rule_path: {{ env \"NOMAD_ALLOC_DIR\" }}/tmp/rules
  storage:
    type: local
    local:
      directory: {{ env \"NOMAD_TASK_DIR\" }}/rules
  wal:
    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/ruler



# 2025-01-08 jedd -- this is from line 460 of the running configuration file - I need
# to reconcile this, as above, with git assets - but ALSO reconcile with the config
# above -- they SHOULD be pulled from the same original ./assets/ file - so should be
# identical


server:
  log_level: info
  http_listen_port: {{ env \"NOMAD_PORT_http\" }}
  grpc_listen_port: {{ env \"NOMAD_PORT_grpc\" }}

common:
  replication_factor: 1
  # Tell Loki which address to advertise
  instance_addr: {{ env \"NOMAD_IP_http\" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env \"NOMAD_IP_http\" }}
    instance_id: {{ env \"node.unique.name\" }}
    instance_port: {{ env \"NOMAD_PORT_http \"}}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env \"attr.unique.network.ip-address\" }}:8500

ingester:
  wal:
    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/wal
    flush_on_shutdown: true
    replay_memory_ceiling: \"1G\"

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v12
    index:
      prefix: index_
      period: 24h

storage_config:
  boltdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index
    cache_location: {{ env \"NOMAD_ALLOC_DIR\" }}/data/index-cache
    shared_store: s3
  aws:
    bucketnames: \"nswdoe-obs-loki-blocks-storage-dev\"
    # 2024-02-12 jedd - rotating secrets
    # access_key_id: ********************
    # secret_access_key: CAqbkC8O4hoB0qZTi3CFw5TPbcEXbdPvlqW5UNcx
    access_key_id:  ********************
    secret_access_key: ZapKZfBPOjFllvF86XK4jap1/8Thk+IUPQFaLBX9

    s3forcepathstyle: true
    region: \"ap-southeast-2\"
    insecure: false
    sse_encryption: true

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h

compactor:
  working_directory: {{ env \"NOMAD_ALLOC_DIR\" }}/compactor
  shared_store: s3
  compaction_interval: 5m
  retention_enabled: true

ruler:
  alertmanager_url: https://alertmanager.obs.test.nsw.education
  enable_alertmanager_v2: true
  enable_api: true
  external_url: https://loki.obs.test.nsw.education
  rule_path: {{ env \"NOMAD_ALLOC_DIR\" }}/tmp/rules
  storage:
    type: local
    local:
      directory: {{ env \"NOMAD_TASK_DIR\" }}/rules
  wal:
    dir: {{ env \"NOMAD_ALLOC_DIR\" }}/data/ruler


