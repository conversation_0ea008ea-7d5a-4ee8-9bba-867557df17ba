prometheus.exporter.self "integrations_alloy" { }

logging {
  level  = "info"
  format = "logfmt"
}

tracing {
  sampling_fraction = 0.1
  write_to          = [otelcol.exporter.otlphttp.grafanacloud.input]
}

otelcol.exporter.otlphttp "grafanacloud" {
  client {
    endpoint = "https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp"
    auth = otelcol.auth.basic.grafanacloud.handler
  }
}

otelcol.auth.basic "grafanacloud" {
  username = "533612"
  password = "***********************************************************************************************************************************************************************1In19"
}

discovery.relabel "integrations_alloy" {
  targets = prometheus.exporter.self.integrations_alloy.targets

  rule {
    target_label = "instance"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "alloy_hostname"
    replacement  = "{{ env "node.unique.name" }}"
  }

  rule {
    target_label = "namespace"
    replacement  = "test-obscol"
  }

  rule {
    target_label = "job"
    replacement  = "integrations/alloy-check"
  }

  rule {
    target_label = "env"
    replacement  = "test"
  }  
}

prometheus.exporter.self "integrations_alloy_health" { }

discovery.relabel "integrations_alloy_health" {
  targets = prometheus.exporter.self.integrations_alloy_health.targets

  rule {
    replacement = "{{ env "node.unique.name" }}"
    target_label  = "instance"
  }

  rule {
    target_label = "job"
    replacement  = "{{env "NOMAD_JOB_NAME"}}"
  }

  rule {
    target_label = "env"
    replacement  = "test"
  }  

  rule {
    target_label = "namespace"
    replacement  = "test-obscol"
  }    
}

prometheus.scrape "integrations_alloy_health" {
  targets    = discovery.relabel.integrations_alloy_health.output
  forward_to = [prometheus.relabel.integrations_alloy_health.receiver]
  job_name   = "{{env "NOMAD_JOB_NAME"}}"
}

prometheus.relabel "integrations_alloy_health" {
	forward_to = [prometheus.remote_write.metrics_service.receiver]

	rule {
		source_labels = ["__name__"]
		regex         = "alloy_build_info|alloy_component_controller_evaluating|alloy_component_controller_running_components|alloy_component_dependencies_wait_seconds|alloy_component_dependencies_wait_seconds_bucket|alloy_component_evaluation_seconds|alloy_component_evaluation_seconds_bucket|alloy_component_evaluation_seconds_count|alloy_component_evaluation_seconds_sum|alloy_component_evaluation_slow_seconds|alloy_config_hash|alloy_resources_machine_rx_bytes_total|alloy_resources_machine_tx_bytes_total|alloy_resources_process_cpu_seconds_total|alloy_resources_process_resident_memory_bytes|cluster_node_gossip_health_score|cluster_node_gossip_proto_version|cluster_node_gossip_received_events_total|cluster_node_info|cluster_node_lamport_time|cluster_node_peers|cluster_node_update_observers|cluster_transport_rx_bytes_total|cluster_transport_rx_packet_queue_length|cluster_transport_rx_packets_failed_total|cluster_transport_rx_packets_total|cluster_transport_stream_rx_bytes_total|cluster_transport_stream_rx_packets_failed_total|cluster_transport_stream_rx_packets_total|cluster_transport_stream_tx_bytes_total|cluster_transport_stream_tx_packets_failed_total|cluster_transport_stream_tx_packets_total|cluster_transport_streams|cluster_transport_tx_bytes_total|cluster_transport_tx_packet_queue_length|cluster_transport_tx_packets_failed_total|cluster_transport_tx_packets_total|exporter_send_failed_spans_ratio_total|exporter_sent_spans_ratio_total|go_gc_duration_seconds_count|go_goroutines|go_memstats_heap_inuse_bytes|processor_batch_batch_send_size_ratio_bucket|processor_batch_metadata_cardinality_ratio|processor_batch_timeout_trigger_send_ratio_total|prometheus_remote_storage_bytes_total|prometheus_remote_storage_highest_timestamp_in_seconds|prometheus_remote_storage_metadata_bytes_total|prometheus_remote_storage_queue_highest_sent_timestamp_seconds|prometheus_remote_storage_samples_failed_total|prometheus_remote_storage_samples_retried_total|prometheus_remote_storage_samples_total|prometheus_remote_storage_sent_batch_duration_seconds_bucket|prometheus_remote_storage_sent_batch_duration_seconds_count|prometheus_remote_storage_sent_batch_duration_seconds_sum|prometheus_remote_storage_shards|prometheus_remote_storage_shards_max|prometheus_remote_storage_shards_min|prometheus_remote_write_wal_samples_appended_total|prometheus_remote_write_wal_storage_active_series|receiver_accepted_spans_ratio_total|receiver_refused_spans_ratio_total|rpc_server_duration_milliseconds_bucket|scrape_duration_seconds|up"
		action        = "keep"
	}
}


prometheus.remote_write "metrics_service" {
  endpoint {
    url = "https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push"
    proxy_from_environment = true
    basic_auth {
      username = "768052"
      password = "***********************************************************************************************************************************************************************1In19"
    }
  }
}

loki.write "grafana_cloud_loki" {
  endpoint {
    url = "https://logs-prod-004.grafana.net/loki/api/v1/push"
    proxy_from_environment = true
    basic_auth {
      username = "382995"
      password = "***********************************************************************************************************************************************************************1In19"
    }
  }
}

prometheus.exporter.consul "integrations_consul_exporter" {
	server = "{{ env "node.unique.name"}}.obs.test.nsw.education:8500"
}

discovery.relabel "integrations_consul_exporter" {
	targets = prometheus.exporter.consul.integrations_consul_exporter.targets

	rule {
		target_label = "instance"
		replacement  = "{{ env "node.unique.name" }}"
	}

	rule {
		target_label = "job"
		replacement  = "{{env "NOMAD_JOB_NAME"}}"
	}
    	rule {
		target_label = "env"
		replacement  = "test"
	}
}

prometheus.scrape "integrations_consul_exporter" {
	targets    = discovery.relabel.integrations_consul_exporter.output
	forward_to = [prometheus.remote_write.metrics_service.receiver]
	job_name   = "integrations/consul_exporter"
}

prometheus.exporter.cadvisor "integrations_cadvisor" {
    docker_only = true
}
discovery.relabel "integrations_cadvisor" {
    targets = prometheus.exporter.cadvisor.integrations_cadvisor.targets

	rule {
		target_label = "instance"
		replacement  = "{{ env "node.unique.name" }}"
	}

	rule {
		target_label = "job"
		replacement  = "{{env "NOMAD_JOB_NAME"}}"
	}
    	rule {
		target_label = "env"
		replacement  = "test"
	}
}
prometheus.scrape "integrations_cadvisor" {
    targets    = discovery.relabel.integrations_cadvisor.output
    forward_to = [prometheus.remote_write.metrics_service.receiver]
}


discovery.relabel "metrics_integrations_integrations_nomad" {
	targets = concat(
		[{
			__address__ = "{{ env "node.unique.name" }}.nsw.education:4646",
		}],
	)

	rule {
		target_label = "instance"
		replacement  = "{{ env "node.unique.name" }}"
	}
	rule {
		target_label = "DC"
		replacement  = "dc-cir-un-test"
	}
	rule {
		target_label = "job"
		replacement  = "{{env "NOMAD_JOB_NAME"}}"
	}    
}

prometheus.scrape "metrics_integrations_integrations_nomad" {
	targets    = discovery.relabel.metrics_integrations_integrations_nomad.output
	forward_to = [prometheus.remote_write.metrics_service.receiver]
	job_name   = "integrations/nomad"
	params     = {
		format = ["prometheus"],
	}
	metrics_path = "/v1/metrics"
}

discovery.relabel "metrics_integrations_integrations_traefik" {
	targets = concat(
		[{
			__address__ = "{{ env "node.unique.name" }}.nsw.education:8081",
		}],
	)

	rule {
		target_label = "instance"
		replacement  = "{{ env "node.unique.name" }}"
	}
	rule {
		target_label = "DC"
		replacement  = "dc-cir-un-test"
	}
	rule {
		target_label = "job"
		replacement  = "{{env "NOMAD_JOB_NAME"}}"
	}     
}

prometheus.scrape "metrics_integrations_integrations_traefik" {
	targets    = discovery.relabel.metrics_integrations_integrations_traefik.output
	forward_to = [prometheus.remote_write.metrics_service.receiver]
	job_name   = "integrations/traefik"
}
