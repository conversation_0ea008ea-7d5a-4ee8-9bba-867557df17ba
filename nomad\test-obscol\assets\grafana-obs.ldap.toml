# To troubleshoot and get more log info enable ldap debug logging in grafana.ini
#[[log]]
# filters = ldap:debug
# verbose_logging = true

[[servers]]
# Ldap server host (specify multiple hosts space separated)
host = "adldapnsw.det.nsw.edu.au"
# Default port is 389 or 636 if use_ssl = true
port = 636
# Set to true if ldap server supports TLS
use_ssl = true
# Set to true if connect ldap server with STARTTLS pattern (create connection in insecure, then upgrade to secure connection with TLS)
# start_tls = true
# set to true if you want to skip ssl cert validation
ssl_skip_verify = true
# set to the path to your root CA certificate or leave unset to use system defaults
 root_ca_cert = "/etc/pki/CA/certs/EXT_PROD_CHAIN.crt"

# Search user bind dn
bind_dn = "CN=sys-zbx-app-ro-ldap,OU=Applications,OU=Service Accounts,OU=Management,DC=DETNSW,DC=WIN"
# Search user bind password
# If the password contains # or ; you have to wrap it with trippel quotes. Ex """#password;"""
bind_password = '$I$54n7&9Sj9GS1W1gq'

# User search filter, for example "(cn=%s)" or "(sAMAccountName=%s)" or "(uid=%s)"
search_filter = "(sAMAccountName=%s)"

# An array of base dns to search through
#search_base_dns = ["OU=Accounts,OU=Corporate Offices,DC=DETNSW,DC=WIN"]
search_base_dns = ["DC=DETNSW,DC=WIN"]

# In POSIX LDAP schemas, without memberOf attribute a secondary query must be made for groups.
# This is done by enabling group_search_filter below. You must also set member_of= "cn"
# in [servers.attributes] below.

# Users with nested/recursive group membership and an LDAP server that supports LDAP_MATCHING_RULE_IN_CHAIN
# can set group_search_filter, group_search_filter_user_attribute, group_search_base_dns and member_of
# below in such a way that the user's recursive group membership is considered.
#
# Nested Groups + Active Directory (AD) Example:
#
#   AD groups store the Distinguished Names (DNs) of members, so your filter must
#   recursively search your groups for the authenticating user's DN. For example:
#
#     group_search_filter = "(member:1.2.840.113556.1.4.1941:=%s)"
#     group_search_filter_user_attribute = "distinguishedName"
#     group_search_base_dns = ["ou=groups,dc=grafana,dc=org"]
#
#     [servers.attributes]
#     ...
#     member_of = "distinguishedName"

## Group search filter, to retrieve the groups of which the user is a member (only set if memberOf attribute is not available)
# group_search_filter = "(&(objectClass=posixGroup)(memberUid=%s))"
## Group search filter user attribute defines what user attribute gets substituted for %s in group_search_filter.
## Defaults to the value of username in [server.attributes]
## Valid options are any of your values in [servers.attributes]
## If you are using nested groups you probably want to set this and member_of in
## [servers.attributes] to "distinguishedName"
# group_search_filter_user_attribute = "distinguishedName"
## An array of the base DNs to search through for groups. Typically uses ou=groups
# group_search_base_dns = ["ou=groups,dc=grafana,dc=org"]

# Specify names of the ldap attributes your ldap uses
[servers.attributes]
name = "givenName"
surname = "sn"
username = "sAMAccountName"
member_of = "memberOf"
email =  "mail"

# Map ldap groups to grafana org roles
#[[servers.group_mappings]]
#group_dn = "cn=admins,dc=grafana,dc=org"
#org_role = "Admin"
# The Grafana organization database id, optional, if left out the default org (id 1) will be used
# org_id = 1

#[[servers.group_mappings]]
#group_dn = "cn=users,dc=grafana,dc=org"
#org_role = "Editor"

#[[servers.group_mappings]]
# If you want to match all (or no ldap groups) then you can use wildcard
#group_dn = "*"
#org_role = "Viewer"

