auth_enabled: false

server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

common:
  compactor_address: http://loki.obs.test.nsw.education
  path_prefix: /loki
  replication_factor: 3
  # Tell Loki which address to advertise
  instance_addr: {{ env "NOMAD_IP_grpc" }}
  ring:
    # Tell Loki which address to advertise in ring
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    instance_port: {{ env "NOMAD_PORT_grpc "}}
    kvstore:
      store: consul
      prefix: loki/
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  wal:
    enabled: true
    dir: /loki/tsdb/{{ env "node.unique.name" }}/data/wal
    flush_on_shutdown: true
    #replay_memory_ceiling: "1G" #defaut is 4G

schema_config:
  configs:
  - from: 2022-05-15
    store: boltdb-shipper
    object_store: s3
    schema: v12
    index:
      prefix: index_
      period: 24h
  - from: 2023-07-01
    store: tsdb
    object_store: s3
    schema: v13
    index:
      prefix: index_
      period: 24h

storage_config:
  tsdb_shipper:
    # Nomad ephemeral disk is used to store index and cache
    # it will try to preserve /alloc/data between job updates
    active_index_directory: /loki/tsdb/{{ env "node.unique.name" }}/data/data/index
    cache_location: /loki/tsdb/{{ env "node.unique.name" }}/data/data/index-cache
    #shared_store: s3
  aws:
    bucketnames: "nswdoe-obs-loki-blocks-storage-dev"
    access_key_id: ********************
    secret_access_key: ZapKZfBPOjFllvF86XK4jap1/8Thk+IUPQFaLBX9
    #s3forcepathstyle: true
    region: "ap-southeast-2"
    insecure: false
    #sse_encryption: true

limits_config:
  #enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  allow_structured_metadata: true


compactor:
  working_directory: /loki/tsdb/{{ env "node.unique.name" }}/data/compactor
  #shared_store: s3
  compaction_interval: 5m
  delete_request_store: s3
  retention_enabled: false

# ruler:
#   alertmanager_url: https://alertmanager.obs.test.nsw.education
#   enable_alertmanager_v2: true
#   enable_api: true
#   external_url: https://loki.obs.test.nsw.education
#   rule_path: {{ env "NOMAD_ALLOC_DIR" }}/tmp/rules
#   storage:
#     type: local
#     local:
#       directory: {{ env "NOMAD_TASK_DIR" }}/rules
#   wal:
#     dir: {{ env "NOMAD_ALLOC_DIR" }}/data/ruler