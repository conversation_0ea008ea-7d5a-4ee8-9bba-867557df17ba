multitenancy_enabled: true

activity_tracker:
  filepath: ""

server:
  # available: [debug, info, warn, error]
  log_level: "debug"

  graceful_shutdown_timeout: "30s"
  http_listen_port: {{ env "NOMAD_PORT_http"}}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

alertmanager:
  data_dir: /mimir/alertmanager/

alertmanager_storage:
  backend: "filesystem"
  local:
    path: "/mimir/alertmanager/"

distributor:
  ring:
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

ingester:
  ring:
    replication_factor: 2
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

# frontend_worker:
#   scheduler_address: "mimir-rwb-backend.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
#   id: {{ env "node.unique.name" }}
#   grpc_client_config:
#     grpc_compression: "snappy"

frontend:
  # results_cache:
  #   backend: redis
  #   redis:
  #     endpoint: mimir-rwb-redis.obs.test.nsw.education
  #     db: 2  
  scheduler_address: "mimir-rwb-backend.service.dc-cir-un-test.collectors.obs.test.nsw.education:8096"
  address: {{ env "NOMAD_IP_grpc" }}
  log_queries_longer_than: "5s"
  grpc_client_config:
    grpc_compression: "snappy"

query_scheduler:
  service_discovery_mode: "ring"
  ring:
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  grpc_client_config:
    grpc_compression: "snappy"

ruler:
  enable_api: true
  rule_path: /mimir/tmp/
  external_url: "https://mimir-rwb-backend.obs.test.nsw.education/prometheus/api/v1/rules"
  poll_interval: "1m"
  evaluation_interval: "1m"
  alertmanager_url: "https://alertmanager.obs.test.nsw.education"
  query_frontend:
    address: "https://mimir-rwb-read.obs.test.nsw.education"
    grpc_client_config: 
      grpc_compression: "snappy"    

ruler_storage:
  storage_prefix: "rules"

compactor:
  data_dir: /mimir/data/compactor/
  sharding_ring:
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

store_gateway:
  sharding_ring:
    replication_factor: 1
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_id: {{ env "node.unique.name" }}
    kvstore:
      store: "consul"
      prefix: "mimir/"
      consul:
        host: {{ env "attr.unique.network.ip-address" }}:8500

blocks_storage:
  backend: s3
  bucket_store:
    sync_dir: /mimir/tsdb-sync/{{ env "node.unique.name" }}/
    # index_cache:
    #   backend: redis
    #   redis:
    #     endpoint: mimir-rwb-redis.obs.test.nsw.education
    #     db: 0
    # chunks_cache:
    #   backend: redis
    #   redis:
    #     endpoint: mimir-rwb-redis.obs.test.nsw.education
    #     db: 1  
  tsdb: #Required to be persistent between restarts
    dir: /mimir/tsdb/{{ env "node.unique.name" }}/
    flush_blocks_on_shutdown: true
    memory_snapshot_on_shutdown: true

common:
  storage:
    backend: s3
    s3:
      access_key_id:  ********************
      secret_access_key: +DIkXJNeraesZiAlo6ZGkUUU83dF6X2bT5UNU7Py

      region: ap-southeast-2
      bucket_name: nswdoe-obs-mimir-blocks-storage-dev
      endpoint: s3.ap-southeast-2.amazonaws.com
      insecure: true

      # 2024-11 jedd - this needs Mimir 2.14.x or later
      dualstack_enabled: false


limits:
  compactor_blocks_retention_period: "25w"
  accept_ha_samples: true
  max_global_series_per_user: 0
  max_label_names_per_series: 200
  out_of_order_time_window:  5m
