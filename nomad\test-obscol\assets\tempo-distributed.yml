server:
  log_level: info
  http_listen_port: {{ env "NOMAD_PORT_http" }}
  grpc_listen_port: {{ env "NOMAD_PORT_grpc" }}

distributor:
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        # Use Nomad's template function to get the host's IP address.
        # This assumes Consul is running on the same host as the Tempo component.
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  receivers:
    # This configuration will listen on all ports and protocols that Tempo is capable of.
    jaeger:
      protocols:
        thrift_http:
          #endpoint: 0.0.0.0:{{ env "NOMAD_PORT_jaeger" }}
    zipkin:
      #endpoint: 0.0.0.0:{{ env "NOMAD_PORT_zipkin" }}
    otlp:
      protocols:
        http:
          #endpoint: 0.0.0.0:{{ env "NOMAD_PORT_otlp" }}
        grpc:
          #endpoint: 0.0.0.0:{{ env "NOMAD_PORT_otlp" }}
    opencensus:

ingester:
  max_block_duration: 30m
  lifecycler:
    ring:
      kvstore:
        store: consul
        prefix: tempo/
        consul:
          # Use Nomad's template function to get the host's IP address.
          host: {{ env "attr.unique.network.ip-address" }}:8500
      replication_factor: 2
      

compactor:
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        # Use Nomad's template function to get the host's IP address.
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  compaction:
    compaction_window: 1h
    max_block_bytes: 107374182400
    block_retention: 336h

querier:
  frontend_worker:
    # This should be a service name resolvable via Consul, as your example shows.
    # No need for env var expansion here unless 'tempo-query-frontend-grpc' is dynamic.
    frontend_address: tempo-querier.obs.test.nsw.education:9095

metrics_generator:
  processor:
    service_graphs:
      max_items: 5000
  registry:
    external_labels:
      source: tempo
      cluster: obscol-prod
  ring:
    kvstore:
      store: consul
      prefix: tempo/
      consul:
        # Use Nomad's template function to get the host's IP address.
        host: {{ env "attr.unique.network.ip-address" }}:8500
    instance_id: {{ env "node.unique.name" }}
    instance_addr: {{ env "NOMAD_IP_grpc" }}
    instance_port: {{ env "NOMAD_PORT_grpc" }}
  storage:
    # Use Nomad's template function for the allocation directory.
    path: /tempo_data
    remote_write:
      - url: https://mimir-rwb-write.obs.test.nsw.education/api/v1/push
        headers:
          X-Scope-OrgID: prod
        tls_config:
          insecure_skip_verify: true
        send_exemplars: true

storage:
  trace:
    backend: s3
    wal:
      # Use Nomad's template function for the allocation directory.
      path: /tempo_data/wal
    local:
      # Use Nomad's template function for the allocation directory.
      path: /tempo_data/blocks
    s3:
      bucket: nswdoe-obs-tempo-blocks-storage-dev
      endpoint: s3.ap-southeast-2.amazonaws.com
      # These variables are read from the environment by Tempo because Nomad
      # generates the secrets/s3.env file which is loaded via 'env = true' in the task.
      # So, no need for Nomad's template functions here, just plain environment var reference.
      access_key: ${S3_ACCESS_KEY_ID}
      secret_key: ${S3_SECRET_ACCESS_KEY}
    pool:
      max_workers: 400
      queue_depth: 20000

overrides:
  metrics_generator_processors: [service-graphs, span-metrics]