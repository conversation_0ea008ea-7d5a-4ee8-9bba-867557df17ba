# Grafana LGTM stack prometheus instance for TEST environment (obscol)

job "collector-app-observability" {
  type = "service"
  datacenters = ["dc-cir-un-test"]
  namespace = "collectors"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "prometheus-agent" {
    count = 1

    network {
      port "http" {}
    }

    task "prometheus-app-observability" {
      driver = "docker"
      kill_signal = "SIGTERM"

      config {
        ports = ["http"]
        image = "prom/prometheus:v2.44.0"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus-app-observability.obs.test.nsw.education",
          "--web.page-title=Prometheus for the Observability Platform on DoE ObsCol TEST cluster",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=examplar-storage",
          "--enable-feature=agent",
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-app-observability"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-observability.rule=Host(`prometheus-app-observability.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-app-observability.tls=false",
          "traefik.http.routers.prometheus-app-observability.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    provenance: "obscol-prometheus-app-observability"

  scrape_interval: 60s

scrape_configs:
  - job_name: 'prometheus-app-observability'
    static_configs:
      - targets: ['prometheus-app-observability.obs.test.nsw.education']

  - job_name: 'nomad_metrics'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['nomad-client', 'nomad']
        tags: ['http']

  - job_name: 'mimir_metrics'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['mimir']

  - job_name: 'tempo_metrics'
    static_configs:
      - targets: ['tempo.obs.test.nsw.education']
    scheme: 'https'
    metrics_path: /metrics

  - job_name: 'loki_metrics'
    static_configs:
      - targets: ['loki.obs.test.nsw.education']
    scheme: 'https'
    metrics_path: /metrics

  - job_name: 'phlare_metrics'
    static_configs:
      - targets: ['phlare.obs.test.nsw.education']
    scheme: 'https'
    metrics_path: /metrics

remote_write:
  - name: mimir
    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
    headers: 
        X-Scope-OrgID: test
    tls_config:
        insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 500
        memory = 1024
      }

    }
  }
}
