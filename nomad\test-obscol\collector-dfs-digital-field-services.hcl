
# collector-dfs   -  TEST ObsCol

# Combined standalone prometheus + blackbox exporter for Digital Field Services
# refer:  https://jira.education.nsw.gov.au/browse/OBS-573

job "collector-dfs" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-dfs" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" {
        # to = 9090 
      }
      port "port_blackbox" { 
        # to = 9115
      }
  	}


    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-prometheus-dfs" {
      driver = "docker"

      config {
        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          # "--enable-feature=agent",
          "--web.external-url=http://collector-dfs-prometheus.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Digital Field Services Collector",
        ]

        image = "https://docker.io/prom/prometheus:v2.42.0"

        logging {
          type = "loki"
          config {
            loki-url = "https://loki-s3.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        ports = [ "port_prometheus" ]

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/ultimo.yaml:/etc/prometheus/ultimo.yaml",
          # "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]
      }

      resources {
        cpu    = 256
        memory = 256
      }

      service {
        name = "collector-dfs-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-prometheus.rule=Host(`collector-dfs-prometheus.obs.test.nsw.education`)",
          "traefik.http.routers.collector-dfs-prometheus.tls=false",
          "traefik.http.routers.collector-dfs-prometheus.entrypoints=http",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "collector-dfs"

  scrape_interval: 2m

scrape_configs:
  # Job to scrape this instance of prometheus (ie. self)
  - job_name: 'prometheus-dfs'
    static_configs:
      - targets: ['collector-dfs-prometheus.obs.test.nsw.education']

    # Drop surplus blackbox series
    # approx 2550 prometheus_tsdb_head_series on this instance before
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  # Job to scrape this instance of blackbox (ie. self)
  - job_name: 'blackbox-dfs'
    static_configs:
      - targets: ['collector-dfs-blackbox.obs.test.nsw.education']
    # Drop surplus blackbox series
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: go_(.*)
        action: drop

  - job_name: 'dfs-pinger'
    metrics_path: /probe
    params:
      module: ["icmp"]
    file_sd_configs:
    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
    # Will be (more) useful when we separate out file_sd_config entries.
    - refresh_interval: 5m
      files: 
      - "/etc/prometheus/ultimo.yaml"
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: collector-dfs-blackbox.obs.test.nsw.education

    metric_relabel_configs:
      # Drop surplus blackbox series.  We receive this set from blackbox with icmp / ping target:
      #   probe_dns_lookup_time_seconds
      #   probe_duration_seconds
      #   probe_icmp_duration_seconds{phase="resolve"}
      #   probe_icmp_duration_seconds{phase="rtt"}
      #   probe_icmp_duration_seconds{phase="setup"}
      #   probe_icmp_reply_hop_limit
      #   probe_ip_addr_hash
      #   probe_ip_protocol
      #   probe_success
      # But we really only care about probe_success, so we'll drop everything else.

#      - source_labels: [__name__]
#        regex: scrape_(.*)
#        action: drop
#
      - source_labels: [__name__]
        regex: (probe_success)
        action: keep

#remote_write:
#  - name: mimir
#    url: "https://mimir-distributor.obs.test.nsw.education/api/v1/push"
#    headers: 
#      X-Scope-OrgID: test
#    tls_config:
#      insecure_skip_verify: true


EOH
        destination = "local/prometheus.yaml"
      }

      template {
        data = file("assets/collector-dfs-file-sd-config.yaml")
        destination = "local/ultimo.yaml"
      }

    }


    # TASK blackbox for dfs   = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-blackbox-dfs" {
      driver = "docker"

      config {
        ports = [ "port_blackbox" ]

        image = "prom/blackbox-exporter:v0.22.0"

        dns_servers = [ "************" ]

        args = [
          "--config.file",    "local/config.yml",
          "--web.listen-address=:${ NOMAD_PORT_port_blackbox }",
          # log.level = [ debug, info, warn, error ]
          "--log.level",      "warn"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }          

      }

      env {
        # Should not be needed as all endpoints are internal and 10/8 range.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      resources {
        cpu    = 200
        memory = 256
      }


      service {
        name = "collector-dfs-blackbox"
        port = "port_blackbox"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-dfs-blackbox.rule=Host(`collector-dfs-blackbox.obs.test.nsw.education`)",
          "traefik.http.routers.collector-dfs-blackbox.tls=false",
          "traefik.http.routers.collector-dfs-blackbox.entrypoints=http",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
        }

        check {
          type = "http"
          port = "port_blackbox"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
modules:
  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: ipv4

EOH
        destination = "local/config.yml"
      }

    }

  }

}


