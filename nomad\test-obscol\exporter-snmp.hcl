# Prometheus SNMP exporter for ObsCol TEST env
# We use the assets from repo: prometheus-configuration / snmp / 

variables {
  image_snmp = "quay.education.nsw.gov.au/observability/prom/snmp-exporter:test-obscol"
}

job "exporter-snmp" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "exporter-snmp" {
    network {
      port "port-exporter-snmp" {
        to = 9116 //default
      }
    }

    task "exporter-snmp" {
      driver = "docker"

      config {
        image = var.image_snmp
        ports = ["port-exporter-snmp"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        # 2023-03-29 - removed as part of consul / roundrobin resolution
        # dns_servers = [ "192.168.31.1" ]

        volumes = [
          "/opt/sharednfs/prometheus-configuration/snmp/snmp-enlogic-pdu.yml:/etc/snmp_exporter/snmp.yml"
        ]

      }

      service {
        name = "exporter-snmp"
        port = "port-exporter-snmp"
//
//        check {
//          type = "http"
//          port = "port-exporter-snmp"
//          path = "/"
//          interval = "20s"
//          timeout = "10s"
//        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.exporter-snmp.rule=Host(`exporter-snmp.obs.test.nsw.education`)",
          "traefik.http.routers.exporter-snmp.tls=false",
          "traefik.http.routers.exporter-snmp.entrypoints=http",
        ]

      }


    }
  }
}
