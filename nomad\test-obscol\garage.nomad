
# Garage - an s3-compatible block-storage system using high-availability clustering

# Authors recommend at least 4 - we have 6 nodes in the cluster (2024-07) so we'll 
# use all 6 at this time.  

# Alternative to minio - which had a higher complexity to setup, and whose licence
# changes in 2023 / 2024 were a concern. Minio was preferred in that it uses the
# native file system as a mapping to the S3 'file system', but evidently that was
# being changed in newer versions of Minio anyway.

# Refer:
#   https://garagehq.deuxfleurs.fr/documentation/quick-start/
# also:
#   garage-README.md adjacent to this nomad job file


# WORKFLOW - first time only
#   PREREQUISITES
#     mkdir      /opt/sharednfs/garage/tl0992obscol0[12345]
#     chmod 777  /opt/sharednfs/garage/tl0992obscol0[12345]
#
#   Launch nomad job
# 
#   Configure garage on EACH participating node
#     On the first node (tl0992obscol01) ssh and then:
#       Run:
#         docker ps | grep garage
#       output:   54d767b929d8   quay.education.nsw.gov.au/observability/garage:v1.0.0 
#
#       Take a note of that container ID
#
#       Run:
#         alias garage="docker exec -ti 54d767b929d8 /garage"
#
#       You can then obtain the first host/node's 'node id':
#         garage node id
#       output: 6bde2d2d33b2218296cdc59e7af518c88cd6f12a1e1eb5f4e3a97b074f14c2ba@************:3901
#
#       Repeat the garage aliasing on each other host in the obscol cluster, this time
#       telling each node to connect to the node-id of node obscol-01.
#
#       For example, on tl0992obscol02:
#         garage node connect 6bde2d2d33b2218296cdc59e7af518c88cd6f12a1e1eb5f4e3a97b074f14c2ba@************:3901
#       Repeat for obscol03, 04, 05.
#
#       On any host, at any time, run:
#         garage status
#       output:
#         tl0992obscol05:~# docker exec -ti 50006 /garage status
#         2024-07-26T08:30:07.612330Z  INFO garage_net::netapp: Connected to ************:3901, negotiating handshake...
#         2024-07-26T08:30:07.656205Z  INFO garage_net::netapp: Connection established to 75a81b8ba22fc35e
#         ==== HEALTHY NODES ====
#         ID                Hostname      Address             Tags  Zone  Capacity          DataAvail
#         b7c5636aa5d7fe1e  22f652b6edb2  ************:3901               NO ROLE ASSIGNED
#         75a81b8ba22fc35e  50006f7ea928  ************:3901               NO ROLE ASSIGNED
#         103973e5855e4a23  399ada8c8203  *************:3901              NO ROLE ASSIGNED
#         75e86460246f74d5  4548779d5022  *************:3901              NO ROLE ASSIGNED
#         6bde2d2d33b22182  54d767b929d8  ************:3901               NO ROLE ASSIGNED
#
#       Data for each node will be stored in /opt/sharednfs/garage/{hostname}/data


#       Use the output of 'garage status' to show the ID of each node

#       Prepare a layout of 100G allocated on each node, using zone 'obscol'
#         for i in 103973e5855e4a23 75a81b8ba22fc35e 6bde2d2d33b22182 75e86460246f74d5 b7c5636aa5d7fe1e
#         do
#         garage layout assign ${i} -z obscol -c 100G
#         done


#       Then apply the layout:
#         tl0992obscol01:~# garage layout apply --version 1
#         2024-07-26T08:56:24.679840Z  INFO garage_net::netapp: Connected to ************:3901, negotiating handshake...
#         2024-07-26T08:56:24.723959Z  INFO garage_net::netapp: Connection established to 6bde2d2d33b22182
#         2024-07-26T08:56:24.828128Z  INFO garage_rpc::layout::history: Layout history: pruning old invalid version 0
#         ==== COMPUTATION OF A NEW PARTITION ASSIGNATION ====
#
#         Partitions are replicated 3 times on at least 1 distinct zones.
#
#         Optimal partition size:                     649.4 MB
#         Usable capacity / total cluster capacity:   498.7 GB / 500.0 GB (99.7 %)
#         Effective capacity (replication factor 3):  166.2 GB
#
#         obscol              Tags  Partitions        Capacity  Usable capacity
#           103973e5855e4a23        154 (154 new)     100.0 GB  100.0 GB (100.0%)
#           6bde2d2d33b22182        154 (154 new)     100.0 GB  100.0 GB (100.0%)
#           75a81b8ba22fc35e        154 (154 new)     100.0 GB  100.0 GB (100.0%)
#           75e86460246f74d5        154 (154 new)     100.0 GB  100.0 GB (100.0%)
#           b7c5636aa5d7fe1e        152 (152 new)     100.0 GB  98.7 GB (98.7%)
#           TOTAL                   768 (256 unique)  500.0 GB  498.7 GB (99.7%)
#
#         New cluster layout with updated role assignment has been applied in cluster.
#         Data will now be moved around between nodes accordingly.


# This complets all the once-off initialising tasks.

# For operational activities, refer the wiki - 
#   https://nsw-education.atlassian.net/wiki/spaces/PM/pages/294849162/Garage+-+block+storage



# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_garage = "dxflrs/garage:v1.0.0"
  image_garage = "quay.education.nsw.gov.au/observability/garage:v1.0.0"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "garage" {

  datacenters = ["dc-cir-un-test"]
  namespace = "default"

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

 # Because we have a sub-dir (on the target volume) that has to match the
 # 'unique hostname' we don't want to accidentally deploy on later additions
 # to the test cluster.
 constraint {
   attribute = "${attr.unique.hostname}"
   operator = "regexp"
   value = "tl0992obscol0[12345]"
  }


# Group garage  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "garage" {

    count = 5
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    network {
      # We don't use Traefik - instead we use static ports, and static task-per-host.
      port "port_3900" {
        static = 3900
      }
      port "port_3901" {
        static = 3901
      }
      port "port_3902" {
        static = 3902
      }
      port "port_3903" {
        static = 3903
      }
      port "port_3904" {
        static = 3904
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "garage-task" {
      driver = "docker"

      env = {
      }

      config {
        image = var.image_garage

        ports = ["port_3900", "port_3901", "port_3902", "port_3903", "port_3904"]

        args = [ 
        ]

        volumes = [
          "local/garage-cluster.toml:/etc/garage.toml",
          "/opt/sharednfs/garage:/persistent/"
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }        

      }

      resources {
        cpu = 500
        memory = 1000
        memory_max = 2000
      }



      template {
        data = <<EOH
# metadata_dir = "/var/lib/garage/meta"
# data_dir = "/var/lib/garage/data"

metadata_dir = "/persistent/{{ env "attr.unique.hostname" }}/meta"
data_dir = "/persistent/{{ env "attr.unique.hostname" }}/data"

db_engine = "lmdb"

replication_mode = "3"

compression_level = 2

rpc_bind_addr = "[::]:3901"
#rpc_public_addr = "<this node's public IP>:3901"
rpc_public_addr = "{{ env "NOMAD_IP_port_3901" }}:3901"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "tobscol"
api_bind_addr = "[::]:3900"
root_domain = ".s3.tobscol"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.tobscol"
index = "index.html"

EOH
        destination = "local/garage-cluster.toml"
        perms = "755"
      }


      # THIS IS UNUSED - retained for reference.
      # The volume stanza (above) calls in the garage-cluster.toml as THE config
      # This is the UNUSED garage-single example configuration
      template {
        data = <<EOH
metadata_dir = "/tmp/meta"
data_dir = "/tmp/data"
db_engine = "lmdb"

replication_mode = "none"

rpc_bind_addr = "[::]:3901"
rpc_public_addr = "127.0.0.1:3901"
# rpc_secret = "$(openssl rand -hex 32)"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage.localhost"
index = "index.html"

[k2v_api]
api_bind_addr = "[::]:3904"

[admin]
api_bind_addr = "0.0.0.0:3903"
admin_token = "$(openssl rand -base64 32)"


EOH
        destination = "local/garage-single.toml"
        perms = "755"
      }

      service {
        name = "garage"
        port = "port_3902"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.garage.rule=Host(`garage.obs.test.nsw.education`)",
          "traefik.http.routers.garage.entrypoints=https",
          "traefik.http.routers.garage.tls=false",
        ]

#        check {
#          name     = "garage_alive"
#          type     = "http"
#          port     = "port_console"
#          path     = "/m_aliveinio/health/live"
#          interval = "30s"
#          timeout  = "5s"
#        }

      }

    }

  }
}
