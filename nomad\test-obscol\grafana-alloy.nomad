group "collector-group-redis" {
    constraint {
        operator  = "distinct_hosts"
        value     = "true"
    }
    count = 1

    ephemeral_disk {
      migrate = true
      size    = 500
      sticky  = true
    }
    
    network {
        port "http" {
        }
    }

    task "grafana-alloy-task-redis" {
      driver = "docker"

      config {
        privileged = true
        image = "quay.education.nsw.gov.au/observability/grafana-alloy:test-obscol"
        ports = ["http"]
        entrypoint = [
            "/bin/alloy",
            "run",
            "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
            "--server.http.enable-pprof=true",
            "/local/config.alloy"
            ]
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=collector-grafana-alloy-test"
                }
            }
         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro",
           "/var/run/docker.sock:/var/run/docker.sock"
         ]
      }
      env {
      HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
      NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
    }
      resources {
        cpu    = 500
        memory = 2048

      }

      service {
        name = "grafana-alloy-collector-http"
        port = "http"
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-http.rule=Host(`grafana-alloy-http.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-alloy-http.tls=false",
          "traefik.http.routers.grafana-alloy-http.entrypoints=http,https",
        ]        
      }    
      template {
        data        = file("assets/grafana-alloy-redis.alloy")
        destination = "local/config.alloy"
      }
    }    
  }
group "collector-group-otlp" {
    constraint {
        operator  = "distinct_hosts"
        value     = "true"
    }
    count = 1

    ephemeral_disk {
      migrate = true
      size    = 500
      sticky  = true
    }
    
    network {
        port "http" {
        }
        port "otlp" {
        }
    }

    task "grafana-alloy-task-otlp" {
      driver = "docker"

      config {
        privileged = true
        image = "quay.education.nsw.gov.au/observability/grafana-alloy:test-obscol"
        ports = ["http","otlp"]
        entrypoint = [
            "/bin/alloy",
            "run",
            "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
            "--server.http.enable-pprof=true",
            "/local/config.alloy"
            ]
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=collector-grafana-alloy-test"
                }
            }
         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro",
           "/var/run/docker.sock:/var/run/docker.sock"
         ]
      }
      env {
      HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
      NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
    }
      resources {
        cpu    = 500
        memory = 2048

      }
      service {
        name = "grafana-alloy-collector-http"
        port = "http"
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-http.rule=Host(`grafana-alloy-http.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-alloy-http.tls=false",
          "traefik.http.routers.grafana-alloy-http.entrypoints=http,https",
        ]        
      }
      service {
        name = "grafana-alloy-collector-otlp"
        port = "otlp"       
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-http.rule=Host(`otlp.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-alloy-http.tls=false",
          "traefik.http.routers.grafana-alloy-http.entrypoints=http,https",
        ]        
      }         
      template {
        data        = file("assets/grafana-alloy-otlp.alloy")
        destination = "local/config.alloy"
      }
    }    
  }
group "collector-group-jaeger" {
    constraint {
        operator  = "distinct_hosts"
        value     = "true"
    }
    count = 1

    ephemeral_disk {
      migrate = true
      size    = 500
      sticky  = true
    }
    
    network {
        port "http" {
        }
        port "jaeger" {
            to = 14268
        }
    }

    task "grafana-alloy-task-jaeger" {
      driver = "docker"

      config {
        privileged = true
        image = "quay.education.nsw.gov.au/observability/grafana-alloy:test-obscol"
        ports = ["http","jaeger"]
        entrypoint = [
            "/bin/alloy",
            "run",
            "--server.http.listen-addr=0.0.0.0:${NOMAD_PORT_http}",
            "--server.http.enable-pprof=true",
            "/local/config.alloy"
            ]
        logging {
            type = "loki"
            config {
                loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=collector-grafana-alloy-test"
                }
            }
         volumes = [
           "local:/var/lib/alloy/:rw",
           "local/config.alloy:/etc/alloy/config.yaml:ro",
           "/var/run/docker.sock:/var/run/docker.sock"
         ]
      }
      env {
      HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
      NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
    }
      resources {
        cpu    = 500
        memory = 2048

      }
      service {
        name = "grafana-alloy-collector-http"
        port = "http"
        check {
          type     = "http"
          path     = "/"
          interval = "10s"
          timeout  = "2s"
        }        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-http.rule=Host(`grafana-alloy-http.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-alloy-http.tls=false",
          "traefik.http.routers.grafana-alloy-http.entrypoints=http,https",
        ]        
      }
      service {
        name = "grafana-alloy-collector-jaeger"
        port = "jaeger"       
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-alloy-http.rule=Host(`jaeger.obs.test.nsw.education`)",
          "traefik.http.routers.grafana-alloy-http.tls=false",
          "traefik.http.routers.grafana-alloy-http.entrypoints=http,https",
        ]        
      }         
      template {
        data        = file("assets/grafana-alloy-jaeger.alloy")
        destination = "local/config.alloy"
      }
    }    
  }
