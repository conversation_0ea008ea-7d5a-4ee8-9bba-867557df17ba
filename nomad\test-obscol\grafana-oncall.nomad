# GRAFANA OBS TEST IS CURRENTLY RUNNING FROM /test-obscol/nomad , NOT TERRAFROM


# Grafana ONCALL for test-obscol

variables {
  image_oncall = "quay.education.nsw.gov.au/observability/oncall:test-obscol"
}

job "grafana-oncall" {
  datacenters = ["dc-cir-un-test"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  # While testing, speed-up iterations by constraining to single host
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "tl0992obscol0[3]"
  }

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "oncall" {

    network {
      port "port_oncall" {
        static = 8090
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

#    volume "vol_oncall" {
#      type = "host"
#      source = "vol_oncall"
#      read_only = false
#    }


    ephemeral_disk {
      size = 300
    }

    task "oncall" {
      driver = "docker"

#      volume_mount {
#        volume      = "vol_oncall"
#        destination = "/data"
#        read_only   = false
#      }

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true",
#        "NODE_RED_ENABLE_PROJECTS" = "true",

        "DOMAIN" = "http://tl0992obscol03.nsw.education:8090",
        "SECRET_KEY" = "my_random_secret_must_be_more_than_32_characters_long",
        "RABBITMQ_PASSWORD" = "rabbitmq_secret_pw",
        "MYSQL_PASSWORD" = "mysql_secret_pw",

        # Remove this line if you want to use existing grafana
        "COMPOSE_PROFILES" = "with_grafana",

        "GRAFANA_USER" = "admin",
        "GRAFANA_PASSWORD" = "bigsecret" 
      }

      config {
        image = var.image_oncall
        ports = ["port_oncall"]
      }

      resources {
        cpu    = 500
        memory = 4096
      }

      service {
        name = "oncall"
        port = "port_oncall"
      }

      service {
        name = "grafana-oncall"
        port = "port_oncall"

        tags = [
           "traefik.enable=true",
           "traefik.http.grafana-oncall.rule=Host(`grafana-oncall.obs.test.nsw.education`)",
           "traefik.http.grafana-oncall.oncall.tls=false",
        ]

        check {
          name     = "oncall"
          type     = "http"
          port     = "port_oncall"
          path     = "/oncall/health/live"
          interval = "30s"
          timeout  = "5s"
        }
      }


    }

  }
}
