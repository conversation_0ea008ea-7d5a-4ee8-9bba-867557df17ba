
# generic debian / network tools container for TEST obscol

# mostly used by jedd. others may join the party.

# Needs the command / args to sleep indefinitely otherwise it bombs out immediately.
# Stolen then heavily modified from https://github.com/leowmjw/nomad-box

# designed to spin up on every host - just because that's much more convenient.

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image = "debian:stable"
  # image = "debian:unstable"
  # image = "ubuntu:latest"

  # image = "jonlabelle/network-tools"
  image = "quay.education.nsw.gov.au/observability/jonlabelle-network-tools:2025-04-13"
}
 

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "jedd-nettools" {

  datacenters = ["dc-cir-un-test"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  # Task   = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "jedd-nettools-01" {

    network {
      port "port_nettools" {
        to = 2222
      }
    }

    count = 1

    ephemeral_disk {
      size = 300
    }


    # Task 01  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "jedd-nettools-01" {
      driver = "docker"

      constraint {
        attribute = "${attr.unique.hostname}"
        operator = "regexp"
        value = "tl0992obscol0[1]"
      }

      config {
        image = "${var.image}"
        ports = ["port_nettools"]
        command = "/bin/sleep"
        args = [" infinity"]
        volumes = [
          "local/:/root/"
          ]
        }

      resources {
        cpu    = 500
        memory = 2048
      }

      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]jedd-nettools-01:\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }


      service {
        name = "jedd-nettools-01"
        port = "port_nettools"

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.jedd-nettools-01.rule=Host(`jedd-nettools-01.obs.test.nsw.education`)",
#          "traefik.http.routers.jedd-nettools-01.tls=false",
#          "traefik.http.routers.jedd-nettools-01.entrypoints=http,https",
#        ]
      }
    } // end-task 01

  }

  # Task   = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "jedd-nettools-07" {

    network {
      port "port_nettools" {
        to = 2222
      }
    }

    count = 1

    ephemeral_disk {
      size = 300
    }


    # Task 07  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "jedd-nettools-07" {
      driver = "docker"

      constraint {
        attribute = "${attr.unique.hostname}"
        operator = "regexp"
        value = "tl0475obscol0[7]"
      }

      config {
        image = "${var.image}"
        ports = ["port_nettools"]
        command = "/bin/sleep"
        args = [" infinity"]
        volumes = [
          "local/:/root/"
          ]
        }

      resources {
        cpu    = 500
        memory = 2048
      }


      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]jedd-nettools-07:\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
      }


      service {
        name = "jedd-nettools-07"
        port = "port_nettools"

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.jedd-nettools-07.rule=Host(`jedd-nettools-07.obs.test.nsw.education`)",
#          "traefik.http.routers.jedd-nettools-07.tls=false",
#          "traefik.http.routers.jedd-nettools-07.entrypoints=http,https",
#        ]
      }
    } // end-task 07





  }
}
