# k6 load test - obscol TEST 

job "k6" {
  datacenters = ["dc-cir-un-test"]
  type        = "service"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "k6" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    #volume "vol_k6" {
    #  type = "host"
    #  source = "vol_k6"
    #  read_only = false
    #}

    network {
      port "port_k6"  { 
        to = 6565
      }
    }

    task "k6" {

      driver = "docker"


      #volume_mount {
      #  volume      = "vol_k6"
      #  destination = "/opt/sharednfs/k6"
      #  read_only   = false
      #}

      env = {
        # This is for Influx V1 -- which is way out-dated.  It also needs bucket 'k6' made and token created.
        # "K6_OUT" = "influxdb=http://influxdb.obs.test.nsw.education:8086/k6"

        # This is required for Influx V2 - refer https://github.com/grafana/xk6-output-influxdb
        # regrettably not a stock component for k6, so can't use the above (K6_OUT) env approach.
        # Also requires arg parsing (refer arg[] section)
        K6_INFLUXDB_ORGANIZATION="DG"
        K6_INFLUXDB_BUCKET="k6"
        K6_INFLUXDB_TOKEN="Ju3SZTIxuawPOIgoprAn9mI09fWfyBk830a-PB51e7pA7G-POBJmKzoaVYxqR_oqgSzDazFTgA8dJZLFZ5EcYw=="
        K6_INFLUXDB_ADDRESS="http://dg-pan-01.int.jeddi.org:8086"
        # Weird-arse inverted logic again - true here means it is INsecure which means it ignore absence of TLS
        K6_INFLUXDB_INSECURE=true



        # This is our preferred way - feeding into a custom prometheus instance (that can then feed to mimir)
        K6_PROMETHEUS_RW_SERVER_URL="http://prometheus-remotewrite.obs.test.nsw.education/api/v1/write"


        # Note that the loadimpact/k6 and grafana/k6 images both assert:
        #  "invalid output type 'xk6-influxdb', available types are: cloud, csv, experimental-prometheus-rw, influxdb, json, statsd"

      }


      config {
        # image = "loadimpact/k6"
        image = "grafana/k6"

        # entrypoint = ""

        # dns_servers = ["************"]

        ports = ["port_k6"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [
          "local/obscol-nomad.js:/scripts/obscol-nomad.js",
          "local/basic-k6-io.js:/scripts/basic-k6-io.js"
        ]

        args = [
          "run", 

          # This may be redundant given alloc / container / nomad log redirection
          "--log-output" , "loki=https://loki.obs.test.nsw.education/loki/api/v1/push",

          # this should work for influx v2, but requires compilation / non-bundled plugin
          # "-o", "xk6-influxdb=http://dg-pan-01.int.jeddi.org:8086",

          # Target is defined by K6_PROMETHEUS_RW_SERVER_URL environment variable set above.
          "-o", "experimental-prometheus-rw",

          # Actual script to execute
          "/scripts/obscol-nomad.js",
        ]

      }

#      template {
#        data = file("assets/k6-config.yml")
#        destination = "local/k6.yaml"
#      }

      template {
        data = <<EOH

import http from 'k6/http';
import { check } from "k6";

export let options = {
  stages: [
      // Ramp-up from 1 to 5 VUs in 5s
      { duration: "5s", target: 5 },
      // Stay at rest on 5 VUs for 10s
      { duration: "10s", target: 5 },
      // Ramp-down from 5 to 0 VUs for 5s
      { duration: "5s", target: 0 }
  ]
};

export default function () {
  // This needs auth token.
  // const response = http.get("http://tl0992obscol01.obs.nsw.education:4646/ui/jobs", {headers: {Accepts: "application/json"}});

  // This doesn't
  const response = http.get("http://tl0992obscol01.obs.test.nsw.education:8500/ui/dc-cir-un-test/services", {headers: {Accepts: "application/json"}});

  check(response, { "status is 200": (r) => r.status === 200 });
};

EOH
        destination = "local/obscol-nomad.js"
      }

      template {
        data = <<EOH

import http from 'k6/http';
import { sleep } from 'k6';

export default function () {
  http.get('https://test.k6.io');
  sleep(1);
}

EOH

        destination = "local/basic-k6-io.js"
      }




      resources {
        cpu    = 512
        memory = 512
      }

      service {
        name = "k6"
        port = "port_k6"

        check {
          name     = "K6 healthcheck"
          port     = "port_k6"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.k6.rule=Host(`k6.obs.test.nsw.education`)",
          "traefik.http.routers.k6.tls=false",
        ]

      }

    }
  }
}

