# Loki-rw in TEST ObsCol cluster
# from prod-obscol and change to fit test-obscol

variables {
  # image_loki = "quay.education.nsw.gov.au/observability/loki:test-obscol"
  # image_loki = "quay.education.nsw.gov.au/observability/loki:3.1.0" # have errors with loki_s3 sse object store
  image_loki = "quay.education.nsw.gov.au/observability/loki:3.4.2" #2.9.9
  jaeger_endpoint = "https://jaeger.obs.test.nsw.education"
  loki_url = "https://otel-loki.obs.test.nsw.education/loki/api/v1/push"
  env = "test"
}

job "loki-rwb" {
  datacenters = ["dc-cir-un-${var.env}"]

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    progress_deadline = "5m"
  }


  group "loki-read" {
    count = 3
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    volume "vol_loki" {
      type      = "host"
      source    = "vol_loki_s3"
      read_only = false
    }    

    network {
      port "http" {}
      port "grpc" {}
    }

    task "loki-read" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki
        
        logging {
            type = "loki"
            config {
                loki-url = var.loki_url
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=loki-read"
                }
            } 
        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=read",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }      

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      
      service {
        name = "loki-read"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-read.entrypoints=https",
          "traefik.http.routers.loki-read.rule=Host(`loki.obs.test.nsw.education`)",
        ]

        check {
          name     = "Loki read"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu    = 100
        memory = 2048
      }
    }
  }

  group "loki-write" {
    count = 3

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    volume "vol_loki" {
      type      = "host"
      source    = "vol_loki_s3"
      read_only = false
    }    

    network {
      port "http" {}
      port "grpc" {}
    }

    task "loki-write" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki
        
        logging {
            type = "loki"
            config {
                loki-url = var.loki_url
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=loki-write"
                }
            } 
        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=write",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      service {
        name = "loki-write"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-write.entrypoints=https",
          "traefik.http.routers.loki-write.rule=Host(`loki.obs.test.nsw.education`) && Path(`/loki/api/v1/push`)",
          "traefik.http.routers.loki-write.rule=Host(`loki.obs.test.nsw.education`) && Path(`/otlp/v1/logs`)",
        ]

        check {
          name     = "Loki write"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

    #   service {
    #     name = "loki-frontend"
    #     port = "http"

    #     tags = [
    #       "traefik.enable=true",
    #       "traefik.http.routers.loki-frontend.entrypoints=https",
    #       "traefik.http.routers.loki-frontend.rule=Host(`loki-frontend.obs.test.nsw.education`)",
    #     ]

    #     check {
    #       name     = "Loki frontend"
    #       port     = "http"
    #       type     = "http"
    #       path     = "/ready"
    #       interval = "20s"
    #       timeout  = "1s"

    #       initial_status = "passing"
    #     }
    #   }

      resources {
        cpu    = 100
        memory = 2048
      }
    }
  }
   group "loki-backend" {
    count = 1 # even when doing 100PB of logs per day this should be set to 1
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    volume "vol_loki" {
      type      = "host"
      source    = "vol_loki_s3"
      read_only = false
    }    

    network {
      port "http" {}
      port "grpc" {}
    }

    task "loki-backend" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki
        
        logging {
            type = "loki"
            config {
                loki-url = var.loki_url
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=loki-backend"
                }
            } 
        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=backend",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
      }

      volume_mount {
        volume      = "vol_loki"
        destination = "/loki"
        read_only   = false
      }      

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      
      service {
        name = "loki-backend"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-backend.entrypoints=https",
          "traefik.http.routers.loki-backend.rule=Host(`loki.obs.test.nsw.education`)",
        ]

        check {
          name     = "Loki backend"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu    = 100
        memory = 2048
      }
    }
  }
}