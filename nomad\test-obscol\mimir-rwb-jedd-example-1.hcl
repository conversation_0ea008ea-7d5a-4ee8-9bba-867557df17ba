// Test Observability Collector - HAC - mimir - read / write / backend 3-division mode

// Refer:  https://grafana.com/docs/mimir/latest/operators-guide/architecture/deployment-modes/#read-write-mode

// Similar to the other modes, each Grafana Mimir process is invoked with its -target parameter set to the 
// specific service: -target=read, -target=write, or -target=backend.

// Pay careful attention to the variables section this needs to align with the nomad cluster

variables {
  image_mimir = "quay.education.nsw.gov.au/observability/mimir:test-obscol" #2.13.0
  # image_redis = "quay.education.nsw.gov.au/observability/redis:test-obscol"
  loki_url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
  jaeger_endpoint = "https://otel-jaeger-thrift.obs.test.nsw.education/api/traces"
  mimir_redis_endpoint = "mimir-rwb-redis.obs.test.nsw.education"
  mimir_read_endpoint = "mimir-rwb-read.obs.test.nsw.education"
  mimir_write_endpoint = "mimir-rwb-write.obs.test.nsw.education"
  mimir_backend_endpoint = "mimir-rwb-backend.obs.test.nsw.education"
  env = "test"
}


# JOB = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "mimir-rwb" {

  datacenters = ["dc-cir-un-${var.env}"]
  namespace = "default"

  # 2024-10-23 jedd - experimented with moving distinct_hosts constraint 
  # to JOB level - but it then constraints *groups*, not tasks.
  #constraint {
  #  operator  = "distinct_hosts"
  #  value     = "true"
  #}

  update {
    #max_parallel      = 1

    # jedd 2024-02-12 - timeouts seem to be occurring
    #min_healthy_time  = "10s"
    #healthy_deadline  = "2m"
    #min_healthy_time  = "60s"
    #healthy_deadline  = "5m"

    #canary            = 1
    #auto_promote      = true
    #auto_revert       = true
  }

  # READ group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-read-group" {
    count = 2

    # 2024-10-23 jedd - tried having distinct_hosts constraint moved to JOB level but it fails - instead we will try two constraint stanzas
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    # 2024-10-23 jedd - keeping READ allocs on 0992 hosts
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "tl0992obscol.*"
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-read"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "mimir-rwb-read"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-rwb-read.entrypoints=https",
         "traefik.http.routers.mimir-rwb-read.rule=Host(`${var.mimir_read_endpoint}`)",
       ]      

      check {
        name            = "Mimir rwb read"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }


    task "mimir-rwb-read" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "180s"
      kill_signal = "SIGTERM"
      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc"
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-read"
          }
        }

        args = [
          "-target=read",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      
      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 400
        memory = 256
        memory_max = 1028
      }
    }
  }   # end-READ group


  # WRITE  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-write-group" {
    count = 5

    # 2024-10-23 jedd - tried having distinct_hosts constraint moved to JOB level but it fails - instead we will try two constraint stanzas
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    # 2024-10-23 jedd - keeping WRITE allocs on 0992 hosts
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "tl0992obscol.*"
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }

    ephemeral_disk {
      migrate = true
      sticky  = true
      size = 500
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-write"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "write"
        service = "mimir-write"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-write.entrypoints=http,https",
        "traefik.http.routers.mimir-rwb-write.rule=Host(`${var.mimir_write_endpoint}`)",
      ]   

      check {
        name            = "Mimir rwb write"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    task "mimir-rwb-write" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "180s"
      kill_signal = "SIGTERM"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      
      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-write"
          }
        }

        volumes = [
          "/opt/sharednfs/mimir/:/mimir"
        ]

        #volumes = [
        #    "local/:/mimir/"
        #]
        args = [
          "-target=write",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 1000
        # 2024-02-12 jedd - changed from 2 / 8 to the following (OOM'ing on this task)
        memory = 4096
        memory_max = 18000
      }
    }
  }   # end-WRITE group


  # BACKEND  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-rwb-backend" {
    count = 2

    # 2024-10-23 jedd - tried having distinct_hosts constraint moved to JOB level but it fails - instead we will try two constraint stanzas
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    # 2024-10-23 jedd - MOVIN BACKEND allocs on 0475 hosts (x2)
    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "tl0475obscol.*"
      # value = "tl0992obscol.*"
    }

    volume "vol_mimir"  {
      type = "host"
      source = "vol_mimir"
      read_only = false
    }  

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }
    service {
      name = "mimir-rwb-backend"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "backend"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-backend.entrypoints=https",
        "traefik.http.routers.mimir-rwb-backend.rule=Host(`${var.mimir_backend_endpoint}`)",
    ]      

      check {
        name            = "Mimir rwb backend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "20s"
        timeout         = "1s"
      }
    }

    task "mimir-rwb-backend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "180s"
      kill_signal = "SIGTERM"

      env {
        # 2024-10-28 jedd - experimenting with suppressing proxy setting,
        #    to talk direct to S3 endpoints.
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      volume_mount {
        volume = "vol_mimir"
        destination = "/mimir"
        read_only = false
      }
      
      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-backend"
          }
        }

        args = [
          "-target=backend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu = 200
        memory = 128
        memory_max = 4028
      }
    }
  }   # end-BACKEND group

  # REDIS  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  # group "mimir-rwb-redis" {

  #   # Note that redis is locked to port 6379 on whichever host this runs on.  It's a TCP connection, not 
  #   # an HTTP one, and traefik's TCP routing is not much fun.
  #   # The references to redis are not in this job, but rather than mimir-rwb-config.yml (asset) file,
  #   # which specifies the Traefik DNS resolvable name - mimir-rwb-redis.obs.test.nsw.education.

  #   count = 1

  #   ephemeral_disk {
  #     migrate = true
  #     sticky  = true
  #     size = 300
  #   }

  #   volume "vol_redis"  {
  #     type = "host"
  #     source = "vol_redis"
  #     read_only = false
  #   }  

  #   network {
  #     port "db" {
  #       static = 6379
  #       to = 6379
  #     }
  #   }
  #   service {
  #     name = "mimir-rwb-redis"
  #     port = "db"

  #     meta {
  #       alloc_id  = node.unique.name
  #       component = "redis"
  #     }

  #       tags = [
  #       "traefik.enable=true",
  #       "traefik.http.routers.mimir-rwb-redis.entrypoints=https",
  #       "traefik.http.routers.mimir-rwb-redis.rule=Host(`${var.mimir_redis_endpoint}`)",
  #   ]      

  #     check {
  #       name            = "Mimir rwb redis"
  #       port            = "db"
  #       type            = "tcp"
  #       interval        = "10s"
  #       timeout         = "2s"
  #     }
  #   }

  #   task "mimir-rwb-redis" {
  #     driver       = "docker"
  #     user         = "nobody"
  #     kill_timeout = "180s"
  #     kill_signal = "SIGTERM"

  #     env {
  #       HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
  #       HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
  #       NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
  #       JAEGER_ENDPOINT = var.jaeger_endpoint
  #     }
  #     volume_mount {
  #       volume = "vol_redis"
  #       destination = "/data"
  #       read_only = false
  #     }      
  #     config {
  #       image = var.image_redis

  #       ports = [
  #         "db"
  #       ]
  #       logging {
  #         type = "loki"
  #         config {
  #           loki-url = var.loki_url
  #           loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
  #         }
  #       }
  #       args = [
  #       ]
  #     }

  #     resources {
  #       cpu = 200
  #       memory = 128
  #       memory_max = 4028
  #     }
  #   }
  # }   # end-REDIS group

}
