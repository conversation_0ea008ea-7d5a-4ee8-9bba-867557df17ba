#https://docs.bindplane.observiq.com/docs/install-bindplane-op-server-1#docker

job "ObservIQ-bindplane" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "control" {

    network {
      port "port_http" {
        
        to = 3001
      }
    //   port "port_websocket" {
    //     static = 11057
    //     to = 11057
    //   }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    ephemeral_disk {
      size = 300
    }

    task "bindplane" {
      driver = "docker"

      env = {
        BINDPLANE_CONFIG_USERNAME = "admin"
        BINDPLANE_CONFIG_PASSWORD = "admin"
        BINDPLANE_CONFIG_SERVER_URL = "http://bindplane.obs.test.nsw.education"
        BINDPLANE_CONFIG_REMOTE_URL = "wss://bindplane.obs.test.nsw.education"
        BINDPLANE_CONFIG_SESSIONS_SECRET = "2c23c9d3-850f-4062-a5c8-3f9b814ae144"
        BINDPLANE_CONFIG_SECRET_KEY = "8a5353f7-bbf4-4eea-846d-a6d54296b781"
        BINDPLANE_CONFIG_LOG_OUTPUT = "stdout"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,172.0.0.0/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = "ghcr.io/observiq/bindplane:latest"
        ports = ["port_http"]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }          
        volumes = ["local:/data"]
        }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "bindplane"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.bindplane.rule=Host(`bindplane.obs.test.nsw.education`)",
          "traefik.http.routers.bindplane.tls=false",
          "traefik.http.routers.bindplane.entrypoints=http,https",
        ]
        port = "port_http"
        check {
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}
