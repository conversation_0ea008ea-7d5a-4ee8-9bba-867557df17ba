// phlare - for obscol test environment

job "phlare" {
  datacenters = ["dc-cir-un-test"]
  type        = "service"
  namespace = "default"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "phlare" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_phlare" {
      type = "host"
      source = "vol_phlare"
      read_only = false
    }

    network {
      port "http"  {
        #to = 4100
      }
      port "grpc"  {}
    }

    task "phlare" {

      driver = "docker"
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        JAEGER_ENDPOINT = "http://otel-jaeger-thrift.obs.test.nsw.education/api/traces?format=jaeger.thrift"       
      }
      volume_mount {
        volume      = "vol_phlare"
        destination = "/data"
        read_only   = false
      }

      config {
        image = "grafana/phlare:latest"

        #dns_servers = ["************"]

        ports = ["http","grpc"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        args = [
          "--config.file",
          "local/phlare.yaml",
          "-config.expand-env=true",
        ]

      }

      template {
        data = file("assets/phlare-config.yml")
        destination = "local/phlare.yaml"
      }

      resources {
        cpu    = 500
        memory = 4001
      }

      service {
        name = "phlare"
        port = "http"

        check {
          name     = "Phlare healthcheck"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.phlare.rule=Host(`phlare.obs.test.nsw.education`)",
          "traefik.http.routers.phlare.tls=false",
          "traefik.http.routers.phlare.entrypoints=http,https",
        ]

      }

    }
  }
}

