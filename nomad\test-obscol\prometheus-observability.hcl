# Prometheus instance to watch the GTLM stack for TEST environment (obscol)

variables {
    prometheus_image = "quay.education.nsw.gov.au/observability/prometheus:test-obscol"
    loki_url = "https://loki.obs.test.nsw.education/loki/api/v1/push"    
    env = "test"
}

job "prometheus-observability" {
  type = "service"
  datacenters = ["dc-cir-un-test"]

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "prometheus-observability" {
    count = 1
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }   
    network {
      port "http" {}
    }

    task "prometheus-observability" {
      driver = "docker"

      config {
        ports = ["http"]
        image = var.prometheus_image

        logging {
          type = "loki"

          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--enable-feature=exemplar-storage",
          "--web.external-url=http://prometheus-observability.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.page-title=Prometheus for the Observability Platform",
        ]
      }

      service {
        name = "prometheus-observability"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-observability.rule=Host(`prometheus-observability.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-observability.tls=false",
          "traefik.http.routers.prometheus-observability.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }
      }

      template {
        data = <<EOH
global:
  scrape_interval: 60s
  external_labels:
    provenance: "obscol-prometheus-observability"
    env: "test"

scrape_configs:
  - job_name: 'prometheus-observability'
    static_configs:
      - targets: ['prometheus-observability.obs.test.nsw.education']

  - job_name: 'alertmanager'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['alertmanager']

  - job_name: 'grafana_obscol_test'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['grafana']

  - job_name: 'mimir_obscol_test'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['mimir-rbw-read', 'mimir-rwb-write', 'mimir-rwb-backend']
    relabel_configs:
      - source_labels: [__meta_consul_service_metadata_alloc_id]
        target_label: instance

  - job_name: 'loki_obscol_test'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['loki-read', 'loki-write']  
    relabel_configs:
      - source_labels: [__meta_consul_service_metadata_alloc_id]
        target_label: instance

  - job_name: 'nomad_obscol_test'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['nomad-client', 'nomad']
        tags: ['http']

remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.test.nsw.education/api/v1/push"
    headers: 
        X-Scope-OrgID: test
    tls_config:
        insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 4096
      }

    }
  }
}
