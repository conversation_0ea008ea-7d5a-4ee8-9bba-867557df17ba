# collector-app-group-3 - Collector for group-3 stack and system metrics - PROD ObsCol

# Standard collector architecture 1 job: prometheus

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:test-obscol"
}

job "prometheus-app-group-3" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-group-3" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-group-3-prometheus" {
      driver = "docker"
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-app-group-3.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for group-3 Platforms",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=test"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 500
        memory_max = 1000
      }

      service {
        name = "prometheus-app-group-3"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-group-3.rule=Host(`prometheus-app-group-3.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-app-group-3.tls=false",
          "traefik.http.routers.prometheus-app-group-3.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-app-group-3
    env: test

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-app-group-3'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-app-group-3.obs.test.nsw.education']

  - job_name: 'openmetrics-app-group-3'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: [
    "openmetrics_nexus",
    "openmetrics_nps0",
    "openmetrics_nwix",
    "openmetrics_nwut",
    "openmetrics_obs",
    "openmetrics_olvr",
    "openmetrics_omda",
    "openmetrics_omdc",
    "openmetrics_orae2m",
    "openmetrics_otd",
    "openmetrics_panuid",
    "openmetrics_ppjh",
    "openmetrics_qaauto",
    "openmetrics_qahq",
    "openmetrics_qalr",
    "openmetrics_qaneo",
    "openmetrics_rdeapp",
    "openmetrics_react",
    "openmetrics_remdus",
    "openmetrics_resolv",
    "openmetrics_rredp",
    "openmetrics_rwdc12",
    "openmetrics_salm",
    "openmetrics_sap114",
    "openmetrics_sapa",
    "openmetrics_sapd",
    "openmetrics_sapd52",
    "openmetrics_sapd54",
    "openmetrics_sapia",
    "openmetrics_sas114",
    "openmetrics_sas115",
    "openmetrics_satc",
    "openmetrics_sc9",
    "openmetrics_scne",
    "openmetrics_scoma",
    "openmetrics_scomd",
    "openmetrics_scome",
    "openmetrics_scomq",
    "openmetrics_scord",
    "openmetrics_score",
    "openmetrics_sdetst",
    "openmetrics_sedrms"
]
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: app_id          
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path

remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.test.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: test
    tls_config:
      insecure_skip_verify: true
EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-group-3-prometheus"
  }
}

