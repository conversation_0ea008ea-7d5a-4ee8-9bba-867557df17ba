# collector-app-group-4 - Collector for group-4 stack and system metrics - PROD ObsCol

# Standard collector architecture 1 job: prometheus

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:test-obscol"
}

job "prometheus-app-group-4" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-group-4" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-group-4-prometheus" {
      driver = "docker"
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-app-group-4.obs.test.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for group-4 Platforms",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=test"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu    = 150
        memory = 500
        memory_max = 1000
      }

      service {
        name = "prometheus-app-group-4"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-app-group-4.rule=Host(`prometheus-app-group-4.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus-app-group-4.tls=false",
          "traefik.http.routers.prometheus-app-group-4.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: prometheus-app-group-4
    env: test

  scrape_interval: 60s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-app-group-4'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-app-group-4.obs.test.nsw.education']

  - job_name: 'openmetrics-app-group-4'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: [
    "openmetrics_seismo",
    "openmetrics_sems",
    "openmetrics_serg",
    "openmetrics_sfile",
    "openmetrics_sibi",
    "openmetrics_sima",
    "openmetrics_siobi",
    "openmetrics_siop",
    "openmetrics_sip114",
    "openmetrics_sis115",
    "openmetrics_smaw",
    "openmetrics_smbps",
    "openmetrics_smcms",
    "openmetrics_smqa",
    "openmetrics_smtp4o",
    "openmetrics_soapts",
    "openmetrics_spgps",
    "openmetrics_spsr",
    "openmetrics_sqf019",
    "openmetrics_sqf03a",
    "openmetrics_sqf03c",
    "openmetrics_sqf101",
    "openmetrics_sqf102",
    "openmetrics_sqf104",
    "openmetrics_sqf105",
    "openmetrics_sqf106",
    "openmetrics_sqf107",
    "openmetrics_sqf108",
    "openmetrics_sqf110",
    "openmetrics_sqf111",
    "openmetrics_sqfc01",
    "openmetrics_sqfc10",
    "openmetrics_sqfc2",
    "openmetrics_sqfc3",
    "openmetrics_sqfc9",
    "openmetrics_sqfe01",
    "openmetrics_sqfe02",
    "openmetrics_sqfe03",
    "openmetrics_sqfe04",
    "openmetrics_sqfe05",
    "openmetrics_sqfe06",
    "openmetrics_sqfe07",
    "openmetrics_sqfe09",
    "openmetrics_sqfe20",
    "openmetrics_sqfe21",
    "openmetrics_sqfe25",
    "openmetrics_sqff",
    "openmetrics_sqffsw",
    "openmetrics_sqfl",
    "openmetrics_sqld",
    "openmetrics_sqlfsw",
    "openmetrics_sqlp",
    "openmetrics_sqlp11",
    "openmetrics_sqlq4b",
    "openmetrics_sqlq7c",
    "openmetrics_sqls",
    "openmetrics_sqlt",
    "openmetrics_sqmg",
    "openmetrics_sqmgmt",
    "openmetrics_sqp03b",
    "openmetrics_sqp078",
    "openmetrics_sqp101",
    "openmetrics_sqp102",
    "openmetrics_sqp103",
    "openmetrics_sqp104",
    "openmetrics_sqp106",
    "openmetrics_sqp108",
    "openmetrics_sqp109",
    "openmetrics_sqp111",
    "openmetrics_sqp112",
    "openmetrics_sqp113",
    "openmetrics_sqp114",
    "openmetrics_sqp115",
    "openmetrics_sqp116",
    "openmetrics_sqp118",
    "openmetrics_sqp119",
    "openmetrics_sqp120",
    "openmetrics_sqpe01",
    "openmetrics_sqpe20",
    "openmetrics_sqpe21",
    "openmetrics_sqpfsw",
    "openmetrics_sqpl",
    "openmetrics_sqs0",
    "openmetrics_sqs038",
    "openmetrics_sqs103",
    "openmetrics_sqs104",
    "openmetrics_sqs105",
    "openmetrics_sqs106",
    "openmetrics_sqs112",
    "openmetrics_sqs114",
    "openmetrics_sqs115",
    "openmetrics_sqs116",
    "openmetrics_sqs117",
    "openmetrics_sqs118",
    "openmetrics_sqse02",
    "openmetrics_sqsfsw",
    "openmetrics_srpa",
    "openmetrics_sshr",
    "openmetrics_ssis",
    "openmetrics_stmg",
    "openmetrics_storts",
    "openmetrics_swnps",
    "openmetrics_swrepo",
    "openmetrics_syslog",
    "openmetrics_t4sc",
    "openmetrics_tcdn"
]
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: app_id          
      - source_labels: [__meta_consul_metadata_env]
        target_label: env
      - source_labels: [__meta_consul_service_metadata_domain]
        target_label: domain
      - source_labels: [__meta_consul_service_metadata_metrics_path]
        target_label: __metrics_path__
        regex: '(.+)'  # Do not perform the replace if there was no metrics path

remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.test.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: test
    tls_config:
      insecure_skip_verify: true
EOH
        destination = "local/prometheus.yaml"
      }
    }  // END-task  "task-group-4-prometheus"
  }
}

