# Primary main prometheus instance for TEST environment (obscol)

job "prometheus" {
  # 2022-10-12 jedd - disabling the dual-DC config as it stalls launch
  # datacenters = ["dc-cir-un-test","dc-cir-sw-test"]
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "prometheus" {
    # 2022-10-12 jedd - disabling the dual-DC config as it stalls launch
    # count = 2
    count = 1

    constraint {
        operator  = "distinct_property"
        attribute = "${node.datacenter}"
        value     = "1"
    }
    
    network {
      port "http" {}
    }

    volume "prometheus" {
      type = "host"
      source = "prometheus"
      read_only = false
    }

    # shared checkout of git repo at /opt/sharednfs/prometheus-configuration
    # NOT needed if we doing the volumes [] mount directly extracting from local host file system
#    volume "vol-prometheus-configuration"  {
#      type = "host"
#      source = "vol_prometheus_configuration"
#      read_only = false
#    }

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "prometheus"
        destination = "/prometheus"
        read_only = false
      }

#      We can either do this, and then change configuration to point to various locations within
#      the /prometheus-configuration mount - or just map things in the volumes [] section below,
#      straight out to the file system location (/opt/sharednfs/prometheus-configuration).
#      volume_mount {
#        volume = "vol-prometheus-configuration"
#        destination = "/prometheus-configuration"
#        read_only = false
#      }

      config {
        ports = ["http"]

        # 2022-11-29 jedd - bumping from 2.40.2 to 2.40.3 due to memory leak advised by James
        # image = "https://docker.io/prom/prometheus:v2.40.2"
        # image = "https://docker.io/prom/prometheus:v2.42"
        image = "prom/prometheus:v2.46.0"

        logging {
          type = "loki"

          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",

          # We are eschewing a volume (above) and just mounting volumes directly, refer:
          # https://developer.hashicorp.com/nomad/docs/drivers/docker#volumes
          # "/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
          "/opt/sharednfs/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus.obs.test.nsw.education",
          "--web.page-title=Prometheus on DoE ObsCol TEST cluster",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--enable-feature=examplar-storage",

          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=8d",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]
      }

      service {
        name = "prometheus"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus.rule=Host(`prometheus.obs.test.nsw.education`)",
          "traefik.http.routers.prometheus.tls=false",
          "traefik.http.routers.prometheus.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "test"
          cluster = "obscol"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    provenance: "obscol-prometheus"

  scrape_interval: 60s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['prometheus.obs.test.nsw.education']

  - job_name: 'alertmanager'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['alertmanager']

  - job_name: 'nomad'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['nomad-client', 'nomad']
        tags: ['http']

  # 2022-12-13 -- this collection - 36 hosts - moved to 'prometheus-sd-telegraf'
  # Any external node that runs telegraf
  ###- job_name: 'openmetrics'
  ###  consul_sd_configs:
  ###    - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
  ###      datacenter: 'dc-cir-un-test'
  ###      token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
  ###      services: ['openmetrics']
  ###  relabel_configs:
  ###    - source_labels: [__meta_consul_metadata_cir_app_id]
  ###      target_label: cir_app_id
  ###    - source_labels: [__meta_consul_metadata_env]
  ###      target_label: env

  # Any standalone exporter that lives in the nomad cluster and not the agent
  - job_name: 'prometheus-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['prometheus-exporter']

  # Any standalone rubrik exporter that lives in the nomad cluster and not the agent
  - job_name: 'rubrik-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['rubrik-exporter']

  - job_name: 'nifi'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['nifi']

  - job_name: 'ssl'
    metrics_path: /probe
    params:
      module: ["https"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['https']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-ssl.obs.test.nsw.education

  - job_name: 'snmp_idrac'
    scrape_interval: 60s
    static_configs:
      - targets:
        - *************
        - *************
        - *************
        - ************
        - ************
        - ************
        - *************
    metrics_path: /snmp
    params:
      module: ["dell_idrac"]
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-idrac.obs.test.nsw.education


#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

  - job_name: 'telegraf_topk_processes'
    scrape_interval: 30s
    static_configs:
      - targets: ['tl0992obscol01.nsw.education:11056',
                  'tl0992obscol02.nsw.education:11056',
                  'tl0992obscol03.nsw.education:11056'
                 ]
    scheme: 'http'
    metrics_path: /metrics

  - job_name: 'blackbox-ping'
    metrics_path: /probe
    params:
      module: ["icmp"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        node_meta:
          'blackbox-icmp': 'true'
    relabel_configs:
      - source_labels: [__meta_consul_address]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-blackbox.obs.test.nsw.education

  # 2024-11-21 jedd - hard-coded beyla endpoints on obscol06 and 07 (rhel9 needed)
  - job_name: 'beyla_testobscol'
    scrape_interval: 1m
    static_configs:
      - targets: ['tl0475obscol06.nsw.education:8999',
                  'tl0475obscol07.nsw.education:8999'
                 ]
    scheme: 'http'
    metrics_path: /metrics



# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
#
#  2022-11-14 jedd - disabling this, relocated to standalone nomad job:
#                    'prometheus-app-oliver.nomad'.
#
#  # Using blackbox exporter to do a custom web-check for Oliver school library
#  # with custom URLs and regex string match on target page.
#  - job_name: 'webcheck_oliver'
#    metrics_path: /probe
#    scrape_interval: 2m
#    # No need for proxy HERE as we're hitting blackbox...obs.nsw.education (internal)
#    # proxy_url: "http://proxy.det.nsw.edu.au:80"
#    params:
#      module: ["webcheck_oliver"]
#    file_sd_configs:
#    # Periodicity to re-read the source file(s) below, NOT the scrape proper.
#    # Will be (more) useful when we separate out file_sd_config entries.
#    - refresh_interval: 1h
#      files: 
#      - "/local/prometheus-configuration/test/blackbox/blackbox_webcheck_oliver.yml"
#    relabel_configs:
#    - source_labels: [__address__]
#      target_label: __param_target
#    - source_labels: [__param_target]
#      target_label: instance
#    - target_label: __address__
#      replacement: exporter-blackbox.obs.test.nsw.education



rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - consul_sd_configs:
      - server: 'consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
        services: ['alertmanager']

remote_write:
  - name: mimir
    url: "https://mimir-rwb-write.obs.test.nsw.education/api/v1/push"
    headers: 
        X-Scope-OrgID: test
    tls_config:
        insecure_skip_verify: true

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 4096
      }

    }
  }
}
