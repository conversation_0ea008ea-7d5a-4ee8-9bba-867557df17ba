
// proxy-consul -- front end to access consul on localhost 8500 via 443 (traefik)


variables {
  image_caddy = "quay.education.nsw.gov.au/observability/caddy:v2.10.0"
}



# JOB - proxy-consul  = = = = = = = = = = = = = = = = = = = = = = = = =
job "proxy-consul" {
  datacenters = ["dc-cir-un-test"]

  type = "service"

  group "proxy" {
    network {
      port "port_http" {
        to = 8080
      }
      port "port_metrics" {
        to = 2019
      }
    }

    constraint {
      # This really only is useful if running on a server, not client/agent.
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "tl.*obscol0[123]"
    }

    service {
      name = "proxy-consul"
      port = "port_http"
      
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.proxy-consul.rule=Host(`proxy-consul.obs.test.nsw.education`)",
        "traefik.http.routers.proxy-consul.tls=true"
				# "traefik.http.routers.home.entrypoints=http,https",
      ]

      # Additional service registration for Prometheus scraping
    }

		service {
			name = "proxy-consul-metrics"
			port = "port_metrics"
			tags = ["metrics"]
		}

    task "caddy" {
      driver = "docker"

      config {
        image = "${var.image_caddy}"

        ports = ["port_http", "port_metrics"]

        mount {
          type = "bind"
          target = "/etc/caddy/Caddyfile"
          source = "local/Caddyfile"
          readonly = true
        }
      }

      template {
        data = <<EOF
{
  auto_https off
#  servers {
#    metrics
#  }
  admin :2019
}

:8080 {
  reverse_proxy {{ env "NOMAD_HOST_IP_port_http" }}:8500 {
    header_up X-Forwarded-Proto "https"
#    header_up X-Forwarded-For {remote_host}
    header_up Host {host}
    header_up Authorization {header.Authorization}
  }

  # Enhanced logging with timing information
  log {
    output stdout
    format console {
      time_format "rfc3339"
      level_format "color"
    }
    level INFO
  }

  # Enable Prometheus metrics
#  metrics {
#    path /metrics
#    disable_openmetrics
#  }
}
EOF
        destination = "local/Caddyfile"
      }

      resources {
        cpu    = 200
        memory = 256
      }
    }
  }
}









