
// proxy-nomad -- front end to access nomad proxy on localhost 4646 via 443 (traefik)


variables {
  image_caddy = "quay.education.nsw.gov.au/observability/caddy:v2.10.0"
}



# JOB - proxy-nomad  = = = = = = = = = = = = = = = = = = = = = = = = =
job "proxy-nomad" {
  datacenters = ["dc-cir-un-test"]

  type = "service"

  group "proxy" {
    network {
      port "port_http" {
        to = 8080
      }
      port "port_metrics" {
        to = 2019
      }
    }

    constraint {
      # This really only is useful if running on a server, not client/agent.
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "tl.*obscol0[123]"
    }

    service {
      name = "proxy-nomad"
      port = "port_http"
      
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.proxy-nomad.rule=Host(`proxy-nomad.obs.test.nsw.education`)",
        "traefik.http.routers.proxy-nomad.tls=true"
				# "traefik.http.routers.home.entrypoints=http,https",
      ]

      # Additional service registration for Prometheus scraping
    }

		service {
			name = "proxy-nomad-metrics"
			port = "port_metrics"
			tags = ["metrics"]
		}

    task "caddy" {
      driver = "docker"

      config {
        image = "${var.image_caddy}"

        ports = ["port_http", "port_metrics"]

        mount {
          type = "bind"
          target = "/etc/caddy/Caddyfile"
          source = "local/Caddyfile"
          readonly = true
        }
      }

      template {
        data = <<EOF
{
  auto_https off
#  servers {
#    metrics
#  }
  admin :2019
}

:8080 {
  # Seems we aren't binding to localhost on nomad - so can either substitute in
  #     some nomad variables for NOMAD_ADDR .. or just fixed on 01
  # reverse_proxy localhost:4646 {
  # reverse_proxy pl0992obscol01.nsw.education:4646 {
  reverse_proxy {{ env "NOMAD_HOST_IP_port_http" }}:4646 {
    header_up X-Forwarded-Proto "https"
#    header_up X-Forwarded-For {remote_host}
    header_up Host {host}
    header_up Authorization {header.Authorization}
  }

  # Enhanced logging with timing information
  log {
    output stdout
    format console {
      time_format "rfc3339"
      level_format "color"
    }
    level INFO
  }

  # Enable Prometheus metrics
#  metrics {
#    path /metrics
#    disable_openmetrics
#  }
}
EOF
        destination = "local/Caddyfile"
      }

      resources {
        cpu    = 200
        memory = 256
      }
    }
  }
}




