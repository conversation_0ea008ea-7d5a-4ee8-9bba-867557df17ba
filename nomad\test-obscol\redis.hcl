  # Redis central job.
  # add to match prod redis

variables {
  image_loki = "quay.education.nsw.gov.au/observability/loki:test-obscol"
  image_redis = "quay.education.nsw.gov.au/observability/redis:test-obscol"
  redis_endpoint = "redis.obs.test.nsw.education"
  jaeger_endpoint = "https://otel-jaeger-thrift.obs.test.nsw.education/api/traces"
  loki_url = "https://otel-loki.obs.test.nsw.education/loki/api/v1/push"
  env = "test"
  }

job "redis" {
  datacenters = ["dc-cir-un-${var.env}"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  # REDIS  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "redis" {

    # Note that redis is locked to port 6379 on whichever host this runs on.  It's a TCP connection, not 
    # an HTTP one, and traefik's TCP routing is not much fun.
    # The references to redis are not in this job, but rather than mimir-rwb-config.yml (asset) file,
    # which specifies the Traefik DNS resolvable name - mimir-rwb-redis.obs.test.nsw.education.

    count = 1

    ephemeral_disk {
      migrate = true
      sticky  = true
      size = 300
    }


    network {
      port "db" {
        static = 6379
        to = 6379
      }
    }

    volume "vol_redis"  {
      type = "host"
      source = "vol_redis"
      read_only = false
    }  
    
    service {
      name = "redis"
      port = "db"

      meta {
        alloc_id  = node.unique.name
        component = "redis"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.redis.entrypoints=https",
        "traefik.http.routers.redis.rule=Host(`${var.redis_endpoint}`)",
    ]      

      check {
        name            = "loki rwb redis"
        port            = "db"
        type            = "tcp"
        interval        = "10s"
        timeout         = "2s"
      }
    }

    task "redis" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      
      config {
        image = var.image_redis

        ports = [
          "db"
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
          }
        }
        args = [
        ]
      }

    volume_mount {
        volume = "vol_redis"
        destination = "/data"
        read_only = false
      }
      resources {
        cpu = 200
        memory = 128
        memory_max = 4028
      }
    }
  }   # end-REDIS group
}