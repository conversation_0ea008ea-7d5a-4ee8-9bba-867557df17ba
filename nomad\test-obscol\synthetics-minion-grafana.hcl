job "synthetics-minion-grafana" {
  datacenters = ["dc-cir-un-test"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "synthetics-minion-grafana" {
    network {
      port "port_minion" {}
      }
        
    task "synthetics-minion-grafana" {
      driver = "docker"
      config {
        image = "grafana/synthetic-monitoring-agent"

        # dns_servers = ["************"]

        logging {   
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
      args = [
        "-verbose=true",
        "-debug=true",
        "-api-insecure=false",
        "-api-server-address=${API_SERVER}",
        "-api-token=${API_TOKEN}",
        "-listen-address=:${NOMAD_PORT_port_minion}"
      ]
      ports = ["port_minion"]
      }

      service {
        name = "synthetics-minion-grafana"
        port = "port_minion"
       check {
         type = "http"
         port = "port_minion"
         path = "/"
         interval = "20s"
         timeout = "10s"
       }
      }

      env {
        # See https://grafana.com/docs/grafana-cloud/synthetic-monitoring/private-probes/#deploy-the-agent-using-docker
        # 
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        API_SERVER = "synthetic-monitoring-grpc-au-southeast.grafana.net:443"
        API_TOKEN   = "cmhUhf8ub+64I3Fxkxz1sVFf1YBIbW2Jy1uatzcyApGNkw1S6is1TDlLBVNkXLMTWRNIbrsh9K6TtSi5MRjjm4Lt7nqVJRVYajLBVQCmEaI6FYvm9wfv0aqJgZdBGA9+Vp0wjG6/CqWlrzhiJfSNPHVoDMaOTxfjue+6ZdjHJ/0="
        } 
      resources {
        cpu = 250
        memory = 250
      }
    }
  }
}
