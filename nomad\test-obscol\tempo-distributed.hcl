// Grafana Tempo (prod obscol) - Refactored for Microservices Architecture

variable "tempo_version" {
  type        = string
  description = "Tempo Docker image version"
  default     = "2.5.0" # Using the version from your example
}

variable "image_base_path" {
  type        = string
  description = "Base path for the Tempo Docker image registry"
  default     = "quay.education.nsw.gov.au/observability" # Adjusted to your original image path
}

variable "s3_access_key_id" {
  type        = string
  description = "S3 Access Key ID for Tempo storage"
  default     = "********************" # Placeholder - **MUST BE UPDATED**
}

variable "s3_secret_access_key" {
  type        = string
  description = "S3 Secret Access Key for Tempo storage"
  default     = "WX+RT6bc2p89vNar2nn7+zLpmfuvy1In8atpH2Ns" # Placeholder - **MUST BE UPDATED**
}

job "tempo-distributed" {
  datacenters = ["dc-cir-un-test"] # Keep your specific datacenter
  type        = "service"

  update {
    max_parallel      = 1 # Adjust based on your environment's tolerance for simultaneous updates per group
    health_check      = "checks"
    auto_revert       = true
    healthy_deadline  = "5m"
    min_healthy_time  = "5s"
  }

  // Common network configuration for internal Tempo communication
  // Specific service ports will be defined within each group/task
  #network {
    # Tempo components generally communicate over gRPC (default 9095) and HTTP (default 3200)
    # The example uses dynamically assigned ports for http/grpc, and static for OTLP.
    # We will define these at the group level and map them.
  #}

  // --- Metrics Generator Group ---
  group "metrics-generator" {
    count = 1

    network {
      port "http" { to = 3200 } # Tempo HTTP port
      port "grpc" { to = 9095 } # Tempo gRPC port
      port "otlp" { to = 4317 } # OTLP gRPC receiver
    }

    service {
      name = "tempo-metrics-generator"
      port = "http"
      check {
        name     = "metrics-generator-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-metrics-generator.rule=Host(`metrics-generator.obs.nsw.education`)",
        "traefik.http.routers.tempo-metrics-generator.tls=false",
        "traefik.http.routers.tempo-metrics-generator.entrypoints=http,https",
      ]
    }

    service {
      name = "tempo-metrics-generator-grpc"
      port = "grpc"
      check {
        name     = "metrics-generator-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "metrics-generator" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
          "otlp", # This port is configured for the OTLP gRPC receiver in the config
        ]
        args = [
          "-target=metrics-generator",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-metrics-generator"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for ingester WAL
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml") # This will be the combined tempo.yml
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 500 # Adjusted from example
        memory     = 512
        memory_max = 1024
      }
    }
  }


  // --- Query Frontend Group ---
  group "query-frontend" {
    count = 1

    network {
        
      port "http" { to = 3200 }
      port "grpc" { static = 9095 } # Static port as per example
    }

    service {
      name = "tempo-query-frontend"
      port = "http"
      check {
        name     = "query-frontend-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.tempo-query-frontend.rule=Host(`tempo.obs.test.nsw.education`)", # This was your original Tempo UI
        "traefik.http.routers.tempo-query-frontend.tls=false",
        "traefik.http.routers.tempo-query-frontend.entrypoints=http,https",
      ]
    }

    service {
      name = "tempo-query-frontend-grpc"
      port = "grpc"
      check {
        name     = "query-frontend-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
       tags = [
         "traefik.enable=true",
         "traefik.http.routers.tempo-querier.entrypoints=https",
         "traefik.http.routers.tempo-querier.rule=Host(`tempo-querier.obs.test.nsw.education`)",
       ]   
    }

    task "query-frontend" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"
      
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
        ]
        args = [
          "-target=query-frontend",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-query-frontend"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for ingester WAL
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 200
        memory     = 256
        memory_max = 1024
      }
    }
  }


  // --- Ingester Group ---
  group "ingester" {
    count = 3 # Typically more ingesters for higher write throughput

    network {
      port "http" { to = 3200 }
      port "grpc" { 
        static = 9095 
        to = 9095
        }
    }

    service {
      name = "tempo-ingester"
      port = "http"
      check {
        name     = "ingester-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
    }

    service {
      name = "tempo-ingester-grpc"
      port = "grpc"
      check {
        name     = "ingester-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "ingester" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
        ]
        args = [
          "-target=ingester",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-ingester"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for ingester WAL
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 300
        memory     = 512
        memory_max = 2048
      }
    }
  }


  // --- Compactor Group ---
  group "compactor" {
    count = 1

    ephemeral_disk {
      size   = 10000 # Increased for compactor's temporary storage
      sticky = true
    }

    network {
      port "http" { to = 3200 }
      port "grpc" { to = 9095 }
    }

    service {
      name = "tempo-compactor"
      port = "http"
      check {
        name     = "compactor-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
    }

    service {
      name = "tempo-compactor-grpc"
      port = "grpc"
      check {
        name     = "compactor-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "compactor" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
        ]
        args = [
          "-target=compactor",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-compactor"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for compactor
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 1000 # Increased as compactor can be CPU intensive
        memory     = 1024
        memory_max = 4096
      }
    }
  }


  // --- Distributor Group ---
  group "distributor" {
    count = 1

    network {
      port "http" { to = 3200 }
      port "grpc" { to = 9095 }
      port "otlp" { to = 4317 } # OTLP gRPC receiver
      port "zipkin" { static = 9411 } # Static port as per your original job
      port "jaeger" { to = 14268 } # Jaeger thrift_http receiver
    }

    service {
      name = "tempo-distributor"
      port = "http"
      check {
        name     = "distributor-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
    }

    service {
      name = "tempo-distributor-otlp"
      port = "otlp"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otlp-traces.rule=Host(`traces.obs.tet.nsw.education`) && Path(`/v1/traces`)",
        "traefik.http.routers.otlp-traces.tls=false",
        "traefik.http.routers.otlp-traces.entrypoints=http,https",
      ]
    }

    service {
      name = "tempo-distributor-zipkin"
      port = "zipkin"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.zipkin.rule=Host(`zipkin.obs.test.nsw.education`)",
        "traefik.http.routers.zipkin.tls=false",
        "traefik.http.routers.zipkin.entrypoints=http,https",
      ]
    }

    service {
      name = "tempo-distributor-jaeger"
      port = "jaeger"
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.jaeger.rule=Host(`jaeger.obs.test.nsw.education`)",
        "traefik.http.routers.jaeger.tls=false",
        "traefik.http.routers.jaeger.entrypoints=http,https",
      ]
    }

    service {
      name = "tempo-distributor-grpc"
      port = "grpc"
      check {
        name     = "distributor-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }
    }

    task "distributor" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
          "otlp",
          "zipkin",
          "jaeger",
        ]
        args = [
          "-target=distributor",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-distributor"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for ingester WAL
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 500
        memory     = 512
        memory_max = 1024
      }
    }
  }


  // --- Querier Group ---
  group "querier" {
    count = 1

    network {
      port "http" { to = 3200 }
      port "grpc" { to = 9095 }
    }

    service {
      name = "tempo-querier"
      port = "http"
      check {
        name     = "querier-http"
        port     = "http"
        type     = "http"
        path     = "/ready"
        interval = "10s"
        timeout  = "2s"
      }
    }

    service {
      name = "tempo-querier-grpc"
      port = "grpc"
      check {
        name     = "querier-grpc"
        port     = "grpc"
        type     = "grpc"
        interval = "10s"
        timeout  = "2s"
        grpc_use_tls = false
        tls_skip_verify = true
      }

    }

    task "querier" {
      driver      = "docker"
      user        = "nobody"
      kill_timeout = "90s"

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }

      config {
        image = "${var.image_base_path}/tempo:${var.tempo_version}"
        ports = [
          "http",
          "grpc",
        ]
        args = [
          "-target=querier",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
        ]
        logging {
          type = "loki"
          config {
            loki-url           = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-querier"
          }
        }
        volumes = [
          "/opt/sharednfs/tempo/:/tempo_data", # Dedicated path for ingester WAL
        ]
      }

      template {
        data        = file("assets/tempo-distributed.yml")
        destination = "local/config.yml"
      }

      template {
        data = <<-EOH
        S3_ACCESS_KEY_ID=${var.s3_access_key_id}
        S3_SECRET_ACCESS_KEY=${var.s3_secret_access_key}
        EOH
        destination = "secrets/s3.env"
        env         = true
      }

      resources {
        cpu        = 500
        memory     = 1024
        memory_max = 2048
      }
    }
  }
}