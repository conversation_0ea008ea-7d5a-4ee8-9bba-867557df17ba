// Grafana <PERSON> (test obscol)

variables {
  image_tempo = "quay.education.nsw.gov.au/observability/tempo:test-obscol"
}

job "tempo" {
  datacenters = ["dc-cir-un-test"]
  type = "service"
  namespace = "default"

  update {
    max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true
    healthy_deadline = "5m"
    min_healthy_time = "5s"
  }

  group "tempo" {
    count = 1

    network {
      
      port "port_jaeger" {
        to = 14268
      }
      port "port_tempo" {
        to = 3200
      }
    }

    volume "vol_tempo"  {
      type = "host"
      source = "vol_tempo"
      read_only = false
    }

    service {
      name = "tempo"
      port = "port_tempo"

      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo.rule=Host(`tempo.obs.test.nsw.education`)",
          "traefik.http.routers.tempo.tls=false",
          "traefik.http.routers.tempo.entrypoints=http,https",
        ]      
    }


    service {
      name = "tempo-jaeger"
      port = "port_jaeger"
      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo-jaeger.rule=Host(`tempo-jaeger.obs.test.nsw.education`)",
          "traefik.http.routers.tempo-jaeger.tls=false",
          "traefik.http.routers.tempo-jaeger.entrypoints=http,https",
        ]
    }

    task "tempo" {

      driver = "docker"
      kill_signal = "SIGTERM"      
      
      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        JAEGER_ENDPOINT = "http://tempo-jaeger.obs.test.nsw.education/api/traces?format=jaeger.thrift"             
      }

      config {
        image = var.image_tempo
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=test"
                }
            } 
        
        args = [
          "--config.file=/etc/tempo/config/tempo.yml",
        ]

        ports = [
          "port_jaeger",
          "port_tempo",
        ]

        volumes = [
          "local/config:/etc/tempo/config",
        ]
        
      }

      volume_mount {
        volume = "vol_tempo"
        destination = "/mnt/tempo"
        read_only = false
      }
      template {
        data = file("assets/tempo-config.yml")
        destination = "local/config/tempo.yml"
        change_mode   = "signal"
        change_signal = "SIGHUP"
      }

      resources {
        cpu    = 10000
        memory = 8000
      }
    }
  }
}
