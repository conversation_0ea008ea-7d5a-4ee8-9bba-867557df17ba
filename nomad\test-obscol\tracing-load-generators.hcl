## extracted and modified as per https://github.com/hashicorp/nomad-open-telemetry-getting-started

variables {
  otel_image = "otel/opentelemetry-collector:0.38.0"
}

job "otel-traces" {
  datacenters = ["dc-cir-un-test"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  # Synthetic load generators
  group "load-generators" {
    network {
      port "port_frontend" {
        to = 8080
      }
      port "port_route" {
        to = 8083
      }
      port "port_customer"{
        to = 8081
      }
    }
    task "hotrod_tempo" {
        driver = "docker"

        config {
            image = "jaegertracing/example-hotrod:latest"
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
                }
            }
            ports = [
                "port_frontend",
                "port_route",
                "port_customer"
            ]             
        }
        resources {
            cpu = 200
            memory = 128
        }
        env {
            #OTEL_EXPORTER_JAEGER_ENDPOINT="http://tempo-jaeger.obs.test.nsw.education/api/traces"
            OTEL_EXPORTER_JAEGER_ENDPOINT="http://otel-jaeger-thrift.obs.test.nsw.education/api/traces"
        }
    }


  }
}