#  traefik - load balancer (LB) for obscol-test - runs on all nodes (system job)
variables {
  image_traefik = "quay.education.nsw.gov.au/observability/traefik:test-obscol"
  # loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
  loki_url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
  env = "test"
}

job "traefik" {
  datacenters = ["dc-cir-un-test"]
  type        = "system"
  region      = "global"
  namespace = "default"

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  group "traefik" {
    network {
      port "http" {
        static = 80
      }

      port "https" {
        static = 443
      }

      port "traefik" {
        static = 8081
      }

      port "grpc" {
        static = 7233
      }
      port "otlpgrpc" {
        static = 4317
      }

    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        image = var.image_traefik

        # 2024-10-29 jedd - re-enabling logging loki for troubleshooting mimir
        # not sure why it was previously disabled)
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
          }
        }

        network_mode = "host"

        cap_add = ["net_bind_service"]

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
          # Prefer the following, but our mapping for providers.file talks straight to /local
          # "local/provider-vault.yaml:/etc/traefik.d/provider-vault.yaml",
        ]
      }

      template {
        data = <<EOF
debug = false
logLevel = "INFO"
defaultEntryPoints = ["https","http"]

[entryPoints]
    [entryPoints.http]
    address = ":80"
    [entryPoints.https]
    address = ":443"
      [entryPoints.https.http.tls]
        [[entryPoints.https.http.tls.domains]]
        main = "obscol.obs.test.nsw.education"
        sans = ["*.obs.test.nsw.education"]

    [entryPoints.traefik]
    address = ":8081"

    [entryPoints.grpc]
    address = ":7233"

    [entryPoints.otlpgrpc]
    address = ":4317"

    # 2023-05-03 jedd - for astronomy demo - need a tcp (non-http) entrypoint
    # [entryPoints.postgres]
    # address = ":6001"

    # 2023-05-03 jedd - for astronomy demo - need a tcp (non-http) entrypoint
    # [entryPoints.redis]
    # address = ":6002"


[api]
    dashboard = true
    insecure  = true

# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = false

    [providers.consulCatalog.endpoint]
      address = "consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
      scheme  = "http"

# Enable File Provider for "Dynamic Configuration" elements
[providers.file]
  directory = "/local/traefik.d"

  # 2024-11-04 jedd - we COULD have this dynamic, so we could add
  #      dynamic entries, like the vault.obs.test.nsw.education one,
  #      but probably minimal use.
  # watch = true


# 2021-09-09 jedd - enable metrics out of traefik
[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]

# 2023-02-02 jedd - promote tracing config from prod
# 2024-05-09 jedd - removing references to jaeger as traefik is complaining at launch via tf
[tracing]
  [tracing.otlp.http]
    endpoint = "https://otel-collector-http.obs.test.nsw.education/v1/traces"

EOF
        destination = "local/traefik.toml"
      }

      template {
        data = <<EOF
# 2024-11-15 james - removed vault config as it is not used, vault registers in consul directly        
# We could move these to asset sourced, or prometheus-configuration (repo)
# sourced too, if we ever have more than one or two of them.
# http:
#   routers:
#     vault:
#       entryPoints:
#         - "http"
#         - "https"
#       rule: "Host(`vault.obs.test.nsw.education`)"
#       service: vault-service

#   services:
#     vault-service:
#       loadBalancer:
#         servers:
#           # This means each traefik allocation will redirect vault. traffic
# 					# to the host machine on the job's defined port #.
#           - url: "http://{{ env "NOMAD_IP_port_vault" }}:{{ env "NOMAD_PORT_port_vault" }}"

EOF
        destination = "local/traefik.d/provider-vault.yaml"
      }


      template {
        destination = "local/traefik.d/certificates.toml"
        data = <<EOF
[tls.stores]
  [tls.stores.default]
    # This one should be used if the client did not perform an SNI handshake.
    [tls.stores.default.defaultCertificate]
      certFile = "/local/obscol.obs.test.nsw.education.pem"
      keyFile = "/local/obscol.obs.test.nsw.education.key"

[[tls.certificates]]
  certFile = "/local/obscol.obs.test.nsw.education.pem"
  keyFile = "/local/obscol.obs.test.nsw.education.key"

EOF
      }

      template {
        destination = "local/obscol.obs.test.nsw.education.pem"
        data = <<EOF
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7e
SPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOF
      }

      template {
        destination = "local/obscol.obs.test.nsw.education.key"
        data = <<EOF
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
      }

      resources {
        cpu    = 300
        memory = 512
      }
    }
  }
}

