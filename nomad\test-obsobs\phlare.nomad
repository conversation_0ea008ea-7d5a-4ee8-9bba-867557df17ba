
// test obsobs - phlare nomad job

job "phlare" {
  type        = "service"

  datacenters = ["dc-obsobs-test"]

  group "phlare" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay    = "25s"
      mode     = "delay"
    }

    volume "vol_phlare" {
      type = "host"
      source = "vol_phlare"
      read_only = false
    }

    network {
      port "port_phlare"  {
        static = 4100
      }
    }

    task "phlare" {

      driver = "docker"

      volume_mount {
        volume      = "vol_phlare"
        destination = "/mnt/phlare"
        read_only   = false
      }

      config {
        image = "grafana/phlare:0.1.0"

        dns_servers = ["************"]

        ports = ["port_phlare"]

        logging {
          type = "loki"
          config {
            loki-url = "http://loki.int.jeddi.org:3100/loki/api/v1/push"
          }
        }        

        args = [
          "--config.file",
          "local/phlare.yaml",
        ]

      }


      template {
        data = <<EOH

scrape_configs:
  - job_name: "phlareself"
    scrape_interval: "1m"
    static_configs:
      - targets: ["tl0992obsobs01.test.nsw.education:4100"]

  - job_name: "phlare-prometheus"
    scrape_interval: "1m"
    static_configs:
      - targets: ["tl0992obsobs01.test.nsw.education:9090"]

EOH
        destination = "local/phlare.yaml"
      }

      resources {
        cpu    = 512
        memory = 512
      }

      service {
        name = "phlare"
        port = "port_phlare"

        check {
          name     = "Phlare healthcheck"
          port     = "port_phlare"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.phlare.rule=Host(`phlare.obsobs.test.nsw.education`)",
          "traefik.http.routers.phlare.tls=false",
          "traefik.http.routers.phlare.entrypoints=http,https,phlare",
        ]

      }

    }
  }
}

