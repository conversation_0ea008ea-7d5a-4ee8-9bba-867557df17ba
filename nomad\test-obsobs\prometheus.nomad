
// Obs-Obs watcher instance of Prometheus in the TEST env.


job "prometheus" {
  type = "service"

  datacenters = ["dc-obsobs-test"]

  group "prometheus" {
    count = 1

    network {
      port "http" {
        static = 9090
        }
    }

    volume "vol_prometheus"  {
      type = "host"
      source = "vol_prometheus"
      read_only = false
    }

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "vol_prometheus"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["http"]

        # 2022-11-29 jedd - bumping from 2.39.0 to 2.40.3 due to memory leak advised by <PERSON>
        # image = "https://docker.io/prom/prometheus:v2.39.0"
        image = "https://docker.io/prom/prometheus:v2.40.3"

        # dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "http://tl0992obsobs01.nsw.education:3100/loki/api/v1/push"
          }
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/test/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          # "--web.external-url=https://prometheus.obsobs.test.nsw.education",
          "--web.external-url=http://tl0992obsobs01.test.nsw.education",
          "--web.page-title=ObsObs Test Prometheus",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--storage.tsdb.path=/prometheus",
          "--storage.tsdb.retention.time=8d",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }

#        tags = [
#          "traefik.enable=true",
#          "traefik.http.routers.prometheus.rule=Host(`prometheus.obsobs.test.nsw.education`)",
#          "traefik.http.routers.prometheus.tls=false",
#          "traefik.http.routers.prometheus.entrypoints=http,https,prometheus",
#        ]

        meta {
          cir_app_id = "obs"
          cluster    = "obsobs"
          env        = "test"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    provenance: "obsobs-prometheus"

  scrape_interval: 1m

scrape_configs:
  - job_name: 'prometheus-obsobs-test'
    static_configs:
      - targets: ["tl0992obsobs01.test.nsw.education:9090"]

  - job_name: 'prometheus-obscol-test'
    scheme: 'https'
    static_configs:
      - targets: ['prometheus-app-group-0.obs.test.nsw.education',
                  'prometheus-app-group-1.obs.test.nsw.education',
                  'prometheus-app-group-2.obs.test.nsw.education',
                  'prometheus-app-group-3.obs.test.nsw.education',
                  'prometheus-app-group-4.obs.test.nsw.education',
                  'prometheus-app-group-5.obs.test.nsw.education',
                  'prometheus-app-group-6.obs.test.nsw.education'
                 ]

    metrics_path: /metrics


#  - job_name: 'nomad-obsobs-test'
#    metrics_path: /v1/metrics
#    params:
#      format: ['prometheus']
#    consul_sd_configs:
#      - server: 'consul.service.dc-obsobs-test.collectors.obsobs.test.nsw.education:8500'
#        datacenter: 'dc-obsobs-test'
#        @TODO fix up the token for OBSOBS
#        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
#        services: ['nomad-client', 'nomad']
#        tags: ['http']



# Loki locally on ObsObs 
  - job_name: 'loki-obsobs-test'
    static_configs:
      - targets: ['tl0992obsobs01.test.nsw.education:3100']
    scheme: 'http'
    metrics_path: /metrics

# Loki on ObsCol test
  - job_name: 'loki-obscol-test'
    static_configs:
      - targets: ['loki.obs.test.nsw.education']
    scheme: 'https'
    metrics_path: /metrics

# Telegraf targets - host metrics of ourself (obsobs) and target (obscol)
  # Elsewhere this job name might be called 'openmetrics' but it may be nice
  # to distinguish this.  Perhaps 'telegraf-static' or some variant.
  - job_name: 'telegraf'
    static_configs:
      - targets: ['tl0992obsobs01.test.nsw.education:11055',
                  'tl0992obscol01.nsw.education:11055',
                  'tl0992obscol02.nsw.education:11055',
                  'tl0992obscol03.nsw.education:11055',
                  'tl0992obscol04.nsw.education:11055',
                  'tl0992obscol05.nsw.education:11055',
                  ]


#rule_files:
#  - /etc/prometheus/rules.d/*.rules
#  - /etc/prometheus/rules.d/*.yaml
#  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - static_configs:
      - targets:
        - tl0992obsobs01.test.nsw.education:9093
#    - consul_sd_configs:
#      - server: 'consul.service.dc-obsobs-test.collectors.obsobs.test.nsw.education:8500'
#        datacenter: 'dc-obsobs-test'
#        # @TODO fix up token
#        token: 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'
#        services: ['alertmanager']

EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 2048
      }

    }
  }
}
