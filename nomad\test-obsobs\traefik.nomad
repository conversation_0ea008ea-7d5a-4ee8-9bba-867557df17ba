
// Obs-Obs watcher instance of T<PERSON><PERSON><PERSON>  in the TEST env - a PoC to monitor ObsCol stack


job "traefik" {
  type        = "system"

  datacenters = ["dc-obsobs-test"]
  
  group "traefik" {

    network {
      dns {
        servers = ["192.168.31.1"]
      }

      port "http" {
        static = 80
      }

      port "https" {
        static = 443
      }

      port "traefik" {
        static = 8081
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      user = "root:root"

      config {
        image        = "https://docker.io/library/traefik:latest"

#        logging {
#            type = "loki"
#            config {
#                loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
#                }
#            }        

        network_mode = "host"

        # Evidently from docker 20.03 the behaviour was changed - docker defined that unprivileged port start 
        # at 0 instead of 1024 so the NET_BIND_SERVICE capability is not needed. 
        # cap_add = ["NET_BIND_SERVICE"]

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
        ]
      }

      template {
        data = <<EOF
debug = false
logLevel = "INFO"
defaultEntryPoints = ["https","http"]

[entryPoints]
    [entryPoints.http]
    address = ":80"

    [entryPoints.https]
    address = ":443"

      [entryPoints.https.http.tls]
        [[entryPoints.https.http.tls.domains]]
        main = "obscol.obsobs.test.nsw.education"
        sans = ["*.obsobs.test.nsw.education"]

    [entryPoints.traefik]
    address = ":8081"


# We use standard ports for everything, so we can come into those services even if Traefik is
# unavailable.  Single host + single instance of any container == no contention.

    [entryPoints.grafana]
    address = ":3000"    

    [entryPoints.loki]
    address = ":3100"    

    [entryPoints.mimir]
    address = ":8080"    

    [entryPoints.prometheus]
    address = ":9090"


[api]
    dashboard = true
    insecure  = true



# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = false

    [providers.consulCatalog.endpoint]
      address = "consul.service.dc-obsobs-test.collectors.obsobs.test.nsw.education:8500"
      scheme  = "http"

# Enable File Provider for "Dynamic Configuration" elements
[providers.file]
  directory = "/local/traefik.d"

[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]

EOF

        destination = "local/traefik.toml"
      }

      template {
        destination = "local/traefik.d/certificates.toml"
        data = <<EOF
[tls.stores]
  [tls.stores.default]
    # This one should be used if the client did not perform an SNI handshake.
    [tls.stores.default.defaultCertificate]
      certFile = "/local/obscol.obsobs.test.nsw.education.pem"
      keyFile = "/local/obscol.obsobs.test.nsw.education.key"

[[tls.certificates]]
  certFile = "/local/obscol.obsobs.test.nsw.education.pem"
  keyFile = "/local/obscol.obsobs.test.nsw.education.key"

EOF
      }

      template {
        destination = "local/obscol.obsobs.test.nsw.education.pem"
        data = <<EOF
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7eSPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOF
      }

      template {
        destination = "local/obscol.obsobs.test.nsw.education.key"
        data = <<EOF
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
      }

      resources {
        cpu    = 300
        memory = 256
      }
    }
  }
}

